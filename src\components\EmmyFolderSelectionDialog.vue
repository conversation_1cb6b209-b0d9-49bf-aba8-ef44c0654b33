<template>
  <CardDialog v-model="modelValue" positive-button="Suivant" @update:model-value="modelValue = $event">
    <VRow class="flex-column content-layout">
      <VCol>
        <VRow class="align-center">
          <VCol cols="6">
            <SearchInput :model-value="pageFilter.search" @update:model-value="updateSearch" />
          </VCol>
          <VCol>
            <VCheckbox
              v-if="userStore.isAdminPlus"
              v-model="showOnlyEmmyFoldersInStep80"
              label="Seulemement les dossiers à l'étape 80"
            />
          </VCol>
          <VCol class="flex-grow-0">
            <EmmyFolderCreateDialog @save:emmy-folder="handleSaveEmmyFolder">
              <template #activator="{ props }">
                <NjBtn v-bind="props"> Créer </NjBtn>
              </template>
            </EmmyFolderCreateDialog>
          </VCol>
        </VRow>
      </VCol>
      <VCol class="content-layout">
        <NjDataTable
          class="content-layout_main"
          :headers="emmyHeader"
          :page="data.value!"
          :pageable="pageable"
          :selections="emmyfolderSelections"
          @update:pageable="updatePageable"
          @update:selections="emmyfolderSelections = $event"
        />
      </VCol>
    </VRow>
    <template #actions>
      <NjBtn variant="outlined" @click="modelValue = false">Annuler</NjBtn>
      <NjBtn @click="addOperationsInEmmyFolder(emmyfolderSelections[0])">Suivant</NjBtn>
    </template>
    <template #activator="scopre">
      <slot name="activator" v-bind="scopre"></slot>
    </template>
  </CardDialog>
</template>

<script setup lang="ts">
import type { EmmyFolderFilter } from '@/api/emmyFolder'
import { useDialogStore } from '@/stores/dialog'
import { useSnackbarStore } from '@/stores/snackbar'
import { useUserStore } from '@/stores/user'
import type { EmmyFolder } from '@/types/emmyFolder'
import EmmyFolderCreateDialog from '@/views/emmyfolder/EmmyFolderCreateDialog.vue'
import type { DataTableHeader } from './okta/NjDataTable.type'
import type { OperationFilter } from '@/api/operation'

const modelValue = defineModel({
  type: Boolean,
})
const props = defineProps<{
  operationfilterToAdd: OperationFilter
}>()

const emit = defineEmits(['operations-added'])
const dialogStore = useDialogStore()
const snackbarStore = useSnackbarStore()
const userStore = useUserStore()

const emmyHeader: DataTableHeader[] = [
  {
    title: 'Numéro dossier',
    value: 'emmyCode',
  },
  {
    title: 'Nom',
    value: 'name',
  },
  {
    title: 'Nb. opérations',
    value: 'operationsCount',
  },
  {
    title: 'Etape',
    value: 'stepId',
  },
  {
    title: 'Responsable',
    value: 'creationUser',
    formater: (_, value) => displayFullnameUser(value),
  },
  {
    title: 'Validation Instruction',
    value: 'na1',
  },
  {
    title: 'Envoi PNCEE',
    value: 'na2',
  },
  {
    title: 'Délivrance PNCEE',
    value: 'na3',
  },
  {
    title: 'Nb. demandés kWhc',
    value: 'na4',
  },
  {
    title: 'Mnt. dem. €',
    value: 'na5',
  },
  {
    title: 'Réf. client',
    value: 'na6',
  },
]

const showOnlyEmmyFoldersInStep80 = ref(true)
watch(showOnlyEmmyFoldersInStep80, () => {
  reload()
})

const { data, pageable, pageFilter, updatePageable, updateFilter, reload } = usePagination<
  EmmyFolder,
  EmmyFolderFilter
>(
  (filter, pageable) =>
    emmyFolderApi.findAll(
      {
        ...filter,
        stepIds: showOnlyEmmyFoldersInStep80.value ? [80] : [80, 90, 100],
        myFolders: filter.myFolders ? true : undefined,
      },
      pageable
    ),
  {}
)

const updateSearch = (value: string) => {
  updateFilter({ search: value })
}

// const emmyFolderCreateDialog = ref(false)

const addOperationsInEmmyFolder = async (v: EmmyFolder) => {
  if (v.stepId !== 80) {
    if (
      !(await dialogStore.addAlert({
        title: "Ajouter une opération à un dossier EMMY au delà de l'étape 80",
        message:
          "Vous allez ajouter un dossier EMMY qui est au delà de l'étape 80.\nÊtes-vous sûr de vouloir continuer ?",
        maxWidth: 640,
      }))
    ) {
      return
    }
  }
  emmyFolderApi
    .addOperation(v.id, props.operationfilterToAdd)
    .then(() => {
      modelValue.value = false
      snackbarStore.setSuccess(`Ajout au dossier EMMY effectué avec succés`)
      emit('operations-added')
    })
    .catch(async (e) => {
      snackbarStore.setError(await handleAxiosException(e))
    })
}

const emmyfolderSelections = ref<EmmyFolder[]>([])

const handleSaveEmmyFolder = async (v: EmmyFolder) => {
  await addOperationsInEmmyFolder(v)
  // emmyFolderCreateDialog.value = false
}
</script>
