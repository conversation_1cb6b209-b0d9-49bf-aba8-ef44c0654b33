<template>
  <VCol class="flex-grow-0 my-1">
    <VRow>
      <VCol cols="3">
        <NjSwitch v-model="operationEventsFilter.message" label="Message" inline />
      </VCol>
      <VCol cols="3">
        <NjSwitch v-model="operationEventsFilter.commentary" label="Commentaire" inline />
      </VCol>
      <VCol cols="6">
        <VSelect
          v-model="selected"
          label="Changement"
          inline
          :items="['Documents', 'Passage étape', 'Volume et valorisation', 'Autres']"
          multiple
        >
          <template #selection="{ item }">
            <VChip>{{ item.title }} </VChip>
          </template>
        </VSelect>
      </VCol>
    </VRow>
  </VCol>
  <div ref="element" class="content-layout__main pa-3 w-100">
    <OperationEventHistoryCard
      v-for="operationEvent in operationEvents"
      :key="operationEvent.uuid"
      :operation-event="operationEvent"
      :operation="props.operation"
      :documents-available="documentsAvailable.value"
      class="mb-4"
      @reload="reload"
      @treated="emit('treated', $event)"
    />
  </div>
</template>
<script setup lang="ts">
import { operationEventApi, type OperationEventFilter } from '@/api/operationEvent'
import { type OperationEvent } from '@/types/operationEvent'
import { type Page } from '@/types/pagination'
import { useInfiniteScroll } from '@vueuse/core'
import OperationEventHistoryCard from './OperationEventHistoryCard.vue'
import type { EnhancedDocument } from '@/types/document'
import type { PropType } from 'vue'
import type { Operation } from '@/types/operation'

const props = defineProps({
  operation: {
    type: Object as PropType<Operation>,
    required: true,
  },
})

const emit = defineEmits<{
  treated: [any]
}>()
const operationEvents = ref<OperationEvent[]>([])
const operationEventsPage = ref(emptyValue<Page<OperationEvent>>())
const operationEventsFilter = ref<OperationEventFilter>({
  message: true,
  commentary: true,
  volumeOrValuationChange: true,
  validateStep: true,
  documents: true,
  others: true,
})
const selected = ref<string[]>(['Documents', 'Passage étape', 'Volume et valorisation', 'Autres'])
const operationEventPageNumber = ref(0)

const getOperationEvents = async () => {
  operationEventsFilter
  await handleAxiosPromise(
    operationEventsPage,
    operationEventApi.findAll(
      props.operation.id,
      { ...operationEventsFilter.value, top: true },
      { sort: ['creationDateTime,DESC'], size: 5, page: operationEventPageNumber.value++ }
    )
  )
  operationEvents.value.push(...operationEventsPage.value.value!.content)
}

const element = useTemplateRef('element')
useInfiniteScroll(
  element,
  async () => {
    if (!operationEventsPage.value.value?.last) {
      await getOperationEvents()
    }
  },
  {
    distance: 10,
  }
)

const reload = () => {
  operationEventPageNumber.value = 0
  operationEvents.value = []
  return getOperationEvents()
}

// to avoid routing side effect using go backward
watch(
  () => props.operation.id,
  () => {
    reload()
  }
)

watch(
  operationEventsFilter,
  () => {
    reload()
  },
  {
    deep: true,
  }
)

watch(
  selected,
  (v) => {
    const copy = { ...operationEventsFilter.value }
    copy.volumeOrValuationChange = !!v.find((i) => i === 'Volume et valorisation')
    copy.validateStep = !!v.find((i) => i === 'Passage étape')
    copy.documents = !!v.find((i) => i === 'Documents')
    copy.others = !!v.find((i) => i === 'Autres')
    operationEventsFilter.value = copy
  },
  {
    deep: true,
  }
)

const documentsAvailable = ref(emptyValue<Page<EnhancedDocument>>())
onMounted(() => {
  handleAxiosPromise(documentsAvailable, operationDocumentApi.findAll({ operationId: props.operation.id }, {}))
})

defineExpose({
  reload: reload,
})
</script>
