import type { Page, Pageable } from '@/types/pagination'
import type { AxiosInstance, AxiosPromise } from 'axios'
import axiosInstance from '.'

export type LoggingEventFilter = Partial<{ level: string; search: string }>

export interface LoggingEventException {
  eventId: number
  i: number
  traceLine: string
}

export interface LoggingEvent {
  eventId: number
  formattedMessage: string
  loggerName: string
  levelString: string
  referenceFlag: number | null
  arg0: string
  arg1: string
  arg2: string
  arg3: string
  callerFilename: string
  callerClass: string
  callerMethod: string
  callerLine: string
}

class LoggingEventApi {
  public constructor(private axios: AxiosInstance) {}

  public findAll(filter: LoggingEventFilter, pageable: Pageable): AxiosPromise<Page<LoggingEvent>> {
    return this.axios.get('/logs', {
      params: { ...filter, ...pageable },
    })
  }

  public getStacktrace(eventId: number): AxiosPromise<Array<LoggingEventException>> {
    return this.axios.get('/logs/' + eventId + '/stacktrace')
  }
}

export const loggingEventApi = new LoggingEventApi(axiosInstance)
