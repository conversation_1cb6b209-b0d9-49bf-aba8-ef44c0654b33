<template>
  <template v-if="edit">
    <VTextField
      v-if="type === 'text'"
      :model-value="modelValue"
      :label="label"
      :rules="rules"
      @update:model-value="emit('update:model-value', $event)"
    />
  </template>
  <template v-else>
    <NjDisplayValue :label="label"> </NjDisplayValue>
  </template>
</template>

<script setup lang="ts">
import type { ValidationRule } from '@/types/rule'
import type { PropType } from 'vue'
import NjDisplayValue from './NjDisplayValue.vue'

const props = defineProps({
  modelValue: [String, Object, Array, Number, Boolean],
  edit: {
    type: Boolean,
    default: undefined,
  },
  type: {
    type: String as PropType<'text' | 'email' | 'radio'>,
    required: true,
  },
  label: { type: String, required: true },
  rules: Array as PropType<ValidationRule[]>,
})

const emit = defineEmits<{
  (e: 'update:model-value', value: any): void
}>()

const editInject = inject<boolean>('form-editable', false)
const edit = computed<boolean>(() => props.edit ?? editInject)
</script>
