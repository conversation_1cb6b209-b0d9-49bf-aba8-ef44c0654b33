import type { BusinessPlan, BusinessPlanRequest, BusinessPlanSummary } from '@/types/businessPlan'
import type { Page, Pageable } from '@/types/pagination'
import type { AxiosInstance, AxiosPromise } from 'axios'
import axiosInstance from '.'

export interface BusinessPlanFilter {
  search?: string
  myBusinessPlans?: boolean
  ids?: number[]
}

const businessPlanUri = '/business_plans'

class BusinessPlanApi {
  public constructor(private axios: AxiosInstance) {}

  public create(request: BusinessPlanRequest): AxiosPromise<BusinessPlan> {
    return this.axios.post(businessPlanUri, request)
  }

  public edit(id: number, request: BusinessPlanRequest): AxiosPromise<BusinessPlan> {
    return this.axios.put(businessPlanUri + '/' + id, request)
  }

  public findAll(filter: Record<string, unknown>, pageable: Pageable): AxiosPromise<Page<BusinessPlan>> {
    return this.axios.get(businessPlanUri, {
      params: { ...filter, ...pageable },
    })
  }

  public findById(id: number): AxiosPromise<BusinessPlan> {
    return this.axios.get(businessPlanUri + '/' + id)
  }

  public getSummaryById(id: number): AxiosPromise<BusinessPlanSummary> {
    return this.axios.get(businessPlanUri + '/' + id + '/summary')
  }

  public deleteById(id: number): AxiosPromise<void> {
    return this.axios.delete(businessPlanUri + '/' + id)
  }

  public deleteByIds(ids: number[]): AxiosPromise<void> {
    return this.axios.delete(businessPlanUri, { params: { ids } })
  }
}

export const businessPlanApi = new BusinessPlanApi(axiosInstance)
