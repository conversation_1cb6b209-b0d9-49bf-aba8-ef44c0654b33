<template>
  <VCard class="content-layout">
    <VCardTitle class="content-layout__header py-0">
      <VRow class="align-center">
        <VCol>
          {{ id ? 'Détails' : 'Nouveau Document' }}
        </VCol>
        <VCol class="flex-grow-0 me-n4">
          <NjIconBtn v-if="id" icon="mdi-pencil" color="primary" rounded="0" @click="toggleEdit" />
          <NjIconBtn icon="mdi-close" color="primary" rounded="0" @click="handleCancel" />
        </VCol>
      </VRow>
    </VCardTitle>
    <VDivider />
    <VCardText class="content-layout__main content-layout">
      <DocumentTypeForm
        v-if="edit"
        ref="editDocumentTypeRef"
        :document="documentType.value"
        @cancel="
          () => {
            edit = false
            emit('cancel')
          }
        "
        @saved="handleSave"
      />
      <DocumentTypeDetail v-else :document-type="documentType.value" />
    </VCardText>
  </VCard>
</template>
<script lang="ts" setup>
import { documentTypeApi } from '@/api/documentType'
import { makeEmptyDocumentType, type DocumentType } from '@/types/documentType'
import DocumentTypeForm from './DocumentTypeForm.vue'
import DocumentTypeDetail from './DocumentTypeDetail.vue'
import { VCard, VCardTitle, VCol, VRow } from 'vuetify/components'

const props = defineProps({
  id: {
    type: Number,
    default: undefined,
  },
})

const emit = defineEmits(['saved', 'cancel'])

const documentType = ref(emptyValue<DocumentType>())
const edit = ref(true)

const editDocumentTypeRef = ref<typeof DocumentTypeForm | null>(null)
const checkUnsaved = (confirmCallback: () => void) =>
  editDocumentTypeRef.value?.checkUnsaved(confirmCallback) ?? confirmCallback()

const handleSave = (docType: DocumentType) => {
  documentType.value.value = docType
  edit.value = false
  emit('saved')
}

const handleCancel = () => {
  checkUnsaved(() => {
    edit.value = false
    emit('cancel')
  })
}

const toggleEdit = () => {
  if (!edit.value) {
    edit.value = true
    return
  }
  checkUnsaved(() => (edit.value = false))
}

watch(
  () => props.id,
  (v) => {
    documentType.value.value = makeEmptyDocumentType()
    edit.value = true
    if (v) {
      edit.value = false
      handleAxiosPromise(documentType, documentTypeApi.getOne(v), (value) => {
        documentType.value.value = value.data
      })
    }
  },
  {
    immediate: true,
  }
)

defineExpose({ checkUnsaved })
</script>
