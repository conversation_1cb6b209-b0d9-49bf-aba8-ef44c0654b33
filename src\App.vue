<template>
  <VApp>
    <!-- Hidden element for measurement -->
    <div
      ref="zoomDetector"
      style="position: absolute; visibility: hidden; height: 1em; width: 1em; pointer-events: none"
    ></div>
    <VAppBar
      class="px-3"
      flat
      color="white"
      :density="display.mdAndDown ? 'compact' : 'comfortable'"
      border="bottom"
      :style="{ 'background-color': backgroundColor }"
    >
      <RouterLink :to="{ name: 'Home' }" class="d-flex">
        <img :src="logoSvg" style="height: 4rem" />
      </RouterLink>

      <VDivider vertical class="my-5 mx-2" />

      <div v-if="env !== 'prd'" class="mx-2" :color="envColor" variant="elevated" rounded="chip">
        {{ env.toUpperCase() }}
      </div>
      <WarningMenu v-if="userStore.isAdminPlus" />

      <h3 v-if="env !== 'prd'" class="ma-4">{{ buildInfoHeader }}</h3>

      <div v-if="showBirthday">
        <VTooltip>
          <template #activator="{ props }">
            <div v-bind="props" class="d-flex align-center">
              <span style="font-size: 1.75rem">🎂</span> {{ birthYears }} an(s)
            </div>
          </template>
          Joyeux anniversaire Capte ! Cela fait maintenant {{ birthYears }} an(s) (22/01/2024).
        </VTooltip>
      </div>

      <VSpacer></VSpacer>

      <VTabs slider-color="primary" :mandatory="false">
        <VTab v-for="link in links" :key="link.routeName" :to="{ name: link.routeName }">
          {{ link.label }}
        </VTab>
      </VTabs>
      <RoleView v-if="isDEV" />
      <UserMenu v-if="userStore.currentUser && isAppReady" :user="userStore.currentUser" />
    </VAppBar>

    <SideBar v-if="sidebarStore.items.length" :items="sidebarStore.items" />

    <VMain>
      <RouterView
        v-if="isAppReady"
        style="border-top: 1px solid rgba(var(--v-border-color), var(--v-border-opacity)); overflow: auto; height: 100%"
      />
      <!-- <HelloWorld /> -->
    </VMain>

    <VSnackbar
      v-model="snackbarStore.state.modelValue"
      :timeout="snackbarStore.state.timeout"
      :color="snackbarStore.state.color"
      >{{ snackbarStore.state.message }}</VSnackbar
    >

    <VDialog
      v-for="d in dialogStore.state"
      :key="d.id"
      v-model="d.modelValue"
      :max-width="d.maxWidth"
      :persistent="d.persistent"
      :width="d.width"
    >
      <VCard v-click-outside="() => (d.persistent ? undefined : d.result(false))" class="content-layout">
        <VCardTitle class="content-layout__header">
          <slot name="title">
            <VRow v-if="d.title" class="flex-column" no-gutters>
              <VCol>
                <VRow class="align-center">
                  <VCol class="text-wrap">
                    {{ d.title }}
                  </VCol>
                  <VCol v-if="d.closeable" class="flex-grow-0">
                    <NjIconBtn icon="mdi-close" rounded="0" @click="d.modelValue = false"></NjIconBtn>
                  </VCol>
                </VRow>
              </VCol>
            </VRow>
          </slot>
        </VCardTitle>
        <VDivider />
        <ErrorAlert :message="d.error" />
        <VCardText class="content-layout__main pb-3" style="flex-basis: auto">
          <div v-for="l in d.message.toString().split('\n')" :key="l">
            {{ l }}
          </div>
        </VCardText>
        <VDivider />
        <VCardActions class="content-layout__footer">
          <VSpacer />
          <NjBtn v-if="d.negativeButton" variant="outlined" @click="d.result(false)">{{ d.negativeButton }}</NjBtn>
          <NjBtn v-if="d.positiveButton" @click="d.result(true)">{{ d.positiveButton }}</NjBtn>
        </VCardActions>
      </VCard>
    </VDialog>
    <CheckNewVersion />
  </VApp>
</template>

<script setup lang="ts">
import logoSvg from '@/assets/capte-logo/CAPTE-couleur.png'
import { useAuth } from '@okta/okta-vue'
import { useElementSize, useIntervalFn, useNow } from '@vueuse/core'
import { formatDistanceToNow, isAfter, isBefore, parseISO, setDefaultOptions } from 'date-fns'
import { fr } from 'date-fns/locale'
import { useDisplay } from 'vuetify'
import { VCol, VDialog, VRow, VSpacer } from 'vuetify/components'
import axiosInstance from './api'
import { gtfAxiosInstance } from './api/external/gtf'
import CheckNewVersion from './components/CheckNewVersion.vue'
import SideBar from './components/SideBar.vue'
import { useAdminConfigurationStore } from './stores/adminConfiguration'
import { fetchEvents, removeEvents, setFontRatio } from './stores/analytics'
import { useDialogStore } from './stores/dialog'
import { usePeriodsStore } from './stores/periods'
import { useSidebarStore } from './stores/sidebar'
import { useSnackbarStore } from './stores/snackbar'
import { useStepsStore } from './stores/steps'
import { useTerritoriesStore } from './stores/territory'
import { useUserStore } from './stores/user'
import { useValuationTypesStore } from './stores/valuationTypes'
import type { User } from './types/user'
import RoleView from './views/RoleView.vue'
import UserMenu from './views/UserMenu.vue'
import WarningMenu from './views/WarningMenu.vue'

const display = useDisplay()
const isDEV = ref(import.meta.env.DEV)

const links = computed(
  (): {
    label: string
    routeName: string
  }[] => {
    const links = []

    if (userStore.currentUser.roles.length > 0) {
      links.push({
        label: 'Simulations CEE',
        routeName: 'SimulationAllView',
      })
    }

    if (
      userCanManageOperation(userStore.currentUser) ||
      userHasRole(userStore.currentUser, 'DAF', 'DAF_SIEGE', 'SEE_OSH')
    ) {
      links.splice(0, 0, {
        label: 'Opérations CEE',
        routeName: 'OperationAllView',
      })
    }

    if (userHasRole(userStore.currentUser, 'ADMIN', 'ADMIN_PLUS')) {
      links.push({
        label: 'Administration',
        routeName: 'AdminView',
      })
    }

    if (userStore.canAccessSales) {
      links.push({
        label: 'Ventes CEE',
        routeName: 'CEESalesView',
      })
    }
    if (
      userHasRole(
        userStore.currentUser,
        'ADMIN',
        'ADMIN_PLUS',
        'AGENCE_PLUS',
        'SUPPORT_AGENCE_PLUS',
        'DAF',
        'DAF_SIEGE'
      )
    ) {
      links.push({
        label: 'Suivi DAF',
        routeName: 'StockCEEAllView',
      })
    }

    if (isDEV.value) {
      links.push({
        label: 'Composants',
        routeName: 'DebugComponents',
      })
    }
    return links
  }
)

const userStore = useUserStore()
const snackbarStore = useSnackbarStore()
const dialogStore = useDialogStore()
const territoryStore = useTerritoriesStore()

const router = useRouter()

onMounted(() => {
  console.info('env: ', import.meta.env.VITE_APP_ENV)
  if (env.value !== 'prd') {
    document.title = `[${env.value.toUpperCase()}] Capte`
  }
})
// Pour s'assurer que le set de GUID se fasse avant tout connexion car le getUser est une promise
const isAppReady = ref(false)
const auth = useAuth()
const interceptorId = ref<number>()
const gtfInterceptorId = ref<number>()
if (import.meta.env.DEV) {
  onMounted(() => {
    auth.getUser().then(async (v) => {
      const gid = v.preferred_username?.replace('@engie.com', '')
      // const gid = 'ADH145'
      console.debug('gid', gid)
      axiosInstance.defaults.headers.common['x-api-sub'] = gid
      await handleLogin()
      await loadStores(userStore.currentUser)
      isAppReady.value = true
      console.info('[DEV] App is ready')
    })
  })
} else {
  onMounted(async () => {
    // axiosInstance.defaults.headers.common.Authorization = auth.getIdToken()
    interceptorId.value = axiosInstance.interceptors.request.use((config) => {
      config.headers.Authorization = auth.getIdToken()
      return config
    })
    await handleLogin()
    await loadStores(userStore.currentUser)
    isAppReady.value = true
  })
}

onUnmounted(() => {
  if (interceptorId.value) {
    gtfAxiosInstance.interceptors.request.eject(interceptorId.value)
  }
  if (gtfInterceptorId.value) {
    gtfAxiosInstance.interceptors.request.eject(gtfInterceptorId.value)
  }
})

onMounted(() => {
  gtfInterceptorId.value = gtfAxiosInstance.interceptors.request.use((config) => {
    config.headers.Authorization = auth.getIdToken()
    return config
  })
})

const buildInfo = ref({
  shaGit: import.meta.env.VITE_BUILD_COMMIT,
  date: import.meta.env.VITE_BUILD_DATE,
  version: import.meta.env.VITE_APP_VERSION,
})
const buildInfoHeader = ref('')
onMounted(() => {
  console.info('BUILD_COMMIT', buildInfo.value.shaGit)
  console.info('BUILD_DATE', buildInfo.value.date)
  console.info('BUILD_VERSION', buildInfo.value.version)
  setDefaultOptions({ locale: fr })
  if (buildInfo.value.date !== undefined) {
    buildInfoHeader.value = 'MaJ il y a ' + formatDistanceToNow(parseISO(buildInfo.value.date), {})
  }
})

const handleLogin = async () => {
  await authenticationApi
    .loginOne()
    .then((r) => {
      userStore.set(r.data)
      if (!userStore.currentUser.active) {
        router.push({ name: 'UserProfileRequestCreate' })
      }
    })
    .catch(async (e) => {
      logException(e)
    })
}

const env = computed(() => import.meta.env.VITE_APP_ENV)
const envColor = computed(() => {
  switch (env.value) {
    case 'dev':
      return '#757575'
    case 'ppr':
      return '#2E7D32'
    case 'rec':
      return '#FF5252'
    default:
      return ''
  }
})
const backgroundColor = computed(() => {
  switch (env.value) {
    case 'dev':
      return '#E0E0E0 !important'
    case 'ppr':
      return '#E3F3E2 !important'
    case 'rec':
      return '#FDEAD9 !important'
    default:
      return ''
  }
})

const sidebarStore = useSidebarStore()

// Permet de charger dès le début les étapes de l'appli
const stepsStore = useStepsStore()
const adminConfigurationStore = useAdminConfigurationStore()
const periodsStore = usePeriodsStore()
const valuationTypesStore = useValuationTypesStore()

onUnmounted(() => {
  stepsStore.clearTimeouts()
  adminConfigurationStore.clearTimeouts()
  periodsStore.clearTimeouts()
  valuationTypesStore.clearTimeouts()
})

const loadStores = (user: User) => {
  const loadPromise = []
  if (user.active) {
    loadPromise.push(stepsStore.load(), adminConfigurationStore.load(), periodsStore.load(), valuationTypesStore.load())
  }
  loadPromise.push(territoryStore.load())

  return Promise.all(loadPromise)
}

const now = useNow({
  interval: 1000 * 3600,
})
// const now = shallowRef(new Date(2025,0,27))
const showBirthday = computed(() => {
  return (
    isBefore(now.value, new Date(now.value.getFullYear(), 1, 1)) &&
    isAfter(now.value, new Date(now.value.getFullYear(), 0, 21))
  )
})
const birthday = new Date(2024, 0, 22)
const birthYears = computed(() => {
  return now.value.getFullYear() - birthday.getFullYear()
})

const {} = useIntervalFn(
  async () => {
    const events = (await fetchEvents()).slice(0, 50)
    // console.info('Fetched analytics events:', events)
    if (events.length === 0) {
      return
    }
    analyticsEventApi
      // filter pour résoudre un bug temporaire suite à des changements de gestion en local de l'analytics, pourra être supprimer dans 1 mois
      .saveAll(events.filter((event) => typeof event.timestamp === 'string'))
      .then(() => {
        removeEvents(events)
      })
      .catch((e) => {
        console.error('Error saving analytics events:', e)
      })
  },
  1000 * 60 * 5
)

const zoomDetectorComponent = useTemplateRef('zoomDetector')
const { height: zoomDetectorComponentHeight } = useElementSize(zoomDetectorComponent)
watch(zoomDetectorComponentHeight, (v) => {
  setFontRatio(v / 16)
})
</script>
