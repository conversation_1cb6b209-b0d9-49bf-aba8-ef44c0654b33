<template>
  <CardDialog v-model="modelValue" title="Ajouter des documents" :disabled="uploading" width="50%">
    <GenericDocumentSubmit ref="genericDocumentSubmit" v-bind="$attrs" />
    <template #actions>
      <NjBtn variant="outlined" @click="modelValue = false"> Fermer </NjBtn>
      <NjBtn v-if="displaySaveBtn" :loading="uploading" @click="saveDocument"> Enregistrer </NjBtn>
    </template>
  </CardDialog>
</template>
<script setup lang="ts">
import { useSnackbarStore } from '@/stores/snackbar'
import GenericDocumentSubmit from './GenericDocumentSubmit.vue'
import type SendDocumentResult from './sendDocumentResult'

const modelValue = defineModel<boolean>({ default: false })

const emits = defineEmits<{
  'save-document': [SendDocumentResult[]]
}>()

const snackbarStore = useSnackbarStore()
const genericDocumentSubmitRef = useTemplateRef('genericDocumentSubmit')

const displaySaveBtn = ref(true)
const uploading = ref(false)

const saveDocument = async () => {
  uploading.value = true
  const response = (await genericDocumentSubmitRef.value?.uploadFiles()) ?? false
  uploading.value = false
  if (response !== false) {
    emits('save-document', response)
  }
  if (response === false || response.some((it) => it.type === 'error')) {
    return
  }
  displaySaveBtn.value = false
  modelValue.value = false
  snackbarStore.setSuccess("Le(s) fichier(s) ont bien été ajouté aux documents de l'opération")
}

watch(modelValue, (v) => {
  if (v) {
    displaySaveBtn.value = true
    genericDocumentSubmitRef.value?.reset()
  }
})
</script>
