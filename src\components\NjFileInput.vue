<template>
  <div class="nj-file-input">
    <div
      style="cursor: pointer"
      @dragenter.prevent
      @dragover.prevent="onDragOver"
      @dragleave.prevent="onDragLeave"
      @drop.prevent="onDrop"
      @click="onClick"
    >
      <slot>
        <VCard
          variant="outlined"
          style="border: 1px dashed #007acd"
          :class="{ 'nj-file-input__drop-zone--drag-over': isFileOver }"
          class="nj-file-input__drop-zone d-flex pa-4 flex-column align-center"
        >
          <VIcon icon="mdi-file" size="x-large" color="primary" />
          <div :align="display.lgAndUp ? 'center' : ''" style="position: relative; z-index: 5000">
            Déposer un ou plusieurs document(s)
            <input ref="inputFileRef" type="file" style="display: none" :multiple="props.multiple" @change="update" />
          </div>
          <div class="text-grey">ou</div>
          <NjBtn variant="outlined">Parcourir mes dossiers</NjBtn>
        </VCard>
      </slot>
    </div>
    <span class="nj-file-input__extension">
      Formats autorisés : pdf, png, xls, xlsx, doc, docx, jpeg, jpg, png, pptx, txt, csv, msg
    </span>
  </div>
</template>

<script setup lang="ts">
import { useDisplay } from 'vuetify'
import { debounce } from 'lodash'

const props = defineProps({
  multiple: Boolean,
})

const emit = defineEmits<{
  'new-files': [arg: FileList]
}>()
const display = useDisplay()

function onDrop(e: any) {
  console.debug('Dropped files:', e.dataTransfer.files)
  if (!e.dataTransfer.files.length) {
    return
  }
  emit('new-files', e.dataTransfer.files)
  updateIsFileOver(false)
}

const inputFileRef = useTemplateRef('inputFileRef')

function onClick() {
  inputFileRef.value!.click()
}

const update = (e: Event) => {
  emit('new-files', (e.target as any)?.files)
  inputFileRef.value!.value = ''
}

const isFileOver = ref(false)

const onDragLeave = (e: DragEvent) => {
  console.debug('Drag leave event', e)
  updateIsFileOver(false)
}
const onDragOver = (e: DragEvent) => {
  console.debug('Drag over event', e)
  updateIsFileOver(true)
}
const updateIsFileOver = debounce((v: boolean) => {
  console.debug('Update isFileOver:', v)
  if (isFileOver.value != v) {
    isFileOver.value = v
  }
}, 0)
</script>

<style scoped lang="scss">
.nj-file-input {
  &__drop-zone {
    transition: all 0.2s linear;
    overflow: visible;
    &--drag-over {
      scale: 1.05;
      background-color: white !important;
    }
  }

  &__extension {
    font-weight: 400;
    font-size: 14px;
    line-height: 20px;
    letter-spacing: 0px;
    vertical-align: middle;
    color: var(--text-neutral-tertiary, #60798b);
  }
}
</style>
