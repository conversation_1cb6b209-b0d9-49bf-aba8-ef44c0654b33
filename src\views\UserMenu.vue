<template>
  <VMenu>
    <template #activator="{ props }">
      <VBtn icon="mdi-link-variant" color="primary" v-bind="props" />
    </template>
    <VCard min-width="400px">
      <VCardText>
        <VList density="compact">
          <VListItem :href="adminConfiguration.yammerLink?.data" target="_blank" class="justify-text-center">
            <template #prepend>
              <img height="30" width="30" :src="vivaEngageIcon" />
            </template>

            <template #append>Viva Engage Capte</template>
          </VListItem>
          <VListItem :href="adminConfiguration.sharepointLink?.data" target="_blank">
            <template #prepend>
              <img height="30" width="30" :src="sharepointIcon" />
            </template>
            <template #append>Sharepoint Pôle CEE & Subventions</template>
          </VListItem>
          <VListItem
            v-if="adminConfiguration.operationalDashboardLink?.data"
            :href="adminConfiguration.operationalDashboardLink?.data"
            target="_blank"
          >
            <template #prepend>
              <img height="30" width="30" :src="pbiLogo" />
            </template>
            <template #append>Tableau de bord opérationnel</template>
          </VListItem>
        </VList>
      </VCardText>
    </VCard>
  </VMenu>
  <VMenu ref="notificationMenuRef" v-model="menuActive" location="bottom start" :close-on-content-click="false">
    <template #activator="{ props }">
      <VBtn icon="mdi-bell" color="primary" v-bind="props">
        <VBadge v-if="notificationsUnreadCount" :content="notificationsUnreadCount" color="green">
          <VIcon>mdi-bell</VIcon>
        </VBadge>
        <VIcon v-else>mdi-bell</VIcon>
      </VBtn>
    </template>
    <VCard>
      <VCardTitle class="d-flex align-center"
        >Notifications <VSpacer /><NjIconBtn
          v-show="notifications.length"
          title="Tout supprimer"
          icon="mdi-delete-empty-outline"
          color="primary"
          @click="clearAllNotifications"
      /></VCardTitle>
      <VCardText>
        <VRow ref="list" dense class="flex-column flex-nowrap" style="max-height: 400px; width: 500px; overflow: auto">
          <template v-if="notifications.length">
            <VCol v-for="notification in notifications" :key="notification.id">
              <VCard @click="onNotificationClick(notification)">
                <VCardText>
                  <VRow class="align-center capte-user-menu" dense>
                    <VCol v-if="!notification.readDateTime" cols="1" align-self="center">
                      <VIcon color="primary">mdi-circle-medium</VIcon>
                    </VCol>
                    <VCol>
                      <ErrorAlert
                        v-if="notification.metaData?.['@type'] == 'AlertValidateStep50NotificationMetadata'"
                        density="compact"
                        type="warning"
                        :message="`Relance: Votre opération
                      ${(notification.metaData as AlertValidateStep50NotificationMetadata).operation.chronoCode} est en attente de validation à l'étape 50`"
                      />
                      <template
                        v-else-if="notification.metaData?.['@type'] == 'FinalVersionToSendNotificationMetadata'"
                      >
                        L'opération
                        {{ (notification.metaData as FinalVersionToSendNotificationMetadata).operation.chronoCode }}
                        dont vous êtes le demandeur à été terminée. Vous devez maintenant envoyer la VF.
                      </template>
                      <template v-else-if="notification.metaData?.['@type'] == 'RgeEndOfValidityNotificationMetadata'">
                        La date de fin de validité du RGE de l'opération
                        {{ notification.metaData.operation.chronoCode }}
                        approche.
                      </template>
                      <template v-else-if="notification.metaData?.['@type'] == 'MessageNotificationMetadata'">
                        Vous avez un nouveau message de
                        {{ displayFullnameUser(notification.metaData.message.sender) }}.
                      </template>
                      <template
                        v-else-if="notification.metaData?.['@type'] == 'OperationToProcessNotificationMetadata'"
                      >
                        La simulation n°{{ notification.metaData.operation?.id }}
                        est prête à être transformée en opération.
                      </template>

                      <template
                        v-else-if="
                          notification.metaData?.['@type'] == 'ResponseToAtypicalValuationNotificationMetadata'
                        "
                      >
                        La demande de valorisation atypique pour l'opération n°{{
                          notification.metaData.operation?.chronoCode
                        }}
                        a été {{ notification.metaData.accepted ? 'acceptée' : 'refusée' }}
                      </template>
                      <template
                        v-else-if="notification.metaData?.['@type'] == 'ProfileRequestToProcessNotificationMetadata'"
                      >
                        {{ displayFullnameUser(notification.metaData.userProfileRequest.user) }} souhaite rejoindre
                        CAPTE.
                      </template>
                    </VCol>
                    <VCol class="flex-grow-0 my-n4 me-n4 capte-user-menu__action">
                      <NjIconBtn
                        density="compact"
                        icon="mdi-close"
                        variant="flat"
                        color="primary"
                        @click.stop.prevent="deleteNotification(notification)"
                      />
                    </VCol>
                  </VRow>
                </VCardText>
              </VCard>
            </VCol>
          </template>
          <VCol v-else-if="notificationsPage.loading">
            <VProgressCircular indeterminate color="primary" />
          </VCol>
          <VCol v-else> Vous n'avez pas encore de notification </VCol>
        </VRow>
      </VCardText>
    </VCard>
  </VMenu>
  <VMenu>
    <template #activator="{ props }">
      <VBtn icon="mdi-account" color="primary" v-bind="props" />
    </template>
    <VCard min-width="400px">
      <VCardText>
        <VRow class="flex-column dense">
          <VCol>
            <UserDisplay :user="user" />
          </VCol>
        </VRow>
      </VCardText>
    </VCard>
  </VMenu>
</template>
<script setup lang="ts">
import { notificationApi } from '@/api/notification'
import pbiLogo from '@/assets/pbi-logo/pbi-logo.svg'
import sharepointIcon from '@/assets/sharepoint/sharepoint.ico'
import vivaEngageIcon from '@/assets/vivaengage/vivaengage.ico'
import NjIconBtn from '@/components/NjIconBtn.vue'
import { useAdminConfigurationStore } from '@/stores/adminConfiguration'
import { useDialogStore } from '@/stores/dialog'
import { useSnackbarStore } from '@/stores/snackbar'
import type {
  AlertValidateStep50NotificationMetadata,
  FinalVersionToSendNotificationMetadata,
  MessageNotificationMetadata,
  Notification,
  OperationToProcessNotificationMetadata,
  RgeEndOfValidityNotificationMetadata,
} from '@/types/notification'
import type { Page } from '@/types/pagination'
import { displayFullnameUser, type UserWithEntities } from '@/types/user'
import { useInfiniteScroll } from '@vueuse/core'
import type { PropType } from 'vue'
import type { RouteLocationRaw } from 'vue-router'
import { VBadge, VBtn, VCard, VCardText, VCol, VIcon, VMenu, VRow, VSpacer } from 'vuetify/components'
import UserDisplay from './admin/users/UserDisplay.vue'

defineProps({
  user: {
    type: Object as PropType<UserWithEntities>,
    required: true,
  },
})
const router = useRouter()
const adminConfiguration = useAdminConfigurationStore()
const notificationMenuRef = ref<VMenu | null>(null)
const notificationsPage = ref(emptyValue<Page<Notification>>())
const notificationsPageNumber = ref(0)
const menuActive = ref(false)
const dialogStore = useDialogStore()

const list = ref<any>(null)

const notifications = ref<Notification[]>([])

const getNotification = async () => {
  await handleAxiosPromise(
    notificationsPage,
    notificationApi.findAll(
      {
        size: 10,
        page: notificationsPageNumber.value++,
        sort: ['id,DESC'],
      },
      {}
    )
  )
  notifications.value.push(...notificationsPage.value.value!.content)
}

const notificationsUnreadCount = ref(0)
const loadNotificationsUnreadCount = () => {
  notificationApi.findAll({ size: 1 }, { read: false }).then((response) => {
    notificationsUnreadCount.value = response.data.totalElements
  })
}
onMounted(loadNotificationsUnreadCount)

const listRef = computed(() => list.value?.$el)
useInfiniteScroll(
  listRef,
  async () => {
    if (!notificationsPage.value.value?.last) {
      await getNotification()
    }
  },
  {
    distance: 10,
  }
)

const onNotificationClick = (notification: Notification) => {
  if (!notification.readDateTime) {
    notificationApi.update(notification.id, { read: true }).then((r) => {
      notification.readDateTime = r.data.readDateTime
      notificationsUnreadCount.value--
    })
  }

  if (!notification.metaData?.['@type']) {
    return
  }

  if (
    [
      'FinalVersionToSendNotificationMetadata',
      'RgeEndOfValidityNotificationMetadata',
      'AlertValidateStep50NotificationMetadata',
    ].includes(notification.metaData?.['@type'])
  ) {
    menuActive.value = false
    router.push(mapNotificationToRouterLocationRaw(notification)!)
  } else if (notification.metaData?.['@type'] === 'OperationToProcessNotificationMetadata') {
    menuActive.value = false
    router.push(mapNotificationToRouterLocationRaw(notification)!)
  }

  if (notification.metaData?.['@type'] == 'MessageNotificationMetadata') {
    if ((notification.metaData as MessageNotificationMetadata).message.operationId) {
      menuActive.value = false
      router.push(mapNotificationToRouterLocationRaw(notification)!)
    } else if ((notification.metaData as MessageNotificationMetadata).message.operationsGroupId) {
      menuActive.value = false
      router.push(mapNotificationToRouterLocationRaw(notification)!)
    }
  } else if (notification.metaData?.['@type'] == 'ProfileRequestToProcessNotificationMetadata') {
    menuActive.value = false
    router.push(mapNotificationToRouterLocationRaw(notification)!)
  }
}

const mapNotificationToRouterLocationRaw = (notification: Notification): RouteLocationRaw | undefined => {
  if (!notification.metaData?.['@type']) {
    return
  }

  if (
    [
      'FinalVersionToSendNotificationMetadata',
      'RgeEndOfValidityNotificationMetadata',
      'AlertValidateStep50NotificationMetadata',
    ].includes(notification.metaData?.['@type'])
  ) {
    return {
      name: 'OperationOneView',
      params: {
        id: (
          notification.metaData as
            | FinalVersionToSendNotificationMetadata
            | RgeEndOfValidityNotificationMetadata
            | AlertValidateStep50NotificationMetadata
        ).operation.id,
      },
    }
  } else if (notification.metaData?.['@type'] === 'OperationToProcessNotificationMetadata') {
    return {
      name: 'SimulationOneView',
      params: {
        id: (notification.metaData as OperationToProcessNotificationMetadata).operation.id,
      },
    }
  }

  if (notification.metaData?.['@type'] == 'MessageNotificationMetadata') {
    if ((notification.metaData as MessageNotificationMetadata).message.operationId) {
      return {
        name: 'OperationOneView',
        params: {
          id: (notification.metaData as MessageNotificationMetadata).message.operationId,
        },
      }
    } else if ((notification.metaData as MessageNotificationMetadata).message.operationsGroupId) {
      return {
        name: 'OperationsGroupOneView',
        params: {
          id: (notification.metaData as MessageNotificationMetadata).message.operationsGroupId,
        },
      }
    }
  } else if (notification.metaData?.['@type'] == 'ProfileRequestToProcessNotificationMetadata') {
    return {
      name: 'UserProfileRequestAllView',
      query: {
        territoryIds: notification.metaData.userProfileRequest.entity.territory?.id,
      },
    }
  }
}

const snackbarStore = useSnackbarStore()
const deleteNotification = (notification: Notification) => {
  notificationApi
    .delete(notification.id)
    .then(() => {
      const index = notifications.value.indexOf(notification)
      notifications.value.splice(index, 1)
      snackbarStore.setSuccess('Notification supprimée')
      if (!notification.readDateTime) {
        notificationsUnreadCount.value--
      }
    })
    .catch(async (e) => {
      snackbarStore.setError(await handleAxiosException(e))
    })
}

const clearAllNotifications = async () => {
  if (
    !(await dialogStore.addAlert({
      title: 'Supprimer toutes les notifications',
      message: 'Vous êtes sur le point de supprimer toutes vos notifications. Êtes-vous sur de vouloir continuer ?',
      maxWidth: '640px',
    }))
  ) {
    return
  }
  notificationApi
    .deleteAll()
    .then(() => {
      notifications.value = []
      notificationsPageNumber.value = 0
      return getNotification().then(loadNotificationsUnreadCount)
    })
    .catch(async (e) => {
      snackbarStore.setError(await handleAxiosException(e))
    })
}
</script>

<style lang="scss">
.capte-user-menu {
  &__action {
    display: none;
  }

  &:hover &__action {
    display: initial;
  }
}
</style>
