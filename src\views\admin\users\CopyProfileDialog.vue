<template>
  <AlertDialog
    v-bind="$attrs"
    :model-value="props.modelValue"
    closable
    @update:model-value="(value: boolean) => emit('update:model-value', value)"
    @click:positive="validate"
    @click:negative="emit('update:model-value', false)"
  >
    <template #title> Copier le profile d'un utilisateur vers un autre utilisateur </template>

    <VForm ref="formRef">
      <VRow>
        <VCol cols="4">
          <RemoteAutoComplete
            v-model="baseUserId"
            label="Utilisateur de base"
            :query-for-one="(v) => userApi.getOne(v)"
            :query-for-all="(v, p) => userApi.getAll(p, { search: v })"
            :item-title="(it) => `${it.firstName} ${it.lastName} (${it.gid})`"
            item-value="id"
            infinite-scroll
            clearable
            :rules="[requiredRule]"
          />
        </VCol>
        <VCol cols="4">
          <VRow class="flex-column" no-gutters>
            <VCol>
              <VCheckbox v-model="withRoles" label="Copier les rôles" />
            </VCol>
            <VCol>
              <VCheckbox v-model="withTerritories" label="Copier les territoires" />
            </VCol>
            <VCol>
              <VCheckbox v-model="withEntities" label="Copier les organisations" />
            </VCol>
          </VRow>
        </VCol>
        <VCol cols="4">
          <RemoteAutoComplete
            v-model="targetUserIds"
            label="Utilisateur destinataires"
            :query-for-one="(v) => userApi.getOne(v)"
            :query-for-all="(v, p) => userApi.getAll(p, { search: v })"
            :item-title="(it) => `${it.firstName} ${it.lastName} (${it.gid})`"
            item-value="id"
            infinite-scroll
            clearable
            :rules="[requiredRule]"
          />
        </VCol>
      </VRow>
    </VForm>
  </AlertDialog>
</template>

<script setup lang="ts">
import { userApi } from '@/api/user'
import { useSnackbarStore } from '@/stores/snackbar'
import { VForm } from 'vuetify/components'
import { requiredRule } from '@/types/rule'
const props = defineProps<{
  modelValue: boolean
}>()

const emit = defineEmits<{
  'update:model-value': [boolean]
  save: []
}>()

const withRoles = ref(false)
const withEntities = ref(false)
const withTerritories = ref(false)

const baseUserId = ref<number | undefined>()
const targetUserIds = ref<number | undefined>()
const snackbarStore = useSnackbarStore()
const formRef = ref<typeof VForm | null>()
const validate = async () => {
  if ((await formRef.value!.validate()).valid) {
    try {
      const baseUser = (await userApi.getOne(baseUserId.value!)).data
      const targetUser = (await userApi.getOne(targetUserIds.value!)).data

      const user = { ...targetUser }
      if (withRoles.value) {
        user.roles = baseUser.roles
      }
      if (withEntities.value) {
        user.entities = baseUser.entities
      }
      if (withTerritories.value) {
        user.territories = baseUser.territories
      }

      await userApi.update(targetUser.id, mapToUserRequest(user))
      snackbarStore.setSuccess('Profile copié avec succès')
      emit('save')
    } catch (e) {
      snackbarStore.setError(await handleAxiosException(e))
    }

    console.debug(targetUserIds.value)
  }
}

watch(
  () => props.modelValue,
  (v) => {
    if (v) {
      baseUserId.value = undefined
      targetUserIds.value = undefined
    }
  }
)
</script>
