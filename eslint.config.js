import pluginVue from 'eslint-plugin-vue'
import vueTsEslintConfig from '@vue/eslint-config-typescript'
// import skipFormatting from '@vue/eslint-config-prettier/skip-formatting'
import eslintPluginPrettierRecommended from 'eslint-plugin-prettier/recommended'

export default [
  {
    name: 'app/files-to-lint',
    files: ['**/*.{ts,mts,tsx,vue}'],
  },

  {
    name: 'app/files-to-ignore',
    ignores: ['**/dist/**', '**/dist-ssr/**', '**/coverage/**'],
  },

  ...pluginVue.configs['flat/recommended'],
  ...vueTsEslintConfig(),
  // skipFormatting
  eslintPluginPrettierRecommended,

  // Override rules here
  {
    rules: {
      // 'eqeqeq': ['warn', 'smart'],
      '@typescript-eslint/no-explicit-any': 'off',
      'vue/valid-v-slot': [
        'error',
        {
          allowModifiers: false,
        },
      ],
      'vue/component-options-name-casing': ['warn', 'PascalCase'],
      'vue/component-name-in-template-casing': [
        'warn',
        'PascalCase',
        {
          registeredComponentsOnly: false,
          ignores: ['component'],
        },
      ],
      'vue/multi-word-component-names': ['off'],
      'vue/define-macros-order': [
        'warn',
        {
          order: ['defineOptions', 'defineModel', 'defineProps', 'defineEmits', 'defineSlots'],
          defineExposeLast: true,
        },
      ],
      // TODO à activer
      // 'vue/define-emits-declaration': ['warn', 'type-literal'],

      // Activation de Prettier comme une règle ESLint
      'prettier/prettier': 'warn',
    },
  },

  // Temporary disabled
  {
    rules: {
      'vue/require-default-prop': 'off',
      'vue/no-template-shadow': 'off',
      'vue/require-prop-types': 'off',
      '@typescript-eslint/no-non-null-asserted-optional-chain': 'off',
      '@typescript-eslint/no-unused-expressions': 'off',
      '@typescript-eslint/no-empty-object-type': 'off',
    },
  },
]
