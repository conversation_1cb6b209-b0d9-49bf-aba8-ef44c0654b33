import { defineStore } from 'pinia'

export const useSnackbarStore = defineStore('snackbar', () => {
  const state = ref({
    modelValue: false,
    message: '',
    color: undefined as string | undefined,
    timeout: undefined as number | undefined,
  })

  const setSuccess = (message: string, timeout: number = 5000) => {
    state.value = {
      modelValue: true,
      color: 'success',
      message,
      timeout,
    }
  }

  const setError = (message: string, timeout: number = 5000) => {
    state.value = {
      modelValue: true,
      color: 'error',
      message,
      timeout,
    }
  }

  const setWarning = (message: string, timeout: number = 5000) => {
    state.value = {
      modelValue: true,
      color: 'warning',
      message,
      timeout,
    }
  }

  return { state, setSuccess, setError, setWarning }
})
