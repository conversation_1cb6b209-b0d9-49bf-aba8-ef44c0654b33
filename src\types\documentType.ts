import type { LocalDateTime } from './date'
import { type Document } from './document'

export interface DocumentType {
  id: number
  name: string
  shareable: boolean
  fusionOrder: number
  inFinalVersion: boolean
  inInitialVersion: boolean
  inOtherVersion: boolean
  requiredInOperationsGroupForOperationInOperationsGroup: boolean
  requiredForSubsidiary: boolean
  requiredForSubcontracting: boolean
  ignoreForSelfWorks: boolean
  uniqueDoc: boolean
  instructions: string
  template: Document | null
  fillableTemplate: Document | null
  creator: any
  creationDateTime: string
  mustBeWord: boolean
  mustBePdf: boolean
  mustBeExcel: boolean
  updateDateTime: LocalDateTime
}

export interface DocumentTypeRequest
  extends Omit<DocumentType, 'id' | 'template' | 'fillableTemplate' | 'creator' | 'creationDateTime'> {
  templateId?: number
  fillableTemplateId?: number
}

export function makeEmptyDocumentType(): DocumentType {
  return {
    id: 0,
    name: '',
    shareable: false,
    fusionOrder: 0,
    inFinalVersion: false,
    inInitialVersion: false,
    inOtherVersion: false,
    requiredInOperationsGroupForOperationInOperationsGroup: false,
    requiredForSubsidiary: false,
    requiredForSubcontracting: false,
    ignoreForSelfWorks: false,
    uniqueDoc: false,
    instructions: '',
    template: null,
    fillableTemplate: null,
    creator: null,
    creationDateTime: '',
    mustBeExcel: false,
    mustBePdf: false,
    mustBeWord: false,
    updateDateTime: '',
  }
}

export function mapToDocumentTypeRequest(documentType: DocumentType): DocumentTypeRequest {
  return {
    ...documentType,
    templateId: documentType.template?.id,
    fillableTemplateId: documentType.fillableTemplate?.id,
  }
}

export const getExtensionsInList = (documentType: DocumentType): string[] => {
  const result: string[] = []

  if (documentType.mustBeExcel) {
    result.push('.xlsm', '.xlsx', '.xls')
  }
  if (documentType.mustBeWord) {
    result.push('.docx', '.doc', '.docm')
  }
  if (documentType.mustBePdf) {
    result.push('.pdf')
  }

  return result
}

export const displayExtensionList = (extensions: string[]): string => {
  if (extensions.length > 0) {
    return '(' + extensions.join(', ') + ')'
  }

  return ''
}

// Flemme de le mettre en BDD
export const CONTROL_REPORT_DOCUMENT_TYPE_ID = 36
