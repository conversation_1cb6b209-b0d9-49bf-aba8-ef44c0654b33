<template>
  <VCard :class="{ 'content-layout': expend }">
    <VCardTitle class="content-layout__header">
      <slot name="title" />
    </VCardTitle>
    <VDivider />
    <VCardText class="content-layout__main">
      <VRow v-if="template.value" class="flex-column">
        <VCol>
          <NjDisplayValue label="Nom" :value="template.value.name" />
        </VCol>
        <VCol>
          <NjDisplayValue
            label="Type"
            :value="conventionTypeValuesLabels.find((i) => i.type == template.value?.type)?.label"
          />
        </VCol>
        <VCol v-if="template.value?.document != null">
          <VRow align="center">
            <VCol>
              <VLink @click="downloadTemplate">
                {{ template.value.document.originalFilename }}
              </VLink>
            </VCol>
            <VCol class="flex-grow-0">
              <NjBtn :loading="generatingPreview" @click="preview"> Générer preview </NjBtn>
            </VCol>
          </VRow>
        </VCol>
      </VRow>
    </VCardText>
  </VCard>
</template>
<script setup lang="ts">
import { useSnackbarStore } from '@/stores/snackbar'
import { conventionTypeValuesLabels, type ConventionTemplate } from '@/types/convention/conventionTemplate'

const props = defineProps({
  id: {
    type: Number,
  },
  expend: Boolean,
})

const template = ref(emptyValue<ConventionTemplate>())
watch(
  () => props.id,
  (v) => {
    if (v) {
      handleAxiosPromise(template, conventionTemplateApi.findOne(v))
    }
  },
  {
    immediate: true,
  }
)

const snackbarStore = useSnackbarStore()
const downloadingFile = ref(false)
const downloadTemplate = () => {
  if (!template.value.value?.id) {
    return
  }
  downloadingFile.value = true
  conventionTemplateApi
    .download(template.value.value.id)
    .then((response) => {
      downloadFile(template.value.value?.name + '-template' + '.docx', response.data)
      snackbarStore.setSuccess('Le téléchargement a réussi')
    })
    .catch(() =>
      snackbarStore.setError(
        'Le téléchargement du fichier a échoué. Aviez-vous envoyé ce fichier sur boCEE ? Si oui, veuillez nous contacter.'
      )
    )
    .finally(() => (downloadingFile.value = false))
}

const generatingPreview = ref(false)
const preview = () => {
  if (!template.value.value?.id) {
    return
  }
  generatingPreview.value = true
  conventionTemplateApi
    .preview(template.value.value.id)
    .then((response) => {
      downloadFile(template.value.value?.name + '-preview' + '.docx', response.data)
      snackbarStore.setSuccess('La génération du fichier de preview a réussi')
    })
    .catch(async (error) => {
      const responseObj = JSON.parse(await error.response.data.text())
      snackbarStore.setError(responseObj.message ?? 'La génération du fichier de preview a échoué')
    })
    .finally(() => (generatingPreview.value = false))
}
</script>
