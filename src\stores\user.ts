import type { ProfileType, UserWithEntities } from '@/types/user'
import { defineStore } from 'pinia'

export const useUserStore = defineStore('user', () => {
  const currentUser = ref<UserWithEntities>(makeEmptyUserWithEntities())

  const set = (user: UserWithEntities) => {
    currentUser.value = user
    // currentUser.value.roles = JSON.parse(user.rolesJsonRaw)
  }

  const isAgency = computed(() => userHasRole(currentUser.value, 'AGENCE', 'AGENCE_PLUS', 'SUPPORT_AGENCE_PLUS'))
  const isSiege = computed(() => userHasRole(currentUser.value, 'SIEGE', 'INSTRUCTEUR', 'ADMIN_PLUS'))

  const isOperationManager = computed(() => {
    return userCanManageOperation(currentUser.value)
  })

  const isAdmin = computed(() => userHasRole(currentUser.value, 'ADMIN', 'ADMIN_PLUS'))
  const isAdminPlus = computed(() => userHasRole(currentUser.value, 'ADMIN_PLUS'))

  const canCertified = computed(() => userHasRole(currentUser.value, 'SIEGE', 'INSTRUCTEUR', 'ADMIN', 'ADMIN_PLUS'))
  const canAccessSales = computed(() => userHasRole(currentUser.value, 'VENTE_CEE', 'ADMIN_PLUS', 'DAF_SIEGE'))
  const canSellCee = computed(() => userHasRole(currentUser.value, 'VENTE_CEE'))

  const hasRole = (...requiredRoles: ProfileType[]) => {
    return userHasRole(currentUser.value, ...requiredRoles)
  }

  return {
    currentUser,
    set,
    isSiege,
    isAdmin,
    isAdminPlus,
    isOperationManager,
    isAgency,
    hasRole,
    canCertified,
    canAccessSales,
    canSellCee,
  }
})
