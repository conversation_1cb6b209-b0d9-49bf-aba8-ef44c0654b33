import type { AxiosInstance, AxiosPromise } from 'axios'
import axiosInstance from '.'
import type { Valuation, ValuationFilter, ValuationRequest } from '@/types/valuation'
import type { Page, Pageable } from '@/types/pagination'

const valuationUrl = '/valuations'
class ValuationApi {
  public constructor(private axios: AxiosInstance) {}

  public create(valuationRequest: ValuationRequest): AxiosPromise<Valuation> {
    return this.axios.post(valuationUrl, valuationRequest)
  }

  public update(id: number, valuationRequest: ValuationRequest): AxiosPromise<Valuation> {
    return this.axios.put(`${valuationUrl}/${id}`, valuationRequest)
  }

  public getAll(pageable: Pageable, filter: ValuationFilter): AxiosPromise<Page<Valuation>> {
    return this.axios.get(valuationUrl, {
      params: { ...pageable, ...filter },
    })
  }

  public getAppropriate(id: number, typeId: number, simulateStep60: boolean = false): AxiosPromise<Valuation[]> {
    return this.axios.get(`operations/${id}/valuation`, {
      params: {
        typeId,
        simulateStep60,
      },
    })
  }
}

export const valuationApi = new ValuationApi(axiosInstance)
