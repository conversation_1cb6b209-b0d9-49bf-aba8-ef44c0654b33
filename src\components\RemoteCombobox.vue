<template>
  <VCombobox
    v-bind="$attrs"
    v-model="value"
    v-model:search="searchInput"
    v-model:focused="hasFocus"
    v-model:menu="menu"
    :label="label"
    :items="items.content"
    :loading="loading"
    :hide-no-data="loading"
    :error-messages="error"
    :item-title="itemTitle as any"
    :item-value="itemValue as any"
    :placeholder="placeholder"
    no-data-text="Aucun élément trouvé"
    no-filter
    :clearable="clearable"
    :rules="rules"
    :readonly="readonly"
    :return-object="returnObject"
    @focus="initList"
  >
    <!-- eslint-disable-next-line prettier/prettier -->
    <template v-for="(_, slot) of $slots as VCombobox['$slots']" #[slot]="scope">
      <slot :name="slot" v-bind="scope" />
    </template>

    <template #append-item>
      <div
        v-if="!items.last"
        v-intersect="intersectInfiniteScroll"
        style="height: 60px"
        class="d-flex align-center justify-center"
      >
        <VProgressCircular indeterminate />
      </div>
    </template>

    <template #loader="{ isActive }">
      <VProgressLinear :active="isActive" color="primary" absolute height="4" indeterminate></VProgressLinear>
    </template>
  </VCombobox>
</template>

<script setup lang="ts">
import type { Page, Pageable } from '@/types/pagination'
import type { ValidationRule } from '@/types/rule'
import { isAxiosError, type AxiosPromise } from 'axios'
import { debounce } from 'lodash'
import type { PropType } from 'vue'
import type { VCombobox } from 'vuetify/components'

const props = defineProps({
  modelValue: {
    type: [Number, String, Array],
  },
  label: String,
  placeholder: String,
  delay: {
    type: Number,
    default: 500,
  },
  clearable: Boolean,
  queryForAll: {
    type: Function as PropType<(v: string, pageable: Pageable) => AxiosPromise<Page<any>>>,
    required: true,
  },
  queryForOne: {
    type: Function as PropType<(v: any) => AxiosPromise<any>>,
  },
  itemTitle: {
    type: [String, Function] as PropType<'none' | string | ((item: Record<string, any>, fallback?: any) => any)>,
    default: 'label',
  },
  itemValue: {
    type: [String, Function] as PropType<string | ((item: Record<string, any>, fallback?: any) => any)>,
    default: 'id',
  },
  rules: Array as PropType<ValidationRule[]>,
  errorMessages: {
    type: String,
  },
  returnObject: Boolean,
  readonly: Boolean,
})
const emit = defineEmits(['update:model-value'])
const searchInput = ref('')
const loading = ref(false)
const error = ref<string>()
const items = ref<Page<unknown>>(makeEmptyPage())
const menu = ref<boolean>(false)
const hasFocus = ref<boolean>(false)
const value = computed({
  get() {
    return props.modelValue !== 0 && props.modelValue ? props.modelValue : undefined
  },
  set(v) {
    emit('update:model-value', v)
  },
})

const initList = () => {
  if (items.value.content.length === 0 && !props.readonly) {
    query('')
  }
}

const pageNumber = ref(0)
watch(pageNumber, (v) => {
  if (v) {
    load(searchInput.value)
  }
})

const load = (v: string) => {
  props
    .queryForAll(v, { page: pageNumber.value })
    .then((r) => {
      if (!pageNumber.value) {
        items.value = r.data
      } else {
        items.value = {
          ...r.data,
          content: items.value.content.concat(r.data.content),
        }
      }
      menu.value = hasFocus.value
    })
    .catch(async (e) => {
      error.value = await handleAxiosException(e)
    })
    .finally(() => {
      loading.value = false
    })
}

const debounced = debounce(load, props.delay)

const query = (value: string) => {
  if (value != null && value != undefined) {
    loading.value = true
    error.value = undefined
    pageNumber.value = 0
    debounced(value)
  }
}
const fieldSelector = (o: any, keySelector: string | ((o: Record<string, any>) => unknown)): unknown => {
  if (typeof keySelector === 'string') {
    return o[keySelector]
  } else {
    return keySelector(o)
  }
}
watch(searchInput, (v, oldV) => {
  const criteraName = v === '' ? oldV : v
  let needToSearch
  if (v !== '') {
    needToSearch = true
  } else if (props.itemTitle === 'none') {
    const o = items.value.content.find(
      (it: any) => (typeof props.itemTitle === 'string' ? it[props.itemTitle!] : props.itemTitle!(it)) === criteraName
    )
    needToSearch = !o || oldV !== fieldSelector(o, props.itemTitle)
  } else {
    needToSearch = !items.value.content.find(
      (it: any) =>
        (typeof props.itemValue === 'string' ? it[props.itemValue!] : props.itemValue!(it)) === props.modelValue
    )
  }
  if (needToSearch) {
    query(v)
  }
})

watch(
  () => props.modelValue,
  (v) => {
    if (v && (typeof v === 'number' || typeof v === 'object') && props.queryForOne) {
      loading.value = true
      props
        .queryForOne?.(v as number)
        .then((r) => {
          items.value = makePage([r.data])
        })
        .catch(async (e) => {
          if (isAxiosError(e) && e.response?.status === 404) {
            items.value = makeEmptyPage()
            searchInput.value = ''
            emit('update:model-value', undefined)
          } else {
            error.value = await handleAxiosException(e)
          }
        })
        .finally(() => {
          loading.value = false
        })
    }
  },
  {
    immediate: true,
  }
)

const intersectInfiniteScroll = (
  isIntersecting: boolean,
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  entries: IntersectionObserverEntry[],
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  observer: IntersectionObserver
) => {
  // console.debug('intersectInfiniteScroll', entries, observer, isIntersecting)
  if (isIntersecting) {
    // let moreVendors = loadMoreFromApi()
    // this.vendors = [ ...this.vendors, ...moreVendors]
    pageNumber.value++
  }
}

watch(
  () => props.errorMessages,
  () => {
    error.value = props.errorMessages
  }
)
</script>
