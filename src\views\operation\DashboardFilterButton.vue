<template>
  <VBtn
    v-if="!selected"
    prepend-icon="mdi-filter-variant"
    variant="text"
    density="compact"
    class="rounded-0"
    color="primary"
    :disabled
    @click="toggleFilter"
    >{{ label }}</VBtn
  >
  <VBtn
    v-else
    prepend-icon="mdi-close"
    variant="text"
    density="compact"
    class="rounded-0"
    color="primary"
    @click="toggleFilter"
    >Effacer</VBtn
  >
</template>

<script setup lang="ts">
import type { OperationFilter } from '@/api/operation'
import { cloneDeep, isMatch } from 'lodash'
import { dashboardCardKey, dashboardDataKey } from '../dashboard/keys'

const props = withDefaults(
  defineProps<{
    filter: OperationFilter
    disabledMessage?: string
    // disabled?: boolean
    label?: string
  }>(),
  {
    label: 'Filtrer',
  }
)
const { filter: globalFilter } = inject(dashboardDataKey)!
const { selected: cardSelected, disabled: disabled } = inject(dashboardCardKey, {
  selected: ref(false),
  disabled: ref(false),
})
const selected = computed(() => {
  return isMatch(globalFilter.value, props.filter)
})

const toggleFilter = () => {
  if (selected.value) {
    Object.keys(props.filter).forEach((key) => {
      delete globalFilter.value[key as keyof typeof globalFilter.value]
      if (key === 'operationStatuses') {
        globalFilter.value[key] = ['DOING']
      }
    })
  } else {
    globalFilter.value = { ...globalFilter.value, ...cloneDeep(props.filter) }
  }
}

watch(
  selected,
  (v) => {
    cardSelected.value = v
  },
  {
    immediate: true,
  }
)
</script>
