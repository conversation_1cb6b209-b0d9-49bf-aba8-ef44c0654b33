<template>
  <VMenu v-model="dateMenu" :close-on-content-click="false">
    <template #activator="{ props }">
      <VTextField
        v-bind="{ ...$attrs, ...props }"
        :model-value="textFieldValue"
        :label="label"
        :error-messages="error"
        :disabled="disabled"
        class="bg-white"
        readonly
        clearable
        @click:clear="clearRange"
      />
    </template>
    <VCard class="d-flex flex-column gap-4 pa-4 bg-grey-lighten-4">
      <NjDatePicker v-model="minDate" label="Date minimum" clearable />
      <NjDatePicker v-model="maxDate" label="Date maximum" clearable />
    </VCard>
  </VMenu>
</template>

<script setup lang="ts">
import { formatHumanReadableLocalDate, type LocalDate } from '@/types/date'

const minDate = defineModel<LocalDate | null>('minDate')
const maxDate = defineModel<LocalDate | null>('maxDate')

defineProps({
  label: {
    type: String,
  },
  disabled: Boolean,
})

const textFieldValue = computed<string | null>(() => {
  if (minDate.value && maxDate.value) {
    return `Entre le ${formatHumanReadableLocalDate(minDate.value)} et le ${formatHumanReadableLocalDate(maxDate.value)}`
  } else if (minDate.value) {
    return `A partir du ${formatHumanReadableLocalDate(minDate.value)}`
  } else if (maxDate.value) {
    return `Au maximum le ${formatHumanReadableLocalDate(maxDate.value)}`
  } else {
    return ''
  }
})

const clearRange = () => {
  minDate.value = null
  maxDate.value = null
}

const dateMenu = ref(false)

const error = ref('')
</script>
