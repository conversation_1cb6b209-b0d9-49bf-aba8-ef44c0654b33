<template>
  <VAlert
    rounded="0"
    :model-value="show === undefined ? !!message : show"
    :type="type"
    :closable="closable"
    @update:model-value="!$event ? emit('close') : undefined"
  >
    <slot>
      <template v-if="message">
        <div v-for="l in message.split('\n')" :key="l">
          {{ l }}
        </div>
      </template>
    </slot>
  </VAlert>
</template>

<script lang="ts" setup>
import type { PropType } from 'vue'

defineProps({
  type: {
    type: String as PropType<'success' | 'info' | 'warning' | 'error'>,
    default: 'error',
  },
  show: {
    type: Boolean,
    default: undefined,
  },
  message: {
    type: String,
  },
  closable: Boolean,
})
const emit = defineEmits(['close'])
</script>
