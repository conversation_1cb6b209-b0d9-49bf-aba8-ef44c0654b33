<template>
  <VBtn
    :icon="icon"
    :disabled="disabled"
    v-bind="$attrs"
    :variant="variant === 'square' ? 'elevated' : variant === 'square-outline' ? 'outlined' : 'text'"
    :size="size ?? (variant === 'square' || variant === 'square-outline' ? 40 : undefined)"
    :rounded="variant === 'square' || variant === 'square-outline' ? 0 : undefined"
    :flat="variant === 'square' || variant === 'square-outline'"
    :color="variant === 'square' || variant === 'square-outline' ? 'primary' : ($attrs.color as string)"
    :density="density"
  />
</template>

<script setup lang="ts">
import type { PropType } from 'vue'

defineProps({
  icon: {
    type: String,
    required: true,
  },
  size: [String, Number],
  variant: String as PropType<'square' | 'square-outline' | 'icon' | 'flat'>,
  disabled: Boolean,
  density: String as PropType<null | 'default' | 'comfortable' | 'compact'>,
})
</script>
