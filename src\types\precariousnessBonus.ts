import type { ComputedParameterFormula } from './calcul/computedParameterFormula'
import type { MappingTable } from './calcul/mappingTable'
import type { ParameterFormula } from './calcul/parameterFormula'
import type { LocalDate } from './date'
import type { Historisable } from './historisation'

export interface CasePrecariousnessBonusSheet {
  label: string
  emmyValue: string
  parameters: ParameterFormula[]
  computedParameters: ComputedParameterFormula[]
}

export interface PrecariousnessBonusSheet extends Partial<Historisable> {
  id: number
  name: string
  casEmmyParameterId: number

  commitmentDate?: LocalDate
  endOperationDate?: LocalDate
  formulaValidationRules: FormulaValidationRule[]
  ceePrecariteFormula: string
  ceeClassicalFormula: string
  parameters: ParameterFormula[]
  mappingTables: MappingTable[]
  certified: boolean

  casList: CasePrecariousnessBonusSheet[]
}

export interface FormulaValidationRule {
  formula: string
  errorMessage: string
}

export const makeEmptyFormulaValidationRule = (): FormulaValidationRule => ({
  formula: '',
  errorMessage: '',
})

export type PrecariousnessBonusSheetRequest = Omit<PrecariousnessBonusSheet, 'id'>

export function makeEmptyPrecariousnessBonusSheet(): PrecariousnessBonusSheet {
  return {
    id: 0,
    casList: [],
    commitmentDate: '',
    endOperationDate: '',
    ceeClassicalFormula: '',
    ceePrecariteFormula: '',
    formulaValidationRules: [],
    parameters: [],
    mappingTables: [],
    name: '',
    casEmmyParameterId: 0,
    certified: false,
  }
}
