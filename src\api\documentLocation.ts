import type { AxiosInstance, AxiosPromise } from 'axios'
import axiosInstance from '.'
import type { DocumentLocation } from '@/types/document'

class DocumentLocationApi {
  public constructor(private axios: AxiosInstance) {}

  public findByOperationId(operationId: number): AxiosPromise<DocumentLocation[]> {
    return this.axios.get(`/operations/${operationId}/documents_location`)
  }
}
export const documentLocationApi = new DocumentLocationApi(axiosInstance)
