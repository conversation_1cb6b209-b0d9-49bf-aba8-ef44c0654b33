<template>
  <NjPage :error-message="items.error" :loading="items.loading" title="Explorateur de fichiers">
    <template #body>
      <VList :loading="items.loading">
        <VProgressLinear v-if="items.loading" indeterminate />
        <VListItem v-if="currentFolder" @click="goParent"> ... </VListItem>
        <VListItem
          v-for="item in items.value"
          :key="item.filename"
          :prepend-icon="item.type === 'FOLDER' ? 'mdi-folder' : undefined"
          @click="clickItem(item)"
        >
          <template #append>
            <VIcon icon="mdi-delete" @click.stop="onDelete(item)" />
          </template>
          {{ item.filename }}
        </VListItem>
      </VList>
    </template>
  </NjPage>
</template>

<script setup lang="ts">
import axiosInstance from '@/api'
import { useDialogStore } from '@/stores/dialog'
import { useSnackbarStore } from '@/stores/snackbar'
import { VProgressLinear } from 'vuetify/components'

const props = defineProps<{
  id: string
}>()

const items =
  ref(
    emptyValue<
      {
        filename: string
        type: 'FILE' | 'FOLDER'
      }[]
    >()
  )
const router = useRouter()
const snackbarStore = useSnackbarStore()
const clickItem = async (item: any) => {
  if (item.type === 'FILE') {
    axiosInstance
      .get(`/explorer/${props.id}${currentFolder.value ?? ''}/${item.filename}`, {
        responseType: 'blob',
      })
      .then((blob) => {
        downloadFile(item.filename, blob.data)
      })
      .catch(async (e) => {
        snackbarStore.setError(await handleAxiosException(e))
      })
  } else {
    router.push({
      query: {
        link: currentFolder.value + '/' + item.filename,
      },
    })
  }
}

const route = useRoute()
const currentFolder = computed(() => route.query['link'] ?? '')
const load = () => {
  handleAxiosPromise(items, axiosInstance.get('/explorer/' + props.id + '/' + (currentFolder.value ?? '')))
}
watch(
  currentFolder,
  () => {
    load()
  },
  {
    immediate: true,
  }
)

const goParent = () => {
  router.push({ query: { link: currentFolder.value.slice(0, currentFolder.value.lastIndexOf('/')) } })
}
const dialogStore = useDialogStore()
const onDelete = async (item: any) => {
  if (
    await dialogStore.addAlert({
      title: 'Suppression fichier/dossier',
      message: `Voulez-vous vraiment supprimer le fichier/dossier ${item.filename} ?`,
      maxWidth: '640px',
    })
  ) {
    await axiosInstance
      .delete(`/explorer/${props.id}${currentFolder.value ?? ''}/${item.filename}`)
      .catch(async (e) => {
        snackbarStore.setError(await handleAxiosException(e))
      })
  }
  load()
}
</script>
