<template>
  <VDialog v-model="activeDialog" width="100%" persistent>
    <VCard class="content-layout">
      <VCardTitle class="d-flex align-center content-layout__header">
        <span class="d-flex w-100">
          {{ isExpired ? 'Fiche opération invalide' : 'Changement de fiche opération' }}
        </span>
        <NjIconBtn icon="mdi-window-close" variant="flat" @click="activeDialog = false" />
      </VCardTitle>
      <VCardText class="content-layout__main" style="flex-basis: auto">
        <VRow>
          <VCol v-if="isExpired">
            <VAlert
              text="La fiche opération que vous avez sélectionnée n'est plus compatible, voulez-vous la conserver malgré tout ou
              voulez-vous la changer?"
              type="warning"
            />

            <VAlert
              v-if="operation.standardizedOperationSheet?.substituteStandardizedOperationSheet"
              type="info"
              class="mt-4"
              variant="outlined"
            >
              Pour cette fiche, nous vous recommandons de la modifier en
              {{ operation.standardizedOperationSheet?.substituteStandardizedOperationSheet.operationCode }}
            </VAlert>
            <VDivider />
          </VCol>
        </VRow>
        <VRow v-if="step === 'warn'" class="flex-column my-n2">
          <VCol>
            <NjDisplayValue
              label="Date de validité de l'opération"
              :value="`${formatHumanReadableLocalDate(
                operation.standardizedOperationSheet?.startDate
              )} - ${formatHumanReadableLocalDate(operation.standardizedOperationSheet?.expirationDate)}`"
            />
          </VCol>
          <VCol v-if="operation.stepId < 50">
            <NjDisplayValue
              label="Date d'engagement prévisionnelle"
              :value="formatHumanReadableLocalDate(operation.estimatedCommitmentDate)"
            />
          </VCol>
          <VCol v-else>
            <NjDisplayValue label="Date d'engagement" :value="formatHumanReadableLocalDate(operation.signedDate)" />
          </VCol>
        </VRow>
        <VRow v-else-if="step === 'select'">
          <VCol>
            <VRow>
              <VCol cols="4">
                <VTextField v-model="search" class="flex-grow-0" label="Recherche" prepend-inner-icon="mdi-magnify" />
              </VCol>
            </VRow>
            <VRow>
              <VCol>
                <NjDataTable
                  v-model:selections="selected"
                  :page="data.value"
                  :pageable="pageable"
                  :headers="headers"
                  :loading="data.loading"
                  @update:pageable="updatePageable"
                />
              </VCol>
            </VRow>
          </VCol>
        </VRow>
        <VRow v-else>
          <VCol>
            <VCard>
              <VCardTitle class="d-flex align-center">
                <span class="d-flex w-100"> Édition </span>
              </VCardTitle>
              <VDivider />
              <VCardText>
                <OperationForm
                  ref="operationFormRef"
                  v-model:operation="tempOperation"
                  expanded-detail
                  edit
                  preview
                  @update:operation="save"
                />
              </VCardText>
            </VCard>
          </VCol>
        </VRow>
      </VCardText>
      <VCardActions class="content-layout__footer">
        <VSpacer />
        <NjBtn
          v-if="step === 'warn' && operation.standardizedOperationSheet?.substituteStandardizedOperationSheet"
          variant="outlined"
          @click="makeTheRecommandation"
        >
          Recommandation
        </NjBtn>
        <NjBtn
          v-if="step === 'warn' || (step === 'select' && !isExpired)"
          variant="outlined"
          @click="activeDialog = false"
        >
          {{ isExpired ? 'Conserver' : 'Annuler' }}
        </NjBtn>
        <NjBtn v-if="(step === 'select' && isExpired) || step === 'verify'" variant="outlined" @click="previousStep">
          Précédent
        </NjBtn>
        <NjBtn :disabled="step === 'select' && selected.length === 0" @click="nextStep">
          {{ step === 'warn' ? 'Modifier' : step === 'select' ? 'Suivant' : 'Valider' }}
        </NjBtn>
      </VCardActions>
    </VCard>
  </VDialog>
</template>
<script lang="ts" setup>
import NjBtn from '@/components/NjBtn.vue'
import NjDisplayValue from '@/components/NjDisplayValue.vue'
import type { DataTableHeader } from '@/components/okta/NjDataTable.type'
import NjDataTable from '@/components/okta/NjDataTable.vue'
import type {
  StandardizedOperationSheet,
  StandardizedOperationSheetFilter,
} from '@/types/calcul/standardizedOperationSheet'
import { formatHumanReadableLocalDate } from '@/types/date'
import type { Operation } from '@/types/operation'
import { clone, debounce } from 'lodash'
import type { PropType } from 'vue'
import OperationForm from './operation/OperationForm.vue'

const props = defineProps({
  modelValue: Boolean,
  operation: {
    type: Object as PropType<Operation>,
    default: makeEmptyOperation,
  },
})
const emit = defineEmits(['update:model-value', 'update:operation'])
const activeDialog = computed<boolean>({
  get() {
    return props.modelValue
  },
  set(v) {
    emit('update:model-value', v)
  },
})

const tempOperation = ref(clone(props.operation))

const search = ref('')

const { data, pageable, pageFilter, updatePageable } = usePagination<
  StandardizedOperationSheet,
  StandardizedOperationSheetFilter
>(
  (filter, pageable) => standardizedOperationSheetApi.findAll(pageable, filter),
  {
    validDate: props.operation.signedDate ?? props.operation.estimatedCommitmentDate,
    search: search.value,
    visible: true,
  },
  {
    page: 0,
    size: 10,
    sort: ['operationCode'],
  }
)

const debounceSearch = debounce((v: string | undefined) => {
  pageFilter.value.search = v!
}, 300)

const operationFormRef = ref<typeof OperationForm | null>(null)
const saveOperation = () => {
  operationFormRef.value!.saveOperation()
}

watch(
  () => search.value,
  (v) => {
    if (v || v === '') {
      data.value.loading = true
      debounceSearch(v)
    }
  },
  {
    immediate: true,
  }
)

const headers: DataTableHeader[] = [
  {
    title: 'Code opération',
    value: 'operationCode',
  },
  {
    title: 'Description',
    value: 'description',
  },
  {
    title: 'Date de début',
    value: 'startDate',
  },
  {
    title: 'Date de fin',
    value: 'expirationDate',
  },
]

const step = ref('warn')
const isExpired = computed(() => {
  return !isStandardizedOperationSheetAndCommitmentCompatible(props.operation)
})
const selected = ref<StandardizedOperationSheet[]>([])

watch(
  activeDialog,
  (v) => {
    if (v && !isExpired.value) {
      step.value = 'select'
    }
  },
  {
    immediate: true,
  }
)

const save = () => {
  if (tempOperation.value.id === 0) {
    activeDialog.value = false
    emit('update:operation', tempOperation.value)
    return
  }

  activeDialog.value = false
  emit('update:operation', tempOperation.value)
}

const nextStep = () => {
  if (step.value === 'select') {
    tempOperation.value.standardizedOperationSheet = selected.value[0]
    step.value = 'verify'
  } else if (step.value === 'verify') {
    saveOperation()
  } else {
    step.value = 'select'
  }
}

const makeTheRecommandation = () => {
  tempOperation.value.standardizedOperationSheet =
    props.operation.standardizedOperationSheet.substituteStandardizedOperationSheet!
  step.value = 'verify'
}
const previousStep = () => (step.value === 'verify' ? (step.value = 'select') : (step.value = 'warn'))
</script>
