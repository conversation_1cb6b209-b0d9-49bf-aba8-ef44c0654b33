import type { GtfCompanyDto } from '@/api/external/gtf/gtfOrgs'
import type { LocalDateTime } from './date'
import { makeEmptyAddress } from './operation'
import type { Address } from './address'

export interface Company {
  id: number

  name: string
  shortName: string
  siret: string
  type: string
  address: Address
  country: string // ISO 3 code
  frRegimeCommunautaire: string
  capital: number

  toObserve: boolean

  gtfUpdateDateTime?: LocalDateTime

  enabled: boolean
}

export type CompanyRequest = Company

export const makeEmptyCompany = (): Company => ({
  id: 0,
  name: '',
  shortName: '',
  address: makeEmptyAddress(),
  country: '',
  siret: '',
  type: '',
  toObserve: false,
  frRegimeCommunautaire: '',
  capital: 0,
  enabled: false,
})

export const mapGtfCompanyDtoToCompany = (gtfCompanyDto: GtfCompanyDto): Company => ({
  id: gtfCompanyDto.id,
  address: {
    street: gtfCompanyDto.address_route_number + ' ' + gtfCompanyDto.address_route,
    postalCode: gtfCompanyDto.address_postal_code,
    city: gtfCompanyDto.address_city,
    country: null,
  },
  country: gtfCompanyDto.address_country,
  name: gtfCompanyDto.name,
  shortName: gtfCompanyDto.short_name,
  siret: gtfCompanyDto.siret,
  toObserve: false,
  type: gtfCompanyDto.type,
  frRegimeCommunautaire: gtfCompanyDto.fr_regime_communitaire,
  capital: gtfCompanyDto.capital,
  enabled: gtfCompanyDto.enabled === 'O',
})
