<template>
  <NjPage
    can-go-back
    :title="'Liste des simulations du Business Plan : ' + businessPlan.value?.name"
    expend-body
    v-bind="$attrs"
  >
    <template #after-title
      ><VSpacer />
      <NjBtn
        v-if="userStore.hasRole('ADMIN_PLUS') || businessPlan.value?.creationUser.id === userStore.currentUser.id"
        variant="outlined"
        color="error"
        @click="deleteBusinessPlan"
        >Supprimer</NjBtn
      ></template
    >
    <template #header-actions>
      <VRow class="flex-nowrap" dense>
        <CardDialog v-model="setOperationsDialog" title="Gérer le Business Plan" fixed>
          <template #subtitle>
            Gérer le Business Plan
            <VProgressCircular v-show="loadingManagementOfOperations" indeterminate />
          </template>
          <SetOperationBusinessPlan
            v-if="businessPlan.value"
            v-model:loading="loadingManagementOfOperations"
            :business-plan="businessPlan.value"
          />
          <template #actions>
            <NjBtn @click="setOperationsDialog = false"> Valider </NjBtn>
          </template>
        </CardDialog>
      </VRow>
    </template>
    <template #body>
      <VRow class="w-100">
        <VCol :cols="openDrawer ? 12 : 7">
          <VRow class="flex-column content-layout">
            <VCol class="flex-grow-0">
              <VRow>
                <VCol>
                  <VRow>
                    <VCol>
                      <SearchInput v-model="search" :loading="operations.loading" />
                    </VCol>
                    <VCol class="flex-grow-0">
                      <NjBtn @click="setOperationsDialog = true">Gérer le Business Plan</NjBtn>
                    </VCol>
                  </VRow>
                </VCol>
              </VRow>
            </VCol>
            <VCol>
              <NjDataTable
                v-model:selections="selection"
                :pageable="pageableOperation"
                :page="operations.value!"
                :headers="headers"
                :disabled-row="
                  (ope: Operation) => ope.status === 'CANCELLED' || ope.status === 'IMPROPER' || ope.status === 'LOST'
                "
                :on-click-row="clickRow"
                :clicked-row="clickedRow"
                checkboxes
                multi-selection
                fixed
                @update:pageable="updatePageableOperations"
              >
                <template #[`item.stepId`]="{ item }">
                  <OperationStepChip :operation="item" />
                </template>
              </NjDataTable>
            </VCol>
          </VRow>
        </VCol>
        <VCol v-show="!openDrawer" cols="5">
          <VCard class="content-layout h-100">
            <VCardTitle class="content-layout__header pa-0">
              <VTabs v-model="tabs">
                <VTab>Business Plan</VTab>
              </VTabs>
            </VCardTitle>
            <VDivider />
            <VCardText class="content-layout__main pa-0">
              <VWindow v-model="tabs" class="h-100">
                <VWindowItem :class="tabs === 0 ? 'content-layout' : 'h-100'">
                  <div
                    v-if="businessPlan.value && businessPlanSummary.value"
                    class="flex-column content-layout content-layout__main"
                    dense
                    style="margin: 1px"
                  >
                    <div class="content-layout__main">
                      <NjExpansionPanel>
                        <template #title>
                          <div class="d-flex align-center">
                            Business Plan
                            <VSpacer />
                            <NjIconBtn
                              :icon="edit ? 'mdi-close' : 'mdi-pencil'"
                              color="primary"
                              @click.stop="toggleEdit"
                            ></NjIconBtn>
                          </div>
                        </template>
                        <VRow class="flex-column" dense>
                          <VCol>
                            <NjDisplayValue v-if="!edit" label="Nom" :value="businessPlan.value.name" />
                            <VTextField v-else v-model="businessPlan.value.name" label="Nom" :rules="[requiredRule]" />
                          </VCol>
                          <VCol>
                            <NjDisplayValue
                              v-if="!edit"
                              label="A Traiter"
                              :value="businessPlan.value.toProcess"
                              checkbox
                            />
                            <NjSwitch
                              v-else
                              v-model="businessPlan.value.toProcess"
                              label="A Traiter"
                              :rules="[requiredRule]"
                            />
                          </VCol>
                          <VCol>
                            <NjDisplayValue
                              label="Créateur"
                              :value="displayFullnameUser(businessPlan.value.creationUser)"
                            />
                          </VCol>
                          <VCol>
                            <NjDisplayValue
                              label="Date de création"
                              :value="formatHumanReadableLocalDate(businessPlan.value.creationDateTime)"
                            />
                          </VCol>
                          <VCol>
                            <NjDisplayValue
                              label="Total kWhc Classique"
                              :value="formatNumber(businessPlanSummary.value.classicCumacSum) + ' kWh cumac'"
                            />
                          </VCol>
                          <VCol>
                            <NjDisplayValue
                              label="Total kWhc Précarité"
                              :value="formatNumber(businessPlanSummary.value.precariousnessCumacSum) + ' kWh cumac'"
                            />
                          </VCol>
                          <VCol>
                            <NjDisplayValue
                              label="Total kWhc Demandé"
                              :value="
                                formatNumber(
                                  businessPlanSummary.value.precariousnessCumacSum +
                                    businessPlanSummary.value.classicCumacSum
                                ) + ' kWh cumac'
                              "
                            />
                          </VCol>
                          <VCol>
                            <NjDisplayValue
                              label="Total Montant Classique €"
                              :value="formatPriceNumber(businessPlanSummary.value.classicValuationAmountSum)"
                            />
                          </VCol>
                          <VCol>
                            <NjDisplayValue
                              label="Total Montant Précarité €"
                              :value="formatPriceNumber(businessPlanSummary.value.precariousnessValuationAmountSum)"
                            />
                          </VCol>
                          <VCol>
                            <NjDisplayValue
                              label="Total Montant Demandé €"
                              :value="
                                formatPriceNumber(
                                  businessPlanSummary.value.classicValuationAmountSum +
                                    businessPlanSummary.value.precariousnessValuationAmountSum
                                )
                              "
                            />
                          </VCol>
                          <VCol>
                            <NjDisplayValue
                              label="Total Marge CEE Agence #"
                              :value="
                                formatPriceNumber(
                                  businessPlanSummary.value.classicValuationAmountSum +
                                    businessPlanSummary.value.precariousnessValuationAmountSum -
                                    businessPlanSummary.value.feeSum
                                )
                              "
                            />
                          </VCol>
                        </VRow>
                      </NjExpansionPanel>
                    </div>
                    <VDivider v-if="edit" />
                    <div v-if="edit" class="content-layout__footer d-flex justify-end pa-2">
                      <NjBtn :loading="editing.loading" @click="update">Sauvegarder</NjBtn>
                    </div>
                  </div>
                </VWindowItem>
              </VWindow>
            </VCardText>
          </VCard>
        </VCol>
      </VRow>

      <ConfirmUnsavedDataDialog v-model:unsaved-data-dialog="unsavedDataDialog" @save="update" />
      <CommentaireDialog
        v-model="commentaireDialog.props.modelValue"
        v-model:mandatory-message="mandatoryMessage"
        :no-link="!!mandatoryMessage"
        :advised-recipient="advisedRecipient"
        :meta-message-request="metaMessageRequest"
        :business-plan-id="businessPlan.value?.id ?? 0"
        @send="commentaireDialog.props['onClick:positive']"
        @close="commentaireDialog.props['onClick:negative']"
      />
    </template>
    <template #drawer>
      <OperationDrawer
        ref="operationDrawerRef"
        v-model="openDrawer"
        :operation="selectedOperation.value"
        in-operation-group
      />
    </template>
  </NjPage>
</template>
<script setup lang="ts">
import CardDialog from '@/components/CardDialog.vue'
import NjBtn from '@/components/NjBtn.vue'
import NjDisplayValue from '@/components/NjDisplayValue.vue'
import NjExpansionPanel from '@/components/NjExpansionPanel.vue'
import NjPage from '@/components/NjPage.vue'
import type { DataTableHeader } from '@/components/okta/NjDataTable.type'
import NjDataTable from '@/components/okta/NjDataTable.vue'
import { useSnackbarStore } from '@/stores/snackbar'
import type { BusinessPlan, BusinessPlanSummary } from '@/types/businessPlan'
import { formatHumanReadableLocalDate } from '@/types/date'
import { formatNumber, formatPriceNumber } from '@/types/format'
import type { MetaMessageRequest } from '@/types/message'
import { type Operation } from '@/types/operation'
import type { Page } from '@/types/pagination'
import type { PromisableValue } from '@/types/promisableValue'
import { requiredRule } from '@/types/rule'
import { displayFullnameUser } from '@/types/user'
import { debounce } from 'lodash'
import { VCol, VDivider, VRow } from 'vuetify/components'
import OperationDrawer from '../operation/OperationDrawer.vue'
import CommentaireDialog from '../operation/dialog/CommentaireDialog.vue'
import SetOperationBusinessPlan from './SetOperationBusinessPlan.vue'
import OperationStepChip from '../operation/OperationStepChip.vue'
import { useUserStore } from '@/stores/user'
import { useDialogStore } from '@/stores/dialog'

const props = defineProps({
  id: {
    type: Number,
    required: true,
  },
})
const userStore = useUserStore()
const snackbarStore = useSnackbarStore()
const dialogStore = useDialogStore()
const selection = ref<Operation[]>([])
const tabs = ref()

const headers: DataTableHeader<Operation>[] = [
  {
    title: 'Nom',
    value: 'simulationName',
  },
  {
    title: 'Adresse',
    value: 'finalAddress',
    formater: (item, value) => formatAddressable(value),
  },
  {
    title: 'Code',
    value: 'standardizedOperationSheet.operationCode',
  },
  {
    title: 'kWhc class.',
    value: 'classicCumac',
    formater: (_, value) => formatNumber(value),
    cellClass: 'text-right justify-end',
  },
  {
    title: 'kWhc Préca.',
    value: 'precariousnessCumac',
    formater: (_, value) => formatNumber(value),
    cellClass: 'text-right justify-end',
  },
  {
    title: 'kWhc Total',
    value: 'totalCumac',
    formater: (item) => formatNumber(item.classicCumac + item.precariousnessCumac),
    cellClass: 'text-right justify-end',
  },

  {
    title: 'Mnt. Class €',
    value: 'ClassicAmount',
    sortable: false,
    formater: (item) => formatPriceNumber((getClassicValuationValue(item) * item.classicCumac) / 1000),
    cellClass: 'text-right justify-end',
  },
  {
    title: 'Mnt. Préca €',
    value: 'PrecariousnessAmount',
    sortable: false,
    formater: (item) => formatPriceNumber((getPrecariousnessValuationValue(item) * item.precariousnessCumac) / 1000),
    cellClass: 'text-right justify-end',
  },
  {
    title: 'Charge Pôle CEE',
    value: 'fee',
    cellClass: 'text-right justify-end',
    formater: (operation) => formatPriceNumber(getChargeCellule(operation)),
  },
  {
    title: 'Marge CEE agence €',
    value: 'agencyProfit',
    cellClass: 'text-right justify-end',
    formater: (o) => formatPriceNumber(getNetMargin(o)),
  },
]

const businessPlan = ref(emptyValue<BusinessPlan>())
const businessPlanSummary = ref(emptyValue<BusinessPlanSummary>())

const load = () => {
  return Promise.all([handleAxiosPromise(businessPlan, businessPlanApi.findById(props.id)), loadSummary()]).then(() => {
    onSave()
  })
}
const loadSummary = () => {
  return handleAxiosPromise(businessPlanSummary, businessPlanApi.getSummaryById(props.id))
}

watch(() => props.id, load, { immediate: true })

const openDrawer = ref(false)
const operationDrawerRef = ref<typeof OperationDrawer | null>(null)
const selectedOperation = ref(emptyValue<Operation>())
const clickedRow = ref<Operation>()
const clickRow = (item: Operation) => {
  check(() => {
    operationDrawerRef.value!.check(async () => {
      clickedRow.value = item
      if (item.id !== selectedOperation.value.value?.id) {
        await handleAxiosPromise(selectedOperation, simulationApi.findById(item.id), {
          afterError: () =>
            snackbarStore.setError(
              selectedOperation.value.error ?? "Une erreur est survenue lors de la récpération de l'opération"
            ),
        })
        openDrawer.value = true
        return
      }
      openDrawer.value = !openDrawer.value
      if (!openDrawer.value) {
        clickedRow.value = undefined
      }
    })
  })
}

//Operation
const {
  data: operations,
  pageable: pageableOperation,
  pageFilter: pageFilterOperations,
  updatePageable: updatePageableOperations,
  reload: reloadOperations,
} = usePagination<Operation>(
  (filter, pageable) => operationApi.findAll(filter, pageable),
  {
    businessPlanIds: props.id,
    withSimulations: true,
  },
  {}
)

const search = ref('')

const debounceSearch = debounce((v: string) => {
  pageFilterOperations.value.search = v
  reloadOperations()
}, 300)

watch(search, (v) => {
  operations.value.loading = true
  debounceSearch(v)
})

const setOperationsDialog = ref()

watch(setOperationsDialog, (v) => {
  if (!v) {
    Promise.all([reloadOperations(), loadSummary()]).then(() => {
      if (businessPlan.value.value && operations.value.value) {
        // businessPlan.value.value.operationsNumber = operations.value.value.totalElements
      }
    })
  }
})

//Valider étape 30
const numberOfOperationsWithSelfWorks = ref(emptyValue<Page<Operation>>())

const getNumberOfOperationsWithSelfWorks = async () => {
  return handleAxiosPromise(
    numberOfOperationsWithSelfWorks,
    operationApi.findAll({ selfWorks: true, operationsGroupId: props.id }, { size: 1 })
  )
}

onMounted(getNumberOfOperationsWithSelfWorks)

const loadingManagementOfOperations = ref(false)

const edit = ref(false)

const { check, succeedSave, unsavedDataDialog, onSave, copitedData } = useUnsavedData(businessPlan)
const editing = ref(emptyValue<BusinessPlan>())
const update = async () => {
  if (
    !!businessPlan.value.value?.id &&
    businessPlan.value.value.toProcess &&
    !(copitedData.value[0] as PromisableValue<BusinessPlan>).value!.toProcess &&
    !(await sendMailForProcessOperations())
  ) {
    return
  }
  handleAxiosPromise(
    editing,
    businessPlanApi.edit(props.id, {
      name: businessPlan.value.value!.name,
      toProcess: businessPlan.value.value!.toProcess,
    })
  )
    .then(() => {
      snackbarStore.setSuccess('Business Plan mise à jour avec succès')
      edit.value = false
      succeedSave()
    })
    .catch(async (e) => {
      snackbarStore.setError(await handleAxiosException(e))
    })
}
const toggleEdit = () => {
  check(() => {
    edit.value = !edit.value
  })
}

const commentaireDialog = useConfirmAlertDialog()
const advisedRecipient = ref<string[]>([])
const mandatoryMessage = ref('')
const metaMessageRequest = ref<MetaMessageRequest>()
const sendMailForProcessOperations = async (): Promise<boolean> => {
  metaMessageRequest.value = { '@type': 'OperationToProcessMessageRequest' }
  mandatoryMessage.value = toProcessMail({
    businessPlanSummary: businessPlanSummary.value.value!,
    businessPlan: businessPlan.value.value!,
  })
  advisedRecipient.value = []
  userApi
    .getAll(
      { size: 1000 },
      { roles: ['AGENCE_PLUS'], entityIds: businessPlanSummary.value.value!.entityIds, active: true }
    )
    .then((v) => {
      advisedRecipient.value = v.data.content
        .map((it) => it.email)
        .filter((it) => it)
        .sort() as string[]
    })
    .catch(() =>
      snackbarStore.setError("Nous n'avons pas pu récupérer la liste des contacts pour le traitement de la simulation")
    )
  // advisedRecipient.value = mutableOperation.value.instructor

  if (!(await commentaireDialog.confirm())) {
    return false
  }
  mandatoryMessage.value = ''
  advisedRecipient.value = []
  metaMessageRequest.value = undefined

  return true
}

const router = useRouter()
const deleteBusinessPlan = async () => {
  if (
    await dialogStore.addAlert({
      title: 'Supprimer le Business Plan "' + businessPlan.value.value?.name + '"',
      message:
        'Vous allez supprimer le Business Plan "' +
        businessPlan.value.value?.name +
        '".\nÊtes-vous sûr de vouloir confirmer ?',
      maxWidth: 640,
    })
  ) {
    businessPlanApi
      .deleteById(businessPlan.value.value!.id)
      .then(() => {
        snackbarStore.setSuccess('Business Plan supprimé avec succès')
        router.push({ name: 'BusinessPlanAllView' })
      })
      .catch(async (e) => {
        snackbarStore.setError(await handleAxiosException(e))
      })
  }
}
</script>
