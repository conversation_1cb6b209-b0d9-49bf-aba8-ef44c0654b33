<template>
  <DocumentDataTable>
    <template #tbody>
      <OperationsGroupDocumentDataTableRow
        v-for="item in items"
        :key="item.id"
        :item="item"
        :operations-group="operationsGroup"
        :hide-actions="hideActions"
        @send="reload()"
        @replace-document="reload()"
        @desactivate-document="reload()"
        @delete-document="reload()"
        @edit-document="reload()"
      />
    </template>
  </DocumentDataTable>
</template>
<script setup lang="ts">
import OperationsGroupDocumentDataTableRow from './OperationsGroupDocumentDataTableRow.vue'

import type { PropType } from 'vue'
import DocumentDataTable from '../document/DocumentDataTable.vue'
import type { EnhancedOperationsGroup } from '@/types/operationsGroup'
import type { DocumentDataTableItem } from '../operationdocument/types'

defineProps({
  items: Array as PropType<DocumentDataTableItem[]>,
  totalDocumentElements: Number,
  operationsGroup: Object as PropType<EnhancedOperationsGroup>,
  reload: {
    type: Function,
    default: () => {},
  },
  hideActions: Boolean,
})
</script>
