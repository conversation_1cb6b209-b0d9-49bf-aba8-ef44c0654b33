<template>
  <VBtn
    v-bind="$attrs"
    rounded="0"
    flat
    :color
    :variant="variant === 'filled' ? 'flat' : variant"
    :height="density ? undefined : height"
    :class="{ 'px-8': !noPadding }"
    :to
    :href
    :replace
    :exact
    :disabled
    :loading
    :density
    :prepend-icon
  >
    <template #append>
      <slot name="append" />
    </template>

    <slot />
  </VBtn>
</template>

<script setup lang="ts">
import type { PropType } from 'vue'
import type { RouteLocationRaw } from 'vue-router'
import type { VBtn } from 'vuetify/components'

defineProps({
  variant: {
    type: String as PropType<'outlined' | 'filled' | 'text'>,
    default: 'filled',
  },
  color: {
    type: String,
    default: 'primary',
  },
  height: {
    type: [String, Number],
    default: '40px',
  },
  loading: Boolean,
  disabled: Boolean,
  // Router Props
  href: String,
  replace: Boolean,
  to: [String, Object] as PropType<RouteLocationRaw>,
  exact: Boolean,
  density: String as PropType<'default' | 'comfortable' | 'compact'>,
  prependIcon: {
    type: String as PropType<string>,
  },
  noPadding: Boolean,
})
</script>
