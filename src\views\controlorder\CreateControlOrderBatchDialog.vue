<template>
  <AlertDialog
    v-bind="props"
    title="Créer un lot de contrôle"
    positive-button="Valider"
    negative-button="Annuler"
    max-width="700px"
  >
    <VProgressCircular v-if="summary.loading" color="primary" class="ms-4" indeterminate />
    <VRow v-else-if="summary.value" class="flex-column">
      <VCol>
        <NjDisplayValue label="Nombre de cumac classique" :value="formatNumber(summary.value.cumacClassicSum)" />
      </VCol>
      <VCol>
        <NjDisplayValue label="Nombre de cumac précarité" :value="formatNumber(summary.value.cumacPrecariousnessSum)" />
      </VCol>
      <VCol>
        <NjDisplayValue
          label="Nombre de cumac"
          :value="formatNumber((summary.value.cumacClassicSum ?? 0) + (summary.value.cumacPrecariousnessSum ?? 0))"
        />
      </VCol>
      <VCol>
        <NjDisplayValue
          label="Nombre de fiche opération standardisée"
          :value="formatNumber(summary.value.standardizedOperationSheetNumber)"
        />
      </VCol>
      <ErrorAlert
        v-if="summary.value.controlOrderExportTemplateNumber != 1"
        message="Impossible de créer un lot de contrôle à partir de ces opérations car plusieurs modèles d'export sont utilisés"
      />
    </VRow>
    <ErrorAlert v-else-if="summary.error" :message="summary.error" />
  </AlertDialog>
</template>
<script setup lang="ts">
import type { OperationFilter } from '@/api/operation'
import type { PropType } from 'vue'
import type { ControlOrderBatchSummaryBeforeCreation } from '@/types/controlOrder'
import { VProgressCircular, VRow } from 'vuetify/components'
import NjDisplayValue from '@/components/NjDisplayValue.vue'
import { formatNumber } from '@/types/format'

const props = defineProps({
  modelValue: Boolean,
  onClickPositive: Function,
  onClickNegative: Function,
  filter: {
    type: Object as PropType<OperationFilter>,
  },
})

const summary = ref(emptyValue<ControlOrderBatchSummaryBeforeCreation>())
watch(
  () => props.modelValue,
  (v) => {
    if (v) {
      handleAxiosPromise(summary, controlOrderBatchApi.getSummaryBeforeCreation(props.filter!))
    }
  }
)
</script>
