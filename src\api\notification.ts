import type { Page, Pageable } from '@/types/pagination'
import type { AxiosInstance, AxiosPromise } from 'axios'
import axiosInstance from '.'
import type { Notification, UpdateNotificationRequest } from '@/types/notification'

export type NotificationFilter = Partial<{ read: boolean }>

class NotificationApi {
  public constructor(private axios: AxiosInstance) {}

  public findAll(pageable: Pageable, filter: NotificationFilter): AxiosPromise<Page<Notification>> {
    return this.axios.get('/notifications', { params: { ...pageable, ...filter } })
  }

  public update(id: number, request: UpdateNotificationRequest): AxiosPromise<Notification> {
    return this.axios.put(`/notifications/${id}`, request)
  }

  public delete(id: number): AxiosPromise {
    return this.axios.delete(`/notifications/${id}`)
  }

  public readAll(): AxiosPromise {
    return this.axios.put(`/notifications/read`)
  }

  public deleteAll(): AxiosPromise {
    return this.axios.delete(`/notifications`)
  }
}
export const notificationApi = new NotificationApi(axiosInstance)
