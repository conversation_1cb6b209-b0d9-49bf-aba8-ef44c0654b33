<template>
  <VDialog
    :model-value="unsavedDataDialog ? unsavedDataDialog.active : modelValue"
    max-width="640"
    persistent
    @update:model-value="updateModelValue"
  >
    <VCard>
      <VCardTitle> Changements non enregistrés </VCardTitle>
      <VCardText>
        Vous avez saisi des changements qui ne sont pas encore enregistrés.<br />
        Voulez-vous les enregistrer ou bien quitter sans sauvegarder ?
      </VCardText>
      <VCardActions>
        <VSpacer />
        <NjBtn variant="outlined" @click="cancelConfirmCancelEdit"> Annuler </NjBtn>
        <NjBtn variant="outlined" @click="confirmConfirmCancelEdit"> Quitter </NjBtn>
        <NjBtn @click="save">Sauvegarder</NjBtn>
      </VCardActions>
    </VCard>
  </VDialog>
</template>

<script lang="ts" setup>
import type { UnsavedDataDialogData } from '@/types/unsavedData'
import type { PropType } from 'vue'
import NjBtn from './NjBtn.vue'

const props = defineProps({
  modelValue: {
    type: Boolean,
  },
  unsavedDataDialog: {
    type: Object as PropType<UnsavedDataDialogData>,
  },
})

const emit = defineEmits(['update:model-value', 'update:unsaved-data-dialog', 'cancel', 'confirm', 'save'])
const updateModelValue = (value: boolean) => {
  if (props.unsavedDataDialog) {
    emit('update:unsaved-data-dialog', {
      ...props.unsavedDataDialog,
      active: value,
    })
  }
  emit('update:model-value', value)
}
const cancelConfirmCancelEdit = () => {
  emit('update:unsaved-data-dialog', {
    active: false,
  })
  emit('cancel')
}
const confirmConfirmCancelEdit = () => {
  cancelConfirmCancelEdit()
  props.unsavedDataDialog?.confirmCallback?.()
  emit('confirm')
}
const save = () => {
  cancelConfirmCancelEdit()
  emit('save')
}
</script>
