import type { LocalDate } from './date'
import type { OshCommentaryType } from './osh/oshFolder'
export interface OperationExportResultDto {
  id: number
  chronoCode: string | null //"Chrono"
  formatedEntityName: string | null //"Organisation"
  operationName: string | null //"Réservation / Installation"
  formatedStepName: string | null //"Etape en cours"
  reservedClassicCumac: number | null //"Nb.rés.Clas.kWhc"
  reservedPrecariousnessCumac: number | null //"Nb.rés.Préc.kWhc"
  effectiveEndWorksDate: LocalDate | null //"Fin trv.ret."
  numberOfDays: number | null //"Nbr.JR"
  standardizedOperationSheetCode: string | null //"Code opération"
  reservedClassicValuationValue: number | null //"Valo.rés.Clas.€"
  reservedClassicAmountValue: number | null //"Mnt.rés.Clas.€"
  reservedPrecariousnessValuationValue: number | null //"Valo.rés.Préc.€"
  reservedPrecariousnessAmountValue: number | null //"Mnt.rés.Préc.€"
  valuationType: string | null //"Type de valorisation"
  commercialStatusLabel: string | null //"Statut offre commerciale"
  displayedReservationUser: string | null //"Demandeur"
  beneficiarySocialReason: string | null //"Nom du client"
  beneficiarySiren: string | null //"N° SIREN"
  finalAddressStreet: string | null //"Adresse"
  finallAddressAdditionalPostalAddress: string | null //"Complément adresse"
  finallAddressPostalCode: string | null //"Code postal"
  finallAddressCity: string | null //"Ville"
  customerOfferNumber: string | null //"N° offre"
  //"N° devis"
  //signedDate //"Sign.conv."
  customerFinancialIncentive: number | null //"Moins-value €"
  standardizedOperationSheetDescription: string | null //"Libellé opération"
  worksCreationDate: LocalDate | null // LocalDate | null.of(2024 1 1) "Cré.chantier"
  propertyCode: string | null //"N° installation"
  finalWorksType: string | null //"Type"
  worksCode: string | null //"N° Chantier"
  // 0 "Chg.neutr.€"
  estimatedEndOperationDate: LocalDate | null //"Fin trv.estimée"
  emmyFolderEmmyCode: string | null //"N° EMMY"
  emmyFolderName: string | null //"Nom du dossier EMMY"
  emmyFolderPnceeSubmissionDate: LocalDate | null //"Env.PNCEE"
  askedClassicCumac: number | null //"Nb.dem.Clas.kWhc"
  askedClassicValuationValue: number | null //"Valo.dem.Clas.€"
  askedClassicAmount: number | null //"Mnt.dem.Clas.€"
  askedPrecariousnessCumac: number | null //"Nb.dem.Préc.kWhc"
  askedPrecariousnessValuationValue: number | null //"Valo.dem.Préc.€"
  askedPrecariousnessAmount: number | null //"Mnt.dem.Préc.€"
  //"N° pièce de l'entrée en stock"
  //"Ent.Stk."
  emmyFolderPnceeIssuedDate: LocalDate | null //"Délivr.PNCEE"
  //"Mnt.versé €"
  //"Versement"
  //"PAEE"
  atypical: boolean //"Atypique"
  actualEndWorksDate: LocalDate | null //"Fin trv.déclarée"
  signedDate: LocalDate | null //"Engag.opérat."
  regionName: string | null //"Région"
  statusValue: number | null //"Processus"
  maxStepId: number | null //"Etape/retour"
  reservedDate: LocalDate | null //"Date réservation"
  dateStep50: LocalDate | null //"Date arr.dir.env."
  periodNumber: string | null //"Période"
  //"Date enreg."
  //"Etape siège"
  offersDispatchDate: LocalDate | null //"Date env.offre"
  //"Date fin validité"
  instructorDisplayed: string | null //"Instructeur de l'opération"
  //"Nature logement"
  //"Alerte Valo"
  accountedMessagesNumber: number | null //"Com.comptabilisé"
  boostType: string | null //"Coup de pouce"
  secondInterlocutor: string | null //"Autre interlocuteur"
  epcEfficacity: string | null //"CPE - %eco"
  epcDuration: string | null //"CPE - Durée"
  date10: LocalDate | null //"10 Soumettre une réservation CEE"
  date20: LocalDate | null //"20 Valider réservation région"
  date30: LocalDate | null //"30 Préparer et envoyer Offre avec volet CEE au client"
  date40: LocalDate | null //"40 Attente retour signature de la convention du client"
  date50: LocalDate | null //"50 Réaliser les travaux et regrouper les éléments CEE pour l'opération"
  date60: LocalDate | null //"60 Valider l'opération CEE par le BP Envt"
  date70: LocalDate | null //"70 Valider le contrôle du dossier ARRETE CONTROLE"
  date80: LocalDate | null //"80 Regrouper les opérations pour constitution du dossier EMMY"
  date90: LocalDate | null //"90 Envoyer la demande au PNCEE"
  date100: LocalDate | null //"100 Attente de la décision de délivrance"
  date110: LocalDate | null //"110 Mettre en place la note de crédit/Transfert de stock"
  //"Terminé"
  manual: boolean //"Saisie manuelle"
  headOperation: boolean //"Opé. Chapeau"
  oshCommentary: OshCommentaryType
  oshHidden: boolean
}
