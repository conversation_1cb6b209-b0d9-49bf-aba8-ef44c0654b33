import type { DataTableHeader } from '@/components/okta/NjDataTable.type'
import { displayFullnameUser } from '@/types/user'
import { formatHumanReadableLocalDate } from '@/types/date'
import { formatNumber, formatPriceNumber } from '@/types/format'
import { includes } from 'lodash'
import { useAdminConfigurationStore } from '@/stores/adminConfiguration'

const adminConfigurationStore = useAdminConfigurationStore()

export const originalHeaders: DataTableHeader[] = [
  {
    title: 'Nom Simulation',
    value: 'simulationName',
  },
  {
    title: 'Numéro simulation',
    value: 'id',
  },
  {
    title: 'Installation',
    value: 'property',
  },
  {
    title: 'Créateur',
    value: 'creationUser',
    formater: (_, value) => displayFullnameUser(value),
  },
  {
    title: 'Réservé',
    value: 'reserved',
    sortable: false,
  },
  {
    title: 'Nom opération',
    value: 'operationName',
  },
  {
    title: 'Chrono opération',
    value: 'chronoCode',
  },
  {
    title: 'Code opération',
    value: 'standardizedOperationSheet.operationCode',
  },
  {
    title: 'Étape',
    value: 'stepId',
    sortable: false,
  },
  {
    title: 'Engagement prévisionnel',
    value: 'estimatedCommitmentDate',
    formater: (item, value) => (item.stepId > 0 ? formatHumanReadableLocalDate(value) : ''),
  },
  {
    title: 'Engagement réel',
    value: 'signedDate',
    formater: (_, value) => formatHumanReadableLocalDate(value),
  },
  {
    title: 'Date de réservation',
    value: 'creationDateTime',
    formater: (_, value) => formatHumanReadableLocalDate(value),
  },
  {
    title: 'Libellé opération',
    value: 'standardizedOperationSheet.description',
  },
  {
    title: 'Début validité opération',
    value: 'standardizedOperationSheet.startDate',
    formater: (_, value) => formatHumanReadableLocalDate(value),
  },
  {
    title: 'Fin validité opération',
    value: 'standardizedOperationSheet.expirationDate',
    formater: (_, value) => formatHumanReadableLocalDate(value),
  },
  {
    title: 'kWhc class.',
    value: 'classicCumac',
    formater: (_, value) => formatNumber(value),
    cellClass: 'text-right justify-end',
  },
  {
    title: 'kWhc Préca.',
    value: 'precariousnessCumac',
    formater: (_, value) => formatNumber(value),
    cellClass: 'text-right justify-end',
  },
  {
    title: 'Nb. Rés. Class. kWhc',
    value: 'reservedClassicCumac',
    formater: (_, value) => formatNumber(value),
    cellClass: 'text-right justify-end',
  },
  {
    title: 'Nb. Rés. Préca. kWhc',
    value: 'reservedPrecariousnessCumac',
    formater: (_, value) => formatNumber(value),
    cellClass: 'text-right justify-end',
  },
  {
    title: 'Valo. Rés.Class €',
    value: 'reservedClassicValuationValue',
    formater: (_, value) => formatPriceNumber(value),
    cellClass: 'text-right justify-end',
  },
  {
    title: 'Mnt. Rés. Class €',
    value: 'reservedClassicAmount',
    sortable: false,
    formater: (_, value) => formatPriceNumber(value),
    cellClass: 'text-right justify-end',
  },
  {
    title: 'Valo. Rés.Préca €',
    value: 'reservedPrecariousnessValuationValue',
    formater: (_, value) => formatPriceNumber(value),
    cellClass: 'text-right justify-end',
  },
  {
    title: 'Mnt. Rés. Préca €',
    value: 'reservedPrecariousnessAmount',
    sortable: false,
    formater: (_, value) => formatPriceNumber(value),
    cellClass: 'text-right justify-end',
  },
  {
    title: 'Coup de pouce',
    value: 'cdp',
    sortable: false,
  },
  {
    title: 'CPE',
    value: 'epc',
    sortable: false,
  },
  {
    title: 'Précarité',
    value: 'precariousness',
    sortable: false,
  },
  {
    title: "Nombre de renvoi à l'étape 50",
    value: 'backToStep50Counter',
    sortable: false,
    cellClass: 'text-right justify-end',
  },
  {
    title: 'Second interlocuteur',
    value: 'secondInterlocutor',
  },
  {
    title: 'À traiter',
    value: 'toProcess',
    formater: (_, value) => (value ? 'OUI' : 'NON'),
  },
  {
    title: 'Statut Convention',
    value: 'conventionSent',
    sortable: false,
    formater: (item) => {
      return includes(item.documentTypeIdsSent, adminConfigurationStore.conventionDocumentTypeId?.valueAsInt) ||
        includes(
          item.operationsGroup?.documentTypeIdsSent,
          adminConfigurationStore.conventionDocumentTypeId?.valueAsInt
        )
        ? 'Envoyée'
        : 'En attente'
    },
  },
]
