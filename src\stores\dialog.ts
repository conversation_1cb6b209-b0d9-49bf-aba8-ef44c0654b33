import { defineStore } from 'pinia'
import type { FunctionalComponent } from 'vue'

export interface DialogRecord {
  id: number
  modelValue: boolean
  title: string
  message: string | FunctionalComponent
  positiveButton?: string
  negativeButton?: string
  closeable?: boolean
  error?: string
  result: (val: boolean) => void
  maxWidth?: string | number
  persistent?: boolean
  width?: string | number
}

export type DialogStoreRequest = Omit<DialogRecord, 'id' | 'modelValue' | 'result' | 'error'>

export const useDialogStore = defineStore('dialog', () => {
  const idAutoIncrementState = ref(1)

  const state = ref<DialogRecord[]>([])

  // TODO Sera renommer plus tard en addConfirm
  const addAlert = (request: DialogStoreRequest): Promise<boolean> => {
    return new Promise((resolve) => {
      state.value.push({
        id: idAutoIncrementState.value++,
        modelValue: true,
        message: request.message,
        title: request.title,
        closeable: true,
        positiveButton: request.positiveButton ?? 'Valider',
        negativeButton: request.negativeButton ?? 'Annuler',
        result(val) {
          this.modelValue = false
          resolve(val)
          setTimeout(() => {
            state.value.splice(state.value.indexOf(this), 1)
          }, 2000)
        },
        maxWidth: request.maxWidth,
        persistent: request.persistent,
        width: request.width,
      })
    })
  }

  // TODO Sera renommer plus tard en addAlert
  const addAlert2 = (request: Omit<DialogStoreRequest, 'negativeButton'>): Promise<boolean> => {
    return new Promise((resolve) => {
      state.value.push({
        id: idAutoIncrementState.value++,
        modelValue: true,
        message: request.message,
        title: request.title,
        closeable: true,
        positiveButton: request.positiveButton ?? 'OK',
        result(val) {
          this.modelValue = false
          resolve(val)
          setTimeout(() => {
            state.value.splice(state.value.indexOf(this), 1)
          }, 2000)
        },
        maxWidth: request.maxWidth,
        persistent: request.persistent,
        width: request.width,
      })
    })
  }

  // const setConfirm = (message: string, timeout: number = 5000) => {
  //   state.value = {
  //     modelValue: true,
  //     color: 'error',
  //     message,
  //     timeout,
  //   }
  // }

  return { state, addAlert, addAlert2 }
})
