<template>
  <VForm ref="formRef" class="content-layout">
    <VRow class="content-layout__main">
      <VCol>
        <VRow class="flex-column mt-1">
          <VCol>
            <VTextField v-model="documentType.name" label="Nom" :rules="[requiredRule]" />
          </VCol>
          <VCol>
            <RichTextInput v-model="documentType.instructions" label="Consignes" />
          </VCol>
          <VCol>
            <NjExpansionPanel title="Règles">
              <VRow class="flex-column" dense>
                <VCol>
                  <VSwitch v-model="documentType.shareable" label="Partageable" />
                </VCol>
                <VCol>
                  <VSwitch
                    v-model="documentType.requiredInOperationsGroupForOperationInOperationsGroup"
                    label="Le document doit être mis au niveau du regroupement pour les opérations dans un regroupement"
                  />
                </VCol>
                <VCol>
                  <VSwitch
                    v-model="documentType.ignoreForSelfWorks"
                    label="Le document est ignoré si travaux en propre"
                  />
                </VCol>
                <VCol>
                  <VSwitch v-model="documentType.uniqueDoc" label="Unique" />
                </VCol>
                <VCol>
                  <VSwitch
                    v-model="documentType.requiredForSubsidiary"
                    label="Le document est obligatoire pour les filiales"
                  />
                </VCol>
                <VCol>
                  <VSwitch
                    v-model="documentType.requiredForSubcontracting"
                    label="Le document est obligatoire pour les opérations sous traitées"
                  />
                </VCol>
              </VRow>
            </NjExpansionPanel>
          </VCol>

          <VCol>
            <NjExpansionPanel title="Fusion">
              <VRow class="flex-column" dense>
                <VCol>
                  <VTextField v-model="documentType.fusionOrder" type="number" label="Ordre pour fusion" />
                </VCol>
                <VCol>
                  <VSwitch v-model="documentType.inInitialVersion" label="Présent : Version Initiale" />
                </VCol>
                <VCol>
                  <VSwitch v-model="documentType.inFinalVersion" label="Présent : Version Finale" />
                </VCol>
                <VCol>
                  <VSwitch v-model="documentType.inOtherVersion" label="Présent : Autre Version" />
                </VCol>
              </VRow>
            </NjExpansionPanel>
          </VCol>

          <VCol>
            <NjExpansionPanel title="Types de documents imposées">
              <VRow class="flex-column" dense>
                <VCol>
                  <VSwitch v-model="documentType.mustBeWord" label="Document Word" />
                </VCol>
                <VCol>
                  <VSwitch v-model="documentType.mustBeExcel" label="Document Excel" />
                </VCol>
                <VCol>
                  <VSwitch v-model="documentType.mustBePdf" label="Document PDF" />
                </VCol>
              </VRow>
            </NjExpansionPanel>
          </VCol>
          <VCol>
            <NjExpansionPanel title="Modèle">
              <VRow class="flex-column" dense>
                <VCol>
                  <NjDisplayValue label="Modèle vierge">
                    <template #value>
                      <template v-if="templateFile || (documentType.template && documentType.template.id !== 0)">
                        <VRow class="align-center">
                          <VCol>
                            <component
                              :is="documentType.template && documentType.template.id !== 0 ? VLink : 'div'"
                              @click="
                                documentType.template && documentType.template.id !== 0 ? downloadTemplate() : undefined
                              "
                            >
                              {{ templateFile ? templateFile.name : documentType.template?.originalFilename }}
                            </component>
                          </VCol>
                          <VCol class="flex-grow-0">
                            <NjIconBtn
                              icon="mdi-delete-outline"
                              @click="
                                () => {
                                  documentType.template = null
                                  templateFile = null
                                }
                              "
                            />
                          </VCol>
                        </VRow>
                      </template>
                      <NjFileInput
                        v-else
                        @new-files="
                          (event) => {
                            templateFile = event[0]
                          }
                        "
                      />
                    </template>
                  </NjDisplayValue>
                </VCol>
                <VCol>
                  <NjDisplayValue label="Modèle complétable">
                    <template #value>
                      <template
                        v-if="
                          fillableTemplateFile ||
                          (documentType.fillableTemplate && documentType.fillableTemplate.id !== 0)
                        "
                      >
                        <VRow class="align-center">
                          <VCol>
                            <component
                              :is="
                                documentType.fillableTemplate && documentType.fillableTemplate.id !== 0 ? VLink : 'div'
                              "
                              @click="
                                documentType.fillableTemplate && documentType.fillableTemplate.id !== 0
                                  ? downloadFillableTemplate()
                                  : undefined
                              "
                            >
                              {{
                                fillableTemplateFile
                                  ? fillableTemplateFile.name
                                  : documentType.fillableTemplate?.originalFilename
                              }}
                            </component>
                          </VCol>
                          <VCol class="flex-grow-0">
                            <NjIconBtn
                              icon="mdi-delete-outline"
                              @click="
                                () => {
                                  documentType.fillableTemplate = null
                                  fillableTemplateFile = null
                                }
                              "
                            />
                          </VCol>
                        </VRow>
                      </template>
                      <NjFileInput
                        v-else
                        @new-files="
                          (event) => {
                            fillableTemplateFile = event[0]
                          }
                        "
                      />
                    </template>
                  </NjDisplayValue>
                </VCol>
              </VRow>
            </NjExpansionPanel>
          </VCol>
        </VRow>
      </VCol>
    </VRow>
  </VForm>
  <VCardActions style="padding-right: 0 !important">
    <VSpacer />
    <NjBtn variant="outlined" color="error" :disabled="saving.loading" @click="cancel"> Annuler </NjBtn>
    <NjBtn :loading="saving.loading" @click="save"> Enregistrer </NjBtn>
  </VCardActions>
  <ConfirmUnsavedDataDialog v-model:unsaved-data-dialog="unsavedDataDialog" @save="save" />
</template>
<script lang="ts" setup>
import { useSnackbarStore } from '@/stores/snackbar'
import type { DocumentType } from '@/types/documentType'
import { requiredRule } from '@/types/rule'
import { cloneDeep } from 'lodash'
import { VCol, VRow, VSwitch, VTextField, type VForm } from 'vuetify/components'
import VLink from '@/components/VLink.vue'
import NjFileInput from '@/components/NjFileInput.vue'

const props = defineProps({
  document: {
    type: Object as PropType<DocumentType>,
    default: () => makeEmptyDocumentType(),
  },
})

const emit = defineEmits(['saved', 'cancel'])
const documentType = ref(cloneDeep(props.document))
const documentTypeSave = ref(cloneDeep(props.document))

const snackbarStore = useSnackbarStore()
const formRef = ref<VForm | null>(null)

const { failedSave, succeedSave, unsavedDataDialog, check } = useUnsavedData(documentType)

const templateFile = ref<File | null>(null)
const fillableTemplateFile = ref<File | null>(null)

const downloadTemplate = () => {
  documentTypeApi
    .downloadTemplate(props.document.id)
    .then((response) => {
      downloadFile(documentType.value.template!.originalFilename, response.data)
      snackbarStore.setSuccess('Le téléchargement a réussi')
    })
    .catch(() =>
      snackbarStore.setError(
        'Le téléchargement du fichier a échoué. Aviez-vous envoyé ce fichier sur boCEE ? Si oui, veuillez nous contacter.'
      )
    )
}

const downloadFillableTemplate = () => {
  documentTypeApi
    .downloadFillableTemplate(props.document.id)
    .then((response) => {
      downloadFile(documentType.value.fillableTemplate!.originalFilename, response.data)
      snackbarStore.setSuccess('Le téléchargement a réussi')
    })
    .catch(() =>
      snackbarStore.setError(
        'Le téléchargement du fichier a échoué. Aviez-vous envoyé ce fichier sur boCEE ? Si oui, veuillez nous contacter.'
      )
    )
}

const saving = ref(emptyValue<DocumentType>())
const save = async () => {
  if ((await formRef.value!.validate()).valid) {
    if (props.document.id) {
      handleAxiosPromise(
        saving,
        documentTypeApi.update(
          props.document.id,
          mapToDocumentTypeRequest(documentType.value!),
          templateFile.value,
          fillableTemplateFile.value
        ),
        {
          afterSuccess: (v) => {
            snackbarStore.setSuccess('Document bien mis à jour')
            succeedSave()
            emit('saved', v.data)
          },
          afterError: async (err) => {
            failedSave()
            snackbarStore.setError(await handleAxiosException(err))
          },
        }
      )
    } else {
      handleAxiosPromise(
        saving,
        documentTypeApi.create(
          mapToDocumentTypeRequest(documentType.value!),
          templateFile.value,
          fillableTemplateFile.value
        ),
        {
          afterSuccess: () => {
            snackbarStore.setSuccess('Document bien créée')
            succeedSave()
            emit('saved')
          },
          afterError: async (err) => {
            failedSave()
            snackbarStore.setError(await handleAxiosException(err))
          },
        }
      )
    }
  }
}

const checkUnsaved = (confirmCallback: () => void): boolean => {
  return check(confirmCallback)
}

const cancel = () => {
  checkUnsaved(() => {
    documentType.value = documentTypeSave.value
    emit('cancel')
  })
}

watch(
  () => props.document,
  (v) => {
    if (v && v.id) {
      documentType.value = cloneDeep(v)
      documentTypeSave.value = cloneDeep(v)
      succeedSave()
    }
  },
  {
    immediate: true,
  }
)

defineExpose({ checkUnsaved })
</script>
