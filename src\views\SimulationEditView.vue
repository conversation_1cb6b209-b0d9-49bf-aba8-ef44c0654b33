<template>
  <OperationFormPage v-if="simulation.value && simulation.loading == false" :simulation="simulation.value" edit />
</template>

<script lang="ts" setup>
import type { Operation } from '@/types/operation'
import OperationFormPage from '@/views/OperationFormPageView.vue'

const props = defineProps({
  simulationId: {
    type: Number,
    required: true,
  },
})

const simulation = ref(emptyValue<Operation>())

onMounted(() => {
  handleAxiosPromise(simulation, simulationApi.findById(props.simulationId), (response) => {
    simulation.value.value = response.data
  })
})
</script>
