import type { AxiosInstance, AxiosPromise } from 'axios'
import axiosInstance from '.'
import type { ControlOrderAfterSaleServiceStepHistory } from '@/types/controlorderAfterSaleServiceStepHistory'

class ControlOrderAfterSaleServiceStepHistoryApi {
  public constructor(private axios: AxiosInstance) {}

  public findAll(operationId: number): AxiosPromise<ControlOrderAfterSaleServiceStepHistory[]> {
    return this.axios.get('operations/' + operationId + '/control_order_after_sales_service_step_histories')
  }
}
export const controlOrderAfterSaleServiceStepHistoryApi = new ControlOrderAfterSaleServiceStepHistoryApi(axiosInstance)
