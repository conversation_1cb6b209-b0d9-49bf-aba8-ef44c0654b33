import type { DataTableHeader } from '@/components/okta/NjDataTable.type'
import type { CeeStockEntryDto, CeeStockOperationWithAccountingEntrySummary } from '@/types/ceeStockEntry'
import type { LocalDateTime } from '@/types/date'

const commonFirstPartColumns: DataTableHeader<CeeStockEntryDto>[] = [
  {
    title: 'Chrono',
    value: 'operation.chronoCode',
    formater: (item: CeeStockEntryDto) => item.chronoCode,
  },
  {
    title: 'Nom',
    value: 'operation.operationName',
    formater: (item: CeeStockEntryDto) => item.operationName,
  },
  {
    title: 'Étape',
    value: 'operation.stepId',
    formater: (item: CeeStockEntryDto) => item.stepId.toString(),
  },
]

const commonSecondPartColumns: DataTableHeader<CeeStockEntryDto>[] = [
  {
    title: 'Organisation',
    value: 'operation.entity.name',
    formater: (item: CeeStockEntryDto) => item.entityId + ' - ' + item.entityName,
  },
  {
    title: 'Date de la 1ère entrée en stock',
    value: 'minCreationDateTime',
    sortable: false,
    formater: (item: CeeStockEntryDto) => formatHumanReadableLocalDate(item.minCreationDateTime),
  },
  {
    title: 'Prime CEE Agence €',
    value: 'ceeStockOperationHistory',
    sortable: false,
    formater: (item: CeeStockEntryDto) =>
      formatPriceNumber(
        (item.classicCumac * item.classicValuationValue +
          item.precariousnessCumac * item.precariousnessValuationValue) /
          1000
      ),
    cellClass: 'text-right justify-end',
  },
  {
    title: 'Charge Cel. CEE €',
    value: 'chargeClass',
    sortable: false,
    formater: (item: CeeStockEntryDto) =>
      formatPriceNumber(((item.classicCumac + item.precariousnessCumac) * item.fee) / 1000),

    cellClass: 'text-right justify-end',
  },
  {
    title: 'Nb. kWhc',
    value: 'ceeStockOperationHistory',
    sortable: false,
    formater: (item: any) => formatKwhcNumber(item.classicCumac + item.precariousnessCumac),
    cellClass: 'text-right justify-end',
  },
]

const commonClassicColumns: DataTableHeader<CeeStockEntryDto>[] = [
  {
    title: 'Nb. Class. kWhc',
    value: 'classicCumac',
    formater: (_: any, value: number) => formatKwhcNumber(value),
    cellClass: 'text-right justify-end',
  },
  {
    title: 'Prime CEE Ag. Class.',
    value: 'primeClass',
    sortable: false,
    formater: (item: CeeStockEntryDto) => formatPriceNumber((item.classicCumac * item.classicValuationValue) / 1000),
    cellClass: 'text-right justify-end',
  },
  {
    title: 'Charge Cel. CEE Class. €',
    value: 'chargeClass',
    sortable: false,
    formater: (item: CeeStockEntryDto) => formatPriceNumber(classicFee(item)),

    cellClass: 'text-right justify-end',
  },
]
const commonPrecariousnessColumns: DataTableHeader<CeeStockEntryDto>[] = [
  {
    title: 'Nb. Preca. kWhc',
    value: 'precariousnessCumac',
    formater: (_: any, value: number) => formatKwhcNumber(value),
    cellClass: 'text-right justify-end',
  },
  {
    title: 'Prime CEE Ag. Préca.',
    value: 'primePreca',
    sortable: false,
    formater: (item: CeeStockEntryDto) =>
      formatPriceNumber((item.precariousnessCumac * item.classicValuationValue) / 1000),
    cellClass: 'text-right justify-end',
  },
  {
    title: 'Charge Cel. CEE Préca. €',
    value: 'chargePrec',
    sortable: false,
    formater: (item: CeeStockEntryDto) => formatPriceNumber(precariousnessFee(item)),
    cellClass: 'text-right justify-end',
  },
]
const commonOthersColumns: DataTableHeader<CeeStockEntryDto>[] = [
  {
    title: 'Numéro de chantier',
    value: 'worksNumber',
    sortable: false,
    formater: (item: CeeStockEntryDto) =>
      [item.worksId1, item.worksId2, item.worksId3].filter((s) => s != null).join('/'),
  },
  {
    title: 'Status du chantier',
    value: 'worksStatus',
    sortable: false,
  },
  {
    title: 'Type de chantier',
    value: 'worksType',
    sortable: false,
  },
  {
    title: 'Dossier EMMY',
    value: 'operation.emmyFolder.emmyCode',
    formater: (item: CeeStockEntryDto) => item.emmyFolderCode ?? '',
  },
  {
    title: 'Date de réservation',
    value: 'operation.reservedDate',
    formater: (item: CeeStockEntryDto) => formatHumanReadableLocalDate(item.operationReservedDate),
  },
  {
    title: 'Etape 10',
    value: 'step10DateTime',
    formater: (_: any, value: LocalDateTime) => formatHumanReadableLocalDate(value),
    sortable: false,
  },
  {
    title: 'Date mvt',
    value: 'creationDateTime',
    formater: (_: any, value: LocalDateTime) => formatHumanReadableLocalDate(value),
  },
]

const commonAccountingEntries = [
  {
    title: 'n° pièce SAP entrée en stock',
    value: 'lastEntrySapCode',
    sortable: false,
  },
  {
    title: 'Date pièce entrée en stock',
    value: 'lastEntryCreationDate',
    formater: (_: any, value: LocalDateTime) => formatHumanReadableLocalDate(value),
    sortable: false,
  },
  {
    title: 'n° pièce SAP coût CEE imputé sur chantier',
    value: 'lastWorksCeeCostSapCode',
    sortable: false,
  },
  {
    title: 'Date pièce coût CEE imputé sur chantier',
    value: 'lastWorksCeeCostCreationDate',
    formater: (_: any, value: LocalDateTime) => formatHumanReadableLocalDate(value),
    sortable: false,
  },
]
const accountingEntryColumns = commonAccountingEntries.concat([
  {
    title: 'n° pièce SAP transfert stock',
    value: 'lastEntityTransferSapCode',
    sortable: false,
  },
  {
    title: 'Date pièce Transfert stock',
    value: 'lastEntityTransferCreationDate',
    formater: (_: any, value: LocalDateTime) => formatHumanReadableLocalDate(value),
    sortable: false,
  },
  {
    title: 'n° pièce SAP dernière sortie de stock',
    value: 'lastExitSapCode',
    sortable: false,
  },
  {
    title: 'Date pièce Dernière sortie de stock',
    value: 'lastExitCreationDate',
    formater: (_: any, value: LocalDateTime) => formatHumanReadableLocalDate(value),
    sortable: false,
  },
  {
    title: 'n° pièce SAP Dernière vente stock',
    value: 'lastSellSapCode',
    sortable: false,
  },
  {
    title: 'Date pièce Dernière vente stock',
    value: 'lastSellCreationDate',
    formater: (_: any, value: LocalDateTime) => formatHumanReadableLocalDate(value),
    sortable: false,
  },
])

export const defaultCeeStockOperationHeaders: DataTableHeader<CeeStockEntryDto>[] = (
  [] as DataTableHeader<CeeStockEntryDto>[]
).concat(
  commonFirstPartColumns,
  commonSecondPartColumns,
  commonClassicColumns,
  commonPrecariousnessColumns,
  commonOthersColumns,
  commonAccountingEntries
)

const calculateAmount = (cumac: number, valuation: number) => {
  return (cumac * valuation) / 1000
}

const classicCumacAmount = (dto: CeeStockOperationWithAccountingEntrySummary) =>
  calculateAmount(dto.classicCumac, dto.classicValuationValue)
const classicFee = (dto: CeeStockOperationWithAccountingEntrySummary | CeeStockEntryDto) =>
  calculateAmount(dto.classicCumac, dto.fee)

const precariousnessCumacAmount = (dto: CeeStockOperationWithAccountingEntrySummary) =>
  calculateAmount(dto.precariousnessCumac, dto.precariousnessValuationValue)
const precariousnessFee = (dto: CeeStockOperationWithAccountingEntrySummary | CeeStockEntryDto) =>
  calculateAmount(dto.precariousnessCumac, dto.fee)

const totalCumac = (dto: CeeStockOperationWithAccountingEntrySummary) => dto.classicCumac + dto.precariousnessCumac
const totalSoldCumac = (dto: CeeStockOperationWithAccountingEntrySummary) =>
  dto.soldClassicCumac + dto.soldPrecariousnessCumac
const totalSoldAmount = (dto: CeeStockOperationWithAccountingEntrySummary) =>
  dto.classicCumacSoldAmount + dto.precariousnessCumacSoldAmount

const notSoldClassicCumac = (dto: CeeStockOperationWithAccountingEntrySummary) =>
  dto.classicCumac - dto.soldClassicCumac
const notSoldPrecariousnessCumac = (dto: CeeStockOperationWithAccountingEntrySummary) =>
  dto.precariousnessCumac - dto.soldPrecariousnessCumac

const diffClassicAndSoldAmount = (dto: CeeStockOperationWithAccountingEntrySummary) =>
  classicCumacAmount(dto) - dto.classicCumacSoldAmount
const diffPrecariousnessAndSoldAmount = (dto: CeeStockOperationWithAccountingEntrySummary) =>
  precariousnessCumacAmount(dto) - dto.precariousnessCumacSoldAmount

export const dafSiegeCeeStockOperationHeaders: DataTableHeader<CeeStockOperationWithAccountingEntrySummary>[] = (
  [] as DataTableHeader<CeeStockOperationWithAccountingEntrySummary>[]
).concat(
  [
    {
      title: 'Actions',
      value: 'action',
      sortable: false,
    },
  ],
  commonFirstPartColumns,
  [
    {
      title: 'Code société',
      value: 'entity.company.id',
      formater: (item: CeeStockOperationWithAccountingEntrySummary) => item.companyId.toString(),
    },
  ],
  commonSecondPartColumns,
  {
    title: 'Nb.kWhc vendus/obligation',
    value: 'ceeSaleCumacsTotal',
    formater: (item: CeeStockOperationWithAccountingEntrySummary) =>
      formatNumber(item.soldClassicCumac + item.soldPrecariousnessCumac),
    sortable: false,
    cellClass: 'text-right justify-end',
  },

  {
    title: 'Montant total des ventes',
    value: 'soldAmount',
    formater: (item: CeeStockOperationWithAccountingEntrySummary) => formatPriceNumber(totalSoldAmount(item)),
    sortable: false,
    cellClass: 'text-right justify-end',
  },
  {
    title: 'Valorisation unitaire vendu',
    value: 'soldUnitPrice',
    formater: (dto: CeeStockOperationWithAccountingEntrySummary) =>
      formatPriceNumber((totalSoldAmount(dto) * 1000) / totalSoldCumac(dto)),
    sortable: false,
    cellClass: 'text-right justify-end',
  },
  {
    title: 'Solde total Nb. kWhc',
    value: 'cumacNotSold',
    formater: (dto: CeeStockOperationWithAccountingEntrySummary) => formatNumber(totalCumac(dto) - totalSoldCumac(dto)),
    sortable: false,
    cellClass: 'text-right justify-end',
  },
  {
    title: 'Marge dossier vendu',
    value: 'marginSold',
    formater: (dto: CeeStockOperationWithAccountingEntrySummary) =>
      formatPriceNumber(classicCumacAmount(dto) + precariousnessCumacAmount(dto) - totalSoldAmount(dto)),
    sortable: false,
    cellClass: 'text-right justify-end',
  },
  {
    title: 'Valorisation stock restant',
    value: 'cumacNotSoldAmount',
    formater: (dto: CeeStockOperationWithAccountingEntrySummary) =>
      formatPriceNumber(
        calculateAmount(notSoldClassicCumac(dto), dto.classicValuationValue) +
          calculateAmount(notSoldPrecariousnessCumac(dto), dto.precariousnessValuationValue)
      ),
    sortable: false,
    cellClass: 'text-right justify-end',
  },
  commonClassicColumns,
  {
    title: 'Nb.Clas.kWhc vendus/obligation',
    value: 'soldClassicCumac',
    formater: (_, value) => formatKwhcNumber(value),
    cellClass: 'text-right justify-end',
    sortable: false,
  },
  {
    title: 'Montant vendu Class. (€)',
    value: 'classicCumacSoldAmount',
    formater: (_, value) => formatPriceNumber(value),
    cellClass: 'text-right justify-end',
    sortable: false,
  },
  {
    title: 'Valorisation unitaire vendu Clas. (€)',
    value: 'classicSoldValuation',
    formater: (dto) => formatPriceNumber((dto.classicCumacSoldAmount * 1000) / dto.soldClassicCumac),
    cellClass: 'text-right justify-end',
    sortable: false,
  },
  {
    title: 'Solde total Nb. kWhc Clas.',
    value: 'diffClassicCumacSold',
    formater: (dto) => formatPriceNumber(notSoldClassicCumac(dto)),
    cellClass: 'text-right justify-end',
    sortable: false,
  },
  {
    title: 'Marge dossier vendu Clas. (€)',
    value: 'diffClassAndSoldAmount',
    formater: (dto) => formatPriceNumber(diffClassicAndSoldAmount(dto)),
    cellClass: 'text-right justify-end',
    sortable: false,
  },
  {
    title: 'Valorisation stock restant Clas. (€)',
    value: 'classicCumacSoldAmount',
    formater: (dto) => formatPriceNumber(calculateAmount(notSoldClassicCumac(dto), dto.classicValuationValue)),
    cellClass: 'text-right justify-end',
    sortable: false,
  },
  commonPrecariousnessColumns,
  {
    title: 'Nb.Pré.kWhc vendus/obligation',
    value: 'soldPrecariousnessCumac',
    formater: (_, value) => formatKwhcNumber(value),
    cellClass: 'text-right justify-end',
    sortable: false,
  },
  {
    title: 'Montant vendu Préc. (€)',
    value: 'precariousnessCumacSoldAmount',
    formater: (_, value) => formatPriceNumber(value),
    cellClass: 'text-right justify-end',
    sortable: false,
  },
  {
    title: 'Valorisation unitaire vendu Prec. (€)',
    value: 'precariousnessSoldValuation',
    formater: (dto) => formatPriceNumber((dto.precariousnessCumacSoldAmount * 1000) / dto.soldPrecariousnessCumac),
    cellClass: 'text-right justify-end',
    sortable: false,
  },
  {
    title: 'Solde total Nb. kWhc Prec.',
    value: 'diffPrecariousnessCumacSold',
    formater: (dto) => formatPriceNumber(notSoldPrecariousnessCumac(dto)),
    cellClass: 'text-right justify-end',
    sortable: false,
  },
  {
    title: 'Marge dossier vendu Prec. (€)',
    value: 'diffPrecariousnessAndSoldAmount',
    formater: (dto) => formatPriceNumber(diffPrecariousnessAndSoldAmount(dto)),
    cellClass: 'text-right justify-end',
    sortable: false,
  },
  {
    title: 'Valorisation stock restant Prec. (€)',
    value: 'precariousnessCumacSoldAmount',
    formater: (dto) =>
      formatPriceNumber(calculateAmount(notSoldPrecariousnessCumac(dto), dto.precariousnessValuationValue)),
    cellClass: 'text-right justify-end',
    sortable: false,
  },
  commonOthersColumns,

  accountingEntryColumns
)
