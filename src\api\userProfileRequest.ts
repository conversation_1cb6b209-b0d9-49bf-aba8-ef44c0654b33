import type { Page, Pageable } from '@/types/pagination'
import type { ProfileType } from '@/types/user'
import type { UserProfileRequest, UserProfileRequestRequest } from '@/types/userProfileRequest'
import type { AxiosInstance, AxiosPromise } from 'axios'
import axiosInstance from '.'
import type { Entity } from '@/types/entity'

export interface UserFilter {
  search?: string
  roles?: ProfileType[]
  excludedRoles?: ProfileType[]
  withoutSelf?: boolean
  operationId?: number
  active?: boolean
  emailSearch?: string
  entity: Entity
}

export type UpdateSelfUserProfileRequest = Pick<
  UserProfileRequestRequest,
  'userRole' | 'function' | 'requestComment' | 'entityId'
>

export interface UserPorfileRequestFilter {
  hasResponse: boolean
  territoryIds?: number[]
}

export interface ResponseUserProfileRequestRequest {
  response: boolean
  comment: string
}

class UserProfileRequestApi {
  public constructor(private axios: AxiosInstance) {}

  private baseUri = '/user_profile_requests'

  public respond(id: number, request: ResponseUserProfileRequestRequest): AxiosPromise<UserProfileRequest> {
    return this.axios.put(this.baseUri + '/' + id, request)
  }

  public getAll(pageable: Pageable, filter: UserPorfileRequestFilter): AxiosPromise<Page<UserProfileRequest>> {
    return this.axios.get(this.baseUri, {
      params: { ...pageable, ...filter },
    })
  }
}

export const userProfileRequestApi = new UserProfileRequestApi(axiosInstance)
