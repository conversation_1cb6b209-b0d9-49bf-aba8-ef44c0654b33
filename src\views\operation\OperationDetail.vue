<template>
  <VRow :class="expandedDetail ? 'flex-row' : 'flex-column'" no-gutters>
    <VCol>
      <VAlert v-if="!(validateStepDurationRule == true)" variant="outlined" rounded="0" type="error" closable
        >La validité de l'étape {{ operation.stepId }} a expiré</VAlert
      >
      <NjExpansionPanel v-if="operation.stepId === 0" title="Simulation" :model-value="expandedDetail">
        <SimulationDisplayValue :operation="operation" />
      </NjExpansionPanel>
      <NjDivider v-if="operation.stepId === 0" />
      <StandardizedOperationSheetDetail
        :expanded-detail="expandedDetail"
        :step-id="operation.stepId"
        :standardized-operation-sheet="operation.standardizedOperationSheet"
        :rge-granted-date="operation.rgeGrantedDate"
        :rge-end-of-validity-date="operation.rgeEndOfValidityDate"
        :validate-standardized-operation-sheet-rule="validateStandardizedOperationSheetRule"
        :validate-standardized-operation-sheet-and-commitment-validity-rule="
          validateStandardizedOperationSheetAndCommitmentValidityRule
        "
        :validate-rge-rule="validateRgeRule"
        :boost-bonus-sheets="boostBonusSheets.value"
        :epc-bonus-sheets="epcBonusSheets.value"
      />
      <NjDivider />
      <NjExpansionPanel title="Installation" :model-value="expandedDetail">
        <PropertyDisplayValue
          :model-value="operation?.property"
          :final-address="operation?.finalAddress"
          :final-property-name="operation?.finalPropertyName"
          :co-owner-ship-syndicate="operation?.coOwnerShipSyndicateName"
        />
        <VCard
          v-if="operation?.coOwnerShipSyndicateName || operation?.coOwnerShipSyndicateImmatriculationNumber"
          class="mt-4"
        >
          <VCardText class="pa-2">
            <template v-if="operation?.coOwnerShipSyndicateName">
              <NjDisplayValue
                label="Syndicat de copropriété"
                :value="operation?.coOwnerShipSyndicateName"
              ></NjDisplayValue>
            </template>
            <template v-if="operation?.coOwnerShipSyndicateImmatriculationNumber">
              <NjDisplayValue
                label="Numéro d'immatriculation du syndicat de copropriété"
                :value="operation?.coOwnerShipSyndicateImmatriculationNumber"
              ></NjDisplayValue>
            </template>
          </VCardText>
        </VCard>
      </NjExpansionPanel>
      <NjDivider />
      <OperationField
        :expanded-detail="expandedDetail"
        :operation="operation"
        :instructor="operation.instructor"
        :validate-estimated-commitment-date-rule="validateEstimatedCommitmentDateRule"
        :validate-estimated-end-work-rule="validateEstimatedEndWorkRule"
        :validate-operation="validateOperation"
        :validate-step-duration-rule="validateStepDurationRule"
        :validate-subcontractor-rule="validateSubcontractorRule"
        :validate-rge-rule="validateRgeRule"
      />
      <NjDivider v-if="!expandedDetail" />
    </VCol>
    <VCol>
      <NjExpansionPanel v-if="operation" title="Volume et valorisation" :model-value="expandedDetail">
        <VRow class="flex-column" dense>
          <template v-if="operation.headOperation">
            <VCol>
              <NjDisplayValue label="Opération chapeau" value="OUI" />
            </VCol>
            <VCol>
              <NjDisplayValue label="Nombre estimé d'opération" :value="operation.estimatedNumberOfOperationInHead" />
            </VCol>
          </template>
          <VCol v-if="operation.atypicalClassicValuationValue || operation.atypicalPrecariousnessValuationValue">
            <VAlert type="info" variant="outlined"> Cette opération est soumise à une valorisation atypique </VAlert>
          </VCol>
          <VCol>
            <NjDisplayValue label="Valorisation" :value="operation.valuationType.name" />
          </VCol>
          <VCol v-if="operation.atypicalClassicValuationValue || operation.atypicalPrecariousnessValuationValue">
            <NjDisplayValue label="Valorisations atypiques">
              <template #value>
                <div class="d-flex text-no-wrap justify-end" style="gap: 4px">
                  <span class="nj-display-value__label"> Classique : </span>
                  <span class="font-weight-bold">
                    {{ operation.atypicalClassicValuationValue + ' €/MWhc' }}
                  </span>
                </div>
                <div class="d-flex text-no-wrap justify-end" style="gap: 4px">
                  <span class="nj-display-value__label"> Précarité : </span>
                  <span class="font-weight-bold">
                    {{ operation.atypicalPrecariousnessValuationValue + ' €/MWhc' }}
                  </span>
                </div>
              </template>
            </NjDisplayValue>
          </VCol>
          <VCol v-if="warningMessages.length > 0">
            <VAlert type="warning">
              <template #text>
                <div v-for="(message, index) in warningMessages" :key="index">
                  <VIcon>mdi-circle-small</VIcon>
                  {{ message }}
                </div>
              </template>
            </VAlert>
          </VCol>
          <VCol>
            <DetailValuationDisplayValue
              ref="detailValuationRef"
              :operation="operation"
              :classic-c-e-e="totalCEEClassique"
              :precariousness-c-e-e="totalCEEPrecariousness"
              :volume-c-e-e="sum(volumeCEE)"
              @update:operation="$emit('update:operation', $event)"
            />
          </VCol>
          <template v-if="!(operation.parameterValues.length === 0)">
            <VCol>
              <StandardizedOperationSheetCalculator
                :model-value="operation!.parameterValues"
                :predefined-values="predefinedValues"
                :standardized-operation-sheet="operation.standardizedOperationSheet"
                mode="display"
                @update:cumac="volumeCEE = $event"
              />
            </VCol>
            <VCol>
              <NjDisplayValue label="Coup de Pouce" :value="operation.boostBonusSimulation != null ? 'OUI' : 'NON'" />
            </VCol>
            <VCol v-if="operation.boostBonusSimulation != null && boostBonusSheet.value">
              <BoostBonusSheetCalculator
                v-model="operation!.boostBonusSimulation!.parameterValues"
                result-title="Total CEE avec Coup de Pouce "
                :calcul-formula="boostBonusSheet.value.calculFormula"
                :field-required="false"
                :predefined-value="predefinedValuesForBoostBonusSheet"
                mode="display"
                @update:cumac="volumeCEEAfterBoostBonusSheet = $event"
              />
            </VCol>
            <VCol>
              <!--  showEpcBonus nécessaire car pour des besoins, on initialise toujours epcBonusParameterValues à non null -->
              <NjDisplayValue
                label="CPE"
                :value="operation.epcBonusParameterValues != null && showEpcBonus ? 'OUI' : 'NON'"
              />
            </VCol>
            <VCol v-if="operation.epcBonusParameterValues != null && epcBonusSheet.value">
              <EpcBonusCalculator
                :model-value="operation.epcBonusParameterValues"
                :epc-bonus-sheet="epcBonusSheet.value"
                :volume-cee="volumeCEE"
                @update:b-cpe="epcBonus = $event"
              />
            </VCol>
            <VCol>
              <NjDisplayValue
                label="Précarité"
                :value="operation.precariousnessBonusParameterValues != null ? 'OUI' : 'NON'"
              />
            </VCol>
            <VCol v-if="operation.precariousnessBonusParameterValues != null && precariousnessBonusSheet.value">
              <PrecariousnessBonusSheetCalculator
                v-model:cee-classic="calculatedClassicCumac"
                v-model:cee-precariousness="calculatedPrecariousnessCumac"
                :predefined-values="predefinedValuesForPrecariousnessBonus"
                :values="operation.precariousnessBonusParameterValues"
                :precariousness-bonus-sheet="precariousnessBonusSheet.value!"
                :cumacs="volumeCEEForPrecariousness"
                mode="display"
              />
            </VCol>
          </template>
          <VCol v-if="operation.legacyBoostBonusSimulation">
            <LegacyBoostBonusSimulationDisplayValue
              :legacy-boost-bonus-simulation="operation.legacyBoostBonusSimulation"
            />
          </VCol>

          <span
            v-show="false"
            id="calculatedLine"
            :operationLineClassicCumac="operationLineClassicCumac"
            :operationLinePrecariousnessCumac="operationLinePrecariousnessCumac"
          />
        </VRow>
      </NjExpansionPanel>
      <NjDivider />
      <NjExpansionPanel title="Incitation financière" :model-value="expandedDetail">
        <VRow class="flex-column" dense>
          <VCol v-if="!['DOING', 'DONE'].includes(operation.status)">
            <VAlert
              type="warning"
              :text="`L'opération est ${mapToReadableStatus(operation.status)}, les montants sont donnés à titre indicatif`"
            />
          </VCol>
          <VCol>
            <NjDisplayValue
              label="Offre commerciale avant incitation financière CEE/€ TTC"
              :value="formatPriceNumber(operation.commercialOfferWithoutFinancialIncentive)"
            />
          </VCol>
          <VCol>
            <NjDisplayValue
              label="Incitation financière CEE client/€ TTC"
              :value="formatPriceNumber(operation.customerFinancialIncentive)"
            />
          </VCol>

          <VCol>
            <NjDisplayValue
              label="Montant de l'offre commerciale après déduction de l'incitation financière CEE/€ TTC"
              :value="formatPriceNumber(commercialOfferWithFinancialIncentive)"
            />
          </VCol>
          <VCol>
            <NjDisplayValue
              label="Charge Pôle CEE"
              :value="
                formatPriceNumber(
                  ((operation.classicCumac + operation.precariousnessCumac) / 1000) * getPoleChargeCEE(operation)
                )
              "
            />
          </VCol>
          <VCol>
            <NjDisplayValue label="Marge CEE Nette TTC" :value="formatPriceNumber(netMargin)" />
          </VCol>
        </VRow>
      </NjExpansionPanel>
      <NjDivider v-if="!expandedDetail" />
    </VCol>
    <VCol>
      <NjExpansionPanel :model-value="expandedDetail">
        <template #title>
          <div class="d-flex">
            Bénéficiaire
            <VSpacer />
            <AlertIcon :rules="[validateBeneficiaryRule]" />
          </div>
        </template>
        <BeneficiaryDisplayValue
          ref="beneficiaryDisplayValueRef"
          :with-color="operation.stepId >= 30"
          :model-value="operation.beneficiary"
          :legacy="operation.legacyBeneficiary"
        />
      </NjExpansionPanel>
      <NjDivider />
      <WorksField :operation="operation" :model-value="expandedDetail" />
      <NjDivider />
      <NjExpansionPanel :model-value="expandedDetail">
        <template #title>
          <div class="d-flex">
            Sous traitant
            <VSpacer />
            <AlertIcon :rules="[validateSubcontractorRule]" />
          </div>
        </template>
        <SubcontractorDisplayValue
          :model-value="operation?.subcontractor"
          :subcontractor-model-value="operation?.operationsGroup?.subcontractor"
        />
      </NjExpansionPanel>
      <NjDivider />
      <NjExpansionPanel title="Professionnel" :model-value="expandedDetail">
        <VRow class="flex-column" dense>
          <VCol>
            <NjDisplayValue label="Raison social" :value="operation.entity.name" />
          </VCol>
          <VCol>
            <NjDisplayValue label="SIRET" :value="operation.entity.siret" />
          </VCol>
        </VRow>
      </NjExpansionPanel>
      <NjDivider />
      <NjExpansionPanel title="Organisme de contrôle" :model-value="expandedDetail">
        <!-- <NjDisplayValue label="Numéro de commande" :value="operation.orderNumber" /> -->
        <ControlOrganismDisplayValue :model-value="operation?.controlOrganism" />
      </NjExpansionPanel>
      <NjDivider v-if="operation.stepId > 0" />
      <NjExpansionPanel v-if="operation.stepId > 0" title="Doublon/cumul" :model-value="expandedDetail">
        <VRow class="flex-column" dense>
          <VCol>
            <VRow align="center">
              <VCol class="nj-display-value__label"> Base EES </VCol>
              <VCol class="flex-grow-0" style="flex-basis: content">
                <VCheckbox
                  hide-details="auto"
                  density="compact"
                  disabled
                  :model-value="operation?.eesDuplicate"
                ></VCheckbox>
              </VCol>
            </VRow>
          </VCol>
          <VCol>
            <VRow align="center">
              <VCol class="nj-display-value__label"> Client </VCol>
              <VCol class="flex-grow-0" style="flex-basis: content">
                <VCheckbox
                  hide-details="auto"
                  density="compact"
                  disabled
                  :model-value="operation?.customerDuplicate"
                ></VCheckbox>
              </VCol>
            </VRow>
          </VCol>
          <VCol>
            <VRow align="center">
              <VCol class="nj-display-value__label"> Cumul </VCol>
              <VCol class="flex-grow-0" style="flex-basis: content">
                <VCheckbox hide-details="auto" density="compact" disabled :model-value="operation?.cumul"></VCheckbox>
              </VCol>
            </VRow>
          </VCol>
        </VRow>
      </NjExpansionPanel>
      <NjDivider v-if="operation.stepId > 60 && operation.controlOrderBatch" />
      <ControlOrderField
        v-if="operation.stepId > 60 && operation.controlOrderBatch"
        :operation="operation"
        :model-value="expandedDetail"
      />
      <NjDivider v-if="operation.emmyFolder !== null" />
      <NjExpansionPanel v-if="operation.emmyFolder !== null" title="Dossier EMMY" :model-value="expandedDetail">
        <EmmyFolderDisplayValue :operation="operation" />
      </NjExpansionPanel>
      <NjDivider />
      <NjExpansionPanel title="Commentaire" :model-value="expandedDetail">
        <VRow class="flex-column" dense>
          <NjDisplayValue label="Nombre de messages comptabilisées:" :value="accountedMessages.value?.totalElements" />
          <template v-if="(accountedMessages.value?.totalElements ?? 0) > 0">
            <NjDisplayValue label="Pour les raisons suivantes:">
              <template #value>
                <VRow>
                  <VCol>
                    <div v-for="message in accountedMessages.value?.content" :key="message.id">
                      <div v-for="reason in message.reasons" :key="reason.id" class="text-end">
                        {{ reason.reason }}
                      </div>
                    </div>
                  </VCol>
                </VRow>
              </template>
            </NjDisplayValue>
          </template>
        </VRow>
      </NjExpansionPanel>
      <NjDivider v-if="operation.stepId > 0" />
      <NjExpansionPanel v-if="operation.stepId > 0" title="Simulation" :model-value="expandedDetail">
        <SimulationDisplayValue :operation="operation" />
      </NjExpansionPanel>
      <NjDivider />
      <NjExpansionPanel v-if="operation.stepId > 10" title="Administration" :model-value="expandedDetail">
        <VRow dense class="flex-column">
          <VCol v-if="operation.atypicalClassicValuationValue || operation.atypicalPrecariousnessValuationValue">
            <NjDisplayValue label="Valorisations atypiques">
              <template #value>
                <div class="w-50">
                  <div class="d-flex text-no-wrap justify-end" style="gap: 4px">
                    <span class="nj-display-value__label"> Classique : </span>
                    <span class="font-weight-bold">
                      {{ operation.atypicalClassicValuationValue + ' €/MWhc' }}
                    </span>
                  </div>
                  <div class="d-flex text-no-wrap justify-end" style="gap: 4px">
                    <span class="nj-display-value__label"> Précarité : </span>
                    <span class="font-weight-bold">
                      {{ operation.atypicalPrecariousnessValuationValue + ' €/MWhc' }}
                    </span>
                  </div>
                </div>
              </template>
            </NjDisplayValue>
          </VCol>
          <VCol v-if="operation.specialFee">
            <NjDisplayValue label="Charge pôle CEE spécifique" :value="operation.specialFee">
              <template #value>
                <div class="d-flex justify-end" style="font-size: 1rem">
                  <div class="text-primary font-weight-bold ms-2">
                    {{ operation.specialFee ? formatPriceNumber(operation.specialFee) + ' €/MWhC' : 'Aucune' }}
                  </div>
                </div>
              </template>
            </NjDisplayValue>
          </VCol>
          <VCol v-else>
            <NjDisplayValue
              :label="
                'Charge pôle CEE unitaire de l\'organisation (' +
                (operation.feeIssuedEntity ? operation.feeIssuedEntity.name : resolvedChargePoleCEEEntity.value!.name) +
                ')'
              "
              :value="(operation.finalFee ?? operation.entity.entityDetails.effectiveFee) + ' €/MWhc'"
            />
          </VCol>
          <VCol v-if="operation.stepId > 0 && userIsAdmin(userStore.currentUser)">
            <NjDisplayValue
              label="Date de réservation"
              :value="formatHumanReadableLocalDate(operation.reservedDate)"
              :color-title="operation.reservedDate && !(validateOperationDurationRule == true) ? 'warning' : ''"
            />
          </VCol>

          <VCol v-if="operation.stepId > 40 && userIsAdmin(userStore.currentUser)">
            <NjDisplayValue
              label="Date de l'arrivé au pôle CAP"
              :value="formatHumanReadableLocalDate(operation.caseDate)"
            />
          </VCol>
          <VCol>
            <NjDisplayValue label="Organisation actuelle" :value="displayEntity(operation.entity)" />
          </VCol>
          <VCol>
            <NjDisplayValue label="Organisation avant réorganisation" :value="displayEntity(operation.leadingEntity)" />
          </VCol>
        </VRow>
      </NjExpansionPanel>
      <template v-if="displayDocument">
        <NjDivider />
        <NjExpansionPanel title="Documents" :model-value="expandedDetail">
          <DocumentListView :operation-id="operation.id" :operations-group-id="operation.operationsGroup?.id" />
        </NjExpansionPanel>
      </template>
    </VCol>

    <AlertDialog
      title="Total Cumac modifié"
      max-width="640px"
      v-bind="alertModifyCumacDialog.props"
      :negative-button="false"
    >
      Une donnée saisie a modifié le total des kWh cumacs obtenus pour cette opération.<br />
      Veuillez vérifier les changements, et les valider.
    </AlertDialog>
  </VRow>
  <slot name="actions" />
</template>
<script lang="ts" setup>
import BeneficiaryDisplayValue from '@/components/BeneficiaryDisplayValue.vue'
import ControlOrganismDisplayValue from '@/components/ControlOrganismDisplayValue.vue'
import NjDisplayValue from '@/components/NjDisplayValue.vue'
import NjExpansionPanel from '@/components/NjExpansionPanel.vue'
import StandardizedOperationSheetCalculator from '@/components/StandardizedOperationSheetCalculator.vue'
import SubcontractorDisplayValue from '@/components/SubcontractorDisplayValue.vue'
import { useAdminConfigurationStore } from '@/stores/adminConfiguration'
import { useUserStore } from '@/stores/user'
import { formatHumanReadableLocalDate } from '@/types/date'
import { resolveEntityChargePoleCEE } from '@/types/entity'
import { formatPriceNumber } from '@/types/format'
import type { Message } from '@/types/message'
import type { Page } from '@/types/pagination'
import { userIsAdmin } from '@/types/user'
import type { Valuation } from '@/types/valuation'
import DocumentListView from '@/views/operation/documentList/DocumentListView.vue'
import { debounce, sum } from 'lodash'
import type { PropType } from 'vue'
import { VCol } from 'vuetify/components'
import EpcBonusCalculator from '../admin/epcBonus/EpcBonusCalculator.vue'
import PrecariousnessBonusSheetCalculator from '../admin/precariousnessBonus/PrecariousnessBonusSheetCalculator.vue'
import { useOperation } from '../operationComposition'
import ControlOrderField from './ControlOrderField.vue'
import DetailValuationDisplayValue from './DetailValuationDisplayValue.vue'
import PropertyDisplayValue from './PropertyDisplayValue.vue'
import SimulationDisplayValue from './SimulationDisplayValue.vue'
import EmmyFolderDisplayValue from './EmmyFolderDisplayValue.vue'
import OperationField from './OperationField.vue'
import type { Operation } from '@/types/operation'
import { getPoleChargeCEE } from '@/types/operation'
import StandardizedOperationSheetDetail from './StandardizedOperationSheetDetail.vue'
import WorksField from './WorksField.vue'
import { useValidateOperation } from '../validateOperationComposition'
import AlertIcon from '../AlertIcon.vue'
import LegacyBoostBonusSimulationDisplayValue from './LegacyBoostBonusSimulationDisplayValue.vue'
import { displayEntity } from '@/types/entity'
import NjDivider from '@/components/NjDivider.vue'
import { mapToReadableStatus } from '@/types/operation'

const props = defineProps({
  operation: {
    type: Object as PropType<Operation>,
    default: makeEmptyOperation,
  },
  expandedDetail: Boolean,
})

// eslint-disable-next-line @typescript-eslint/no-unused-vars
const emit = defineEmits<{
  'update:operation': [value: Operation]
}>()
const userStore = useUserStore()
const adminConfigurationStore = useAdminConfigurationStore()

const accountedMessages = ref(emptyValue<Page<Message>>())
const detailValuationRef = ref<typeof DetailValuationDisplayValue | null>(null)

const valuations = computed(() => detailValuationRef.value?.valuations)

const {
  cumacHasChanged,
  predefinedValues,
  volumeCEE,
  epcBonus,
  volumeCEEAfterBoostBonusSheet: volumeCEEAfterBoostBonusSheet,
  boostBonusSheet: boostBonusSheet,
  epcBonusSheet: epcBonusSheet,
  predefinedValuesForBoostBonusSheet: predefinedValuesForBoostBonusSheet,
  predefinedValuesForPrecariousnessBonus: predefinedValuesForPrecariousnessBonus,
  precariousnessBonusSheet: precariousnessBonusSheet,
  showEpcBonus: showEpcBonus,
  boostBonusSheets: boostBonusSheets,
  epcBonusSheets: epcBonusSheets,
  commercialOfferWithFinancialIncentive: commercialOfferWithFinancialIncentive,
  calculatedClassicCumac: calculatedClassicCumac,
  calculatedPrecariousnessCumac: calculatedPrecariousnessCumac,
  totalCEEClassique,
  totalCEEPrecariousness,
  volumeCEEForPrecariousness,
  operationLineClassicCumac,
  operationLinePrecariousnessCumac,
} = useOperation(
  computed<Operation | undefined>(() => {
    return {
      ...props.operation,
      epcBonusParameterValues:
        Object.keys(props.operation.epcBonusParameterValues ?? {}).length === 0
          ? null
          : props.operation.epcBonusParameterValues,
    }
  })
)

const {
  validateStandardizedOperationSheetRule,
  validateStandardizedOperationSheetAndCommitmentValidityRule,
  validateEstimatedCommitmentDateRule,
  validateEstimatedEndWorkRule,
  validateOperationDurationRule,
  validateOperation,
  validateStepDurationRule,
  validateSubcontractorRule,
  validateBeneficiaryRule,
  validateRgeRule,
} = useValidateOperation(
  computed<Operation>(() => props.operation),
  computed<Valuation[] | undefined>(() => valuations.value)
)

watch(
  () => props.operation.id,
  (v) => {
    if (v) {
      handleAxiosPromise(accountedMessages, messageApi.getAll({}, { operationId: props.operation.id, accounted: true }))
    }
  },
  {
    immediate: true,
  }
)

const beneficiaryDisplayValueRef = ref<typeof BeneficiaryDisplayValue | null>(null)

const warningMessages = computed(() =>
  [
    !['DOING', 'DONE'].includes(props.operation.status)
      ? `L'opération est ${mapToReadableStatus(props.operation.status)}, les montants sont donnés à titre indicatif`
      : undefined,
    displayAlert.value ? 'Le coût du traitement est supérieur au montant en € de la réservation' : undefined,
    netMargin.value < 0 ? 'La marge nette de cette opération est négative' : undefined,
    props.operation.parameterValues.length === 0
      ? props.operation.headOperation
        ? 'Cette opération est une opération chapeau'
        : "Les kWhc ont été saisi manuellement, l'export de cette opération doit donc être complété manuellement dans EMMY"
      : undefined,
  ].filter((message) => !!message)
)

const displayAlert = computed(
  () => detailValuationRef.value?.totalValuation < parseInt(adminConfigurationStore.profitabilityThreshold?.data ?? '')
)

const netMargin = computed(() => getNetMargin(props.operation, undefined, detailValuationRef.value?.totalValuation))

const router = useRouter()
const displayDocument = computed(
  () => !(router.currentRoute.value.name == 'OperationOneView' || router.currentRoute.value.name == 'SimulationOneView')
)

const alertModifyCumacDialog = useConfirmAlertDialog()
const updateAlertModifyDialog = debounce((v: boolean) => {
  alertModifyCumacDialog.props.modelValue = v
}, 300)
watch(cumacHasChanged, (v) => {
  if (v) {
    updateAlertModifyDialog(true)
  } else {
    updateAlertModifyDialog(false)
  }
})

const resolvedChargePoleCEEEntity = ref(succeedValue(makeEmptyEntity()))
watchEffect(async () => {
  resolvedChargePoleCEEEntity.value = await resolveEntityChargePoleCEE(
    props.operation.entity,
    props.operation.entity.entityDetails.effectiveFee!
  )
})

defineExpose({
  valuations: valuations,
})
</script>
