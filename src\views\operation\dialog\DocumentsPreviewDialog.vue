<template>
  <AlertDialog
    v-if="data.value?.content"
    :model-value="modelValue"
    width="40%"
    :title="`Documents requis à l'étape  ${stepId}`"
  >
    <VRow class="flex-column my-n2">
      <VCol>
        Vous pouvez télécharger {{ (data.value?.totalElements ?? 0) > 1 ? ' les documents' : ' le document' }}
        requis à cette étape en cliquant sur
        {{ (data.value?.totalElements ?? 0) > 1 ? ' les liens' : ' le lien' }}
        de téléchargement disponible
      </VCol>
      <!-- eslint-disable-next-line vue/valid-v-for -->
      <VCol v-for="doc in data.value?.content">
        {{ doc.name }}
        <VLink v-if="doc.template" @click="downloadTemplate(doc)">(Télécharger) </VLink>
      </VCol>
    </VRow>
    <template #actions>
      <NjBtn @click="resetDialog()"> Ok </NjBtn>
    </template>
  </AlertDialog>
</template>
<script lang="ts" setup>
import NjBtn from '@/components/NjBtn.vue'
import type { Page } from '@/types/pagination'
import type { DocumentType } from '@/types/documentType'
import { useSnackbarStore } from '@/stores/snackbar'
import VLink from '@/components/VLink.vue'

const props = defineProps({
  modelValue: Boolean,
  stepId: {
    type: Number,
    required: true,
  },
  operationId: Number,
})

const emit = defineEmits(['update:model-value', 'update:operation', 'click:positive'])

const snackbarStore = useSnackbarStore()

const data = ref(emptyValue<Page<DocumentType>>())

watch(
  () => props.stepId,
  async (v) => {
    if (v) {
      await handleAxiosPromise(
        data,
        documentTypeApi.getAll(
          {},
          { stepId: props.stepId, missingToValidateOperation: props.operationId, withTemplate: true }
        )
      )
      if (!data.value.value?.totalElements) {
        resetDialog()
      }
    }
  }
)

const downloadTemplate = (document: DocumentType) => {
  documentTypeApi
    .downloadTemplate(document.id)
    .then((response) => {
      downloadFile(document.template!.originalFilename, response.data)
      snackbarStore.setSuccess('Le téléchargement a réussi')
    })
    .catch(() =>
      snackbarStore.setError(
        'Le téléchargement du fichier a échoué. Aviez-vous envoyé ce fichier sur boCEE ? Si oui, veuillez nous contacter.'
      )
    )
}

const resetDialog = () => {
  emit('click:positive')
  data.value = emptyValue<Page<DocumentType>>()
}
</script>
