<template>
  <VCol
    v-if="
      displayValidationOfStep70InControlOrderBatchAlert ||
      displayValidationOfStep75AAlert ||
      displayValidationOfStep80InSatisfyingControlOrderBatchAlert ||
      displayValidationOfStep75To75CAlert ||
      displayValidationOfStep75CTo75DAlert ||
      displayValidationOfStep75DTo80Alert
    "
  >
    <div>
      <ErrorAlert
        v-if="displayValidationOfStep70InControlOrderBatchAlert"
        message="Délai de validation de l'étape 70 à l'étape 70A dépassé"
      />
      <ErrorAlert
        v-else-if="displayValidationOfStep75AAlert"
        message="Délai de validation de l'étape 70A à l'étape 70B dépassé"
      />
      <ErrorAlert
        v-else-if="displayValidationOfStep80InSatisfyingControlOrderBatchAlert"
        :message="`Délai de validation de l'étape 75 à l'étape 80 dépassé`"
      />
      <ErrorAlert
        v-else-if="displayValidationOfStep75To75CAlert"
        message="Délai de validation de l'étape 75 à l'étape 75C dépassé"
      />
      <ErrorAlert
        v-else-if="displayValidationOfStep75CTo75DAlert"
        message="Délai de validation de l'étape 75C à l'étape 75D dépassé"
      />
      <ErrorAlert
        v-else-if="displayValidationOfStep75DTo80Alert"
        message="Délai de validation de l'étape 75D à l'étape 80 dépassé"
      />
    </div>
  </VCol>
</template>
<script lang="ts" setup>
import { useAdminConfigurationStore } from '@/stores/adminConfiguration'
import { parseLocalDateTime } from '@/types/date'
import { add } from 'date-fns'
import { type ControlOrderBatchStepHistory } from '@/types/controlOrderBatchStepHistory'
import { type ControlOrderAfterSaleServiceStepHistory } from '@/types/controlorderAfterSaleServiceStepHistory'
import { type PropType, computed, ref, watch } from 'vue'
import { type Operation, type OperationStepHistory } from '@/types/operation'
import { addBusinessDay } from '@/types/bankHoliday'

const props = defineProps({
  operation: Object as PropType<Operation>,
  operationStepHistories: Array as PropType<OperationStepHistory[]>,
  canValidateStep70: Boolean,
})

const adminConfigurationStore = useAdminConfigurationStore()
const validationStep60Date = computed(() => {
  const dateTime = props.operationStepHistories?.filter((h: OperationStepHistory) => h.step.id == 60)[0]
    ?.creationDateTime

  return dateTime ? parseLocalDateTime(dateTime) : new Date()
})

const displayValidationOfStep70InControlOrderBatchAlert = computed(() => {
  const maxValidationStep70Date =
    add(validationStep60Date.value, {
      days: parseInt(adminConfigurationStore.validationOfStep70InControlOrderBatchAlert?.data!),
    })?.getTime() ?? 0

  return (
    props.operation?.stepId == 70 &&
    (!props.operation.controlOrderBatch || props.operation.controlOrderBatch.step == null) &&
    new Date().getTime() > maxValidationStep70Date
  )
})

const controlOrderBatchStepHistories = ref(emptyValue<ControlOrderBatchStepHistory[]>())
watch(
  () => props.operation,
  (v) => {
    const controlOrderBatch = v?.controlOrderBatch
    if (
      controlOrderBatch &&
      ['TO_BE_CONTROLLED', 'CONTROLLED_BY_CONTROL_OFFICE'].includes(controlOrderBatch.step ?? '')
    ) {
      handleAxiosPromise(
        controlOrderBatchStepHistories,
        controlOrderBatchStepHistoryApi.findAllByControlOrderBatchId(controlOrderBatch.id)
      )
    }
  },
  {
    immediate: true,
    deep: true,
  }
)

const displayValidationOfStep80InSatisfyingControlOrderBatchAlert = computed(() => {
  const toBeControlledStepDateTime = controlOrderBatchStepHistories.value.value?.find(
    (i) => i.validatedStep == 'SENT_BY_CONTROL_OFFICE'
  )?.validatedDateTime

  const numberOfDays = parseInt(adminConfigurationStore.validateStep75To80Alert?.data ?? '0')

  return (
    props.canValidateStep70 &&
    props.operation?.controlOrderDetails?.controlOrderStatus != 'NOT_SATISFYING' &&
    toBeControlledStepDateTime &&
    new Date().getTime() > add(parseLocalDateTime(toBeControlledStepDateTime), { days: numberOfDays }).getTime()
  )
})

const controlOrderAfterSaleServiceStepHistories = ref(emptyValue<ControlOrderAfterSaleServiceStepHistory[]>())
watch(
  () => props.operation,
  (v) => {
    const controlOrderBatch = v?.controlOrderBatch
    if (controlOrderBatch && controlOrderBatch.step == 'TO_BE_CONTROLLED') {
      handleAxiosPromise(
        controlOrderAfterSaleServiceStepHistories,
        controlOrderAfterSaleServiceStepHistoryApi.findAll(props.operation?.id ?? 0)
      )
    }
  },
  {
    immediate: true,
    deep: true,
  }
)

const displayValidationOfStep75To75CAlert = computed(() => {
  const controlOrderDetails = props.operation?.controlOrderDetails
  const toBeControlledStepDateTime = controlOrderBatchStepHistories.value.value?.find(
    (i) => i.validatedStep == 'SENT_BY_CONTROL_OFFICE'
  )?.validatedDateTime

  const numberOfDays = parseInt(adminConfigurationStore.validateStep75To75CAlert?.data ?? '0')

  return (
    controlOrderDetails?.controlOrderStatus == 'NOT_SATISFYING' &&
    [null, 'TO_BE_SENT_BY_CONTROL_OFFICE', 'SENT_BY_CONTROL_OFFICE'].includes(
      props.operation?.controlOrderDetails?.afterSalesServiceStatus ?? null
    ) &&
    toBeControlledStepDateTime &&
    add(parseLocalDateTime(toBeControlledStepDateTime), { days: numberOfDays }) < new Date()
  )
})

const displayValidationOfStep75AAlert = computed(() => {
  const controlOrderBatch = props.operation?.controlOrderBatch
  const toBeControlledStepDateTime = controlOrderBatchStepHistories.value.value?.find(
    (i) => i.validatedStep == null
  )?.validatedDateTime

  const numberOfDays = parseInt(adminConfigurationStore.validateStep75AAlert?.data ?? '0')

  return (
    controlOrderBatch?.step == 'CONTROLLED_BY_CONTROL_OFFICE' &&
    toBeControlledStepDateTime &&
    addBusinessDay(parseLocalDateTime(toBeControlledStepDateTime), numberOfDays) < new Date()
  )
})

const displayValidationOfStep75CTo75DAlert = computed(() => {
  const controlOrderDetails = props.operation?.controlOrderDetails
  const toBeControlledStepDateTime = controlOrderAfterSaleServiceStepHistories.value.value?.find(
    (i) => i.validatedStep == 'SENT_BY_CONTROL_OFFICE'
  )?.validatedDateTime

  const numberOfDays = parseInt(adminConfigurationStore.validateStep75CAlert?.data ?? '0')

  return (
    controlOrderDetails?.controlOrderStatus == 'NOT_SATISFYING' &&
    props.operation?.controlOrderDetails?.afterSalesServiceStatus == 'SENT_AFTER_SALES_SERVICE_SHEETS' &&
    toBeControlledStepDateTime &&
    add(parseLocalDateTime(toBeControlledStepDateTime), { days: numberOfDays }) < new Date()
  )
})

const displayValidationOfStep75DTo80Alert = computed(() => {
  const controlOrderDetails = props.operation?.controlOrderDetails
  const toBeControlledStepDateTime = controlOrderAfterSaleServiceStepHistories.value.value?.find(
    (i) => i.validatedStep == 'SENT_AFTER_SALES_SERVICE_SHEETS'
  )?.validatedDateTime

  const numberOfDays = parseInt(adminConfigurationStore.validateStep75DAlert?.data ?? '0')

  return (
    controlOrderDetails?.controlOrderStatus == 'NOT_SATISFYING' &&
    props.operation?.stepId == 70 &&
    props.operation?.controlOrderDetails?.afterSalesServiceStatus ==
      'ADMINISTRATIVE_CONTROL_OF_AFTER_SALES_SERVICE_SHEETS_AND_SUPPORT_DOCUMENTS' &&
    toBeControlledStepDateTime &&
    add(parseLocalDateTime(toBeControlledStepDateTime), { days: numberOfDays }) < new Date()
  )
})
</script>
