import { useStepsStore } from '@/stores/steps'
import { add, isBefore, parseISO } from 'date-fns'
import { isEmpty, isEqual } from 'lodash'
import type { Beneficiary } from './beneficiary'
import {
  makeEmptyStandardizedOperationSheet,
  type ControlOrderType,
  type StandardizedOperationSheet,
} from './calcul/standardizedOperationSheet'
import type { ControlOrganism } from './controlOrganism'
import type { BoostBonusSimulation, BoostBonusSimulationRequest } from './boostBonus'
import type { LocalDate, LocalDateTime } from './date'
import type { EmmyFolder } from './emmyFolder'
import type { CommercialStatus, Step as Step } from './steps'
import type { Property } from './property'
import type { CoOwnerShipSyndicate } from './coOwnerShipSyndicate'
import type { OperationsGroup } from './operationsGroup'
import type { Entity } from './entity'
import type { Period } from './period'
import type { Subcontractor } from './subcontractor'
import { makeEmptyUser, type User } from './user'
import type { Valuation, ValuationType } from './valuation'
import type { Works, WorksType } from './works'
import type { ControlOrderAfterSalesServiceStatus, ControlOrderBatch, ControlOrderStatus } from './controlOrder'
import type { Address } from './address'
import { useSnackbarStore } from '@/stores/snackbar'
import type { BusinessPlan } from './businessPlan'
import { useAdminConfigurationStore } from '@/stores/adminConfiguration'

export type ParameterValues = Record<string, string | number>

export interface Operation {
  id: number
  simulationName: string
  operationName: string
  entity: Entity
  leadingEntity: Entity
  stepId: number
  standardizedOperationSheet: StandardizedOperationSheet
  issuedFromStandardizedOperationSheetCumac: number | null
  operationLineCumacWithoutBonus: number[]
  operationLineClassicCumac: number[]
  operationLinePrecariousnessCumac: number[]
  classicCumac: number
  precariousnessCumac: number
  reservedClassicCumac: number
  reservedPrecariousnessCumac: number
  classicValuationValue: number
  precariousnessValuationValue: number
  reservedClassicValuationValue?: number
  reservedPrecariousnessValuationValue?: number
  soldClassicCumac: number
  soldPrecariousnessCumac: number
  availableClassicCumac: number
  availablePrecariousnessCumac: number
  specialFee: number | null
  finalFee: number | null
  feeIssuedEntity: Entity | null
  atypicalClassicValuationValue?: number
  atypicalPrecariousnessValuationValue?: number
  property: Property | null
  finalAddress: Address
  coOwnerShipSyndicate: CoOwnerShipSyndicate | null
  coOwnerShipSyndicateName: string | null
  coOwnerShipSyndicateImmatriculationNumber: string | null
  finalPropertyName: string
  toProcess: boolean
  eesDuplicate: boolean
  customerDuplicate: boolean
  cumul: boolean
  period: Period | null
  signedDate: LocalDate | null
  status: OperationStatus
  reservedDate: LocalDate
  caseDate: LocalDate
  lostDate: LocalDate
  lostReasons: Record<string, string> | null
  beneficiary: Beneficiary | null
  legacyBeneficiary: string | null
  controlOrganism: ControlOrganism | null
  estimatedCommitmentDate: LocalDate
  estimatedEndOperationDate: LocalDate
  actualEndWorksDate: LocalDate | null
  endContractDate: LocalDate | null
  precariousnessBonusParameterValues?: Record<string, string | number>
  parameterValues: ParameterValues[]
  lastValidatedStepDateTime: LocalDateTime
  creationUser: User
  applicantUser: User
  creationDateTime: LocalDateTime
  updateUser: User
  updateDateTime: LocalDateTime
  instructor: User
  secondInterlocutor: string
  boostBonusSimulation: BoostBonusSimulation | null
  legacyBoostBonusSimulation: LegacyBoostBonusSimulation | null
  epcBonusParameterValues: Record<string, string | number> | null
  valuationType: ValuationType
  commentary: string
  subcontractor: Subcontractor | null
  commercialStatus: CommercialStatus | null
  customerOfferNumber: string | null
  offersDispatchDate: LocalDate | null
  chronoCode: string
  operationsGroup: OperationsGroup | null
  customerFinancialIncentive: number
  commercialOfferWithoutFinancialIncentive: number
  finalWorksType: WorksType | null
  backToStep50Counter: number
  accountedMessagesNumber: number
  headOperation: boolean
  estimatedNumberOfOperationInHead: number | null
  emmyFolder: EmmyFolder | null
  emmyLotId: number
  exportToCsvDateTime: LocalDateTime | null
  validateImportInEmmyUser: User | null
  validateImportInEmmyDateTime: LocalDateTime | null

  works1?: Works
  works2?: Works
  works3?: Works
  rgeGrantedDate: LocalDate | null
  rgeEndOfValidityDate: LocalDate | null

  controlOrderBatch: ControlOrderBatch | null

  controlOrderDetails: ControlOrderDetails | null

  heatFundActive: boolean
  ademeCode: string | null
  ademeConventionSignedDate: LocalDate | null
  fromBocee: boolean

  selfWorks: boolean
  duplicateChronoCode: string | null | undefined

  businessPlan?: BusinessPlan

  documentTypeIdsSent?: number[]

  oshHidden: boolean

  orderNumber?: string
  controlReportIssueDate?: LocalDate

  mustSendFinalVersion: boolean
}

export interface OperationRequest
  extends Omit<
    Operation,
    | 'standardizedOperationSheet'
    | 'id'
    | 'creationDateTime'
    | 'creationUser'
    | 'applicantUser'
    | 'updateUser'
    | 'instructor'
    | 'boostBonusSimulation'
    | 'valuationType'
    | 'beneficiary'
    | 'legacyBeneficiary'
    | 'controlOrganism'
    | 'subcontractor'
    | 'status'
    | 'commercialStatus'
    | 'soldClassicCumac'
    | 'soldPrecariousnessCumac'
    | 'availableClassicCumac'
    | 'availablePrecariousnessCumac'
    | 'chronoCode'
    | 'operationsGroup'
    | 'entity'
    | 'leadingEntity'
    | 'finalFee'
    | 'feeIssuedEntity'
    | 'reservedClassicCumac'
    | 'reservedPrecariousnessCumac'
    | 'backToStep50Counter'
    | 'reservedClassicValuationValue'
    | 'reservedPrecariousnessValuationValue'
    | 'accountedMessagesNumber'
    | 'period'
    | 'emmyFolder'
    | 'exportToCsvDateTime'
    | 'validateImportInEmmyUser'
    | 'validateImportInEmmyDateTime'
    | 'lastValidatedStepDateTime'
    | 'controlOrderBatch'
    | 'fromBocee'
    | 'legacyBoostBonusSimulation'
    | 'businessPlan'
    | 'mustSendFinalVersion'
  > {
  standardizedOperationSheetId: number
  valuationTypeId: number
  controlOrganismId: number
  beneficiaryId: number
  subcontractorId: number
  boostBonusSimulation: BoostBonusSimulationRequest | null
  operationsGroupId: number
  entityId: string
  leadingEntityId: string
  periodId?: number
  instructorId: number
  controlOrderBatchId: number
  businessPlanId?: number | null
}

export interface ControlOrderDetails {
  controlOrderType?: ControlOrderType
  controlOrderStatus?: ControlOrderStatus
  afterSalesServiceStatus?: ControlOrderAfterSalesServiceStatus
  commmentary?: string
  worksConformityAfterSalesService?: boolean
}

export const operationStatus = ['DOING', 'DONE', 'CANCELLED', 'IMPROPER', 'LOST', 'KO_PNCEE'] as const

export const operationStatusLabel = [
  {
    value: 'DOING',
    title: 'En cours',
  },
  {
    value: 'DONE',
    title: 'Terminée',
  },
  {
    value: 'CANCELLED',
    title: 'Abandonnée',
  },
  {
    value: 'IMPROPER',
    title: 'Non conforme',
  },
  {
    value: 'LOST',
    title: 'Offre Perdue',
  },
  {
    value: 'KO_PNCEE',
    title: 'Refusée par le PNCEE',
  },
]

export type OperationStatus = (typeof operationStatus)[number]

export interface OperationStepHistory {
  step: Step
  creationDateTime: LocalDateTime
  user: User
}

export const activitySectors: readonly { label: string; value: string }[] = [
  {
    label: 'Résidentiel',
    value: 'RESIDENTIAL',
  },
  {
    label: 'Résidentiel collectif',
    value: 'RESIDENTIAL_COLLECTIVE',
  },
  {
    label: 'Bureaux',
    value: 'OFFICE',
  },
  {
    label: 'Bureaux avec ERP',
    value: 'OFFICE_AND_ERP',
  },
  {
    label: 'Enseignement',
    value: 'EDUCATION',
  },
  {
    label: 'Magasins',
    value: 'SHOP',
  },
  {
    label: 'Hôtellerie',
    value: 'HOTEL',
  },
  {
    label: 'Restauration',
    value: 'CATERING',
  },
  {
    label: 'Santé',
    value: 'HEALTH',
  },
  {
    label: 'Autres',
    value: 'OTHERS',
  },
] as const

export type ActivitySector = (typeof activitySectors)[number]['value']

export const oldEnergyTypes: readonly { label: string; value: string }[] = [
  {
    label: 'Gaz',
    value: 'GAS',
  },
  {
    label: 'Fuel',
    value: 'FUEL',
  },
  {
    label: 'Charbon',
    value: 'COAL',
  },
]
export type OldEnergyType = (typeof oldEnergyTypes)[number]['value']

export const energyTypes: readonly { label: string; value: string }[] = [
  {
    label: 'Electricité',
    value: 'ELECTRICITY',
  },
  {
    label: 'Gaz',
    value: 'GAS',
  },
  {
    label: 'Réseaux de chaleurs',
    value: 'HEAT_NETWORKS',
  },
  {
    label: 'Réseaux de chaleurs avec + de 50',
    value: 'HEAT_NETWORK_WITH_MORE_THAN_50',
  },
  {
    label: 'Biomasse',
    value: 'BIOMASS',
  },
]
export type EnergyType = (typeof energyTypes)[number]['value']

export interface LegacyBoostBonusSimulation {
  boostType: 'CPTER' | 'CPTERRES' | 'CPRES'
  activitySector: ActivitySector
  buildingsNumber: number
  surface: number
  flatsNumber: number
  oldEnergyType: OldEnergyType
  oldEnergyType2: OldEnergyType
  newEnergyType: EnergyType
  newEnergyType2: EnergyType
  oldPower: number
  oldPower2: number
  newPower: number
  newPower2: number
}

export const makeEmptyAddress = (): Address => ({
  street: '',
  postalCode: '',
  city: '',
  additionalPostalAddress: '',
  country: null,
})

export const makeEmptyOperation = (): Operation => ({
  id: 0,
  simulationName: '',
  operationName: '',
  entity: makeEmptyEntity(),
  leadingEntity: makeEmptyEntity(),
  stepId: 0,
  standardizedOperationSheet: makeEmptyStandardizedOperationSheet(),
  issuedFromStandardizedOperationSheetCumac: 0,
  operationLineCumacWithoutBonus: [],
  operationLineClassicCumac: [],
  operationLinePrecariousnessCumac: [],
  classicCumac: 0,
  precariousnessCumac: 0,
  reservedClassicCumac: 0,
  reservedPrecariousnessCumac: 0,
  classicValuationValue: 0,
  precariousnessValuationValue: 0,
  soldClassicCumac: 0,
  soldPrecariousnessCumac: 0,
  availableClassicCumac: 0,
  availablePrecariousnessCumac: 0,
  specialFee: null,
  finalFee: null,
  feeIssuedEntity: null,
  property: null,
  finalAddress: makeEmptyAddress(),
  coOwnerShipSyndicate: null,
  coOwnerShipSyndicateName: null,
  coOwnerShipSyndicateImmatriculationNumber: null,
  finalPropertyName: '',
  beneficiary: null,
  legacyBeneficiary: null,
  controlOrganism: null,
  toProcess: false,
  customerDuplicate: false,
  eesDuplicate: false,
  cumul: false,
  period: null,
  signedDate: '',
  reservedDate: '',
  caseDate: '',
  status: 'DOING',
  lostDate: '',
  lostReasons: null,
  estimatedCommitmentDate: '',
  estimatedEndOperationDate: '',
  parameterValues: [{}],
  creationDateTime: '',
  creationUser: makeEmptyUser(),
  applicantUser: makeEmptyUser(),
  updateDateTime: '',
  updateUser: makeEmptyUser(),
  instructor: makeEmptyUser(),
  secondInterlocutor: '',
  boostBonusSimulation: null,
  legacyBoostBonusSimulation: null,
  epcBonusParameterValues: null,
  valuationType: makeEmptyValuationType(),
  commentary: '',
  subcontractor: null,
  commercialStatus: null,
  customerOfferNumber: '',
  offersDispatchDate: null,
  chronoCode: '',
  operationsGroup: null,
  commercialOfferWithoutFinancialIncentive: 0,
  customerFinancialIncentive: 0,
  actualEndWorksDate: null,
  finalWorksType: null,
  backToStep50Counter: 0,
  accountedMessagesNumber: 0,
  headOperation: false,
  estimatedNumberOfOperationInHead: null,
  emmyLotId: 0,
  emmyFolder: null,
  exportToCsvDateTime: null,
  validateImportInEmmyDateTime: null,
  validateImportInEmmyUser: null,
  lastValidatedStepDateTime: '',
  rgeGrantedDate: null,
  rgeEndOfValidityDate: null,
  controlOrderBatch: null,
  controlOrderDetails: null,
  heatFundActive: false,
  ademeCode: null,
  ademeConventionSignedDate: null,
  fromBocee: false,
  selfWorks: false,
  duplicateChronoCode: null,
  documentTypeIdsSent: [],
  endContractDate: null,
  oshHidden: false,
  mustSendFinalVersion: false,
})

export const mapToOperationRequest = (operation: Operation): OperationRequest => {
  const request: OperationRequest = {
    simulationName: operation.simulationName,
    operationName: operation.operationName,
    entityId: operation.entity.id,
    leadingEntityId: operation.leadingEntity.id,
    standardizedOperationSheetId: operation.standardizedOperationSheet.id,
    issuedFromStandardizedOperationSheetCumac: operation.issuedFromStandardizedOperationSheetCumac,
    operationLineCumacWithoutBonus: operation.operationLineCumacWithoutBonus,
    operationLineClassicCumac: operation.operationLineClassicCumac,
    operationLinePrecariousnessCumac: operation.operationLinePrecariousnessCumac,
    classicCumac: operation.classicCumac,
    precariousnessCumac: operation.precariousnessCumac,
    classicValuationValue: operation.classicValuationValue,
    precariousnessValuationValue: operation.precariousnessValuationValue,
    specialFee: operation.specialFee,
    atypicalClassicValuationValue: operation.atypicalClassicValuationValue,
    atypicalPrecariousnessValuationValue: operation.atypicalPrecariousnessValuationValue,
    beneficiaryId: operation.beneficiary?.id ?? 0,
    controlOrganismId: operation.controlOrganism?.id ?? 0,
    toProcess: operation.toProcess,
    customerDuplicate: operation.customerDuplicate,
    eesDuplicate: operation.eesDuplicate,
    cumul: operation.cumul,
    periodId: operation.period?.id,
    signedDate: operation.signedDate,
    reservedDate: operation.reservedDate,
    caseDate: operation.caseDate,
    lostDate: operation.lostDate,
    lostReasons: operation.lostReasons,
    property: operation.property,
    finalAddress: operation.finalAddress,
    coOwnerShipSyndicate: operation.coOwnerShipSyndicate,
    coOwnerShipSyndicateName: operation.coOwnerShipSyndicateName,
    coOwnerShipSyndicateImmatriculationNumber: operation.coOwnerShipSyndicateImmatriculationNumber,
    parameterValues: operation.parameterValues,
    estimatedCommitmentDate: operation.estimatedCommitmentDate,
    estimatedEndOperationDate: operation.estimatedEndOperationDate,
    precariousnessBonusParameterValues: operation.precariousnessBonusParameterValues,
    boostBonusSimulation:
      operation.boostBonusSimulation == null
        ? null
        : {
            parameterValues: operation.boostBonusSimulation.parameterValues,
            boostBonusSheetId: operation.boostBonusSimulation.boostBonusSheet.id,
          },
    epcBonusParameterValues: operation.epcBonusParameterValues,
    valuationTypeId: operation.valuationType.id,
    instructorId: operation.instructor.id,
    secondInterlocutor: operation.secondInterlocutor,
    stepId: operation.stepId,
    commentary: operation.commentary,
    subcontractorId: operation.subcontractor?.id ?? 0,
    customerOfferNumber: operation.customerOfferNumber,
    offersDispatchDate: operation.offersDispatchDate,
    operationsGroupId: operation.operationsGroup?.id ?? 0,
    commercialOfferWithoutFinancialIncentive: operation.commercialOfferWithoutFinancialIncentive,
    customerFinancialIncentive: operation.customerFinancialIncentive,
    actualEndWorksDate: operation.actualEndWorksDate,
    finalWorksType: operation.finalWorksType,
    finalPropertyName: operation.finalPropertyName,
    headOperation: operation.headOperation,
    estimatedNumberOfOperationInHead: operation.estimatedNumberOfOperationInHead,
    emmyLotId: 0,
    works1: operation.works1,
    works2: operation.works2,
    works3: operation.works3,
    rgeGrantedDate: operation.rgeGrantedDate,
    rgeEndOfValidityDate: operation.rgeEndOfValidityDate,
    controlOrderBatchId: operation.controlOrderBatch?.id ?? 0,

    controlOrderDetails: operation.controlOrderDetails,

    heatFundActive: operation.heatFundActive,
    ademeCode: operation.ademeCode,
    ademeConventionSignedDate: operation.ademeConventionSignedDate,
    selfWorks: operation.selfWorks,
    duplicateChronoCode: operation.duplicateChronoCode,
    businessPlanId: operation.businessPlan?.id,
    updateDateTime: operation.updateDateTime,
    documentTypeIdsSent: operation.documentTypeIdsSent,
    endContractDate: operation.endContractDate,
    oshHidden: operation.oshHidden,
    orderNumber: operation.orderNumber,
    controlReportIssueDate: operation.controlReportIssueDate,
  }
  if (operation.boostBonusSimulation && !isEqual(operation.boostBonusSimulation.parameterValues, {})) {
    request.boostBonusSimulation = {
      boostBonusSheetId: operation.boostBonusSimulation.boostBonusSheet.id,
      parameterValues: operation.boostBonusSimulation.parameterValues,
    }
  }
  if (isEmpty(operation.epcBonusParameterValues)) {
    request.epcBonusParameterValues = null
  }
  return request
}

const adminConfigurationStore = useAdminConfigurationStore()

export const isOperationValid = (
  operation: Operation | null | undefined,
  rgeEndofValidityAlertDayNumber: number,
  expirationPeriod?: string,
  commitmentPeriod?: string,
  workPeriod?: string,
  valuations?: Valuation[]
) => {
  return (
    operation &&
    (operation.status !== 'DOING' ||
      ((operation.stepId < 30 || isBeneficiaryValid(operation.beneficiary)) &&
        isStandardizedOperationSheetValid(operation, expirationPeriod) &&
        isEstimatedCommitmentDateValid(operation, commitmentPeriod) &&
        isEstimatedEndWorkValid(operation, workPeriod) &&
        isOperationDurationValid(operation))) &&
    isOperationDurationStepValid(operation) &&
    isOperationSubcontractorValid(operation) &&
    !isValuationDifferent(operation, valuations) &&
    isRGEOperationValid(operation, rgeEndofValidityAlertDayNumber) &&
    isStandardizedOperationSheetAndCommitmentCompatible(operation) &&
    isEndWorkValidAtStep50(operation, adminConfigurationStore.validateStep50FirstAlert?.valueAsInt)
  )
}

export const isStandardizedOperationSheetValid = (
  ope: Operation | null | undefined,
  expirationPeriod: string | undefined
) => {
  return (
    ope &&
    (ope.status !== 'DOING' ||
      !ope.standardizedOperationSheet.expirationDate ||
      ope.stepId > 40 ||
      add(new Date(), { days: parseInt(expirationPeriod ?? '0') }) <
        new Date(ope.standardizedOperationSheet.expirationDate))
  )
}

export const isStandardizedOperationSheetAndCommitmentCompatible = (ope: Operation | null | undefined) => {
  const commitmentDate = ope
    ? ((ope.stepId > 40 || ope.signedDate ? ope.signedDate : ope.estimatedCommitmentDate) ?? new Date())
    : new Date()
  return (
    ope &&
    (ope.status !== 'DOING' ||
      (new Date(ope.standardizedOperationSheet.startDate) <= new Date(commitmentDate) &&
        new Date(commitmentDate) <= new Date(ope.standardizedOperationSheet.expirationDate ?? '2099-12-31')))
  )
}

export const isEstimatedCommitmentDateValid = (
  ope: Operation | null | undefined,
  commitmentPeriod: string | undefined
) => {
  return (
    ope &&
    (ope.status !== 'DOING' ||
      ope.stepId > 40 ||
      add(new Date(), { days: parseInt(commitmentPeriod ?? '0') }) < new Date(ope.estimatedCommitmentDate))
  )
}

export const isEstimatedEndWorkValid = (ope: Operation | null | undefined, workPeriod: string | undefined) => {
  return (
    ope &&
    (ope.status !== 'DOING' ||
      ope.stepId > 50 ||
      !!ope.actualEndWorksDate ||
      add(new Date(), { days: parseInt(workPeriod ?? '0') }) < new Date(ope.estimatedEndOperationDate))
  )
}

export const isOperationDurationStepValid = (ope: Operation | null | undefined) => {
  const steps = useStepsStore().steps
  if (steps && ope) {
    const step = steps.find((it) => it.id === ope?.stepId)
    if (step?.validMonthDuration && ope.lastValidatedStepDateTime) {
      return !isBefore(add(parseISO(ope.lastValidatedStepDateTime), { months: step.validMonthDuration }), new Date())
    }
  }
  return true
}

export const isOperationDurationValid = (ope: Operation | null | undefined) => {
  return ope && ((ope.stepId ?? 0) >= 50 || add(new Date(ope.reservedDate), { months: 12 }) > new Date())
}

export const isOperationSubcontractorValid = (operation: Operation | null | undefined) => {
  return (
    operation &&
    (operation.standardizedOperationSheet.subcontractCompulsory && operation.stepId >= 30
      ? !!operation.subcontractor
      : true)
  )
}

export const isValuationDifferent = (ope: Operation | null | undefined, valuations: Valuation[] | undefined) => {
  return (
    ope &&
    valuations &&
    valuations.filter((valuation) => valuation != null).length > 0 &&
    !(ope.atypicalClassicValuationValue || ope.atypicalPrecariousnessValuationValue) &&
    ((ope.classicCumac &&
      ope.classicValuationValue !== valuations.find((valuation: Valuation) => !valuation.precariousness)?.value) ||
      (ope.precariousnessCumac &&
        ope.precariousnessValuationValue !==
          valuations.find((valuation: Valuation) => valuation.precariousness)?.value))
  )
}

export const isRGEOperationValid = (ope: Operation | null | undefined, numberOfDayDelay: number) => {
  if (!ope || !ope.standardizedOperationSheet.rgeMandatory || ope.stepId != 50) {
    return true
  }
  return add(new Date(), { days: numberOfDayDelay }) < new Date(ope.rgeEndOfValidityDate!)
}

export const lostReasonsLabels: Record<string, string> = {
  superiorValuation: 'Valorisation supérieure des MWh cumac',
  lessExpensive: 'Montant des travaux inférieur',
  otherTechnology: 'Variante travaux - autre technologie',
  betterCompetitor: 'Engagement contractuel concurrent plus performant',
  abandon: 'Abandon par le client',
  competitorName: "Concurrent qui a remporté l'offre",
}

export const mapToReadableStatus = (status: OperationStatus) => {
  switch (status) {
    case 'DOING':
      return 'En cours'
    case 'DONE':
      return 'Terminée'
    case 'CANCELLED':
      return 'Abandonnée'
    case 'IMPROPER':
      return 'Non conforme'
    case 'LOST':
      return 'Offre Perdue'
    case 'KO_PNCEE':
      return 'Refusée par le PNCEE'
    default:
      return ''
  }
}

export const getClassicValuationValue = (ope: Operation) => {
  if (ope.atypicalClassicValuationValue) {
    return ope.atypicalClassicValuationValue
  }
  return ope.classicValuationValue
}

export const getPrecariousnessValuationValue = (ope: Operation) => {
  if (ope.atypicalPrecariousnessValuationValue) {
    return ope.atypicalPrecariousnessValuationValue
  }
  return ope.precariousnessValuationValue
}

export const getPoleChargeCEE = (ope: Operation) => {
  return ope.specialFee ?? ope.finalFee ?? ope.entity.entityDetails.effectiveFee ?? 0
}

export const getChargeCellule = (ope: Operation) => {
  return getPoleChargeCEE(ope) * ((ope.classicCumac + ope.precariousnessCumac) / 1000)
}

export const getTotalValuationAmount = (ope: Operation) =>
  Math.round(
    (((ope.atypicalClassicValuationValue ?? ope.classicValuationValue ?? 0) * ope.classicCumac) / 1000) * 100
  ) /
    100 +
  Math.round(
    (((ope.atypicalPrecariousnessValuationValue ?? ope.precariousnessValuationValue ?? 0) * ope.precariousnessCumac) /
      1000) *
      100
  ) /
    100

export const getNetMargin = (ope: Operation, incentive?: number, valuationAmount?: number) => {
  const finalIncentive = incentive ? incentive : ope.customerFinancialIncentive
  const finalValuationAmount = valuationAmount ? valuationAmount : getTotalValuationAmount(ope)
  return finalValuationAmount - finalIncentive - getChargeCellule(ope)
}

const snackbarStore = useSnackbarStore()
export const downloadPdfDocument = (standardizedOperationSheet: StandardizedOperationSheet) => {
  standardizedOperationSheetApi
    .downloadPdfDocument(standardizedOperationSheet.id)
    .then((response) => {
      downloadFile(standardizedOperationSheet.pdfDocument!.originalFilename, response.data)
      snackbarStore.setSuccess('Le téléchargement a réussi')
    })
    .catch(() =>
      snackbarStore.setError(
        'Le téléchargement du fichier a échoué. Aviez-vous envoyé ce fichier sur boCEE ? Si oui, veuillez nous contacter.'
      )
    )
}

export const downloadInternalPdfDocument = (standardizedOperationSheet: StandardizedOperationSheet) => {
  standardizedOperationSheetApi
    .downloadInternalPdfDocument(standardizedOperationSheet.id)
    .then((response) => {
      downloadFile(standardizedOperationSheet.internalPdfDocument!.originalFilename, response.data)
      snackbarStore.setSuccess('Le téléchargement a réussi')
    })
    .catch(() =>
      snackbarStore.setError(
        'Le téléchargement du fichier a échoué. Aviez-vous envoyé ce fichier sur boCEE ? Si oui, veuillez nous contacter.'
      )
    )
}

export const isStep70Required = (operation: Operation) =>
  !(
    operation.standardizedOperationSheet.controlOrderNature == 'HUNDRED_PERCENT' ||
    !operation.standardizedOperationSheet.controlOrderStartDate ||
    operation.standardizedOperationSheet.controlOrderStartDate > operation.signedDate!
  )

export const isEndWorkValidAtStep50 = (operation: Operation | null | undefined, daysCount?: number) => {
  return (
    operation == null ||
    operation?.stepId !== 50 ||
    daysCount === undefined ||
    new Date() < add(new Date(operation.estimatedEndOperationDate), { days: daysCount })
  )
}

export const COOWNER_SHIP_SYNDICATE_MANDATORY_MIN_DATE = '2025-07-01'
