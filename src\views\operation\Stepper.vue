<template>
  <div class="d-flex">
    <div
      v-for="step in steps"
      :key="step"
      class="stepper__step"
      :class="{ 'stepper__step--passed': step < stepId, 'stepper__step--current': step === stepId }"
    >
      <div class="w-100 d-flex align-center">
        <VIcon :icon="step === stepId ? 'mdi-circle-outline' : 'mdi-circle'" size="small" />
        <hr v-if="step < 110" />
      </div>
      <div
        class="d-flex flex-column font-weight-bold"
        :class="step < stepId ? 'text-primary' : ''"
        :style="step < stepId ? 'cursor: pointer' : ''"
        @click="detailStepId = step"
      >
        <div class="stepper__number">{{ step === 110 ? 'Validée' : step }}</div>
        <VTooltip location="bottom" activator="parent">
          {{ stepsStore.steps?.find((e) => e.id === step)?.name }}
          <template v-if="step < stepId">
            <br />
            Validée par :
            {{ displayFullnameUser(history[step]?.user) }}
            <br />
            Le :
            {{ formatHumanReadableLocalDateTime(history[step]?.creationDateTime) }}
          </template>
        </VTooltip>
      </div>
    </div>
    <CardDialog v-model="detailDialog" :title="`Valider l'étape ${step?.id}`" no-actions width="30%">
      <RichTextDisplayValue :html="step?.explanation" />
    </CardDialog>
  </div>
</template>
<script lang="ts" setup>
import { formatHumanReadableLocalDateTime, type LocalDateTime } from '@/types/date'
import { type Step, steps } from '@/types/steps'
import { displayFullnameUser, type User } from '@/types/user'

const props = defineProps({
  stepId: {
    type: Number,
    required: true,
  },
  id: {
    type: Number,
    required: true,
  },
  creationDateTime: String as PropType<LocalDateTime>,
  creationUser: Object as PropType<User>,
})

const detailDialog = ref(false)
const detailStepId = ref(0)
const step = ref<Step>()

const { history, stepsStore, operationStepHistoryList } = useOperationHistory(
  computed(() => ({
    id: props.id,
    stepId: props.stepId,
    creationDateTime: props.creationDateTime,
    creationUser: props.creationUser,
  }))
)

watch(detailStepId, async (v) => {
  if (v) {
    if (step.value?.id !== v) {
      step.value = stepsStore.steps?.find((e) => e.id === v)
    }
    detailDialog.value = true
  }
})

watch(detailDialog, (v) => {
  if (!v) {
    detailStepId.value = 0
  }
})

defineExpose({ operationStepHistoryList })
</script>

<style scoped lang="scss">
.stepper {
  &__step {
    flex-grow: 1;
    color: #c9c9c9;

    .v-icon {
      color: #778c9b;
    }
    hr {
      flex-grow: 1;
      border: none;
      border-top: 4px dashed var(--color-dash, #c9c9c9);
    }
    &:last-child {
      flex-grow: 0;
    }

    &--passed {
      --color-dash: #009de9;

      .v-icon {
        color: #007acd;
      }
    }

    &--current {
      .stepper__number {
        font-size: 1.5rem;
        color: black;
      }
      color: #c9c9c9;

      .v-icon {
        color: #778c9b;
      }
    }
  }
}
</style>
