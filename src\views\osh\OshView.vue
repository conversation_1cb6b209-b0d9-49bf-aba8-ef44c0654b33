<template>
  <NjPage title="OSH">
    <template #subtitle>
      <VTabs :model-value="tab" @update:model-value="updateTab">
        <VTab value="etape-50">Stocks étape 50 </VTab>
        <VTab value="etape-60-80">Stocks étape 60 à 80</VTab>
        <template v-for="(period, periodIndex) in periods" :key="periodIndex">
          <template v-for="(priority, priorityIndex) in oshPriorities" :key="priorityIndex">
            <VTab :value="`p${period}-d${priority}`">{{ `P${period} - D${priority}` }}</VTab>
          </template>
        </template>
      </VTabs>
      <VDivider />
    </template>
    <template #body>
      <VWindow :model-value="tab" class="content-layout__main h-100" @update:model-value="updateTab">
        <VWindowItem value="etape-50" class="h-100">
          <div class="h-100">
            <VRow class="content-layout" dense>
              <VCol class="flex-grow-0 mt-2">
                <VProgressLinear v-show="operationSummaryStep50.loading" indeterminate absolute />
                <OshHeaderSummaryDisplayValue
                  v-if="operationSummaryStep50.value"
                  :summary="operationSummaryStep50"
                  :filtered-summary="filteredOperationSummaryStep50"
                  :selections="selectionsStep50"
                />
              </VCol>
              <VCol class="flex-grow-0 mt-2">
                <VRow dense>
                  <VCol cols="4">
                    <SearchInput @update:model-value="updateFilterByFieldnameStockStep50('search', $event)" />
                  </VCol>
                  <VCol v-show="!isEqual(stocksStep50DefaultFilter, pageFilterStockStep50)" align-self="center">
                    <VLink icon="mdi-sync" @click="pageFilterStockStep50 = stocksStep50DefaultFilter">
                      Réinitialiser les filtres
                    </VLink>
                  </VCol>
                  <VSpacer />
                  <template v-if="userStore.hasRole('ADMIN_PLUS')">
                    <VCol v-show="showHideStock50Action" class="flex-grow-0">
                      <NjBtn @click="setOshHiddenOperationStock50(true)"> Masquer </NjBtn>
                    </VCol>
                    <VCol v-show="showShowStock50Action" class="flex-grow-0">
                      <NjBtn @click="setOshHiddenOperationStock50(false)"> Restaurer </NjBtn>
                    </VCol>
                  </template>
                  <VCol class="flex-grow-0">
                    <NjBtn v-show="!step50FilterDrawer" @click="step50ShowColumnManager = !step50ShowColumnManager"
                      >Personnaliser</NjBtn
                    >
                  </VCol>
                  <VCol class="flex-grow-0">
                    <NjBtn :loading="exportOperationsLoadingStep50" @click="exportOperationsStep50">Exporter</NjBtn>
                  </VCol>
                  <VCol class="flex-grow-0">
                    <NjBtn @click="handleOpenFilterDrawer">Filtres</NjBtn>
                  </VCol>
                  <VCol v-if="userStore.hasRole('ADMIN_PLUS')" class="flex-grow-0" align-self="center">
                    <NjBtn @click="setPriority">Affecter à un dossier</NjBtn>
                    <AlertDialog
                      style="max-width: 600px"
                      v-bind="setPriorityDialogStep50.props"
                      title="Affecter à un dossier"
                      max-width="640px"
                    >
                      <VRow class="flex-column" dense>
                        <VCol>
                          A quelle dossier souhaiter vous affecter les
                          {{
                            selectionsStep50.length == 0 ? stocksStep50.value?.totalElements : selectionsStep50.length
                          }}
                          opérations ?
                        </VCol>
                        <VCol>
                          <VSelect
                            v-model="selectedOshPriority"
                            style="max-width: 200px"
                            label="Dossier"
                            :items="oshPriorities"
                            :item-title="(item) => `D${item}`"
                            :item-value="(item) => item"
                            clearable
                          />
                        </VCol>
                      </VRow>

                      <template #positiveButton>
                        <NjBtn
                          :disabled="!selectedOshPriority"
                          @click="setPriorityDialogStep50.props['onClick:positive']"
                        >
                          Valider
                        </NjBtn>
                      </template>
                    </AlertDialog>
                  </VCol>
                </VRow>
              </VCol>
              <VCol>
                <NjDataTable
                  v-model:selections="selectionsStep50"
                  :pageable="pageableStockStep50"
                  class="content-layout__main"
                  fixed
                  :headers="step50ColumnManagerRef?.headers!"
                  :page="stocksStep50.value"
                  :loading="stocksStep50.loading"
                  :status="(simu: OperationExportResultDto) => resolveColorStatusDatatable(simu.periodNumber!)"
                  checkboxes
                  multi-selection
                  :on-click-row="(row) => openDrawerWithOperation(row.id)"
                  :clicked-row="selectedOperation"
                  @update:pageable="updatePageableStockStep50"
                >
                  <template #[`item.instructionDelay`]="{ item, headerTitle }">
                    <span v-if="headerTitle == 'Délai instruction'">
                      <VIcon icon="mdi-clock-outline" :color="getInstructionDelayColor(item.effectiveEndWorksDate)" />
                      {{ getInstructionDelayLabel(item.effectiveEndWorksDate) }}
                    </span>
                    <span v-else>
                      {{ formatHumanReadableLocalDate(item.effectiveEndWorksDate!) }}
                    </span>
                  </template>
                </NjDataTable>
              </VCol>
            </VRow>
          </div>
        </VWindowItem>
        <VWindowItem value="etape-60-80" class="h-100">
          <div class="h-100">
            <VRow class="content-layout" style="overflow: hidden" dense>
              <VCol class="flex-grow-0 mt-2">
                <VProgressLinear v-show="operationSummaryStep60To80.loading" indeterminate absolute />
                <OshHeaderSummaryDisplayValue
                  v-if="operationSummaryStep60To80.value"
                  :summary="operationSummaryStep60To80"
                  :filtered-summary="filteredOperationSummaryStep60To80"
                  :selections="selectionsStep60To80"
                />
              </VCol>
              <VCol class="flex-grow-0 mt-2">
                <VRow dense>
                  <VCol cols="4">
                    <SearchInput @update:model-value="updateFilterByFieldnameStockStep60To80('search', $event)" />
                  </VCol>
                  <VCol v-show="!isEqual(step60To80DefaultFilter, pageFilterStockStep60To80)" align-self="center">
                    <VLink icon="mdi-sync" @click="pageFilterStockStep60To80 = step60To80DefaultFilter">
                      Réinitialiser les filtres
                    </VLink>
                  </VCol>
                  <VSpacer />
                  <template v-if="userStore.hasRole('ADMIN_PLUS')">
                    <VCol v-show="showHideAction" class="flex-grow-0">
                      <NjBtn @click="setOshHiddenOperationStock60To80(true)">Masquer</NjBtn>
                    </VCol>
                    <VCol v-show="showShowAction" class="flex-grow-0">
                      <NjBtn @click="setOshHiddenOperationStock60To80(false)">Restaurer</NjBtn>
                    </VCol>
                  </template>
                  <VCol class="flex-grow-0">
                    <NjBtn
                      v-show="!step60FilterDrawer"
                      @click="step60To80ShowColumnManager = !step60To80ShowColumnManager"
                      >Personnaliser</NjBtn
                    >
                  </VCol>
                  <VCol class="flex-grow-0">
                    <NjBtn :loading="exportOperationsLoadingStep60To80" @click="exportOperationsStep60To80"
                      >Exporter</NjBtn
                    >
                  </VCol>
                  <VCol class="flex-grow-0">
                    <NjBtn @click="handleOpenFilterDrawer">Filtres</NjBtn>
                  </VCol>
                  <VCol v-if="userStore.hasRole('ADMIN_PLUS')" class="flex-grow-0" align-self="center">
                    <NjBtn @click="setPriority">Affecter à un dossier</NjBtn>
                    <AlertDialog
                      style="max-width: 600px"
                      v-bind="setPriorityDialogStep60To80.props"
                      title="Affecter à un dossier"
                      max-width="640px"
                    >
                      <VRow class="flex-column" dense>
                        <VCol>
                          A quelle dossier souhaiter vous affecter les
                          {{
                            selectionsStep60To80.length == 0
                              ? stocksStep60To80.value?.totalElements
                              : selectionsStep60To80.length
                          }}
                          opérations ?
                        </VCol>
                        <VCol>
                          <VSelect
                            v-model="selectedOshPriority"
                            style="max-width: 200px"
                            label="Dossier"
                            :items="oshPriorities"
                            :item-title="(item) => `D${item}`"
                            :item-value="(item) => item"
                            clearable
                          />
                        </VCol>
                      </VRow>

                      <template #positiveButton>
                        <NjBtn
                          :disabled="!selectedOshPriority"
                          @click="setPriorityDialogStep60To80.props['onClick:positive']"
                        >
                          Valider
                        </NjBtn>
                      </template>
                    </AlertDialog>
                  </VCol>
                </VRow>
              </VCol>
              <VCol>
                <NjDataTable
                  v-if="pageFilterStockStep60To80.stepIds?.length"
                  v-model:selections="selectionsStep60To80"
                  :pageable="pageableStockStep60To80"
                  class="content-layout__main"
                  fixed
                  :headers="step60To80ColumnManagerRef?.headers"
                  :page="stocksStep60To80.value"
                  :status="(simu: OperationExportResultDto) => resolveColorStatusDatatable(simu.periodNumber!)"
                  :loading="stocksStep60To80.loading"
                  checkboxes
                  multi-selection
                  :on-click-row="(row) => openDrawerWithOperation(row.id)"
                  :clicked-row="selectedOperation"
                  @update:pageable="updatePageableStockStep60To80"
                >
                  <template #[`item.lastValidatedStepDateTime`]="{ item }">
                    <VIcon icon="mdi-clock-outline" :color="getlastValidatedStepDateTimeColor(item)" />
                    {{ formatHumanReadableLocalDate(item.date70 ?? item.date60 ?? item.date50) }}
                  </template>
                </NjDataTable>
                <ErrorAlert v-else message="Vous devez séléctionner au moins une étape" />
              </VCol>
            </VRow>
          </div>
        </VWindowItem>
        <template v-for="(period, periodIndex) in periods" :key="periodIndex">
          <template v-for="(priority, priorityIndex) in oshPriorities" :key="priorityIndex">
            <VWindowItem :value="`p${period}-d${priority}`" class="h-100">
              <div class="h-100">
                <OshFolder
                  :period-id="period"
                  :osh-priority="priority"
                  :current-tab="tab"
                  :on-click-row="(id: number | undefined) => openDrawerWithOperation(id)"
                  :headers="folderColumnManagerRef?.headers"
                  class="h-100"
                  @click:custom="folderShowColumnManager = !folderShowColumnManager"
                />
              </div>
            </VWindowItem>
          </template>
        </template>
      </VWindow>
      <FilterDrawer
        v-model:original-filter="pageFilterStockStep50"
        v-model:valuation-mode="pageFilterStockStep50.valuationMode"
        v-model="step50FilterDrawer"
        mode="osh"
      />
      <FilterDrawer
        v-model:original-filter="pageFilterStockStep60To80"
        v-model:valuation-mode="pageFilterStockStep60To80.valuationMode"
        v-model="step60FilterDrawer"
        mode="osh"
      />

      <ColumnManagerDialog
        id="osh-step50StocksView"
        ref="step50ColumnManagerRef"
        v-model="step50ShowColumnManager"
        :original-headers="stock50Headers"
      />
      <ColumnManagerDialog
        id="osh-step60To80StocksView"
        ref="step60To80ColumnManagerRef"
        v-model="step60To80ShowColumnManager"
        :original-headers="stock60To80Headers"
      />
      <ColumnManagerDialog
        id="osh-folderStocksView"
        ref="folderColumnManagerRef"
        v-model="folderShowColumnManager"
        :original-headers="oshFolderHeader"
      />
    </template>
    <template #drawer>
      <OperationDrawer
        v-model="operationDrawer"
        :operation="selectedOperation.value"
        @update:model-value="
          () => {
            selectedOperation = emptyValue<Operation>()
            selectionsStep50 = []
          }
        "
      />
    </template>
  </NjPage>
</template>
<script setup lang="ts">
import { operationApi, type OperationFilter } from '@/api/operation'
import type { DataTableHeader } from '@/components/okta/NjDataTable.type'
import { useDialogStore } from '@/stores/dialog'
import { useSnackbarStore } from '@/stores/snackbar'
import { useUserStore } from '@/stores/user'
import { formatHumanReadableLocalDate, type LocalDate } from '@/types/date'
import type { Operation } from '@/types/operation'
import type { OperationExportResultDto } from '@/types/operationExportResultDto'
import type { OperationSummary } from '@/types/operationSummary'
import {
  NUMBER_OF_DAY_AFTER_EFFECTIVE_END_WORKS_FOR_INSTRUCTION_DELAY,
  NUMBER_OF_DAY_BEFORE_PNCEE_EXPIRATION,
  getInstructionDelay,
} from '@/types/osh/instructionDelay'
import { emptyValue } from '@/types/promisableValue'
import { defaultExportHeaders, oshFolderHeader } from '@/views/osh/operationStocksHeader'
import { add, parseISO } from 'date-fns'
import { isEmpty, isEqual, omitBy } from 'lodash'
import {
  VCol,
  VDivider,
  VIcon,
  VProgressLinear,
  VRow,
  VSelect,
  VTab,
  VTabs,
  VWindow,
  VWindowItem,
} from 'vuetify/components'
import { useExportOperation } from '../exportOperation'
import FilterDrawer from '../operation/FilterDrawer.vue'
import OperationDrawer from '../operation/OperationDrawer.vue'
import OshFolder from './OshFolder.vue'
import OshHeaderSummaryDisplayValue from './OshHeaderSummaryDisplayValue.vue'
import { trace } from '@/stores/analytics'

const tab = defineModel<'etape-50' | 'etape-60-80' | string>('tab', {
  default: 'etape-50',
})

const snackbarStore = useSnackbarStore()
const userStore = useUserStore()
const router = useRouter()

const step50ColumnManagerRef = useTemplateRef('step50ColumnManagerRef')
const step60To80ColumnManagerRef = useTemplateRef('step60To80ColumnManagerRef')
const folderColumnManagerRef = useTemplateRef('folderColumnManagerRef')

const stocksStep50DefaultFilter: OperationFilter = {
  ...makeEmptyFilter(),
  stepIds: [50],
  operationStatuses: ['DOING'],
  hideWithOshPriority: true,
  oshHidden: false,
}

const {
  data: stocksStep50,
  pageable: pageableStockStep50,
  updatePageable: updatePageableStockStep50,
  updateFilterByFieldname: updateFilterByFieldnameStockStep50,
  pageFilter: pageFilterStockStep50,
  reload: reloadStep50Stock,
} = usePagination<OperationExportResultDto, OperationFilter>(
  (filter, pageable) => operationApi.findAllExportOperationResultDto(filter, pageable),
  stocksStep50DefaultFilter,
  {},
  {
    saveFiltersName: 'OshView-step50',
    lazyLoad: true,
  }
)

const operationSummaryStep50 = ref(emptyValue<OperationSummary>())
const filteredOperationSummaryStep50 = ref(emptyValue<OperationSummary>())

const showHideStock50Action = computed(() => selectionsStep50.value.some((it) => !it.oshHidden))
const showShowStock50Action = computed(() => selectionsStep50.value.some((it) => it.oshHidden))

const loadSummaryStep50 = () => {
  handleAxiosPromise(operationSummaryStep50, operationApi.getSummary(stocksStep50DefaultFilter), {
    afterError: (err) =>
      snackbarStore.setError(err ?? "Echec de la récupération du résumé des opérations à l'étape 50"),
  })
}
const loadFilteredSummaryStep50 = () => {
  if (isEqual(stocksStep50DefaultFilter, pageFilterStockStep50.value)) {
    filteredOperationSummaryStep50.value = emptyValue()
    return
  }
  handleAxiosPromise(filteredOperationSummaryStep50, operationApi.getSummary(pageFilterStockStep50.value), {
    afterError: (err) =>
      snackbarStore.setError(err ?? "Echec de la récupération du résumé des opérations à l'étape 50"),
  })
}
watch(
  pageFilterStockStep50,
  () => {
    loadFilteredSummaryStep50()
  },
  {
    deep: true,
  }
)

const step60To80DefaultFilter: OperationFilter = {
  ...makeEmptyFilter(),
  stepIds: [60, 70, 80],
  operationStatuses: ['DOING'],
  hideWithOshPriority: true,
  oshHidden: false,
}

const {
  data: stocksStep60To80,
  pageable: pageableStockStep60To80,
  pageFilter: pageFilterStockStep60To80,
  updatePageable: updatePageableStockStep60To80,
  updateFilterByFieldname: updateFilterByFieldnameStockStep60To80,
  reload: reloadStockStep60To80,
} = usePagination<OperationExportResultDto, OperationFilter>(
  (filter, pageable) => operationApi.findAllExportOperationResultDto(filter, pageable),
  {
    ...step60To80DefaultFilter,
  },
  {},
  {
    saveFiltersName: 'OshView-step60-80',
    lazyLoad: true,
  }
)
const operationSummaryStep60To80 = ref(emptyValue<OperationSummary>())
const filteredOperationSummaryStep60To80 = ref(emptyValue<OperationSummary>())
const loadSummaryStockStep60To80 = () => {
  handleAxiosPromise(operationSummaryStep60To80, operationApi.getSummary(step60To80DefaultFilter), {
    afterError: (err) =>
      snackbarStore.setError(err ?? "Echec de la récupération du résumé des opérations de l'étape 60 à 80"),
  })
}
const loadFilteredSummaryStockStep60To80 = () => {
  if (isEqual(step60To80DefaultFilter, pageFilterStockStep60To80.value)) {
    filteredOperationSummaryStep60To80.value = emptyValue()
    return
  }
  handleAxiosPromise(filteredOperationSummaryStep60To80, operationApi.getSummary(pageFilterStockStep60To80.value), {
    afterError: (err) =>
      snackbarStore.setError(err ?? "Echec de la récupération du résumé des opérations de l'étape 60 à 80"),
  })
}

watch(
  pageFilterStockStep60To80,
  () => {
    loadFilteredSummaryStockStep60To80()
  },
  {
    deep: true,
  }
)

const periods = [4, 5]
const oshPriorities = [1, 2, 3, 4]

const step50FilterDrawer = ref(false)
const step60FilterDrawer = ref(false)

const stock50Headers: DataTableHeader<OperationExportResultDto>[] = [
  {
    title: 'Délai instruction',
    value: 'instructionDelay',
    cellClass: 'justify-start',
  } as DataTableHeader<OperationExportResultDto>,
].concat(defaultExportHeaders)

const stock60To80Headers: DataTableHeader<OperationExportResultDto>[] = defaultExportHeaders.concat()

const getlastValidatedStepDateTimeColor = (item: OperationExportResultDto) => {
  const date = item.date70 ?? item.date60 ?? item.date50
  if (new Date() > add(parseISO(date!), { months: 3 })) {
    return 'error'
  } else {
    ;('primary')
  }
}

const resolveColorStatusDatatable = (period: string) => {
  if (period == '4') {
    return 'blue'
  }
  if (period == '5') {
    return 'green'
  }
  if (period == '6') {
    return 'orange'
  }
}

// Nombre de jour restant avant d'être 3 mois après la date de fin de travaux
const getInstructionDelayLabel = (effectiveEndWorksDate: LocalDate | null) => {
  if (!effectiveEndWorksDate) {
    return 'Erreur'
  }
  const instructionDelay = getInstructionDelay(effectiveEndWorksDate)
  if (
    instructionDelay <
    NUMBER_OF_DAY_AFTER_EFFECTIVE_END_WORKS_FOR_INSTRUCTION_DELAY - NUMBER_OF_DAY_BEFORE_PNCEE_EXPIRATION
  ) {
    return 'Délai PNCEE dépassé'
  }
  return instructionDelay
}

const getInstructionDelayColor = (effectiveEndWorksDate: LocalDate | null) => {
  if (!effectiveEndWorksDate) {
    return 'error'
  } else if (getInstructionDelay(effectiveEndWorksDate) < 0) {
    return 'error'
  }
  return ''
}

const setPriorityDialogStep50 = useConfirmAlertDialog()
const setPriorityDialogStep60To80 = useConfirmAlertDialog()
const selectionsStep60To80 = ref<OperationExportResultDto[]>([])
const showHideAction = computed(() => selectionsStep60To80.value.some((it) => !it.oshHidden))
const showShowAction = computed(() => selectionsStep60To80.value.some((it) => it.oshHidden))
const selectedOshPriority = ref<number | null>(null)
const route = useRoute()
const setPriority = async () => {
  selectedOshPriority.value = null
  if (await (tab.value === 'etape-50' ? setPriorityDialogStep50.confirm() : setPriorityDialogStep60To80.confirm())) {
    let filter: OperationFilter = {}
    let selectionCount = 0

    if (tab.value === 'etape-50') {
      if (selectionsStep50.value.length) {
        filter.operationIds = selectionsStep50.value.map((i) => i.id)
        selectionCount = selectionsStep50.value.length
      } else {
        filter = pageFilterStockStep50.value
        selectionCount = stocksStep50.value.value?.totalElements ?? -1
      }
    } else if (tab.value === 'etape-60-80') {
      if (selectionsStep60To80.value.length) {
        filter.operationIds = selectionsStep60To80.value.map((i) => i.id)
        selectionCount = selectionsStep60To80.value.length
      } else {
        filter = pageFilterStockStep60To80.value
        selectionCount = stocksStep60To80.value.value?.totalElements ?? -1
      }
    }

    operationApi
      .updateOshPriority(filter, { oshPriority: selectedOshPriority.value! })
      .then(() => {
        if (tab.value === 'etape-50') {
          selectionsStep50.value = []
          reloadStep50Stock()
          loadSummaryStep50()
          loadFilteredSummaryStep50()
          trace('setOshPriority', {
            route: {
              name: route.name,
              params: route.params,
              fullpath: route.fullPath,
            },
            tab: tab.value,
            filter: omitBy(toRaw(filter), (it) => isEmpty(it) || it == null || it === false),
            oshPriority: selectedOshPriority.value,
            selectionCount: selectionCount,
          })
        } else if (tab.value === 'etape-60-80') {
          selectionsStep60To80.value = []
          reloadStockStep60To80()
          loadSummaryStockStep60To80()
          loadFilteredSummaryStockStep60To80()
          trace('setOshPriority', {
            route: {
              name: route.name,
              params: route.params,
              fullpath: route.fullPath,
            },
            tab: tab.value,
            filter: omitBy(toRaw(filter), (it) => isEmpty(it) || it == null || it === false),
            oshPriority: selectedOshPriority.value,
            selectionCount: selectionCount,
          })
        }
      })
      .catch(async (e) => {
        snackbarStore.setError(await handleAxiosException(e))
        throw e
      })
  }
}

const selectionsStep50 = ref<OperationExportResultDto[]>([])
const selectedOperation = ref(emptyValue<Operation>())

const operationDrawer = ref(false)

const openDrawerWithOperation = (operationId: number | undefined) => {
  if (operationId) {
    handleAxiosPromise(selectedOperation, simulationApi.findById(operationId), {
      afterSuccess: () => {
        operationDrawer.value = true
      },
      afterError: (err) => {
        snackbarStore.setError(err)
      },
    })
  } else {
    operationDrawer.value = false
  }
}

const handleOpenFilterDrawer = () => {
  if (tab.value === 'etape-50') {
    step50FilterDrawer.value = !step50FilterDrawer.value
  }
  if (tab.value === 'etape-60-80') {
    step60FilterDrawer.value = !step60FilterDrawer.value
  }
  operationDrawer.value = false
  selectedOperation.value = emptyValue<Operation>()
}
const dialogStore = useDialogStore()
const setOshHiddenOperations =
  (selections: Ref<OperationExportResultDto[]>, reload: () => void) => async (value: boolean) => {
    if (
      await dialogStore.addAlert({
        title: value ? 'Cacher des opérations' : 'Réafficher des opérations',
        message:
          'Vous allez ' +
          (value ? 'cacher' : 'réafficher') +
          ' ' +
          selections.value.length +
          ' opération(s).\nÊtes-vous sur de vouloir continuer ?',
        maxWidth: '640px',
      })
    ) {
      operationApi
        .oshHideOperations(
          {
            operationIds: selections.value.map((it) => it.id),
          },
          {
            oshHidden: value,
          }
        )
        .then(() => {
          snackbarStore.setSuccess('Opérations cachés avec succès')
          selections.value = []
          reload()
        })
        .catch(async (e) => {
          snackbarStore.setError(await handleAxiosException(e))
        })
    }
  }

const setOshHiddenOperationStock50 = setOshHiddenOperations(selectionsStep50, () => {
  reloadStep50Stock()
  loadSummaryStep50()
  loadFilteredSummaryStep50()
})
const setOshHiddenOperationStock60To80 = setOshHiddenOperations(selectionsStep60To80, () => {
  reloadStockStep60To80()
  loadSummaryStockStep60To80()
  loadFilteredSummaryStockStep60To80()
})
const step50ShowColumnManager = ref(false)
const step60To80ShowColumnManager = ref(false)
const folderShowColumnManager = ref(false)

const { exportOperationsLoading: exportOperationsLoadingStep50, exportOperations: exportOperationsStep50 } =
  useExportOperation(pageFilterStockStep50, selectionsStep50, stocksStep50, false)

const { exportOperationsLoading: exportOperationsLoadingStep60To80, exportOperations: exportOperationsStep60To80 } =
  useExportOperation(pageFilterStockStep60To80, selectionsStep60To80, stocksStep60To80, false)

const updateTab = (tabName: any) => {
  router.push({ name: 'OshViewWithPart', params: { tab: tabName } })
}

watch(
  tab,
  () => {
    step50FilterDrawer.value = false
    step60FilterDrawer.value = false

    operationDrawer.value = false
    step50ShowColumnManager.value = false
    step60To80ShowColumnManager.value = false
    folderShowColumnManager.value = false

    if (tab.value === 'etape-50') {
      reloadStep50Stock()
      loadSummaryStep50()
      loadFilteredSummaryStep50()
    } else if (tab.value === 'etape-60-80') {
      reloadStockStep60To80()
      loadSummaryStockStep60To80()
      loadFilteredSummaryStockStep60To80()
    }
  },
  {
    immediate: true,
  }
)
</script>
