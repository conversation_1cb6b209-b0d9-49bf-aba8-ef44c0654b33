<template>
  <VRow>
    <VCol>
      <VTextField
        v-model="mappingTable.id"
        label="Identifiant"
        :rules="[variableNameRule, reservedWordsRule]"
        :hint="variableNameRuleHint"
        :disabled="disabled"
      ></VTextField>
    </VCol>
    <VCol>
      <VBtn v-if="!disabled" color="primary" icon="mdi-delete" size="small" variant="text" @click="emit('delete')" />
    </VCol>
  </VRow>
  <VRow>
    <VCol>
      <h4 class="d-flex align-center">
        Colonnes de tables
        <NjBtn
          v-if="!disabled"
          class="ms-4"
          color="primary"
          variant="outlined"
          @click="addParameterTableCorrespondance(mappingTable)"
        >
          Ajouter une colonne à ma table
        </NjBtn>
      </h4>
    </VCol>
  </VRow>
  <VRow>
    <VCol v-for="(paramColumn, i) in mappingTable.paramColumns" :key="paramColumn" cols="3">
      <VRow>
        <VCol>
          <VSelect
            v-model="mappingTable.paramColumns[i]"
            :items="parameters"
            item-title="label"
            item-value="id"
            :disabled="disabled"
            @update:model-value="generateValues()"
          />
        </VCol>
        <VCol v-if="!disabled" class="flex-grow-0">
          <VBtn
            icon="mdi-delete-empty-outline"
            color="primary"
            variant="text"
            size="small"
            @click="deleteParamColumn(mappingTable, i)"
          />
        </VCol>
      </VRow>
    </VCol>
  </VRow>

  <VRow>
    <VCol cols="12">
      <h4>Valeurs</h4>
    </VCol>
    <!-- <VCol cols="12" v-if="isString(combinaisonsValues[mappingTable.id])">
      <ErrorAlert :message="(combinaisonsValues[mappingTable.id])" />
    </VCol> -->
  </VRow>

  <!-- eslint-disable-next-line vue/valid-v-for -->
  <VRow v-for="(v, i) in mappingTable.data">
    <!-- eslint-disable-next-line vue/valid-v-for -->
    <VCol v-for="p in combinaisonsValues[i]">
      <VTextField readonly :model-value="p" />
    </VCol>
    <VCol>
      <VTextField
        v-model.number="mappingTable.data[i]"
        hint="Valeur"
        :tabindex="iMappingTables * 1000 + i + 1"
        :readonly="disabled"
      />
    </VCol>
  </VRow>
</template>

<script setup lang="ts">
import { generateMappingTableCombinaisons, makeEmptyMappingTable, type MappingTable } from '@/types/calcul/mappingTable'
import type { ParameterFormula } from '@/types/calcul/parameterFormula'
import { variableNameRule, variableNameRuleHint, type ValidationRule } from '@/types/rule'
import { cloneDeep, isEqual } from 'lodash'
import type { PropType } from 'vue'
import { VTextField } from 'vuetify/components'

const props = defineProps({
  modelValue: {
    type: Object as PropType<MappingTable>,
    default: makeEmptyMappingTable,
  },
  parameters: {
    type: Array as PropType<ParameterFormula[]>,
    required: true,
  },
  reservedWordsRule: {
    type: Function as PropType<ValidationRule>,
    default: () => () => true,
  },
  iMappingTables: {
    type: Number,
    required: true,
  },
  disabled: Boolean,
})
const emit = defineEmits(['update:model-value', 'delete'])
const mappingTable = ref(makeEmptyMappingTable())
function addParameterTableCorrespondance(tableCorrespondances: MappingTable) {
  if (tableCorrespondances.paramColumns == null) {
    tableCorrespondances.paramColumns = []
  }
  tableCorrespondances.paramColumns.push('')
}
function deleteParamColumn(mappingTable: MappingTable, i: number) {
  mappingTable.paramColumns.splice(i, 1)
  generateValues()
}
const combinaisonsValues = ref<string[][]>([])
function generateValues(loading: boolean = false) {
  const combinaisons = generateMappingTableCombinaisons(props.modelValue, props.parameters)

  combinaisonsValues.value = combinaisons
  if (!loading) {
    mappingTable.value.data = combinaisons.map(() => 0)
  }
}

watch(
  mappingTable,
  (v) => {
    emit('update:model-value', v)
  },
  {
    deep: true,
  }
)
watch(
  () => props.modelValue,
  (v) => {
    if (!isEqual(v, mappingTable.value)) {
      mappingTable.value = cloneDeep(v)
      generateValues(true)
    }
  },
  {
    immediate: true,
  }
)
</script>
