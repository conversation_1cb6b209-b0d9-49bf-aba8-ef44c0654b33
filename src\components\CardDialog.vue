<template>
  <VDialog
    v-bind="$attrs"
    v-model="modelValue"
    :content-class="{ 'h-100': fixed }"
    :persistent="disabled"
    :min-width="localMinWidth"
    :width="localWidth"
  >
    <template v-if="$slots['activator'] !== undefined" #activator="scope">
      <slot name="activator" v-bind="scope"></slot>
    </template>
    <VCard class="content-layout">
      <VCardTitle class="content-layout__header py-0 pe-0">
        <slot name="title">
          <VRow v-if="title" class="flex-column" no-gutters>
            <VCol>
              <VRow class="align-center">
                <VCol class="text-wrap">
                  <slot name="subtitle">
                    {{ title }}
                  </slot>
                </VCol>
                <VCol v-if="closeable" class="flex-grow-0">
                  <NjIconBtn
                    color="primary"
                    icon="mdi-close"
                    class="rounded-0"
                    :disabled
                    @click="modelValue = false"
                  ></NjIconBtn>
                </VCol>
              </VRow>
            </VCol>
          </VRow>
        </slot>
      </VCardTitle>
      <VDivider />
      <ErrorAlert :message="error" />
      <VCardText class="content-layout__main pb-4" :class="cardTextClass" style="flex-basis: auto">
        <slot></slot>
      </VCardText>
      <VDivider v-if="!noActions" />
      <VCardActions v-if="!noActions" class="content-layout__footer" :actions-class="actionsClass">
        <slot name="pre-actions"></slot>
        <VSpacer />
        <slot name="actions" v-bind="bindedValues">
          <!-- <NjBtn variant="outlined" @click="confirmationDialog.active = false">Annuler</NjBtn>
          <NjBtn color="primary" @click="confirmSelection">Confirmer</NjBtn> -->
        </slot>
      </VCardActions>
    </VCard>
  </VDialog>
</template>
<script setup lang="ts">
import { useDisplay } from 'vuetify'
import NjIconBtn from './NjIconBtn.vue'

const modelValue = defineModel<boolean>({
  default: false,
})

const props = defineProps({
  title: String,
  closeable: {
    type: Boolean,
    default: true,
  },
  noActions: {
    type: Boolean,
    default: false,
  },
  actionsClass: [String, Array, Object],
  error: String,
  fixed: {
    type: Boolean,
  },
  disabled: {
    type: Boolean,
  },
  width: {},
  minWidth: {
    default: '576px',
  },
  cardTextClass: {},
})

const { xs } = useDisplay()

const localWidth = computed((): any => {
  if (xs.value) {
    return '100%'
  } else {
    return props.width
  }
})

const localMinWidth = computed((): any => {
  if (xs.value) {
    return null
  } else {
    return props.minWidth
  }
})

const bindedValues = {
  modelValue: modelValue,
}
</script>
