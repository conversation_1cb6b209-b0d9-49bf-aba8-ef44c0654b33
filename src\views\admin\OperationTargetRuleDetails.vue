<template>
  <VRow class="flex-column">
    <VCol>
      <div class="text-section-title">Type d'opération :</div>
      <NjDisplayValue label="" :value="modelValue.operationCodeTarget" />
    </VCol>
    <VDivider />
    <VCol v-if="targetedFosList.value?.content">
      <div class="text-section-title">Opérations concernées :</div>
      <NjDisplayValue label="" :value="targetedFosList.value.content.map((i) => i.operationCode).join(', ')" />
    </VCol>
    <VDivider />
    <VCol v-if="excludedFosList.value?.content">
      <div class="text-section-title">Opérations non concernées :</div>
      <NjDisplayValue label="" :value="excludedFosList.value.content.map((i) => i.operationCode).join(', ')" />
    </VCol>
    <VDivider />
    <VCol>
      <div class="text-section-title">Période de validité du document :</div>
      <VRow>
        <VCol cols="6">
          <NjDisplayValue label="Début" :value="modelValue.minSignedDate" />
        </VCol>
        <VCol cols="6">
          <NjDisplayValue label="Fin" :value="modelValue.maxSignedDate" />
        </VCol>
        <VDivider />
        <VCol v-if="modelValue.bonusType">
          <NjDisplayValue
            label="Requis pour"
            :value="bonusTypeLabel.find((i) => i.value == modelValue.bonusType)!.label"
          />
        </VCol>
      </VRow>
    </VCol>
    <slot name="more" />
  </VRow>
</template>
<script setup lang="ts">
import { type PropType } from 'vue'
import { type OperationTargetRule } from '@/types/operationTargetRule'
import { VCol, VDivider, VRow } from 'vuetify/components'
import type { Page } from '@/types/pagination'
import type { StandardizedOperationSheet } from '@/types/calcul/standardizedOperationSheet'
import { useSnackbarStore } from '@/stores/snackbar'
import { bonusTypeLabel } from '@/types/operationTargetRule'

const props = defineProps({
  id: Number,
  modelValue: {
    type: Object as PropType<OperationTargetRule>,
    required: true,
  },
})

const snackbarStore = useSnackbarStore()

const targetedFosList = ref(emptyValue<Page<StandardizedOperationSheet>>())
const excludedFosList = ref(emptyValue<Page<StandardizedOperationSheet>>())

watch(
  () => props.id,
  () => {
    if (props.modelValue?.target.length && props.modelValue.target.length > 2) {
      handleAxiosPromise(
        targetedFosList,
        standardizedOperationSheetApi.findAll(
          { size: 100 },
          {
            ids: props.modelValue.target
              .substring(1, props.modelValue.target.length - 1)
              .split(';')
              .map((it) => parseInt(it)),
          }
        ),
        {
          afterError() {
            snackbarStore.setError(targetedFosList.value.error!)
          },
        }
      )
    } else {
      targetedFosList.value = succeedValue(makeEmptyPage())
    }
    if (props.modelValue?.exclusion.length && props.modelValue.exclusion.length > 2) {
      handleAxiosPromise(
        excludedFosList,
        standardizedOperationSheetApi.findAll(
          { size: 100 },
          {
            ids: props.modelValue.exclusion
              .substring(1, props.modelValue.exclusion.length - 1)
              .split(';')
              .map((it) => parseInt(it)),
          }
        ),
        {
          afterError() {
            snackbarStore.setError(excludedFosList.value.error!)
          },
        }
      )
    } else {
      excludedFosList.value = succeedValue(makeEmptyPage())
    }
  },
  {
    immediate: true,
  }
)
</script>
