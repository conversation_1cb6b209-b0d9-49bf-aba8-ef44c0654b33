<template>
  <NjPage :error-message="data.error" :loading="data.loading">
    <template #sub-header>
      <VRow>
        <VCol cols="3">
          <SearchInput
            v-model:loading="data.loading"
            :model-value="pageFilter.search"
            @update:model-value="updateSearch"
          />
        </VCol>
        <VSpacer />
        <VCol class="flex-grow-0">
          <NjBtn :to="{ name: 'EpcBonusSheetOneNewView' }">Nouvelle bonification CPE</NjBtn>
        </VCol>
      </VRow>
    </template>
    <template #body>
      <NjDataTable
        :pageable="pageable"
        :page="data.value!"
        :headers="headers"
        :on-click-row="
          (value) =>
            router.push({
              name: 'EpcBonusSheetOneView',
              params: { id: value.id },
            })
        "
        fixed
        @update:pageable="updatePageable"
      >
        <template #[`item.certified`]="{ item }">
          <VIcon v-show="item.certified" color="#28B750" icon="mdi-shield-check-outline" />
        </template>
      </NjDataTable>
    </template>
  </NjPage>
</template>
<script lang="ts" setup>
import { epcBonusSheetApi } from '@/api/epcBonus'
import NjPage from '@/components/NjPage.vue'
import type { DataTableHeader } from '@/components/okta/NjDataTable.type'
import NjDataTable from '@/components/okta/NjDataTable.vue'
import { usePaginationInQuery } from '@/types/pagination'

const router = useRouter()

const { data, pageable, updatePageable, pageFilter, updateFilter } = usePaginationInQuery(
  (filter, pageable) => epcBonusSheetApi.getAll(pageable, filter),
  {
    defaultPageFilter: {
      search: '',
    },
    defaultPageablePartial: {
      sort: ['startDate'],
    },
    saveFiltersName: 'EpcBonusSheetAllView',
  }
)
const headers: DataTableHeader[] = [
  {
    title: 'Nom',
    value: 'name',
  },
  {
    title: 'Préfixes',
    value: 'targets',
  },

  {
    title: 'Date de début',
    value: 'startDate',
    formater: (_, value) => formatHumanReadableLocalDate(value),
  },
  {
    title: 'Date de fin',
    value: 'endDate',
    formater: (_, value) => formatHumanReadableLocalDate(value),
  },
  {
    title: 'Certifié',
    value: 'certified',
  },
  {
    title: 'MAJ par',
    value: 'updateUser',
    formater(_, value) {
      return displayFullnameUser(value)
    },
  },
  {
    title: 'Date MAJ',
    value: 'updateDateTime',
    formater(_, value) {
      return formatHumanReadableLocalDateTime(value)
    },
  },
]

const updateSearch = (value: string) => {
  updateFilter({ ...pageFilter.value, search: value })
}
</script>
