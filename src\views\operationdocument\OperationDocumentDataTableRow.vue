<template>
  <tr
    class="operation-document-data-table-row"
    :class="{
      'operation-document-data-table-row--required': isDocumentMissingToValidateStep,
      'operation-document-data-table-row--missing': !isDocumentMissingToValidateStep && isDocumentMissing,
    }"
  >
    <td :style="isDocumentMissingToValidateStep ? 'font-size: 14px; font-weight: 700' : ''">
      <template v-if="item.id">
        <VLink @click="downloadDocument">{{ item.documentType.name }}</VLink>
        <VProgressCircular v-show="downloadDocumentLoading" class="ms-2" indeterminate size="small" />
        <EmediaDocumentIcon :emedia-id="item.emediaId" :emedia-errors="item.emediaErrors" />
      </template>
      <template v-else>{{ item.documentType.name }}</template>
    </td>
    <td>{{ item.requiredStepId }}</td>
    <td>
      <div class="d-flex">
        <template v-if="item.id && item.active">
          <VIcon color="success pr-2">mdi-check-circle</VIcon> {{ formatHumanReadableLocalDate(item.creationDateTime) }}
        </template>
        <template v-else-if="item.id && !item.active">
          <VIcon color="error pr-2">mdi-close-circle</VIcon> {{ formatHumanReadableLocalDate(item.creationDateTime) }}
        </template>
        <template v-else-if="isDocumentMissingToValidateStep">
          <VIcon color="primary pr-2">mdi-information-slab-circle</VIcon> <span style="color: #007acd"> Requis </span>
        </template>
      </div>
    </td>
    <td>{{ item.format }}</td>
    <td>
      {{ item.description }}
    </td>
    <td>
      <OperationDocumentDatatableRowActions
        :item="item"
        :operation="operation"
        @send="(id: number) => emits('send', id)"
        @replace-document="reloadData()"
        @desactivate-document="reloadData()"
        @delete-document="reloadData()"
        @edit-document="reloadData()"
      />
    </td>
  </tr>
</template>
<script setup lang="ts">
import { gtfEmediaApi } from '@/api/external/gtf/gtfEmedia'
import { useSnackbarStore } from '@/stores/snackbar'
import { formatHumanReadableLocalDate } from '@/types/date'
import { downloadFile } from '@/types/file'
import type { Operation } from '@/types/operation'
import type { PropType } from 'vue'
import { VIcon } from 'vuetify/components'
import EmediaDocumentIcon from '../emediaDocument/EmediaDocumentIcon.vue'
import OperationDocumentDatatableRowActions from './OperationDocumentDatatableRowActions.vue'
import type { DocumentDataTableItem } from './types'

const props = defineProps({
  item: {
    type: Object as PropType<DocumentDataTableItem>,
    required: true,
  },
  operation: { type: Object as PropType<Operation> },
  reloadData: { type: Function, default: () => {} },
})
const emits = defineEmits<{
  send: [number]
}>()
const isDocumentMissingToValidateStep = computed(
  () => isDocumentMissing.value && (props.operation?.stepId ?? 0) >= props.item.requiredStepId!
)
const isDocumentMissing = computed(() => !props.item.creationDateTime && props.item.requiredStepId)

const snackbarStore = useSnackbarStore()

const downloadDocumentLoading = ref(false)
const downloadDocument = () => {
  downloadDocumentLoading.value = true
  const downloadDocumentName = props.item.documentType.name + '-' + props.item.id + '.' + props.item.format
  if (props.item.emediaId) {
    return gtfEmediaApi
      .downloadDocument(props.item.id!)
      .then((response) => {
        downloadFile(downloadDocumentName, response.data)
        snackbarStore.setSuccess('Le téléchargement a réussi')
      })
      .catch(() => snackbarStore.setError('Le téléchargement du fichier a échoué.'))
      .finally(() => (downloadDocumentLoading.value = false))
  } else {
    operationDocumentApi
      .download(props.item.id!)
      .then((response) => {
        downloadFile(downloadDocumentName, response.data)
        snackbarStore.setSuccess('Le téléchargement a réussi')
      })
      .catch(() =>
        snackbarStore.setError(
          'Le téléchargement du fichier a échoué. Aviez-vous envoyé ce fichier sur boCEE ? Si oui, veuillez nous contacter.'
        )
      )
      .finally(() => (downloadDocumentLoading.value = false))
  }
}
</script>
<style lang="scss">
.operation-document-data-table-row {
  font-size: 0.875rem;
  height: 40px;

  &--required {
    color: black;
    background-color: #e7eefc;
  }

  &--missing {
    color: #60798b;
  }
}
</style>
