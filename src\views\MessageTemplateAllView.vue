<template>
  <VRow class="flex-column">
    <VCol>
      <SearchInput v-model="pageFilter.search" />
    </VCol>
    <VCol>
      <NjDataTable
        :headers="header"
        :pageable="pageable"
        :page="data.value!"
        hide-elements
        hide-total
        :on-click-row="(item) => props.onClickRow(item)"
        @update:pageable="updatePageable"
      >
        <template #[`item.message`]="{ item }">
          {{
            truncate(item.message, {
              length: 32,
              omission: ' [...]',
            })
          }}
        </template>
      </NjDataTable>
    </VCol>
  </VRow>
</template>
<script setup lang="ts">
import type { MessageTemplateFilter } from '@/api/messageTemplate'
import type { MessageTemplate } from '@/types/message'
import { truncate } from 'lodash'

const props = defineProps({
  onClickRow: {
    type: Function,
    default: () => {},
  },
})

const header = [
  {
    title: 'Message',
    value: 'message',
  },
]

const { data, pageable, pageFilter, updatePageable } = usePagination<MessageTemplate, MessageTemplateFilter>(
  (filter, pageable) => messageTemplateApi.findAll(filter, pageable),
  {},
  {
    page: 0,
    size: 10,
    sort: [],
  }
)
</script>
