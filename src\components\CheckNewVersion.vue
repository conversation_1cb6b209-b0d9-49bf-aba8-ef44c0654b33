<template>
  <VDialog v-model="active" max-width="640px">
    <VCard>
      <VCardTitle>Vous n'êtes pas encore sur la dernière version ?</VCardTitle>
      <VCardText
        >Pour cela, il suffit d'appuyer sur les touches suivantes en même temps : <br />
        <kbd>Ctrl</kbd> + <kbd>F5</kbd></VCardText
      >
    </VCard>
  </VDialog>
  <VSnackbar v-model="snackbarActive" color="primary" vertical :timeout="-1">
    <div class="text-subtitle-1 pb-2">Nouvelle version de CAPTE à utiliser</div>
    <p>
      Une nouvelle version de l'application a été mise en place.<br />
      Veuillez rafraichir la page pour vous mettre à jour.
    </p>
    <p>
      Si ce message apparait encore, une fois le rafraichissement de la page effectué,<br />
      consultez l'aide.
    </p>

    <template #actions>
      <VBtn class="me-2" variant="outlined" @click="seeMore"> Aide </VBtn>
      <VBtn variant="outlined" @click="snackbarActive = false"> Fermer </VBtn>
    </template>
  </VSnackbar>
</template>

<script lang="ts" setup>
import axios from 'axios'
import { VCardTitle } from 'vuetify/components'

const active = ref(false)
const snackbarActive = ref(false)

let timeoutId = 0

const seeMore = () => {
  active.value = true
  snackbarActive.value = false
}

const checkUpdate = () => {
  axios
    .get<string>(window.location.origin + '/index.html')
    .then((d) => {
      const versionToMatch = d.data.match('<meta name="version" content="([\\d.]+)" />')?.[1]
      console.debug('versionToMatch', versionToMatch)
      if (versionToMatch !== import.meta.env.VITE_APP_VERSION) {
        snackbarActive.value = true
      }
      timeoutId = setTimeout(checkUpdate, 60_000 * 15)
    })
    .catch(() => {
      timeoutId = setTimeout(checkUpdate, 60_000 * 5)
    })
}

onMounted(() => {
  console.debug(window.location)
  checkUpdate()
})
onUnmounted(() => {
  clearTimeout(timeoutId)
})
</script>
