import type { AxiosInstance, AxiosPromise } from 'axios'
import { gtfAxiosInstance } from '.'

const prefixApiUrl = '/emedia/api/v2/'

class GtfEmediaApi {
  public constructor(private axios: AxiosInstance) {}

  public downloadDocument(documentId: number): AxiosPromise<Blob> {
    return this.axios.get(prefixApiUrl + 'cee_documents/' + documentId, {
      responseType: 'blob',
    })
  }
}

export const gtfEmediaApi = new GtfEmediaApi(gtfAxiosInstance)
