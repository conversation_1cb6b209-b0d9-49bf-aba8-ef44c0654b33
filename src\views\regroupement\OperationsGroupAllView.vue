<template>
  <NjPage title="Liste des regroupements" expend-body>
    <template #header-actions>
      <NjBtn @click="newOperationsGroupDialog = true">Nouveau regroupement</NjBtn>
    </template>

    <template #sub-header>
      <VRow>
        <VCol cols="3">
          <SearchInput v-model="filter.search" :loading="data.loading" />
        </VCol>
        <VCol class="subheader-actions align-center">
          <VCheckbox v-model="filter.myOperationsGroup" label="Mes regroupements" class="flex-grow-0" />
          <NjBtn @click="showFilters = !showFilters"> Filtres </NjBtn>
          <VLink v-show="showResetFilter" icon="mdi-sync" @click="filter = defaultFilter">
            Réinitialiser les filtres
          </VLink>
        </VCol>
        <VCol class="flex-grow-0">
          <NjBtn :disabled="!selections.length" variant="outlined" color="error" @click="deleteOperationsGroup">
            Supprimer
          </NjBtn>
          <AlertDialog v-bind="deleteGroupDialog.props" title="Attention" max-width="640px">
            Êtes-vous sûr de vouloir supprimer
            {{ selections.length === 1 ? 'le regroupement sélectionné' : 'les regroupements sélectionnés' }}?
          </AlertDialog>
        </VCol>
      </VRow>
    </template>
    <template #body>
      <VRow class="w-100">
        <VCol>
          <NjDataTable
            v-model:selections="selections"
            :headers="headers"
            :pageable="pageable"
            :page="data.value!"
            :on-click-row="clickRow"
            :to-row="(item: OperationsGroup) => ({ name: 'OperationsGroupOneView', params: { id: item.id } })"
            multi-selection
            checkboxes
            fixed
            @update:pageable="updatePageable"
          >
            <template #[`item.creationDateTime`]="{ item }">
              {{ formatHumanReadableLocalDate(item.creationDateTime) }}
            </template>
            <template #[`item.beneficiary.socialReason`]="{ item }">
              {{ item.beneficiary?.socialReason }}
            </template>
            <template #[`item.step`]="{ item }">
              <OperationsGroupStepChip :step="item.step ?? undefined" :status="item.status" />
            </template>
            <template #[`item.properties`]="{ item }">
              <VRow v-if="item.properties">
                <VCol>
                  <VRow class="flex-column" dense no-gutter>
                    <VCol v-if="item.properties.length === 1">
                      {{ (item.properties[0].name ?? '') + ' - ' + (item.properties[0].code ?? '') }}
                    </VCol>
                    <VCol>
                      {{ displayAddressProperty(item.properties) }}
                    </VCol>
                  </VRow>
                </VCol>
              </VRow>
              <span v-else />
            </template>
          </NjDataTable>
        </VCol>
      </VRow>
      <CardDialog
        v-model="newOperationsGroupDialog"
        :width="displayBeneficiary ? '80%' : '40%'"
        :height="displayBeneficiary ? '100%' : ''"
        :title="displayBeneficiary ? 'Choix d\'un bénéficiaire' : 'Nouveau Regroupement'"
      >
        <VRow v-if="displayBeneficiary" class="flex-column content-layout">
          <VCol class="content-layout__header">
            <VTextField
              v-model="searchBeneficiary"
              prepend-inner-icon="mdi-magnify"
              placeholder="Rechercher"
              :loading="beneficiaryLoading"
            />
          </VCol>
          <VCol class="content-layout__main">
            <BeneficiaryAllView
              v-model:selections="beneficiary"
              v-model:loading="beneficiaryLoading"
              :search="searchBeneficiary"
              fixed
            />
          </VCol>
        </VRow>
        <VRow v-else>
          <VCol>
            <VForm ref="formRef">
              <VRow class="flex-column">
                <VCol>
                  <NjExpansionPanel title="Regroupement">
                    <VRow class="flex-column">
                      <VCol>
                        <VTextField v-model="name" label="Nom" :rules="[requiredRule]" />
                      </VCol>
                      <VCol>
                        <NjSwitch v-model="onlyCpeOperations" label="Seulement pour des opérations CPE"></NjSwitch>
                      </VCol>
                    </VRow>
                  </NjExpansionPanel>
                </VCol>
                <VDivider />
                <VCol>
                  <NjExpansionPanel>
                    <template #title>
                      <VRow>
                        <VCol> Bénéficiaire </VCol>
                        <VCol class="flex-grow-0" align="end" style="text-wrap: nowrap">
                          <VLink
                            size="small"
                            icon="mdi-format-list-bulleted"
                            style="font-weight: initial; font-size: initial"
                            @click.stop="displayBeneficiary = true"
                          >
                            Bénéficiaire
                          </VLink>
                        </VCol>
                      </VRow>
                    </template>
                    <VRow>
                      <VCol>
                        <BeneficiaryInput :rules="[requiredRule]" :model-value="selectedBeneficiary[0]" />
                      </VCol>
                    </VRow>
                  </NjExpansionPanel>
                </VCol>
                <VDivider />
                <VCol>
                  <NjExpansionPanel title="Organisation">
                    <RemoteAutoComplete
                      v-if="selectEntity"
                      v-model="entity!.id"
                      label="Sélectionnez l'organisation d'appartenance"
                      :query-for-all="
                        (search, pageable) => entityApi.getAll({ search: search, myEntities: true, level: 4 }, pageable)
                      "
                      :query-for-one="(id: string) => entityApi.getOne(id)"
                      item-title="name"
                      :rules="[requiredRule]"
                      infinite-scroll
                    />
                    <NjDisplayValue v-else-if="getUserAgencies.length" label="Nom" :value="getUserAgencies[0].name" />
                    <ErrorAlert
                      v-else
                      message="Vous n'êtes rattaché à aucune agence et ne pourrez donc pas créer une opération. Veuillez
                      contacter un administrateur"
                    />
                  </NjExpansionPanel>
                </VCol>
              </VRow>
            </VForm>
          </VCol>
        </VRow>
        <template #actions>
          <template v-if="displayBeneficiary">
            <NjBtn variant="outlined" @click="displayBeneficiary = false">Annuler</NjBtn>
            <NjBtn :disabled="beneficiary.length == 0" @click="selectBeneficiary"> Valider </NjBtn>
          </template>

          <template v-else>
            <NjBtn variant="outlined" @click="newOperationsGroupDialog = false"> Annuler </NjBtn>
            <NjBtn color="primary" @click="createOperationGroup"> Valider </NjBtn>
          </template>
        </template>
      </CardDialog>
      <OperationsGroupFilterDrawer
        v-model:original-filter="filter"
        :model-value="showFilters"
        @update:model-value="showFilters = false"
      />
    </template>
  </NjPage>
</template>
<script setup lang="ts">
import { entityApi } from '@/api/entity'
import { operationsGroupApi, type OperationsGroupFilter } from '@/api/operationsGroup'
import BeneficiaryInput from '@/components/BeneficiaryDisplayValue.vue'
import CardDialog from '@/components/CardDialog.vue'
import ErrorAlert from '@/components/ErrorAlert.vue'
import NjBtn from '@/components/NjBtn.vue'
import type { DataTableHeader } from '@/components/okta/NjDataTable.type'
import NjDataTable from '@/components/okta/NjDataTable.vue'
import { useSnackbarStore } from '@/stores/snackbar'
import { useUserStore } from '@/stores/user'
import type { Beneficiary } from '@/types/beneficiary'
import { formatHumanReadableLocalDate } from '@/types/date'
import type { Entity } from '@/types/entity'
import type {
  EnhancedOperationsGroup,
  OperationsGroup,
  OperationsGroupRequest,
  PropertyDto,
} from '@/types/operationsGroup'
import { requiredRule } from '@/types/rule'
import { cloneDeep, debounce, isArray, isEqual } from 'lodash'
import type { LocationQuery } from 'vue-router'
import type { VForm } from 'vuetify/components/VForm'
import BeneficiaryAllView from '../BeneficiaryAllView.vue'
import OperationsGroupFilterDrawer from './OperationsGroupFilterDrawer.vue'
import OperationsGroupStepChip from './OperationsGroupStepChip.vue'

const headers: DataTableHeader[] = [
  {
    title: 'Nom',
    value: 'name',
  },
  {
    title: 'Bénéficiaire',
    value: 'beneficiary.socialReason',
  },
  {
    title: 'Organisation',
    value: 'entity.name',
  },
  {
    title: "Nombre d'opération",
    value: 'operationsNumber',
    formater: (_: any, value: number) => formatNumber(value),
    cellClass: 'text-right justify-end',
  },
  {
    title: 'Créateur',
    value: 'creationUser',
    formater: (_, value) => displayFullnameUser(value),
  },
  {
    title: 'Date de création',
    value: 'creationDateTime',
    formater: (_, value) => formatHumanReadableLocalDate(value),
  },
  {
    title: 'Etape',
    value: 'step',
    sortable: false,
  },
  {
    title: 'Installations',
    value: 'properties',
    sortable: false,
  },
]

const userStore = useUserStore()

const makeEmptyFilter = (): OperationsGroupFilter => ({
  search: '',
  myOperationsGroup: false,
  periodIds: [],
  beneficiaryIds: [],
  entityNavFullIds: [],
})

const defaultFilter = {
  ...makeEmptyFilter(),
} as OperationsGroupFilter

const showResetFilter = computed(() => {
  return !isEqual(filter.value, defaultFilter)
})

const filter = ref<OperationsGroupFilter>(cloneDeep(defaultFilter))

const showFilters = ref(false)

const queryMapper = (query: LocationQuery): Record<string, unknown> => ({
  ...query,
  periodIds: mapQueryToTable(query.periodIds, true),
  entityNavFullIds: isArray(query.entityNavFullIds)
    ? query.entityNavFullIds
    : query.entityNavFullIds
      ? [query.entityNavFullIds]
      : [],
  status: mapQueryToTable(query.status, false),
})

const selections = ref<EnhancedOperationsGroup[]>([])
const { data, pageFilter, pageable, updatePageable, updateFilter, reload } =
  usePaginationInQuery<EnhancedOperationsGroup>((filter, pageable) => operationsGroupApi.findAll(filter, pageable), {
    defaultPageFilter: { ...filter.value },
    queryToFilterMapper: queryMapper,
    saveFiltersName: 'OperationGroupAllView',
  })

const router = useRouter()

const clickRow = (operation: EnhancedOperationsGroup) => {
  router.push({
    name: 'OperationsGroupOneView',
    params: { id: operation.id },
  })
}

const debounceFilter = debounce((v: any) => updateFilter(v), 300)

watch(
  filter,
  (v) => {
    debounceFilter(v)
  },
  {
    deep: true,
  }
)

watch(
  () => pageFilter.value,
  (v) => {
    const tempFilter = cloneDeep(v) as any
    Object.keys(tempFilter).forEach((key) => {
      if (isArray(tempFilter[key]) && key !== 'entityNavFullIds' && (tempFilter[key] as any[]).length !== 0) {
        ;(tempFilter[key] as any[]).forEach((val: string, index: number) => {
          if (!Number.isNaN(parseInt(val))) {
            ;(tempFilter[key] as any[])[index] = parseInt(val)
          }
        })
      }
    })
    filter.value = tempFilter as any
  },
  {
    immediate: true,
    deep: true,
  }
)

//Create OperationsGroup
const newOperationsGroupDialog = ref()

const snackbarStore = useSnackbarStore()

//beneficiary
const displayBeneficiary = ref(false)
const searchBeneficiary = ref()
const beneficiaryLoading = ref()

const beneficiary = ref<Beneficiary[]>([])
const selectedBeneficiary = ref<Beneficiary[]>([])

const selectBeneficiary = () => {
  selectedBeneficiary.value = beneficiary.value
  displayBeneficiary.value = false
}

//create regroupement
const name = ref('')
const onlyCpeOperations = ref(false)
const formRef = ref<VForm | null>(null)
const operationsGroup = ref(emptyValue<OperationsGroup>())
const createOperationGroup = async () => {
  const validate = await formRef.value!.validate()
  if (validate.valid) {
    const request: OperationsGroupRequest = {
      name: name.value,
      beneficiaryId: selectedBeneficiary.value[0].id,
      entityId: entity.value.id || getUserAgencies.value[0].id,
      customerFinancialIncentive: undefined,
      commercialOfferWithoutFinancialIncentive: undefined,
      onlyEpcOperations: onlyCpeOperations.value,
      atypicalClassicValuationValue: null,
      atypicalPrecariousnessValuationValue: null,
    }
    handleAxiosPromise(operationsGroup, operationsGroupApi.create(request), {
      afterError: () => snackbarStore.setError('Erreur lors de la création du regroupement'),
      afterSuccess: () =>
        router.push({
          name: 'OperationsGroupOneView',
          params: { id: operationsGroup.value.value?.id },
        }),
    })
  }
}

watch(newOperationsGroupDialog, (v) => {
  if (v) {
    beneficiary.value = []
    selectedBeneficiary.value = []
    entity.value = makeEmptyEntity()
  }
})

const displayAddressProperty = (properties: PropertyDto[] | undefined) => {
  if (!properties) {
    return ''
  } else if (properties.length == 1) {
    return properties[0].street + ', ' + properties[0].postalCode + ' ' + properties[0].city
  }
  return properties.length + ' installations'
}
const getUserAgencies = computed(() => userStore.currentUser.entities.filter((orga) => orga.level === 4))

const selectEntity = computed(
  () =>
    (userStore.currentUser.entities.length && userStore.currentUser.entities.length != getUserAgencies.value.length) ||
    getUserAgencies.value.length > 1
)

const entity = ref<Entity>(makeEmptyEntity())
const deleteGroupDialog = useConfirmAlertDialog()

const deleteOperationsGroup = async () => {
  if (await deleteGroupDialog.confirm()) {
    const ids = selections.value.map((v) => v.id)
    operationsGroupApi
      .delete(ids)
      .then(() => {
        reload()
        snackbarStore.setSuccess('Les regroupements ont bien été supprimé')
      })
      .catch((e) => snackbarStore.setError(e.err ?? 'Echec lors de la suppression des regroupements'))
  }
}
</script>
