import type { DownloadHistory } from '@/types/downloadHistory'
import type { Page, Pageable } from '@/types/pagination'
import type { AxiosInstance, AxiosPromise } from 'axios'
import axiosInstance from '.'

const downloadHistories = '/download_histories'

export interface DownloadHistoryFilter {
  search?: string
}

class DownloadHistoryApi {
  public constructor(private axios: AxiosInstance) {}

  public findAll(pageable: Pageable, filter: DownloadHistoryFilter): AxiosPromise<Page<DownloadHistory>> {
    return this.axios.get(downloadHistories, {
      params: { ...filter, ...pageable },
    })
  }
}

export const downloadHistoryApi = new DownloadHistoryApi(axiosInstance)
