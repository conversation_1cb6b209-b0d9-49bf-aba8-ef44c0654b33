import type { AxiosInstance, AxiosPromise } from 'axios'
import axiosInstance from '.'
import type { OshFolder, OshFolderSummary } from '@/types/osh/oshFolder'
import type { OperationFilter } from './operation'

class OshFolderApi {
  public constructor(private axios: AxiosInstance) {}

  public getOne(periodId: number, oshPriority: number): AxiosPromise<OshFolder> {
    return this.axios.get(`osh_folders/${periodId}/${oshPriority}`)
  }
  public getOneSummary(periodId: number, oshPriority: number, filter: OperationFilter): AxiosPromise<OshFolderSummary> {
    return this.axios.get(`osh_folders/${periodId}/${oshPriority}/summary`, {
      params: filter,
    })
  }
}

export const oshFolderApi = new OshFolderApi(axiosInstance)
