<template>
  <NjPage title="Liste des simulations CEE" :loading="data.loading" :error-message="data.error" expend-body>
    <template #header-actions>
      <DoublonDialog />
    </template>
    <template #sub-header>
      <VRow>
        <VCol cols="3">
          <SearchInput
            v-model:loading="data.loading"
            :model-value="pageFilter.search"
            @update:model-value="updateSearch"
          />
        </VCol>
        <VCol class="subheader-actions align-center flex-grow-0" style="min-width: fit-content">
          <VCheckbox v-model="filter.myRequests" label="Mes simulations" />
          <NjBtn @click="handleFilterDrawer"> Filtres </NjBtn>
          <VLink v-show="showResetFilter" icon="mdi-sync" @click="filter = defaultFilter">
            Réinitialiser les filtres
          </VLink>
        </VCol>
        <VCol class="subheader-actions no-wrap">
          <ButtonsMenuAdapter :buttons="headerButtons" />
        </VCol>
      </VRow>
    </template>
    <template #body>
      <VRow class="w-100">
        <VCol>
          <NjDataTable
            v-model:selections="selection"
            :pageable="pageable"
            :page="data.value!"
            :headers="simulationColumnManagerRef?.headers ?? originalHeaders"
            :on-click-row="
              (value) =>
                router.push({
                  name: 'SimulationOneView',
                  params: { id: value.id },
                })
            "
            :to-row="(item) => ({ name: 'SimulationOneView', params: { id: item.id } })"
            checkboxes
            multi-selection
            fixed
            :disabled-row="(item: Operation) => item.stepId > 0"
            :hide-checkboxes="
              (item: Operation) =>
                item.stepId > 0 ||
                (userStore.hasRole('AGENCE') &&
                  !userStore.hasRole(
                    'ADMIN',
                    'ADMIN_PLUS',
                    'SIEGE',
                    'INSTRUCTEUR',
                    'AGENCE_PLUS',
                    'SUPPORT_AGENCE_PLUS'
                  ) &&
                  item.creationUser.gid != userStore.currentUser.gid)
            "
            @update:pageable="updatePageable"
          >
            <template #[`item.property`]="{ item }">
              <div v-if="item.property?.code">Numéro: {{ item.property?.code }}</div>
              <div v-if="item.property">
                Adresse:
                {{
                  (item.property.streetNumber ?? '') +
                  ' ' +
                  item.property.streetName +
                  ', ' +
                  item.property.postalCode +
                  ' ' +
                  item.property.city
                }}
              </div>
              <div v-if="displayAddress(item.property, item.finalAddress)">
                Adresse retenue:
                {{
                  (item.finalAddress?.street ?? '') +
                  ', ' +
                  (item.finalAddress?.postalCode ?? '') +
                  ' ' +
                  (item.finalAddress?.city ?? '')
                }}
              </div>
            </template>
            <template #[`item.reserved`]="{ item }">
              {{ item.stepId > 0 ? 'OUI' : 'NON' }}
            </template>
            <template #[`item.stepId`]="{ item }">
              <OperationStepChip v-if="item.stepId > 0" :operation="item" />
              <span v-else />
            </template>
            <template #[`item.creationDateTime`]="{ item }">
              {{ item.stepId > 0 ? formatHumanReadableLocalDate(item.creationDateTime) : '' }}
            </template>
            <template #[`item.cdp`]="{ item }">
              {{ item.boostBonusSimulation != null ? 'OUI' : 'NON' }}
            </template>
            <template #[`item.epc`]="{ item }">
              {{ item.epcBonusParameterValues != null ? 'OUI' : 'NON' }}
            </template>
            <template #[`item.precariousness`]="{ item }">
              {{
                item.parameterValues.length > 0
                  ? item.precariousnessBonusParameterValues != null
                    ? 'OUI'
                    : 'NON'
                  : item.precariousnessCumac > 0
                    ? 'OUI'
                    : 'NON'
              }}
            </template>
            <template #[`item.reservedClassicAmount`]="{ item }">
              {{ item.reservedClassicValuationValue ?? 0 * item.reservedClassicCumac }}
            </template>
            <template #[`item.reservedPrecariousnessAmount`]="{ item }">
              {{ item.reservedPrecariousnessValuationValue ?? 0 * item.reservedPrecariousnessCumac }}
            </template>
          </NjDataTable>
        </VCol>
        <FilterDrawer
          v-model:original-filter="filter"
          v-model:valuation-mode="filter.valuationMode"
          :model-value="drawer === 'filter'"
          for-simulation
          @update:model-value="setDrawer('filter', $event)"
        >
          <template #more>
            <VCol>
              <VSelect
                v-model="filter.toProcess"
                clearable
                label="À traiter"
                :items="[
                  {
                    title: 'Oui',
                    value: true,
                  },
                  {
                    title: 'Non',
                    value: false,
                  },
                ]"
              />
            </VCol>
          </template>
        </FilterDrawer>
        <ColumnManagerDialog
          id="simulationAllView"
          ref="simulationColumnManagerRef"
          :original-headers="originalHeaders"
          :model-value="drawer === 'column'"
          @update:model-value="setDrawer('column', $event)"
        />
        <CardDialog v-model="changeEntityDialog" title="Changer d'organisation" width="650px">
          <VRow class="flex-column">
            <VCol>
              <ErrorAlert type="warning" :message="changeEntityMessage" />
            </VCol>
            <VCol>
              <VForm>
                <RemoteAutoComplete
                  v-model="selectedEntity"
                  label="Organisation"
                  :query-for-one="(id) => entityApi.getOne(id)"
                  :query-for-all="(s, pageable) => entityApi.getAll({ search: s, visible: true, level: 4 }, pageable)"
                  item-title="name"
                  item-value="id"
                  :rules="[requiredRule]"
                  infinite-scroll
                />
              </VForm>
            </VCol>
          </VRow>
          <template #actions>
            <NjBtn variant="outlined" @click="changeEntityDialog = false">Annuler</NjBtn>
            <NjBtn :loading="changeEntityLoading" @click="changeEntity">Valider</NjBtn>
          </template>
        </CardDialog>
      </VRow>
    </template>
  </NjPage>
</template>

<script lang="ts" setup>
import type { OperationFilter } from '@/api/operation'
import ColumnManagerDialog from '@/components/ColumnManagerDialog.vue'
import NjBtn from '@/components/NjBtn.vue'
import NjPage from '@/components/NjPage.vue'
import NjDataTable from '@/components/okta/NjDataTable.vue'
import router from '@/router'
import { useDialogStore } from '@/stores/dialog'
import { useSnackbarStore } from '@/stores/snackbar'
import { useUserStore } from '@/stores/user'
import { displayAddress } from '@/types/address'
import { formatHumanReadableLocalDate } from '@/types/date'
import { type Operation } from '@/types/operation'
import { cloneDeep, debounce, isArray, isEqual } from 'lodash'
import type { LocationQuery } from 'vue-router'
import DoublonDialog from './DoublonDialog.vue'
import { useExportOperation } from './exportOperation'
import FilterDrawer from './operation/FilterDrawer.vue'
import { originalHeaders } from './simulationAllViewHeader'
import OperationStepChip from './operation/OperationStepChip.vue'
import { entityApi } from '@/api/entity'
import { requiredRule } from '@/types/rule'

const snackbarStore = useSnackbarStore()
const dialogStore = useDialogStore()
const selection = ref<Operation[]>([])
// const mySimulations = ref<boolean | undefined>(undefined)
// const toProcess = ref<boolean | undefined>(undefined)
const userStore = useUserStore()

const defaultFilter = {
  ...makeEmptyFilter(),
  operationStatuses: ['DOING'],
} as OperationFilter

const filter = ref<OperationFilter>(cloneDeep(defaultFilter))
const showResetFilter = computed(() => {
  return !isEqual(filter.value, defaultFilter)
})

const queryMapper = (query: LocationQuery): Record<string, unknown> => {
  return {
    ...query,
    territoryIds: mapQueryToTable(query.territoryIds, true),
    stepIds: mapQueryToTable(query.stepIds, true),
    periodIds: mapQueryToTable(query.periodIds, true),
    valuationTypeIds: mapQueryToTable(query.valuationTypeIds, true),
    operationStatuses: mapQueryToTable(query.operationStatuses, false),
    standardizedOperationSheetIds: mapQueryToTable(query.standardizedOperationSheetIds, true),
    entityNavFullIds: isArray(query.entityNavFullIds)
      ? query.entityNavFullIds
      : query.entityNavFullIds
        ? [query.entityNavFullIds]
        : [],
    beneficiaryIds: mapQueryToTable(query.beneficiaryIds, true),
    emmyFolderIds: mapQueryToTable(query.emmyFolderIds, true),
    instructorIds: mapQueryToTable(query.instructorIds, true),
    controlOrderNatures: mapQueryToTable(query.controlOrderNatures, false),
  }
}

const { data, pageable, updatePageable, pageFilter, updateFilter, reload } = usePaginationInQuery<
  Operation,
  OperationFilter
>((filter, pageable) => operationApi.findAll({ ...filter, withSimulations: true }, pageable), {
  defaultPageFilter: { ...filter.value },
  defaultPageablePartial: { sort: ['stepId,ASC'] },
  queryToFilterMapper: queryMapper,
  saveFiltersName: 'SimulationAllView',
})

const deleteSimulations = async () => {
  if (
    await dialogStore.addAlert({
      title: 'Attention',
      message: `Êtes-vous sûr de vouloir supprimer ${
        selection.value.length === 1 ? 'la simulation sélectionnée' : 'les simulations sélectionnées'
      }?`,
      positiveButton: 'Valider',
      negativeButton: 'Annuler',
      maxWidth: '640px',
    })
  ) {
    const ids = selection.value.map((v) => v.id)
    simulationApi
      .delete(ids)
      .then(() => {
        reload()
        snackbarStore.setSuccess('Les simulations ont bien été supprimées')
      })
      .catch(async (e) =>
        snackbarStore.setError(
          await handleAxiosException(e, undefined, { defaultMessage: 'Echec lors de la suppression des simulations' })
        )
      )
  }
}

// Search
const updateSearch = (v: string) => {
  data.value.loading = true
  const filter = {
    ...unref(pageFilter),
    search: v,
  }
  updateFilter(filter)
}

const debounceFilter = debounce((v: any) => updateFilter(v), 300)

watch(
  filter,
  (v) => {
    if (!isEqual(v, pageFilter.value)) {
      debounceFilter(v)
    }
  },
  {
    deep: true,
  }
)

watch(
  () => pageFilter.value,
  (v) => {
    if (!isEqual(v, filter.value)) {
      const tempFilter = cloneDeep(v) as any
      Object.keys(tempFilter).forEach((key) => {
        if (isArray(tempFilter[key]) && key !== 'entityNavFullIds' && (tempFilter[key] as any[]).length !== 0) {
          ;(tempFilter[key] as any[]).forEach((val: string, index: number) => {
            if (!Number.isNaN(parseInt(val))) {
              ;(tempFilter[key] as any[])[index] = parseInt(val)
            }
          })
        }
      })
      filter.value = tempFilter as any
    }
  },
  {
    immediate: true,
    deep: true,
  }
)

const drawer = ref<'filter' | 'column'>()
const handleFilterDrawer = () => {
  drawer.value = drawer.value === 'filter' ? undefined : 'filter'
}

const setDrawer = (tag: typeof drawer.value, value: boolean) => {
  if (value) {
    drawer.value = tag
  } else {
    // drawer.value = drawer.value === 'filter' ? undefined : 'filter'
    drawer.value = undefined
  }
}

const simulationColumnManagerRef = ref<typeof ColumnManagerDialog | null>(null)

const { exportOperationsLoading, exportOperations } = useExportOperation(pageFilter, selection, data, true)

const changeEntityDialog = ref(false)

watch(changeEntityDialog, (v) => {
  if (v) {
    selectedEntity.value = ''
  }
})

const getChangeEntityFilter = () => {
  let simulationFilter
  if (selection.value.length == 0) {
    simulationFilter = { ...pageFilter.value, withSimulations: true, stepIds: [0] }
  } else {
    simulationFilter = { operationIds: selection.value.map((item) => item.id), withSimulations: true }
  }
  return simulationFilter
}

const changeEntityMessage = computed(
  () => `
  Attention vous allez changer d'organisation ${
    selection.value.length
      ? selection.value.length + ' simulations'
      : 'toutes les simulations correspondant au filtre selectionné'
  }.
  Vous ne pouvez changer des simulations d'organisation que si elles appartiennent à la même organisation.
`
)
const selectedEntity = ref<string>('')

const changeEntityLoading = ref(false)
const changeEntity = () => {
  changeEntityLoading.value = true
  const operationFilter = getChangeEntityFilter()
  operationApi
    .changeEntity(operationFilter, { entityId: selectedEntity.value })
    .then((response) => {
      snackbarStore.setSuccess(`${response.data} simulation ont changé d'organisation`)
      reload()
      selection.value = []
    })
    .catch(async (err) => snackbarStore.setError(await handleAxiosException(err)))
    .finally(() => {
      changeEntityDialog.value = false
      changeEntityLoading.value = false
    })
}

const disableChangeEntity = computed(() =>
  selection.value.length == 0
    ? (filter.value?.entityNavFullIds ?? []).length != 1
    : new Set(selection.value.map((i) => i.entity.id)).size > 1
)

const headerButtons = computed(() => {
  const res = []

  res.push({
    label: 'Supprimer',
    disabled: selection.value.length === 0,
    variant: 'outlined' as 'outlined' | 'filled' | undefined,
    color: 'red',
    onClick: deleteSimulations,
  })
  if (userStore.hasRole('ADMIN_PLUS')) {
    res.push({
      label: "Changer d'organisation",
      disabled: disableChangeEntity.value,
      onClick: () => {
        changeEntityDialog.value = true
      },
    })
  }
  res.push({
    onClick: exportOperations,
    loading: exportOperationsLoading.value,
    label: 'Exporter',
  })
  res.push({
    label: 'Personnaliser',
    onClick: () => setDrawer('column', drawer.value !== 'column'),
  })
  return res
})
</script>
