import type { LocalDate } from '../date'

export const NUMBER_OF_DAY_AFTER_EFFECTIVE_END_WORKS_FOR_INSTRUCTION_DELAY = 91
export const NUMBER_OF_DAY_BEFORE_PNCEE_EXPIRATION = 364

export const getInstructionDelay = (effectiveEndWorksDate: LocalDate) => {
  return (
    Math.round((parseLocalDate(effectiveEndWorksDate).getTime() - new Date().getTime()) / (1000 * 3600 * 24)) +
    NUMBER_OF_DAY_AFTER_EFFECTIVE_END_WORKS_FOR_INSTRUCTION_DELAY
  )
}
