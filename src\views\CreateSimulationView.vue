<template>
  <SimulationFormView v-if="props.standardizedOperationSheetId" :simulation="simulation" />
  <CreateSimulationOperationAllView v-else />
</template>

<script lang="ts" setup>
import SimulationFormView from '@/views/OperationFormPageView.vue'
import CreateSimulationOperationAllView from '@/views/CreateSimulationOperationAllView.vue'
import { makeEmptyOperation } from '@/types/operation'
import type { StandardizedOperationSheet } from '@/types/calcul/standardizedOperationSheet'

const props = defineProps({
  standardizedOperationSheetId: {
    type: Number,
  },
})

const simulation = ref(makeEmptyOperation())
const standardizedOperationSheet = ref(emptyValue<StandardizedOperationSheet>())

watch(
  () => props.standardizedOperationSheetId,
  () => {
    if (props.standardizedOperationSheetId) {
      handleAxiosPromise(
        standardizedOperationSheet,
        standardizedOperationSheetApi.findOne(props.standardizedOperationSheetId),
        (res) => {
          standardizedOperationSheet.value.value = res.data
          simulation.value.standardizedOperationSheet = standardizedOperationSheet.value.value
        }
      )
    }
  },
  {
    immediate: true,
  }
)
</script>
