<template>
  <NjDataTable
    :headers="headers"
    :pageable="pageable"
    :page="data.value!"
    :loading="data.loading"
    fixed
    @update:pageable="updatePageable"
  >
    <template #[`item.validateStep50MinDateTime`]="{ item }">
      <div v-if="item.validateStep50MinDateTime">
        {{ getMonth(item.validateStep50MinDateTime) }}
        {{ getYear(item.validateStep50MinDateTime) }}
      </div>
      <div v-else></div>
    </template>
  </NjDataTable>
</template>
<script setup lang="ts">
import { ceeStockMonthlyApi, type CeeStockMonthlyFilter } from '@/api/ceeStockMonthly'
import NjDataTable from '@/components/okta/NjDataTable.vue'
import type { CeeStockMonthly } from '@/types/ceeStockMonthly'
import { getMonth, getYear } from '@/types/date'

const props = defineProps({
  filter: Object as PropType<CeeStockMonthlyFilter>,
})

const headers = [
  {
    title: 'Organisation',
    value: 'id',
    formater: (item: CeeStockMonthly) => item.entity.name,
  },
  {
    title: 'Mois',
    value: 'validateStep50MinDateTime',
  },
  {
    title: 'Prime CEE Agence €',
    value: '',
    formater: (item: CeeStockMonthly) => formatPriceNumber(item.ceePrime),
    sortable: false,
    cellClass: 'text-right justify-end',
  },
  {
    title: 'Charge Cellule CEE €',
    value: '',
    formater: (item: CeeStockMonthly) => formatPriceNumber(item.ceeCharge),
    sortable: false,
    cellClass: 'text-right justify-end',
  },
  {
    title: 'Nb. Total kWhc',
    value: 'totalCumac',
    formater: (item: CeeStockMonthly) => formatKwhcNumber(item.classicCumac + item.precariousnessCumac),
    sortable: false,
    cellClass: 'text-right justify-end',
  },
  {
    title: 'Nb. Class. kWhc',
    value: 'classicCumac',
    formater: (_: any, value: number) => formatKwhcNumber(value),
    sortable: false,
    cellClass: 'text-right justify-end',
  },
  {
    title: 'Nb. Préc. kWhc',
    value: 'precariousnessCumac',
    formater: (_: any, value: number) => formatKwhcNumber(value),
    sortable: false,
    cellClass: 'text-right justify-end',
  },
]

const { data, pageable, updatePageable, updateFilter } = usePagination<CeeStockMonthly, CeeStockMonthlyFilter>(
  (filter, pageable) => ceeStockMonthlyApi.findAll(pageable, filter),
  props.filter
)

watch(
  () => props.filter,
  (v) => {
    updateFilter({ ...v })
  },
  {
    deep: true,
  }
)
</script>
