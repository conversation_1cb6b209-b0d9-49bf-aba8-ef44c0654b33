<template>
  <NjPage>
    <template #sub-header>
      <VRow>
        <VSpacer />
        <VCol class="flex-grow-0">
          <NjBtn @click="addConvention = true">Nouvelle convention</NjBtn>
        </VCol>
      </VRow>
      <VDialog v-model="addConvention" width="40%">
        <ConventionOneView :id="0" @cancel="addConvention = false" @update:model-value="reload" />
      </VDialog>
    </template>
    <template #body>
      <VRow class="flex-column">
        <VCol>
          <NjDataTable
            v-model:selections="selection"
            :pageable="pageable"
            :page="data.value!"
            :headers="headers"
            @update:pageable="updatePageable"
          />
        </VCol>
      </VRow>
      <VNavigationDrawer v-model="drawerOpened" location="right" width="600" permanent>
        <ConventionDetailsView v-if="!edit" :id="selection[0]?.id ?? 0">
          <template #title>
            <VRow no-gutter class="align-center">
              <VCol> Détails </VCol>
              <VCol class="flex-grow-0">
                <NjIconBtn
                  icon="mdi-pencil"
                  color="primary"
                  style="margin-inline: 4px"
                  rounded="0"
                  @click="edit = !edit"
                />
              </VCol>
              <VCol class="flex-grow-0">
                <NjIconBtn color="primary" icon="mdi-close" @click="drawerOpened = false" />
              </VCol>
            </VRow>
          </template>
        </ConventionDetailsView>
        <ConventionOneView v-else :id="selection[0]?.id ?? 0" expend @cancel="edit = false">
          <template #title>
            <VRow no-gutter class="align-center">
              <VCol> Édition de convention </VCol>
              <VCol class="flex-grow-0">
                <NjIconBtn
                  icon="mdi-pencil"
                  color="primary"
                  style="margin-inline: 4px"
                  rounded="0"
                  @click="edit = !edit"
                />
              </VCol>
              <VCol class="flex-grow-0">
                <NjIconBtn color="primary" icon="mdi-close" @click="drawerOpened = false" />
              </VCol>
            </VRow>
          </template>
        </ConventionOneView>
      </VNavigationDrawer>
    </template>
  </NjPage>
</template>
<script lang="ts" setup>
import { conventionTemplateApi } from '@/api/conventionTemplate'
import NjPage from '@/components/NjPage.vue'
import type { DataTableHeader } from '@/components/okta/NjDataTable.type'
import NjDataTable from '@/components/okta/NjDataTable.vue'
import type { ConventionTemplate } from '@/types/convention/conventionTemplate'
import { usePaginationInQuery } from '@/types/pagination'
import ConventionOneView from './ConventionOneView.vue'
import ConventionDetailsView from './ConventionDetailsView.vue'
const addConvention = ref(false)
const selection = ref<ConventionTemplate[]>([])
const drawerOpened = ref(false)

const { data, pageable, updatePageable, reload } = usePaginationInQuery(
  (_filter, pageable) => conventionTemplateApi.findAll(pageable),
  {
    defaultPageablePartial: {
      sort: ['name'],
    },
  }
)

const headers: DataTableHeader[] = [
  {
    title: 'Nom',
    value: 'name',
  },
]

watch(selection, (v) => {
  if (v) {
    drawerOpened.value = v.length > 0
    edit.value = false
  }
})

watch(drawerOpened, (v) => {
  if (!v) {
    selection.value = []
    reload()
  }
})

const edit = ref(false)
</script>
