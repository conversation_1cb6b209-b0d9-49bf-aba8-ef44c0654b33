<template>
  <NjExpansionPanel v-bind="$attrs">
    <template #title>
      <div class="d-flex full-width align-center">
        <div class="flex-grow-1">Chantiers</div>
        <div v-if="edit && !isCertynergie()" class="flex-grow-1 text-end text-no-wrap">
          <VLink
            size="small"
            icon="mdi-format-list-bulleted"
            style="font-weight: initial; font-size: initial"
            @click.stop="openWorksDialog"
            >Chantiers</VLink
          >
        </div>
      </div>
    </template>
    <VRow class="flex-column" dense>
      <VCol v-if="operation.works1">
        <WorksDisplayValue :model-value="operation.works1" :deletable="edit" @delete="emit('delete', 1)" />
      </VCol>
      <VCol v-if="operation.works2">
        <WorksDisplayValue :model-value="operation.works2" :deletable="edit" @delete="emit('delete', 2)" />
      </VCol>
      <VCol v-if="operation.works3">
        <WorksDisplayValue :model-value="operation.works3" :deletable="edit" @delete="emit('delete', 3)" />
      </VCol>
      <VCol v-if="!operation.works3 && !operation.works2 && !operation.works1"
        ><i style="font-size: 1rem">Aucun chantier sélectionné</i>
        <VInput v-if="requiredWorks" :model-value="operation.works1" :rules="[requiredRule]"></VInput>
      </VCol>
      <VCol v-if="!hideWorksType">
        <NjDisplayValue
          label="Type de Chantier"
          :value="worksTypes.find((i) => i.value == operation.finalWorksType)?.title ?? ''"
        >
          <template v-if="edit" #value>
            <div class="w-50">
              <VSelect
                :items="worksTypes"
                :model-value="operation.finalWorksType"
                :rules="[requiredRule]"
                @update:model-value="emit('update:finalWorksType', $event)"
              >
              </VSelect>
            </div>
          </template>
        </NjDisplayValue>
      </VCol>
      <VCol v-if="!hideSelfWorks">
        <NjDisplayValue label="Travaux en propre" :value="operation.selfWorks ? 'OUI' : 'NON'">
          <template v-if="edit" #value>
            <div class="w-50">
              <NjSwitch
                :model-value="operation.selfWorks"
                :disabled="operation.operationsGroup && operation.stepId > 30"
                @update:model-value="emit('update:self-works', $event)"
              />
            </div>
          </template>
        </NjDisplayValue>
        <ErrorAlert
          v-if="operation.operationsGroup && operation.stepId > 30"
          type="info"
          message="Le statut de travaux en propre doit être géré à partir du regroupement après l'étape 30"
        ></ErrorAlert>
      </VCol>
    </VRow>
  </NjExpansionPanel>
  <CardDialog v-model="worksDialog" title="Choix des chantiers" fixed>
    <VRow class="flex-column h-100">
      <VCol v-if="selectedWorks.length > 3" class="flex-grow-0">
        <VAlert type="error">Vous ne pouvez sélectionner que 3 chantiers maximum.</VAlert>
      </VCol>
      <VCol class="flex-grow-0">
        <VRow>
          <VCol cols="4" class="d-flex">
            <SearchInput v-model="filter.search" :loading="worksLoading">
              <template v-if="!filter.propertyCode && !entityFilterForWorks" #append-inner>
                <VTooltip
                  text="Nous vous conseillons de saisir un numéro d'installation ou ajouter un filtre par organisation, auquel cas la recherche sera longue."
                >
                  <template #activator="{ props }">
                    <VIcon v-bind="props">mdi-alert</VIcon>
                  </template>
                </VTooltip>
              </template>
            </SearchInput>
          </VCol>
          <VCol>
            <VTextField v-model="filter.propertyCode" label="Code installation" clearable />
          </VCol>
          <VCol>
            <VSelect v-model="filter.workType" label="Type chantier" :items="worksTypes.slice(0, 3)" clearable />
          </VCol>
          <VCol>
            <VSelect v-model="filter.closed" label="Etat chantier" :items="cloturedItems" clearable />
          </VCol>
          <VCol>
            <RemoteAutoComplete
              v-model="entityFilterForWorks"
              label="Sélectionnez une organisation"
              item-title="name"
              chips
              closable-chips
              :query-for-all="
                (v, pageable) =>
                  entityApi.getAll(
                    {
                      startWithNavFullIds: userStore.currentUser.entities.map((it) => it.navFullId),
                      search: v,
                      visible: true,
                    },
                    { ...pageable, sort: ['name,ASC'] }
                  )
              "
              :query-for-one="(v) => entityApi.getOne(v)"
              return-object
              multiple
              infinite-scroll
            />
          </VCol>
        </VRow>
      </VCol>
      <VCol>
        <WorksAllView v-model:selections="selectedWorks" v-model:loading="worksLoading" :filter="filter" />
      </VCol>
    </VRow>
    <template #actions>
      <NjBtn variant="outlined" @click="closeWorksDialog">Annuler</NjBtn>
      <NjBtn :disabled="selectedWorks.length > 3" @click="selectWorks">Valider</NjBtn>
    </template>
  </CardDialog>
</template>

<script setup lang="ts">
import { entityApi } from '@/api/entity'
import type { GtfTraWorkFilter } from '@/api/external/gtf/gtfTra'
import NjSwitch from '@/components/NjSwitch.vue'
import { useUserStore } from '@/stores/user'
import type { Entity } from '@/types/entity'
import type { Operation } from '@/types/operation'
import { requiredRule } from '@/types/rule'
import { type Works, type WorksType, worksTypes } from '@/types/works'
import { last } from 'lodash'
import type { PropType } from 'vue'
import WorksAllView from '../WorksAllView.vue'
import WorksDisplayValue from './WorksDisplayValue.vue'

import { isCertynergie } from '@/types/debug'

const props = defineProps({
  edit: Boolean,
  requiredWorks: Boolean,
  operation: {
    type: Object as PropType<
      Pick<
        Operation,
        'stepId' | 'finalWorksType' | 'works1' | 'works2' | 'works3' | 'operationsGroup' | 'selfWorks' | 'entity' | 'id'
      > &
        Partial<Pick<Operation, 'property'>>
    >,
    default: makeEmptyOperation,
  },
  hideWorksType: Boolean,
  hideSelfWorks: Boolean,
})
const emit = defineEmits<{
  selected: [Works[]]
  delete: [1 | 2 | 3]
  'update:finalWorksType': [WorksType]
  'update:self-works': [boolean]
}>()
const userStore = useUserStore()

// Chantiers
const worksDialog = ref(false)
const selectedWorks = ref<Works[]>([])
const worksLoading = ref(false)

watch([() => props.operation.property?.code, worksDialog], ([propertyCode, modelValue]) => {
  if (modelValue) {
    filter.value.propertyCode = propertyCode
  }
})

const openWorksDialog = () => {
  worksDialog.value = true
  selectedWorks.value = [props.operation.works1, props.operation.works2, props.operation.works3].filter(
    (it) => it != null
  ) as Works[]
}
const closeWorksDialog = () => {
  worksDialog.value = false
}
const selectWorks = () => {
  closeWorksDialog()
  emit('selected', selectedWorks.value)
}

const entityFilterForWorks = ref<Entity[]>([])
watch(
  () => worksDialog.value,
  (v) => {
    if (v) {
      const result: Entity[] = []

      if (props.operation.id > 0) {
        result.push(props.operation.entity)
      }

      if (result.length === 0) {
        for (const it of userStore.currentUser.entities.sort((a, b) => a.navFullId.localeCompare(b.navFullId))) {
          if (result.length === 0 || !it.navFullId.startsWith(last(result)!.navFullId)) {
            result.push(it)
          }
        }
      }
      entityFilterForWorks.value = result
    }
  }
)

watch(entityFilterForWorks, (v) => {
  filter.value.entityNavFullIds = v.map((it) => it.navFullId)
})

const filter = ref<GtfTraWorkFilter>({
  search: '',
  propertyCode: undefined,
  entityNavFullIds: entityFilterForWorks.value.map((it) => it.navFullId),
})

const cloturedItems = ref([
  {
    title: 'Non cloturés',
    value: false,
  },
  {
    title: 'Cloturés',
    value: true,
  },
])
</script>
