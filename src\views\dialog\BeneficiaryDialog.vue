<template>
  <VDialog
    v-model="modelValue"
    :width="beneficiaryMode === 'select' ? '90%' : '40%'"
    :height="beneficiaryMode !== 'display' ? '100%' : ''"
  >
    <BeneficiaryForm
      v-if="beneficiaryMode === 'form'"
      v-model:updated-beneficiary="updatedBeneficiary"
      :beneficiary="formBeneficiary"
      :mode="beneficiaryFormMode"
      :cancel="hideBeneficiaryForm"
      :after-success="hideBeneficiaryForm"
      :can-certified="canCertified"
    />
    <VCard v-else-if="beneficiaryMode === 'display'" :border="false">
      <VCardTitle>
        <div class="d-flex align-center">
          Détail d'un Bénéficiaire
          <VSpacer />
          <NjIconBtn icon="mdi-close" rounded="0" @click="closeBeneficiaryDialog" />
        </div>
      </VCardTitle>
      <BeneficiaryDisplayValue :model-value="localSelected[0]" expanded />
      <VCardActions>
        <VSpacer />
        <NjBtn @click="beneficiaryMode = 'select'">Ok</NjBtn>
      </VCardActions>
    </VCard>
    <VCard v-else class="content-layout">
      <VCardTitle class="content-layout__header">
        <div class="d-flex">
          Choix d'un Bénéficiaire
          <VSpacer />
          <NjIconBtn icon="mdi-close" rounded="0" @click="closeBeneficiaryDialog" />
        </div>
        <VDivider />
      </VCardTitle>
      <VCardText class="content-layout__main content-layout overflow-hidden">
        <VRow class="flex-column content-layout__main content-layout">
          <VCol class="content-layout__header">
            <VRow>
              <VCol>
                <SearchInput v-model="searchBeneficiary" :loading="beneficiaryLoading" />
              </VCol>
              <VSpacer />
              <VCol class="flex-grow-0">
                <NjBtn :disabled="!localSelected[0]" @click="duplicateBeneficiary"> Dupliquer </NjBtn>
              </VCol>
              <VCol class="flex-grow-0">
                <NjBtn :disabled="!canEditBeneficiary" @click="editBeneficiary"> Editer </NjBtn>
              </VCol>
              <VCol class="flex-grow-0">
                <NjBtn :disabled="!localSelected[0]" @click="beneficiaryMode = 'display'"> Visualiser </NjBtn>
              </VCol>
              <VCol class="flex-grow-0">
                <NjBtn @click="createBeneficiary"> Créer </NjBtn>
              </VCol>
            </VRow>
          </VCol>
          <VCol class="content-layout__main content_layout">
            <BeneficiaryAllView
              v-model:selections="localSelected"
              v-model:loading="beneficiaryLoading"
              :search="searchBeneficiary"
              fixed
            />
          </VCol>
        </VRow>
      </VCardText>
      <VCardActions class="content-layout__footer">
        <VSpacer />
        <NjBtn variant="outlined" @click="closeBeneficiaryDialog">Annuler</NjBtn>
        <NjBtn :disabled="!localSelected[0]" @click="selectBeneficiary">Enregistrer</NjBtn>
      </VCardActions>
    </VCard>
  </VDialog>
</template>
<script setup lang="ts">
import { useUserStore } from '@/stores/user'
import { type Beneficiary, makeEmptyBeneficiary } from '@/types/beneficiary'
import BeneficiaryAllView from '@/views/BeneficiaryAllView.vue'
import type { PropType } from 'vue'
import type { BeneficiaryFormAction } from '../BeneficiaryForm.vue'
import BeneficiaryForm from '../BeneficiaryForm.vue'

const modelValue = defineModel<boolean>()

const props = defineProps({
  selected: {
    type: Array as PropType<Beneficiary[]>,
  },
  canCertified: {
    type: Boolean,
  },
})

const emits = defineEmits<{
  'update:selected': [Beneficiary[]]
}>()

watch(modelValue, (v) => {
  if (v) {
    beneficiaryMode.value = 'select'
    localSelected.value = props.selected ?? []
  }
})

watch(
  () => props.selected,
  (v) => {
    localSelected.value = v ?? []
  }
)

const searchBeneficiary = ref<string>('')
const beneficiaryLoading = ref(false)

const localSelected = ref<Beneficiary[]>([])
const formBeneficiary = ref<Beneficiary>()

const hideBeneficiaryForm = () => {
  beneficiaryMode.value = 'select'
  if (localSelected.value[0] && updatedBeneficiary.value) {
    localSelected.value[0] = updatedBeneficiary.value
  }
}

const createBeneficiary = () => {
  formBeneficiary.value = makeEmptyBeneficiary()
  showBeneficiaryForm('create')
}

const editBeneficiary = () => {
  formBeneficiary.value = localSelected.value[0] ?? makeEmptyBeneficiary()
  updatedBeneficiary.value = localSelected.value[0] ?? null
  showBeneficiaryForm('edit')
}

const duplicateBeneficiary = () => {
  formBeneficiary.value = {
    ...localSelected.value[0],
    certified: false,
    phoneNumber: '',
    email: '',
    lastName: '',
    firstName: '',
    capacity: '',
  }
  showBeneficiaryForm('duplicate')
}

const selectBeneficiary = () => {
  emits('update:selected', localSelected.value)
  closeBeneficiaryDialog()
}

const userStore = useUserStore()

const canEditBeneficiary = computed(() => {
  if (localSelected.value[0]) {
    if (localSelected.value[0].certified) {
      return userStore.isSiege
    } else {
      return true
    }
  }
  return false
})

const closeBeneficiaryDialog = () => {
  modelValue.value = false
}

const beneficiaryMode = ref('select')

const beneficiaryFormMode = ref<BeneficiaryFormAction>('create')
const showBeneficiaryForm = (mode: BeneficiaryFormAction) => {
  beneficiaryFormMode.value = mode
  beneficiaryMode.value = 'form'
}

const updatedBeneficiary = ref<Beneficiary | null>(null)
watch(updatedBeneficiary, (v) => {
  if (v && props.selected && props.selected.length > 0 && v.id == props.selected[0].id) {
    emits('update:selected', [v])
  }
})
</script>
