<template>
  <CardDialog
    :model-value="modelValue"
    title="Choix d'une installation"
    fixed
    @update:model-value="emits('update:model-value', $event)"
  >
    <VRow class="flex-column h-100">
      <VCol class="flex-grow-0">
        <VRow>
          <VCol cols="6" class="d-flex">
            <SearchInput v-model="searchProperty" :loading="propertyLoading" />
          </VCol>
          <VCol>
            <RemoteAutoComplete
              v-model="entityFilterForProperties"
              label="Sélectionnez une organisation"
              item-title="name"
              chips
              closable-chips
              :query-for-all="
                (v, pageable) =>
                  entityApi.getAllPublic(
                    {
                      startWithNavFullIds: entityAllowedToFilterIn.map((it) => it.navFullId),
                      search: v,
                      visible: true,
                    },
                    { ...pageable, sort: ['name,ASC'] }
                  )
              "
              :query-for-one="(v) => entityApi.getOne(v)"
              return-object
              multiple
              :rules="[requiredRule]"
              infinite-scroll
              :max-elements="5"
            />
          </VCol>
        </VRow>
      </VCol>
      <VCol>
        <PropertyAllView
          v-model:selections="selectedProperty"
          v-model:loading="propertyLoading"
          :search="searchProperty"
          :nav-full-id="entityFilterForProperties.map((it) => it.navFullId)"
        />
      </VCol>
    </VRow>
    <template #actions>
      <NjBtn variant="outlined" @click="closePropertyDialog">Annuler</NjBtn>
      <NjBtn :disabled="!selectedProperty[0]" @click="selectProperty">Valider</NjBtn>
    </template>
  </CardDialog>
</template>

<script setup lang="ts">
import { entityApi } from '@/api/entity'
import { useUserStore } from '@/stores/user'
import type { Entity } from '@/types/entity'
import type { Property } from '@/types/property'
import { requiredRule } from '@/types/rule'
import { last } from 'lodash'
import PropertyAllView from '../PropertyAllView.vue'
import type { PropType } from 'vue'

const props = defineProps({
  modelValue: {
    type: Boolean,
    required: true,
  },
  selected: {
    type: Array as PropType<Property[]>,
  },
  canCertified: {
    type: Boolean,
  },
  operationEntity: Object as PropType<Entity>,
})

const emits = defineEmits<{
  'update:model-value': [boolean]
  'update:selected': [Property[]]
}>()

const userStore = useUserStore()

watch(
  () => props.modelValue,
  (v) => {
    if (v) {
      searchProperty.value = ''
      selectedProperty.value = []
    }
  }
)

// Installation
const selectedProperty = ref<Property[]>([])
const searchProperty = ref<string>('')
const propertyLoading = ref(false)
const entityFilterForProperties = ref<Entity[]>([])
onMounted(() => {
  const result: Entity[] = []
  for (const it of userStore.currentUser.entities.sort((a, b) => a.navFullId.localeCompare(b.navFullId))) {
    if (result.length === 0 || !it.navFullId.startsWith(last(result)!.navFullId)) {
      result.push(it)
    }
  }
  entityFilterForProperties.value = result
})

const entityAllowedToFilterIn = computed(() => {
  let res: Entity[] = []
  if (userStore.currentUser.entities) {
    res = res.concat(userStore.currentUser.entities)
  }
  if (props.operationEntity) {
    res.push(props.operationEntity)
  }
  return res
})

// const usingSameAddressProperty = ref(false)
watch(
  () => props.modelValue,
  (v) => {
    if (v) {
      let res: Entity[] = []

      if (props.operationEntity?.id) {
        res = [props.operationEntity]
      }
      if (userStore.currentUser.entities && res.length === 0) {
        res = res.concat(userStore.currentUser.entities)
      }
      entityFilterForProperties.value = res
    }
  },
  {
    deep: true,
  }
)

const closePropertyDialog = () => {
  emits('update:model-value', false)
}

// watch(
//   () => mutableOperation.value.property,
//   (v) => {
//     if (v) {
//       selectedEntity.value = v?.entity
//     }
//     if (v?.entity) {
//       mutableOperation.value.entity = v.entity
//       if (mutableOperation.value.id == 0) {
//         mutableOperation.value.instructor = v.entity.entityDetails.effectiveInstructor ?? makeEmptyUser()
//       }
//     }
//   }
// )

const selectProperty = () => {
  closePropertyDialog()
  emits('update:selected', selectedProperty.value)
}
</script>
