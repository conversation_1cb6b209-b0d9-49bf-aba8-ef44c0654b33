<template>
  <VRow class="flex-column" dense>
    <VCol>
      <VTextField
        v-if="edit"
        :model-value="operation.simulationName"
        label="Nom de la simulation"
        :rules="[requiredRule]"
        @update:model-value="emit('update:operation', { ...operation, simulationName: $event })"
      />
      <NjDisplayValue v-else label="Nom" :value="operation.simulationName" />
    </VCol>
    <VCol>
      <NjDisplayValue
        label="Créateur"
        :value="displayFullnameUser(operation.id ? operation.creationUser : userStore.currentUser)"
      />
    </VCol>
    <VCol>
      <NjDisplayValue label="Numéro" :value="operation.id ? operation.id : undefined" />
    </VCol>
    <VCol>
      <NjDisplayValue
        label="Date de création"
        :value="formatHumanReadableLocalDate(operation.creationDateTime || new Date().toISOString())"
      />
    </VCol>
    <VCol v-if="operation.stepId === 0">
      <NjSwitch
        v-if="edit"
        :model-value="operation.toProcess"
        @update:model-value="emit('update:operation', { ...operation, toProcess: $event })"
      >
        <template #label>
          <div class="value__label">À traiter</div>
        </template>
      </NjSwitch>
      <NjDisplayValue v-else label="À traiter" :value="operation.toProcess ? 'OUI' : 'NON'" />
    </VCol>
  </VRow>
</template>
<script lang="ts" setup>
import type { Operation } from '@/types/operation'
import type { PropType } from 'vue'
import { displayFullnameUser } from '@/types/user'
import NjDisplayValue from '@/components/NjDisplayValue.vue'
import { formatHumanReadableLocalDate } from '@/types/date'
import { useUserStore } from '@/stores/user'
import { requiredRule } from '@/types/rule'

defineProps({
  operation: {
    type: Object as PropType<Operation>,
    default: makeEmptyOperation,
  },
  edit: Boolean,
})

const emit = defineEmits<{
  'update:operation': [value: Operation]
}>()

const userStore = useUserStore()
</script>
