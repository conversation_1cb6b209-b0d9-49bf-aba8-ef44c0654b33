import type { Page, Pageable } from '@/types/pagination'
import type {
  SwornStatementTemplateFrame,
  SwornStatementTemplateFrameRequest,
} from '@/types/swornStatementTemplateFrame'
import type { AxiosInstance, AxiosPromise } from 'axios'
import axiosInstance from '.'

export type SwornStatementTemplateFrameFilter = Partial<{
  search: string
  operationId: number
}>

const url = '/sworn_statement_template_frames'
class SwornStatementTemplateFrameApi {
  public constructor(private axios: AxiosInstance) {}

  public getAll(
    pageable: Pageable,
    filter: SwornStatementTemplateFrameFilter
  ): AxiosPromise<Page<SwornStatementTemplateFrame>> {
    return this.axios.get(url, {
      params: { ...pageable, ...filter },
    })
  }

  public async create(
    templateFrameRequest: SwornStatementTemplateFrameRequest,
    templateFile: File | null
  ): AxiosPromise<SwornStatementTemplateFrame> {
    const formData = new FormData()
    if (templateFile) {
      formData.append('templateFile', templateFile, templateFile.name)
      const hash = await hashFile(templateFile)
      formData.append('hash', hash)
    }

    const request = JSON.stringify(engieFormatRequestTransformKey(templateFrameRequest))
    const blob = new Blob([request], {
      type: 'application/json',
    })

    formData.append('request', blob)

    return this.axios.post(url, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    })
  }

  public async update(
    id: number,
    templateFrameRequest: SwornStatementTemplateFrameRequest,
    templateFile: File | null
  ): AxiosPromise<SwornStatementTemplateFrame> {
    const formData = new FormData()
    if (templateFile) {
      formData.append('templateFile', templateFile, templateFile.name)
      const hash = await hashFile(templateFile)
      formData.append('hash', hash)
    }

    const request = JSON.stringify(engieFormatRequestTransformKey(templateFrameRequest))
    const blob = new Blob([request], {
      type: 'application/json',
    })

    formData.append('request', blob)

    return this.axios.put(`${url}/${id}`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    })
  }
  public mergeTemplateFrame(ids: number[]): AxiosPromise<File> {
    return this.axios.post(`${url}/merge`, ids, {
      responseType: 'blob',
    })
  }

  public downloadTemplate(id: number): AxiosPromise<File> {
    return this.axios.get(`${url}/${id}/file`, {
      responseType: 'blob',
    })
  }

  public delete(ids: number[]) {
    return this.axios.delete(`${url}`, {
      params: { ids },
    })
  }
}

export const swornStatementTemplateFrameApi = new SwornStatementTemplateFrameApi(axiosInstance)
