import type { Address } from './address'

export const worksTypes = [
  {
    value: 'P3',
    title: 'P3',
  },
  {
    value: 'P5',
    title: 'P5',
  },
  {
    value: 'P7',
    title: 'P7',
  },
  {
    value: 'P3_P5',
    title: 'P3 + P5',
  },
  {
    value: 'P3_P7',
    title: 'P3 + P7',
  },
] as const

export type WorksType = (typeof worksTypes)[number]['value']

export interface Works {
  id: number
  worksType: string
  propertyCode?: string
  address?: Address
  closed?: boolean
}
