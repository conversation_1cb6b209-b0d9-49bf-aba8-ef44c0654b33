<template>
  <VDialog v-model="activeDialog" width="60%">
    <VCard>
      <VCardTitle class="d-flex align-center">
        <span class="d-flex w-100">
          {{ !localValuation.id ? 'Nouvelle' : 'Édition de ' }} valorisation
          {{ localValuation.precariousness ? 'précarité' : 'classique' }}
        </span>
        <NjIconBtn icon="mdi-window-close" color="primary" @click="activeDialog = false" />
      </VCardTitle>
      <VCardText>
        <VForm ref="formRef" @submit.prevent>
          <VRow class="d-flex align-center">
            <VCol v-if="!localValuation.id">
              <VSelect
                v-model="localValuation.type"
                label="Type"
                :items="activeTypes.content"
                item-title="name"
                return-object
                :rules="[requiredRule]"
              />
            </VCol>
            <VCol>
              <NjDatePicker
                v-if="!localValuation.id"
                v-model="localValuation.startDate"
                label="Date de validité"
                :rules="[requiredRule]"
              />
              <NjDisplayValue v-else label="Date de début" :value="localValuation.startDate" />
            </VCol>
            <VCol>
              <VTextField
                v-model="localValuation.value"
                type="number"
                :label="'Valorisation ' + (localValuation.precariousness ? 'précarité' : 'classisque')"
                suffix="€"
                :rules="modelValue ? [positiveNumericRuleGenerator()] : []"
              />
            </VCol>
          </VRow>
        </VForm>
      </VCardText>
      <VCardActions>
        <VSpacer />
        <NjBtn variant="outlined" @click="activeDialog = false"> Annuler </NjBtn>
        <NjBtn :disabled="disabledRule" @click="save">Sauvegarder</NjBtn>
      </VCardActions>
    </VCard>
  </VDialog>
</template>
<script lang="ts" setup>
import { makeEmptyValuation, type Valuation, type ValuationType, mapToRequest } from '@/types/valuation'
import { requiredRule, positiveNumericRuleGenerator } from '@/types/rule'
import NjDatePicker from '@/components/NjDatePicker.vue'
import { useSnackbarStore } from '@/stores/snackbar'
import type { Page } from '@/types/pagination'
import type { VForm } from 'vuetify/components'
import { valuationApi } from '@/api/valuation'
import type { PropType } from 'vue'
import NjDisplayValue from '@/components/NjDisplayValue.vue'

const props = defineProps({
  modelValue: Boolean,
  valuation: {
    type: Object as PropType<Valuation>,
    required: true,
  },
  activeTypes: {
    type: Object as PropType<Page<ValuationType>>,
    default: () => makeEmptyPage(),
  },
})

const emit = defineEmits(['update:model-value', 'save'])

const activeDialog = computed<boolean>({
  get() {
    return props.modelValue
  },
  set(v) {
    emit('update:model-value', v)
  },
})

const snackbarStore = useSnackbarStore()
const localValuation = ref(makeEmptyValuation())
const formRef = ref<VForm | null>(null)
const saving = ref(emptyValue<Valuation>())
const save = async () => {
  if ((await formRef.value!.validate()).valid) {
    if (localValuation.value.id) {
      handleAxiosPromise(saving, valuationApi.update(localValuation.value.id, mapToRequest(localValuation.value)), {
        afterSuccess: () => {
          snackbarStore.setSuccess('La valorisation a bien été modifiée')
          emit('update:model-value', false)
          emit('save')
        },
        afterError: () => {
          snackbarStore.setError('Une erreur est survenue lors de la modification de la valorisation')
        },
      })
    } else {
      handleAxiosPromise(saving, valuationApi.create(mapToRequest(localValuation.value)), {
        afterSuccess: () => {
          snackbarStore.setSuccess('La valorisation a bien été créée')
          emit('update:model-value', false)
          emit('save')
        },
        afterError: () => {
          snackbarStore.setError('Une erreur est survenue lors de la création de la valorisation')
        },
      })
    }
  }
}

const disabledRule = computed(
  () => !localValuation.value.type || !localValuation.value.startDate || localValuation.value.value <= 0
)

watch(
  () => props.valuation,
  (v) => (localValuation.value = v),
  {
    immediate: true,
  }
)
</script>
