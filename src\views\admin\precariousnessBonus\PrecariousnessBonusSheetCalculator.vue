<template>
  <VRow class="flex-column" dense>
    <template v-if="mode !== 'display'">
      <VCol>
        <NjSelect
          :items="precariousnessBonusSheet.casList"
          return-object
          :model-value="value.casePrecariousnessBonusSheet"
          item-title="label"
          label="Cas de précarité"
          placeholder="Choisissez le cas de bonification de précarité"
          required
          @update:model-value="updateCasePrecariousnessBonusSheet"
        />
      </VCol>
      <VCol>
        <VRow>
          <VCol
            v-for="(parameter, index) in requiredParameters"
            :key="index"
            cols="12"
            :md="mode === 'preview' ? 6 : undefined"
          >
            <NjSelect
              v-if="parameter.type === 'CHOICE'"
              v-model="value.values[parameter.id]"
              :label="parameter.label"
              :items="parameter.data?.split(';')"
              :rules="[requiredRule]"
              required
            />
            <NjTextField
              v-else-if="parameter.type === 'NUMBER'"
              v-model="value.values[parameter.id]"
              :label="parameter.label"
              :suffix="parameter.suffix"
              :rules="[requiredRule, numericRule]"
              type="number"
              required
            />
          </VCol>
          <VCol
            v-for="(parameter, index) in optionalParameters"
            :key="index"
            cols="12"
            :md="mode === 'preview' ? 6 : undefined"
          >
            <NjSelect
              v-if="parameter.type === 'CHOICE'"
              v-model="value.values[parameter.id]"
              :label="parameter.label"
              :items="parameter.data?.split(';')"
              :rules="[requiredRule]"
              recommended
            />
            <NjTextField
              v-else-if="parameter.type === 'NUMBER'"
              v-model="value.values[parameter.id]"
              :label="parameter.label"
              :suffix="parameter.suffix"
              :rules="[requiredRule, numericRule]"
              type="number"
              recommended
            />
          </VCol>
        </VRow>
      </VCol>
    </template>
    <template v-else>
      <VCol>
        <NjDisplayValue label="" :value="value.casePrecariousnessBonusSheet?.label" />
      </VCol>
      <VCol>
        <VRow class="flex-column" dense>
          <VCol v-for="(parameter, index) in requiredParameters" :key="index">
            <NjDisplayValue
              :label="parameter.label"
              :value="value.values[parameter.id] + (parameter.suffix ? ' ' + parameter.suffix : '')"
            />
          </VCol>
          <VCol v-for="(parameter, index) in optionalParameters" :key="index">
            <NjDisplayValue
              :label="parameter.label"
              :value="value.values[parameter.id] + (parameter.suffix ? ' ' + parameter.suffix : '')"
            />
          </VCol>
        </VRow>
      </VCol>
    </template>

    <VCol v-if="value.classicalCee.length > 1">
      <VRow no-gutters class="flex-column">
        <!-- eslint-disable-next-line vue/valid-v-for -->
        <VCol v-for="(_, i) in value.classicalCee">
          <NjDisplayValue
            :label="'Opération n°' + (i + 1)"
            :value="
              formatNumber(value.classicalCee[i].value!) +
              ' kWhc cla. /' +
              formatNumber(value.precariousnessCee[i].value!) +
              ' kWhc préc.'
            "
          />
        </VCol>
      </VRow>
    </VCol>
    <VCol v-if="mode === 'preview'">
      <VRow>
        <VCol cols="12" md="6">
          <VTextField
            readonly
            :model-value="totalClassicCee"
            label="CEE Classique"
            suffix="kWh cumac"
            :error-messages="errorClassicCee"
          />
        </VCol>
        <VCol cols="12" md="6">
          <VTextField
            readonly
            :model-value="totalPrecariousnessCee"
            label="CEE Précarité"
            suffix="kWh cumac"
            :error-messages="errorPrecariousnessCee"
          />
        </VCol>
      </VRow>
    </VCol>
    <VCol v-else>
      <VRow class="flex-column" dense>
        <VCol>
          <VInput
            :model-value="totalClassicCee"
            :rules="[
              positiveOrNullNumericRuleGenerator(
                'Le total ne doit pas être négatif. Veuillez vérifiez votre saisie ou contacter l\'administrateur'
              ),
            ]"
            hide-details="auto"
          >
            <NjDisplayValue
              label="Total CEE classique"
              :value="formatNumber(!isNaN(totalClassicCee ?? 0) ? (totalClassicCee ?? 0) : 0) + ' kWhc'"
              color-value="primary"
              align="end"
            />
            <template #message="{ message }">
              <div class="text-end">
                {{ message }}
              </div>
            </template>
          </VInput>
        </VCol>
        <VCol>
          <VInput
            :model-value="totalPrecariousnessCee"
            :rules="[
              positiveOrNullNumericRuleGenerator(
                'Le total ne doit pas être négatif. Veuillez vérifiez votre saisie ou contacter l\'administrateur'
              ),
            ]"
            hide-details="auto"
          >
            <NjDisplayValue
              label="Total CEE précarité"
              :value="formatNumber(!isNaN(totalPrecariousnessCee ?? 0) ? (totalPrecariousnessCee ?? 0) : 0) + ' kWhc'"
              color-value="primary"
              align="end"
            />
            <template #message="{ message }">
              <div class="text-end">
                {{ message }}
              </div>
            </template>
          </VInput>
        </VCol>
      </VRow>
    </VCol>
    <VCol v-if="activeErrorMessages.length != 0" class="d-flex justify-end">
      <VInput
        class="flex-grow-0"
        :rules="[() => false]"
        :error-messages="activeErrorMessages.join(', ')"
        :model-value="[...activeErrorMessages]"
      />
    </VCol>
  </VRow>
</template>

<script setup lang="ts">
import NjDisplayValue from '@/components/NjDisplayValue.vue'
import { evaluateFormula, parsingFormula, useFormulaEvaluator } from '@/types/calcul/formula'
import { generateMappingTableCombinaisons } from '@/types/calcul/mappingTable'
import { predefinedParameters, type ParameterFormula } from '@/types/calcul/parameterFormula'
import type { CasePrecariousnessBonusSheet, PrecariousnessBonusSheet } from '@/types/precariousnessBonus'
import { requiredRule, numericRule, positiveOrNullNumericRuleGenerator } from '@/types/rule'
import { formatNumber } from '@/types/format'
import { isEqual } from 'lodash'
import type { PropType } from 'vue'
import type { PromisableValue } from '@/types/promisableValue'
import { VCol, VInput } from 'vuetify/components'

const props = defineProps({
  precariousnessBonusSheet: {
    type: Object as PropType<PrecariousnessBonusSheet>,
    required: true,
  },
  predefinedValues: {
    type: Object,
    default: () => ({}),
  },
  values: {
    type: Object,
    default: () => ({}),
  },
  cumacs: {
    type: Array as PropType<number[]>,
    // required: true,
    default: () => [0],
  },
  mode: {
    type: String as PropType<'preview' | 'edit' | 'display'>,
    default: 'edit',
  },
  // ceeClassic: Number,
  // ceePrecariousness: Number,
})

const emit = defineEmits<{
  'update:values': [any]
  'update:ceeClassic': [number[]]
  'update:ceePrecariousness': [number[]]
}>()

const value = ref({
  casePrecariousnessBonusSheet: undefined as undefined | CasePrecariousnessBonusSheet,
  classicalCee: [emptyValue<number>()],
  precariousnessCee: [emptyValue<number>()],
  parameters: [] as ParameterFormula[],
  values: {} as any,
})

const previewParameters = computed((): ParameterFormula[] => {
  let p = props.mode === 'preview' ? predefinedParameters.concat() : []
  p = p.concat(props.precariousnessBonusSheet.parameters)
  if (value.value.casePrecariousnessBonusSheet) {
    p.push(...value.value.casePrecariousnessBonusSheet.parameters)
  }
  p = p.filter((it) => !value.value.casePrecariousnessBonusSheet?.computedParameters.find((cp) => cp.id === it.id))
  return p
})

const filteredParametersForMappingTable = computed(() => {
  return predefinedParameters.concat(props.precariousnessBonusSheet.parameters).filter((it) => it.type === 'CHOICE')
})

const { formulas, updateFormula } = useFormulaEvaluator()

watchEffect(() => {
  updateFormula('classical', props.precariousnessBonusSheet.ceeClassicalFormula)
})
watchEffect(() => {
  updateFormula('precariousness', props.precariousnessBonusSheet.ceePrecariteFormula)
})

watchEffect(() => {
  props.precariousnessBonusSheet.formulaValidationRules.forEach((value, index) => {
    updateFormula('error_message_' + index, value.formula)
  })
})

const combinaisons = computed(() => {
  return props.precariousnessBonusSheet.mappingTables.map((it) => {
    return generateMappingTableCombinaisons(it, filteredParametersForMappingTable.value)
  })
})

const errorMessagesEvaluation = ref<PromisableValue<number>[]>([])
const activeErrorMessages = computed(() => {
  errorMessagesEvaluation.value
  return props.precariousnessBonusSheet.formulaValidationRules
    .filter(
      (rule, index) =>
        errorMessagesEvaluation.value[index] &&
        !errorMessagesEvaluation.value[index].loading &&
        (errorMessagesEvaluation.value[index].value ?? 0) <= 0
    )
    .map((value) => value.errorMessage)
})

watch(
  () => props.values,
  (v, oldV) => {
    if (!isEqual(v, oldV) && !isEqual(v, value.value.values)) {
      value.value.values = { ...v }
    }
  },
  {
    immediate: true,
  }
)

watchEffect(() => {
  const results = props.cumacs.map((it) => {
    const values = { ...props.predefinedValues, ...value.value.values }
    if (props.mode != 'preview') {
      values.V = it
    }
    const valuesToExport = { ...value.value.values }

    // MappingTable
    try {
      props.precariousnessBonusSheet.mappingTables.forEach((mt, iMappingTables) => {
        values[mt.id] =
          mt.data[
            combinaisons.value[iMappingTables].findIndex((it) =>
              isEqual(
                it,
                mt.paramColumns.map((it) => values[it])
              )
            )
          ]
      })
    } catch (e) {
      logException(e)
    }

    // Computed Parameters
    value.value.casePrecariousnessBonusSheet?.computedParameters?.forEach((cp) => {
      const formula = parsingFormula(cp.formula).value
      const result = evaluateFormula(formula, values)
      if (result.value != null) {
        values[cp.id] = result.value
        valuesToExport[cp.id] = result.value
      } else {
        logException(result.error)
      }
    })

    let precariousnessCee = emptyValue<number>()
    if (formulas.value['precariousness']?.value) {
      precariousnessCee = evaluateFormula(formulas.value['precariousness'].value, values)
      if (!precariousnessCee.error) {
        precariousnessCee.value = Math.round(precariousnessCee.value!)
      }
    }

    let classicalCee = emptyValue<number>()
    if (formulas.value['classical']?.value) {
      classicalCee = evaluateFormula(formulas.value['classical'].value, values)
      if (!classicalCee.error) {
        classicalCee.value = Math.round(classicalCee.value!)
      }
    }

    const errorMessageEvaluations: PromisableValue<number>[] = []

    props.precariousnessBonusSheet.formulaValidationRules.forEach((v, index) => {
      if (formulas.value['error_message_' + index]) {
        errorMessageEvaluations.push(evaluateFormula(formulas.value['error_message_' + index].value, values))
      }
    })
    errorMessagesEvaluation.value = errorMessageEvaluations

    return [classicalCee, precariousnessCee, valuesToExport]
  })

  emit('update:values', results[0][2])
  value.value.classicalCee = results.map((it) => it[0])
  value.value.precariousnessCee = results.map((it) => it[1])
  // emit('update:ceePrecariousness', results.map(it => it[1]))
})

// Sync Values

watch(
  () => value.value.classicalCee,
  (v) =>
    emit(
      'update:ceeClassic',
      v.map((it) => it.value!)
    ),
  {
    immediate: true,
  }
)

watch(
  () => value.value.precariousnessCee,
  (v) =>
    emit(
      'update:ceePrecariousness',
      v.map((it) => it.value!)
    ),
  {
    immediate: true,
  }
)

const totalClassicCee = computed(() => value.value.classicalCee.reduce((acc, it) => acc + it.value!, 0))
const errorClassicCee = computed(() => value.value.classicalCee.find((it) => it.error)?.error)
const totalPrecariousnessCee = computed(() => value.value.precariousnessCee.reduce((acc, it) => acc + it.value!, 0))
const errorPrecariousnessCee = computed(() => value.value.precariousnessCee.find((it) => it.error)?.error)

watch(
  [() => props.values?.cas, () => props.precariousnessBonusSheet.casList],
  (v, oldV) => {
    if (!isEqual(v[0], oldV[0]) || !isEqual(v[1], oldV[1])) {
      value.value.casePrecariousnessBonusSheet = props.precariousnessBonusSheet.casList[v[0]]
    }
  },
  {
    immediate: true,
  }
)

const updateCasePrecariousnessBonusSheet = (casePrecariousnessBonusSheet: CasePrecariousnessBonusSheet) => {
  value.value.values.cas = props.precariousnessBonusSheet.casList.findIndex((it) => it === casePrecariousnessBonusSheet)
  value.value.casePrecariousnessBonusSheet = casePrecariousnessBonusSheet
}

import { computed } from 'vue'

const requiredParameters = computed(() => {
  return previewParameters.value.filter((parameter) => !parameter.optional)
})

const optionalParameters = computed(() => {
  return previewParameters.value.filter((parameter) => parameter.optional)
})
</script>
