import type { ProfileType } from './user'

export interface Step {
  id: number
  name: string
  commercialStatus: CommercialStatus
  treatedBy: ProfileType
  validMonthDuration?: number
  nbDocuments: number
  explanation: string
}

export const steps = ref([10, 20, 30, 40, 50, 60, 70, 80, 90, 100, 110])

export const commercialStatus = ['STUDIED', 'DELIVERED', 'SIGNED', 'LOST', 'CEE_KEEP_BY_CUSTOMER'] as const
export type CommercialStatus = (typeof commercialStatus)[number]

export const commercialStatusTitle = [
  {
    title: "Offre identifiée/À l'étude",
    value: 'STUDIED',
  },
  {
    title: 'Offre remise',
    value: 'DELIVERED',
  },
  {
    title: 'Offre gagnée - Convention signée',
    value: 'SIGNED',
  },
  {
    title: 'Offre perdue',
    value: 'LOST',
  },
  {
    title: 'CEE conservé par le client',
    value: 'CEE_KEEP_BY_CUSTOMER',
  },
]
