<template>
  <VRow>
    <VCol>
      <NjDataTable
        v-model:selections="selected"
        :headers="headers"
        :pageable="pageable"
        :page="data.value"
        :fixed="fixed"
        @update:pageable="updatePageable"
        @update:selections="emit('update:model-value', selected)"
      >
        <template #[`item.address`]="{ item }">
          {{
            `${item.address ? item.address + ', ' : ''} ${item.postalCode ? item.postalCode + ', ' : ''} ${
              item.city ?? ''
            }`
          }}
        </template>
        <template #[`item.certified`]="{ item }">
          <VIcon v-show="item.certified" color="#28B750" icon="mdi-shield-check-outline" />
        </template>
      </NjDataTable>
    </VCol>
  </VRow>
</template>
<script lang="ts" setup>
import { controlOrganismApi } from '@/api/controlOrganism'
import type { DataTableHeader } from '@/components/okta/NjDataTable.type'
import { type ControlOrganism, type ControlOrganismFilter, getControlTypeLabel } from '@/types/controlOrganism'
import NjDataTable from '@/components/okta/NjDataTable.vue'
import type { PropType } from 'vue'
import { debounce } from 'lodash'

const props = defineProps({
  modelValue: {
    type: Object as PropType<ControlOrganism[]>,
  },
  search: {
    type: String,
  },
  fixed: Boolean,
  onClickRow: Function as PropType<(row: ControlOrganism) => void>,
})

const emit = defineEmits(['update:model-value', 'update:loading'])
const selected = ref<ControlOrganism[]>(props.modelValue ?? [])
const defaultSort =
  (props.modelValue ?? []).length > 0
    ? ['"ids:' + props.modelValue?.map((b) => b.id).join(';') + '",DESC', 'certified,DESC']
    : ['certified,DESC']

const { data, pageFilter, pageable, updatePageable, updateFilter, reload } = usePagination<
  ControlOrganism,
  ControlOrganismFilter
>(
  (filter, pageable) => controlOrganismApi.getAll(pageable, filter),
  {},
  {
    page: 0,
    size: 100,
    sort: defaultSort,
  }
)

const headers: DataTableHeader[] = [
  {
    title: 'Raison sociale',
    value: 'socialReason',
  },
  {
    title: 'SIREN',
    value: 'siren',
  },
  {
    title: 'Adresse',
    value: 'address',
  },
  {
    title: 'Certifié',
    value: 'certified',
  },
  {
    title: 'Type de contrôle',
    value: 'controlType',
    formater: (_, value) => getControlTypeLabel(value),
  },
  {
    title: 'Nom',
    value: 'contactName',
  },
  {
    title: 'Numéro de téléphone',
    value: 'contactPhoneNumber',
  },
  {
    title: 'Email',
    value: 'contactEmail',
  },
  {
    title: 'Certifié par',
    value: 'certifyingUser',
    formater: (item) => (item.certified ? displayFullnameUser(item.updateUser) : ''),
    sortable: false,
  },
  {
    title: 'Certifié le',
    value: 'certifiedDateTime',
    formater: (item) => (item.certified ? formatHumanReadableLocalDateTime(item.updateDateTime) : ''),
    sortable: false,
  },
  {
    title: 'Créateur',
    value: 'creationUser',
    formater: (_, value) => displayFullnameUser(value),
  },
  {
    title: 'Date de création',
    value: 'creationDateTime',
    formater: (_, value) => (value ? formatHumanReadableLocalDateTime(value) : ''),
  },
  {
    title: 'Mis à jour par',
    value: 'updateUser',
    formater: (_, value) => displayFullnameUser(value),
  },
  {
    title: 'Date de mis à jour',
    value: 'updateDateTime',
    formater: (_, value) => (value ? formatHumanReadableLocalDateTime(value) : ''),
  },
]

const debounceSearch = debounce((v: string | undefined) => {
  updateFilter({ ...pageFilter.value, search: v })
}, 300)

watch(
  () => props.search,
  (v) => {
    if (v || v === '') {
      data.value.loading = true
      debounceSearch(v)
    }
  }
)
watch(
  () => data.value.loading,
  (v) => {
    emit('update:loading', v)
  }
)

onMounted(() => {
  if (props.search) {
    pageFilter.value.search = props.search
  }
})

defineExpose({ reload })
</script>
