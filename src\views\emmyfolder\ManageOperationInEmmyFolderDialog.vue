<template>
  <div>
    <CardDialog
      :model-value="modelValue"
      title="Gérer les opérations"
      :no-actions="noActions"
      fixed
      @update:model-value="emits('update:model-value', $event)"
    >
      <VRow class="h-100">
        <VCol>
          <VRow class="flex-column h-100" dense>
            <VCol class="font-weight-700 flex-grow-0"> Opérations à l'étape 80 </VCol>
            <VCol class="flex-grow-0">
              <VRow>
                <VCol>
                  <SearchInput
                    :model-value="avaibleOperationsPagination.pageFilter.value.search"
                    :loading="avaibleOperationsPagination.data.value.loading"
                    @update:model-value="avaibleOperationsUpdateSearch"
                  />
                </VCol>
                <VCol>
                  <VCheckbox
                    label="Mes opérations"
                    @update:model-value="
                      avaibleOperationsPagination.updateFilter({
                        ...avaibleOperationsPagination.pageFilter.value,
                        myRequests: $event ? true : undefined,
                      })
                    "
                  />
                </VCol>
                <VCol>
                  <VSelect
                    label="Périodes"
                    :items="periodsStore.periods"
                    item-title="name"
                    item-value="id"
                    multiple
                    @update:model-value="
                      (event: readonly number[]) =>
                        avaibleOperationsPagination.updateFilter({
                          ...avaibleOperationsPagination.pageFilter.value,
                          periodIds: event.length > 0 ? event.concat() : undefined,
                        })
                    "
                  />
                </VCol>
              </VRow>
            </VCol>
            <VCol>
              <NjDataTable
                :headers="operationsManagmentHeaders"
                :pageable="avaibleOperationsPagination.pageable.value"
                :page="avaibleOperationsPagination.data.value.value!"
                fixed
                column-fixed
                @update:pageable="avaibleOperationsPagination.updatePageable"
              >
                <template #[`item.action`]="{ item }">
                  <VBtn
                    icon="mdi-plus"
                    variant="text"
                    size="small"
                    color="primary"
                    @click="showConfirmAddOperationDialog = { active: true, operation: item }"
                  />
                </template>
              </NjDataTable>
            </VCol>
          </VRow>
        </VCol>
        <VCol>
          <VRow class="flex-column h-100" dense>
            <VCol class="flex-grow-0"> Opérations dans le dossier Emmy: {{ emmyFolder.name }} </VCol>
            <VCol class="flex-grow-0">
              <VRow>
                <VCol cols="6">
                  <SearchInput
                    :model-value="operationsPagination.pageFilter.value.search"
                    :loading="operationsPagination.data.value.loading"
                    @update:model-value="operationsUpdateSearch"
                  />
                </VCol>
              </VRow>
            </VCol>
            <VCol>
              <NjDataTable
                :headers="operationsManagmentHeaders"
                :pageable="operationsPagination.pageable.value"
                :page="operationsPagination.data.value.value!"
                fixed
                column-fixed
                @update:pageable="operationsPagination.updatePageable"
              >
                <template #[`item.action`]="{ item }">
                  <VBtn
                    v-if="!item.validateImportInEmmyDateTime || userStore.isAdmin"
                    icon="mdi-minus"
                    variant="text"
                    size="small"
                    color="primary"
                    @click="removeOperationFromEmmyFolder(item.id)"
                  />
                </template>
              </NjDataTable>
            </VCol>
          </VRow>
        </VCol>
      </VRow>
      <template #actions>
        <slot name="actions" />
      </template>
    </CardDialog>
    <OperationForEmmyFolderSummaryDialog
      v-model:show-confirm-add-operation-dialog="showConfirmAddOperationDialog"
      :add-operation="addOperationInEmmyFolder"
    />
  </div>
</template>
<script setup lang="ts">
import type { OperationFilter } from '@/api/operation'
import type { DataTableHeader } from '@/components/okta/NjDataTable.type'
import { usePeriodsStore } from '@/stores/periods'
import { useSnackbarStore } from '@/stores/snackbar'
import { useUserStore } from '@/stores/user'
import type { EmmyFolder } from '@/types/emmyFolder'
import type { Operation } from '@/types/operation'
import { useDebouncedSearch } from '@/types/search'
import type { PropType } from 'vue'
import { VCheckbox, VRow, VSelect } from 'vuetify/components'
import OperationForEmmyFolderSummaryDialog from './OperationForEmmyFolderSummaryDialog.vue'

const props = defineProps({
  modelValue: Boolean,
  emmyFolder: {
    type: Object as PropType<EmmyFolder>,
    required: true,
  },
  noActions: Boolean,
})

const emits = defineEmits<{
  'update:model-value': [boolean]
}>()

const snackbarStore = useSnackbarStore()
const userStore = useUserStore()

// Available operations
const avaibleOperationsPagination = usePagination<Operation, OperationFilter>(
  (filter, pageable) => operationApi.findAll(filter, pageable),
  {
    stepIds: [80],
    emmyFolderId: 0,
    operationStatuses: ['DOING'],
  }
)
const { updateSearch: avaibleOperationsUpdateSearch } = useDebouncedSearch(
  avaibleOperationsPagination.pageFilter,
  avaibleOperationsPagination.updateFilter,
  avaibleOperationsPagination.data
)

const operationsPagination = usePagination((filter, pageable) => operationApi.findAll(filter, pageable), {
  emmyFolderId: props.emmyFolder.id,
} as OperationFilter)
const { updateSearch: operationsUpdateSearch } = useDebouncedSearch(
  operationsPagination.pageFilter,
  operationsPagination.updateFilter,
  operationsPagination.data
)

const operationsManagmentHeaders: DataTableHeader[] = [
  {
    title: 'Nom',
    value: 'operationName',
    width: '300px',
  },
  {
    title: 'Chrono',
    value: 'chronoCode',
    width: '64px',
  },
  {
    title: 'Code',
    value: 'standardizedOperationSheet.operationCode',
    width: '64px',
  },
  {
    title: 'Action',
    value: 'action',
    sortable: false,
    width: '30px',
  },
]

const showConfirmAddOperationDialog = ref({
  active: false,
  operation: makeEmptyOperation(),
})

const addOperationInEmmyFolder = () => {
  avaibleOperationsPagination.data.value.loading = true
  operationsPagination.data.value.loading = true
  const operationId = showConfirmAddOperationDialog.value.operation.id
  emmyFolderApi
    .addOperation(props.emmyFolder.id, { operationIds: [operationId] })
    .then(() => {
      showConfirmAddOperationDialog.value.active = false
      avaibleOperationsPagination.reload()
      operationsPagination.updatePageable({
        ...operationsPagination.pageable.value,
        sort: ['"ids:' + operationId + '",DESC'],
      })
      if (updatedOperation.value.removedOperation.find((i) => i == operationId)) {
        updatedOperation.value.removedOperation = updatedOperation.value.removedOperation.filter(
          (i) => i != operationId
        )
      } else {
        updatedOperation.value.addedOperations.push(operationId)
      }
    })
    .catch(async (e) => {
      snackbarStore.setError(await handleAxiosException(e))
      avaibleOperationsPagination.data.value.loading = false
      operationsPagination.data.value.loading = false
    })
}

const removeOperationFromEmmyFolder = (operationId: number) => {
  avaibleOperationsPagination.data.value.loading = true
  operationsPagination.data.value.loading = true
  emmyFolderApi
    .removeOperation(props.emmyFolder.id, operationId)
    .then(() => {
      avaibleOperationsPagination.reload()
      operationsPagination.reload()
      if (updatedOperation.value.addedOperations.find((i) => i == operationId)) {
        updatedOperation.value.addedOperations = updatedOperation.value.addedOperations.filter((i) => i != operationId)
      } else {
        updatedOperation.value.removedOperation.push(operationId)
      }
    })
    .catch(async (e) => {
      snackbarStore.setError(await handleAxiosException(e))
      avaibleOperationsPagination.data.value.loading = false
      operationsPagination.data.value.loading = false
    })
}

const periodsStore = usePeriodsStore()

watch(
  () => props.modelValue,
  (v) => {
    if (v) {
      ;(updatedOperation.value = {
        addedOperations: [],
        removedOperation: [],
      }),
        avaibleOperationsPagination.reload()
      operationsPagination.reload()
    }
  }
)
const updatedOperation = ref<{
  addedOperations: number[]
  removedOperation: number[]
}>({
  addedOperations: [],
  removedOperation: [],
})

defineExpose({ updatedOperation })
</script>
