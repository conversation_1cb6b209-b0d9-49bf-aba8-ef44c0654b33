<template>
  <div>
    <CardDialog
      :model-value="modelValue"
      title="Gérer les opérations"
      fixed
      @update:model-value="emits('update:model-value', $event)"
    >
      <VRow class="h-100 flex-column">
        <VCol>
          <VRow class="flex-column h-100" dense>
            <VCol class="font-weight-700 flex-grow-0"> Opérations à l'étape 70 </VCol>
            <VCol class="flex-grow-0">
              <VRow>
                <VCol>
                  <SearchInput
                    :model-value="avaibleOperationsPagination.pageFilter.value.search"
                    :loading="avaibleOperationsPagination.data.value.loading"
                    @update:model-value="avaibleOperationsUpdateSearch"
                  />
                </VCol>
                <VCol>
                  <VCheckbox
                    label="Mes opérations"
                    @update:model-value="
                      avaibleOperationsPagination.updateFilter({
                        ...avaibleOperationsPagination.pageFilter.value,
                        myRequests: $event ? true : undefined,
                      })
                    "
                  />
                </VCol>
              </VRow>
            </VCol>
            <VCol>
              <NjDataTable
                :headers="operationsManagmentHeaders"
                :pageable="avaibleOperationsPagination.pageable.value"
                :page="avaibleOperationsPagination.data.value.value!"
                fixed
                column-fixed
                @update:pageable="avaibleOperationsPagination.updatePageable"
              >
                <template #[`item.action`]="{ item }">
                  <VBtn
                    icon="mdi-plus"
                    variant="text"
                    size="small"
                    color="primary"
                    :disabled="avaibleOperationsPagination.data.value.loading"
                    :loading="avaibleOperationsPagination.data.value.loading && selectedOperationId == item.id"
                    @click="addOperationInControlOrderBatch(item.id)"
                  />
                </template>
              </NjDataTable>
            </VCol>
          </VRow>
        </VCol>
        <VCol>
          <VRow class="flex-column h-100" dense>
            <VCol class="flex-grow-0"> Opération dans le lot de contrôle: {{ controlOrderBatch.batchCode }} </VCol>
            <VCol class="flex-grow-0">
              <VRow>
                <VCol cols="6">
                  <SearchInput
                    :model-value="operationsPagination.pageFilter.value.search"
                    :loading="operationsPagination.data.value.loading"
                    @update:model-value="operationsUpdateSearch"
                  />
                </VCol>
              </VRow>
            </VCol>
            <VCol>
              <NjDataTable
                :headers="operationsManagmentHeaders"
                :pageable="operationsPagination.pageable.value"
                :page="operationsPagination.data.value.value!"
                fixed
                column-fixed
                @update:pageable="operationsPagination.updatePageable"
              >
                <template #[`item.action`]="{ item }">
                  <VBtn
                    icon="mdi-minus"
                    variant="text"
                    size="small"
                    color="primary"
                    :disabled="operationsPagination.data.value.loading"
                    :loading="operationsPagination.data.value.loading && selectedOperationId == item.id"
                    @click="removeOperationFromControlOrderBatch(item.id)"
                  />
                </template>
              </NjDataTable>
            </VCol>
          </VRow>
        </VCol>
      </VRow>
      <template #actions>
        <slot name="actions" />
      </template>
    </CardDialog>
  </div>
</template>
<script setup lang="ts">
import type { OperationFilter } from '@/api/operation'
import type { DataTableHeader } from '@/components/okta/NjDataTable.type'
import { useSnackbarStore } from '@/stores/snackbar'
import { useDebouncedSearch } from '@/types/search'
import type { PropType } from 'vue'
import type { ControlOrderBatch } from '@/types/controlOrder'
import router from '@/router'
import { useDialogStore } from '@/stores/dialog'
import { deleteControlOrderBatchDialogRequest } from '@/views/dialog/dialogRequest'

const props = defineProps({
  modelValue: Boolean,
  controlOrderBatch: {
    type: Object as PropType<ControlOrderBatch>,
    required: true,
  },
  standardizedOperationSheetId: {
    type: Number,
    required: true,
  },
})

const emits = defineEmits<{
  'update:model-value': [boolean]
  updated: []
}>()

const snackbarStore = useSnackbarStore()

// Available operations
const avaibleOperationsPagination = usePagination((filter, pageable) => operationApi.findAll(filter, pageable), {
  stepIds: [70],
  availableForControlOrder: true,
  standardizedOperationSheetIds: props.standardizedOperationSheetId ? [props.standardizedOperationSheetId] : [],
  committedAfter: props.controlOrderBatch.usedMinimumSatisfyingControlRate.startDate,
  committedBefore: props.controlOrderBatch.usedMinimumSatisfyingControlRate.endDate,
} as OperationFilter)
const { updateSearch: avaibleOperationsUpdateSearch } = useDebouncedSearch(
  avaibleOperationsPagination.pageFilter,
  avaibleOperationsPagination.updateFilter,
  avaibleOperationsPagination.data
)

const operationsPagination = usePagination((filter, pageable) => operationApi.findAll(filter, pageable), {
  controlOrderBatchId: props.controlOrderBatch.id,
} as OperationFilter)
const { updateSearch: operationsUpdateSearch } = useDebouncedSearch(
  operationsPagination.pageFilter,
  operationsPagination.updateFilter,
  operationsPagination.data
)

const operationsManagmentHeaders: DataTableHeader[] = [
  {
    title: 'Nom',
    value: 'operationName',
    width: '300px',
  },
  {
    title: 'Chrono',
    value: 'chronoCode',
    width: '64px',
  },
  {
    title: 'Code',
    value: 'standardizedOperationSheet.operationCode',
    width: '64px',
  },
  {
    title: 'Action',
    value: 'action',
    sortable: false,
    width: '30px',
  },
]

const selectedOperationId = ref<number>()
const addOperationInControlOrderBatch = (operationId: number) => {
  avaibleOperationsPagination.data.value.loading = true
  operationsPagination.data.value.loading = true
  selectedOperationId.value = operationId
  controlOrderBatchApi
    .addOperation(props.controlOrderBatch.id, operationId)
    .then(() => {
      avaibleOperationsPagination.reload()
      operationsPagination.updatePageable({
        ...operationsPagination.pageable.value,
        sort: ['"ids:' + operationId + '",DESC'],
      })
      if (updatedOperation.value.removedOperation.find((i) => i == operationId)) {
        updatedOperation.value.removedOperation = updatedOperation.value.removedOperation.filter(
          (i) => i != operationId
        )
      } else {
        updatedOperation.value.addedOperations.push(operationId)
      }
    })
    .catch(async (e) => {
      snackbarStore.setError(await handleAxiosException(e))
      avaibleOperationsPagination.data.value.loading = false
      operationsPagination.data.value.loading = false
    })
}

const dialogStore = useDialogStore()
const removeOperationFromControlOrderBatch = async (operationId: number) => {
  if (operationsPagination.data.value.value!.totalElements > 1) {
    selectedOperationId.value = operationId
    avaibleOperationsPagination.data.value.loading = true
    operationsPagination.data.value.loading = true
    controlOrderBatchApi
      .removeOperation(props.controlOrderBatch.id, operationId)
      .then(() => {
        avaibleOperationsPagination.reload()
        operationsPagination.reload()
        if (updatedOperation.value.addedOperations.find((i) => i == operationId)) {
          updatedOperation.value.addedOperations = updatedOperation.value.addedOperations.filter(
            (i) => i != operationId
          )
        } else {
          updatedOperation.value.removedOperation.push(operationId)
        }
      })
      .catch(async (e) => {
        snackbarStore.setError(await handleAxiosException(e))
        avaibleOperationsPagination.data.value.loading = false
        operationsPagination.data.value.loading = false
      })
  } else if (await dialogStore.addAlert(deleteControlOrderBatchDialogRequest)) {
    controlOrderBatchApi
      .delete(props.controlOrderBatch.id)
      .then(() => {
        snackbarStore.setSuccess('Le lot de contrôle a bien été supprimé')
        router.push({ name: 'ControlOrderBatchAllView' })
      })
      .catch(async (error) =>
        snackbarStore.setError(
          await handleAxiosException(error, undefined, {
            defaultMessage: 'Une erreur est survenue lors de la suppression du lot de contrôle',
          })
        )
      )
  }
}

watch(
  () => props.modelValue,
  (v) => {
    if (v) {
      ;(updatedOperation.value = {
        addedOperations: [],
        removedOperation: [],
      }),
        avaibleOperationsPagination.reload()
      operationsPagination.reload()
    }
  }
)
const updatedOperation = ref<{
  addedOperations: number[]
  removedOperation: number[]
}>({
  addedOperations: [],
  removedOperation: [],
})

defineExpose({ updatedOperation })
</script>
