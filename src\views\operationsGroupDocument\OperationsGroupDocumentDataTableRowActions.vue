<template>
  <div style="display: flex; justify-content: end; gap: 8px">
    <NjBtn
      v-if="displaySendButton"
      variant="outlined"
      prepend-icon="mdi-send"
      @click="emits('send', item.documentType.id)"
    >
      Envoyer
    </NjBtn>

    <VMenu :close-on-content-click="false">
      <template #activator="{ props }">
        <NjIconBtn icon="mdi-chevron-down" border="0" color="primary" variant="square-outline" v-bind="props" />
      </template>
      <VList class="py-0">
        <VListItem
          v-if="item.templateId"
          base-color="primary"
          prepend-icon="mdi-download"
          :loading="downloadTemplateLoading"
          @click="downloadTemplate"
        >
          Modèle vierge
        </VListItem>
        <VListItem
          v-if="item.active"
          base-color="primary"
          prepend-icon="mdi-swap-horizontal"
          @click="replaceDocument(item)"
        >
          Remplacer
        </VListItem>
        <CardDialog v-model="replaceDocumentDialog" width="40%" title="Remplacer le document" :closeable="false">
          <VRow class="flex-column my-n2">
            <VCol>
              <NjFileInput
                @new-files="
                  (event) => {
                    replaceFile = []
                    replaceFile.push(...event)
                  }
                "
              />
            </VCol>
            <VCol>
              <NjDisplayValue
                v-if="replaceFile.length > 0"
                :label="oldDocument?.documentType.name ?? ''"
                :value="replaceFile[0].name"
              />
            </VCol>
          </VRow>
          <template #actions>
            <NjBtn variant="outlined" @click="replaceDocumentDialog = false">Annuler</NjBtn>
            <NjBtn @click="submitReplaceDocument"> Valider </NjBtn>
          </template>
        </CardDialog>
        <VListItem v-if="item.active" base-color="primary" prepend-icon="mdi-cancel" @click="desactivateDocument(item)">
          Désactiver
        </VListItem>
        <template v-if="userStore.isAdmin && item.id">
          <VListItem base-color="primary" prepend-icon="mdi-pencil" @click="editDocument(item)">
            Modifier le type
          </VListItem>

          <CardDialog v-model="editDocumentDialog" width="30%" title="Modifier le type de document" :closeable="false">
            <RemoteAutoComplete
              v-model="operationsGroupDocumentRequest!.documentTypeId"
              :query-for-all="queryDocumentTypeNotInOperationsGroup(operationsGroupDocument?.id ?? 0)"
              :query-for-one="(id) => documentTypeApi.getOne(id)"
              item-title="name"
              infinite-scroll
            />
            <template #actions>
              <NjBtn variant="outlined" @click="editDocumentDialog = false">Annuler</NjBtn>
              <NjBtn @click="saveDocument"> Valider </NjBtn>
            </template>
          </CardDialog>
          <VListItem base-color="primary" prepend-icon="mdi-delete-outline" @click="deleteDocument(item)">
            Supprimer
          </VListItem>
          <AlertDialog v-bind="deleteDocumentDialog.props" title="Supprimer le document" max-width="640px">
            Êtes-vous sûr de vouloir supprimer le document?
          </AlertDialog>
        </template>
      </VList>
    </VMenu>
  </div>
</template>
<script setup lang="ts">
import NjBtn from '@/components/NjBtn.vue'
import NjIconBtn from '@/components/NjIconBtn.vue'
import { VListItem, VMenu } from 'vuetify/components'
import { useAdminConfigurationStore } from '@/stores/adminConfiguration'
import { useUserStore } from '@/stores/user'
import { useSnackbarStore } from '@/stores/snackbar'
import type { OperationsGroupDocumentRequest } from '@/types/document'
import CardDialog from '@/components/CardDialog.vue'
import NjFileInput from '@/components/NjFileInput.vue'
import type { OperationsGroup } from '@/types/operationsGroup'
import { operationsGroupDocumentApi } from '@/api/operationsGroupDocument'
import { documentTypeApi } from '@/api/documentType'
import type { Pageable } from '@/types/pagination'
import type { DocumentDataTableItem } from '../operationdocument/types'

const props = defineProps({
  item: { type: Object as PropType<DocumentDataTableItem>, required: true },
  operationsGroup: Object as PropType<OperationsGroup>,
  stepId: Number,
})

const emits = defineEmits<{
  editDocument: [void]
  send: [number]
  replaceDocument: [void]
  desactivateDocument: [void]
  deleteDocument: [void]
}>()

const { swornStatementDocumentTypeId, pvDeReceptionDocumentTypeId, conventionDocumentTypeId } =
  useAdminConfigurationStore()

const userStore = useUserStore()
const snackbarStore = useSnackbarStore()

const displaySendButton = computed(
  () =>
    props.operationsGroup &&
    ((swornStatementDocumentTypeId?.valueAsInt == props.item.documentType.id && props.stepId == 50) ||
      (pvDeReceptionDocumentTypeId?.valueAsInt == props.item.documentType.id && props.stepId == 50) ||
      (conventionDocumentTypeId?.valueAsInt == props.item.documentType.id &&
        props.stepId == 30 &&
        userHasRole(userStore.currentUser, 'AGENCE_PLUS', 'SUPPORT_AGENCE_PLUS', 'TERRITOIRE', 'ADMIN', 'ADMIN_PLUS')))
)

const downloadTemplateLoading = ref(false)
const downloadTemplate = () => {
  downloadTemplateLoading.value = true
  documentTypeApi
    .downloadTemplate(props.item.documentType.id)
    .then((response) => {
      downloadFile(props.item.documentType.template!.originalFilename, response.data)
      snackbarStore.setSuccess('Le téléchargement a réussi')
    })
    .catch(() =>
      snackbarStore.setError(
        'Le téléchargement du fichier a échoué. Aviez-vous envoyé ce fichier sur boCEE ? Si oui, veuillez nous contacter.'
      )
    )
    .finally(() => {
      downloadTemplateLoading.value = false
    })
}

const deleteDocumentDialog = useConfirmAlertDialog()
const deleteDocument = async (item: DocumentDataTableItem) => {
  if (await deleteDocumentDialog.confirm()) {
    operationsGroupDocumentApi
      .delete(item.id!)
      .then(() => {
        emits('deleteDocument')
        snackbarStore.setSuccess('Le document à été supprimé')
      })
      .catch(() => {
        snackbarStore.setError('Une erreur est survenue lors de la suppression du document')
      })
  }
}

const desactivateDocument = (item: DocumentDataTableItem) => {
  const request: OperationsGroupDocumentRequest = {
    operationsGroupId: props.operationsGroup?.id!,
    documentTypeId: item.documentType.id,
    description: item.description!,
    active: false,
  }
  operationsGroupDocumentApi
    .update(item.id!, request)
    .then(() => {
      emits('desactivateDocument')

      snackbarStore.setSuccess('Le document à été désactivé')
    })
    .catch(async (err) => {
      snackbarStore.setError(await handleAxiosException(err))
    })
}

//remplacer dialog
const replaceDocumentDialog = ref(false)
const oldDocument = ref<DocumentDataTableItem>()
const replaceFile = ref<File[]>([])
const replaceDocument = (old: DocumentDataTableItem) => {
  oldDocument.value = old
  replaceDocumentDialog.value = true
}

const promisableReplaceOperationDocument = ref(emptyValue<DocumentDataTableItem>())

const submitReplaceDocument = () => {
  if (!oldDocument.value) {
    return
  }
  const request: OperationsGroupDocumentRequest = {
    operationsGroupId: props.operationsGroup!.id,
    documentTypeId: oldDocument.value.documentType.id,
    description: oldDocument.value.description ?? '',
    active: true,
  }
  handleAxiosPromise(
    promisableReplaceOperationDocument,
    operationsGroupDocumentApi.uploadFile(request, replaceFile.value[0], oldDocument.value.id),
    {
      afterSuccess: () => {
        snackbarStore.setSuccess('Le document a été remplacé')
        emits('replaceDocument')
        replaceDocumentDialog.value = false
        replaceFile.value = []
      },
      afterError: () => snackbarStore.setError("Erreur le document n'a pas pu être remplacé"),
    }
  )
}
const editDocumentDialog = ref(false)
const operationsGroupDocumentRequest = ref<OperationsGroupDocumentRequest>()
const operationsGroupDocument = ref<DocumentDataTableItem>()

const editDocument = (doc: DocumentDataTableItem) => {
  operationsGroupDocument.value = doc
  operationsGroupDocumentRequest.value = {
    operationsGroupId: props.operationsGroup!.id,
    documentTypeId: doc.documentType.id,
    description: doc.description ?? '',
    active: doc.active!,
  }
  editDocumentDialog.value = true
}

const saveDocument = () => {
  if (!operationsGroupDocument.value) {
    return
  }

  operationsGroupDocumentApi
    .update(operationsGroupDocument.value!.id!, operationsGroupDocumentRequest.value!)
    .then(() => {
      snackbarStore.setSuccess('Le document à été mis à jour')
      editDocumentDialog.value = false
      emits('editDocument')
    })
    .catch(async (err) => {
      snackbarStore.setError(await handleAxiosException(err))
    })
}

const queryDocumentTypeNotInOperationsGroup = (operationsGroupId: number) => (search: string, pageable: Pageable) => {
  return documentTypeApi.getAll(pageable, {
    search: search,
    notActiveInOperationsGroup: operationsGroupId,
  })
}
</script>
