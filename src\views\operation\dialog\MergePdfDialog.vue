<template>
  <CardDialog
    :model-value="modelValue"
    width="60%"
    title="Fusionner les PDFs"
    @update:model-value="emit('update:model-value', $event)"
  >
    <VRow class="flex-column">
      <VCol>
        <VTabs v-model="tabMergePdfDialog">
          <VTab v-if="shouldDisplayFinalVersionTab">Version finale</VTab>
          <VTab>Version initiale</VTab>
          <VTab>Autre</VTab>
        </VTabs>
        <VDivider />
      </VCol>
      <VCol>
        <VWindow v-model="tabMergePdfDialog">
          <VWindowItem v-if="shouldDisplayFinalVersionTab">
            <UsedDocumentDataTable v-model:selections="selectedForFinalVersion" :items="inFinalVersion" />
          </VWindowItem>
          <VWindowItem>
            <UsedDocumentDataTable v-model:selections="selectedForInitialVersion" :items="inInitialVersion" />
          </VWindowItem>
          <VWindowItem>
            <UsedDocumentDataTable v-model:selections="selectedForOtherVersion" :items="inOtherVersion" />
          </VWindowItem>
        </VWindow>
      </VCol>
    </VRow>

    <template #actions>
      <NjBtn variant="outlined" @click="emit('update:model-value', false)">Annuler</NjBtn>
      <NjBtn :loading="mergeLoader" @click="mergePdf">Fusionner</NjBtn>
    </template>
  </CardDialog>
</template>
<script setup lang="ts">
import UsedDocumentDataTable from '@/views/UsedDocumentDataTable.vue'
import { type MergeDocumentToPdfRequest, type UsedDocument } from '@/types/document'
import { useSnackbarStore } from '@/stores/snackbar'
import { awaitResponse } from '@/types/responseHandler'
import { responseHandlerApi } from '@/api/responseHandler'
import { useUserStore } from '@/stores/user'

const props = defineProps({
  modelValue: Boolean,
  operationId: Number,
  operationName: String,
})

const emit = defineEmits<{
  'update:model-value': [boolean]
}>()

const tabMergePdfDialog = ref()
const documentTypeInOperation = ref(emptyValue<UsedDocument[]>())

const snackbarStore = useSnackbarStore()

const inInitialVersion = ref<UsedDocument[]>([])
const inFinalVersion = ref<UsedDocument[]>([])
const inOtherVersion = ref<UsedDocument[]>([])

const selectedForInitialVersion = ref<UsedDocument[]>([])
const selectedForFinalVersion = ref<UsedDocument[]>([])
const selectedForOtherVersion = ref<UsedDocument[]>([])

watch(
  () => props.modelValue,
  (v) => {
    if (v) {
      handleAxiosPromise(documentTypeInOperation, usedDocumentApi.findByOperation(props.operationId ?? 0), {
        afterSuccess: () => {
          if (documentTypeInOperation.value.value) {
            inInitialVersion.value = documentTypeInOperation.value.value.filter((d) => d.documentType.inInitialVersion)
            selectedForInitialVersion.value = inInitialVersion.value.filter((d) =>
              isExtensionPdf(d.document.originalFilename)
            )
            inFinalVersion.value = documentTypeInOperation.value.value.filter((d) => d.documentType.inFinalVersion)
            selectedForFinalVersion.value = inFinalVersion.value.filter((d) =>
              isExtensionPdf(d.document.originalFilename)
            )
            inOtherVersion.value = documentTypeInOperation.value.value.filter((d) => d.documentType.inOtherVersion)
            selectedForOtherVersion.value = inOtherVersion.value.filter((d) =>
              isExtensionPdf(d.document.originalFilename)
            )
          }
        },
        afterError: () =>
          snackbarStore.setError(documentTypeInOperation.value.error ?? 'Echec de la récupération des documents'),
      })
    }
  }
)

const isExtensionPdf = (filename: string) => filename.split('.').slice(-1)[0].toLowerCase() === 'pdf'

const mergeLoader = ref(false)

const mergePdf = () => {
  mergeLoader.value = true
  mergeDocumentToPdfApi
    .merge(props.operationId ?? 0, getMergeDocumentToPdfRequestByDisplayedTab())
    .then(async (response) => {
      await awaitResponse(response.data)
        .then(async (response) => {
          downloadFile(
            `fusion operation ${props.operationName}.pdf`,
            (await responseHandlerApi.download(response.uuid)).data
          )
          snackbarStore.setSuccess('Le téléchargement a réussi')
        })
        .catch((response) => {
          snackbarStore.setError(
            response.errorMessage ??
              'Le téléchargement du fichier a échoué. Aviez-vous envoyé ce fichier sur boCEE ? Si oui, veuillez nous contacter.'
          )
        })
    })
    .catch(() =>
      snackbarStore.setError(
        'Le téléchargement du fichier a échoué. Aviez-vous envoyé ce fichier sur boCEE ? Si oui, veuillez nous contacter.'
      )
    )
    .finally(() => (mergeLoader.value = false))
}

const getMergeDocumentToPdfRequestByDisplayedTab = () => {
  if (tabMergePdfDialog.value === 0) {
    return mapUsedDocumentListToMergeDocumentToPdfRequest(selectedForFinalVersion.value)
  } else if (tabMergePdfDialog.value === 1) {
    return mapUsedDocumentListToMergeDocumentToPdfRequest(selectedForInitialVersion.value)
  } else {
    return mapUsedDocumentListToMergeDocumentToPdfRequest(selectedForOtherVersion.value)
  }
}

const mapUsedDocumentListToMergeDocumentToPdfRequest = (tab: UsedDocument[]): MergeDocumentToPdfRequest => {
  const request: MergeDocumentToPdfRequest = {
    operationDocumentIds: [],
    operationsGroupDocumentIds: [],
  }
  for (const doc of tab) {
    if (doc.operationDocumentId) {
      request.operationDocumentIds.push(doc.operationDocumentId)
    } else if (doc.operationGroupDocumentId) {
      request.operationsGroupDocumentIds.push(doc.operationGroupDocumentId)
    }
  }
  return request
}

const userStore = useUserStore()

const shouldDisplayFinalVersionTab = computed(() => userStore.isSiege)
</script>
