<template>
  <VTable class="nj-document-data-table">
    <thead>
      <tr>
        <th v-for="(header, index) in headers" :key="index" :width="header.width">
          {{ header.title }}
        </th>
      </tr>
    </thead>
    <tbody>
      <slot name="tbody" />
    </tbody>
  </VTable>
</template>
<script setup lang="ts">
const headers = [
  {
    title: 'Type',
    width: 400,
  },
  {
    title: 'Etape',
    width: 50,
  },
  {
    title: "Date d'ajout",
    width: 50,
  },
  {
    title: 'Format',
    width: 50,
  },
  {
    title: 'Description',
  },
  {},
]
</script>
<style lang="scss">
@import '@/assets/main.scss';

.nj-document-data-table {
  tr > th {
    color: #60798b;
    font-weight: 700 !important;
    white-space: nowrap;
  }
}

.operation-document-data-table {
  &__text-neutral {
    @extend .value__label;
    align-self: start;
  }

  &__value {
    @extend .value__value;
    text-align: end;
  }
}
</style>
