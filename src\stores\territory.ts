import type { Territory } from '@/types/territory'
import { isAxiosError } from 'axios'
import { isEqual } from 'lodash'
import { defineStore } from 'pinia'

export const useTerritoriesStore = defineStore('territories', () => {
  const territories = shallowRef<Territory[]>([])
  const territoryMap: Ref<Record<number, Territory>> = shallowRef({})
  const timeoutId = shallowRef<number>()

  const load = async () => {
    clearTimeouts()

    return await territoryApi
      .findAll({}, { size: 1000 })
      .then((v) => {
        v.data.content.sort((arg1, arg2) => arg1.id - arg2.id)
        if (!isEqual(v, territories.value)) {
          const result: Record<number, Territory> = {}
          v.data.content.forEach((it) => {
            result[it.id] = it
          })
          territoryMap.value = result
        }

        timeoutId.value = setTimeout(load, 30 * 60 * 1000)
      })
      .catch((e) => {
        if (isAxiosError(e) && e.status === 401) {
          return
        }
        logException(e)
        timeoutId.value = setTimeout(load, ((territories.value?.length ?? 0 > 0) ? 30 * 60 : 5) * 1000)
      })
  }

  const clearTimeouts = () => {
    if (timeoutId.value !== undefined) {
      clearTimeout(timeoutId.value)
    }
  }

  return { territories: territories, territoryMap, load, clearTimeouts }
})
