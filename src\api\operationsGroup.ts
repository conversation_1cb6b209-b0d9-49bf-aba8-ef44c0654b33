import type {
  EnhancedOperationsGroup,
  OperationsGroupRequest,
  ValidateStep30OperationsGroupRequest,
  ValidateStep40OperationsGroupRequest,
  OperationsGroupSummaryDto,
} from '@/types/operationsGroup'
import type { Page, Pageable } from '@/types/pagination'
import type { AxiosInstance, AxiosPromise } from 'axios'
import axiosInstance from '.'
import type { OperationStatus } from '@/types/operation'

export interface OperationsGroupFilter {
  search?: string
  myOperationsGroup?: boolean
  periodIds?: number[]
  ids?: number[]
  beneficiaryIds?: number[]
  entityNavFullIds?: string[]
  entityIds?: string[]
  status?: OperationStatus[]
}

const operationsGroupUri = '/operations_groups'

class OperationsGroupApi {
  public constructor(private axios: AxiosInstance) {}

  public create(request: OperationsGroupRequest) {
    return this.axios.post(operationsGroupUri, request)
  }

  public edit(id: number, request: OperationsGroupRequest) {
    return this.axios.put(operationsGroupUri + '/' + id, request)
  }

  public findAll(filter: OperationsGroupFilter, pageable: Pageable): AxiosPromise<Page<EnhancedOperationsGroup>> {
    return this.axios.get(operationsGroupUri, {
      params: { ...filter, ...pageable },
    })
  }

  public findById(id: number): AxiosPromise<EnhancedOperationsGroup> {
    return this.axios.get(operationsGroupUri + '/' + id)
  }

  public findFinancialIncentiveById(id: number): AxiosPromise<OperationsGroupSummaryDto> {
    return this.axios.get(operationsGroupUri + '/' + id + '/summary')
  }

  public delete(ids: number[]): AxiosPromise {
    return this.axios.delete(operationsGroupUri, {
      params: {
        ids,
      },
    })
  }

  public createConvention(id: number) {
    return this.axios.post(operationsGroupUri + '/' + id + '/convention', null, {
      responseType: 'blob',
    })
  }

  public validateStep30 = (id: number, request: ValidateStep30OperationsGroupRequest) => {
    return handleAsyncResponse(this.axios.post(operationsGroupUri + '/' + id + '/validate_step_30', request))
  }

  public validateStep40 = (id: number, request: ValidateStep40OperationsGroupRequest) => {
    return handleAsyncResponse(this.axios.post(operationsGroupUri + '/' + id + '/validate_step_40', request))
  }

  public updateSelfWorks = (id: number, selfWorks: boolean) => {
    return this.axios.put(operationsGroupUri + '/' + id + '/self_works', { selfWorks: selfWorks })
  }

  public removeOperation = (id: number): AxiosPromise<void> => {
    return this.axios.delete('operations/' + id + '/operations_group')
  }

  public previousStep = (id: number, currentStepId: number): AxiosPromise<void> => {
    return this.axios.post(operationsGroupUri + '/' + id + '/step_back', { stepId: currentStepId })
  }
}

export const operationsGroupApi = new OperationsGroupApi(axiosInstance)
