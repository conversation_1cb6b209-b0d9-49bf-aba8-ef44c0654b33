import type { Page, Pageable } from '@/types/pagination'
import type { AxiosInstance, AxiosPromise } from 'axios'
import axiosInstance from '.'
import type { OperationEvent } from '@/types/operationEvent'

export interface OperationEventFilter {
  top?: boolean
  message?: boolean
  commentary?: boolean
  volumeOrValuationChange?: boolean
  validateStep?: boolean
  documents?: boolean
  others?: boolean
}
class OperationEventApi {
  public constructor(private axios: AxiosInstance) {}

  public findAll(
    operationId: number,
    filter: OperationEventFilter,
    pageable: Pageable
  ): AxiosPromise<Page<OperationEvent>> {
    return this.axios.get(`/operations/${operationId}/events`, {
      params: { ...filter, ...pageable },
    })
  }
}
export const operationEventApi = new OperationEventApi(axiosInstance)
