import { responseHandlerApi } from '@/api/responseHandler'
import { AxiosError, AxiosHeaders, type AxiosPromise } from 'axios'
import type { LocalDateTime } from './date'

export interface ResponseHandler {
  uuid: string
  creationDateTime: LocalDateTime
  done: boolean
  errorMessage: string | null
  mediaType: string
}

export const awaitResponse = async (responseHandler: ResponseHandler): Promise<ResponseHandler> => {
  return new Promise(async (resolve, reject) => {
    let result = responseHandler
    while (!result.done) {
      const response = await responseHandlerApi.findOne(responseHandler.uuid)
      if (!response.data.done) {
        await pause(3000)
      } else {
        result = response.data
      }
    }
    if (result.errorMessage) {
      reject(result)
    } else {
      resolve(result)
    }
  })
}

export const handleAsyncResponse = async <T>(responseHandler: AxiosPromise<ResponseHandler>): AxiosPromise<T> => {
  return responseHandler.then((response) => {
    return awaitResponse(response.data)
      .then(() => {
        return generateAxiosResponse(null as any)
      })
      .catch((responseHandler: ResponseHandler) => {
        throw new AxiosError(
          "Message de l'erreur",
          'ERR_BAD_REQUEST',
          undefined,
          {},
          {
            status: 400,
            statusText: 'Bad Request',
            headers: {},
            config: {
              headers: new AxiosHeaders(),
            },
            data: {
              message: responseHandler.errorMessage || 'An error occurred while processing the request',
            },
          }
        )
      })
  })
}
