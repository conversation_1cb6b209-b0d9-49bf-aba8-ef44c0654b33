import type { LocalDateTime } from './date'
import type { Message } from './message'
import type { ObjectChange } from './operationEvent'
import type { User } from './user'

export interface OperationsGroupEvent {
  uuid: string
  creationDateTime: LocalDateTime
  creationUser: User
  event: string
  operationsGroupHistory: OperationsGroupHistory
  documentChangeSet: ObjectChange[]
  changeSet: ObjectChange[]
  concernedOperationId: number
  concernedOperationName: string
  message?: Message
}

export interface OperationsGroupHistory {
  id: number
  name: string
  beneficiaryId: number
  creationUserId: number
  creationDateTime: LocalDateTime
  entityId: number
}
