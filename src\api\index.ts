import type { AxiosRequestHeaders, AxiosResponseHeaders } from 'axios'
import axios, { AxiosHeaders, type AxiosResponse } from 'axios'
import { camelCase, isArray, isObject, snakeCase, memoize } from 'lodash'
import qs from 'qs'

const memoizeSnackCase = memoize(snakeCase)
const memoizeCamelCase = memoize(camelCase)

export const transformKeyObject = (data: any, transformKeyCallback: (key: string) => string): any => {
  if (isArray(data)) {
    return data.map((it) => transformKeyObject(it, transformKeyCallback))
  } else if (isObject(data)) {
    return Object.keys(data).reduce(
      (acc, k) => {
        // No format Key
        if (k === '@type') {
          acc[k] = (data as any)[k]
          // No format inner
        } else if (
          [
            'parameter_values',
            'epc_bonus_parameter_values',
            'precariousness_bonus_parameter_values',
            'lost_reasons',
          ].includes(memoizeSnackCase(k))
        ) {
          acc[transformKeyCallback(k)] = (data as any)[k]
        } else {
          acc[transformKeyCallback(k)] = transformKeyObject((data as any)[k], transformKeyCallback)
        }
        return acc
      },
      {} as Record<string, any>
    )
  } else {
    return data
  }
}

const axiosInstance = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
  paramsSerializer: {
    serialize: (params) => qs.stringify(params, { arrayFormat: 'repeat' }),
  },
  transformResponse: function (data: any, headers: AxiosResponseHeaders) {
    if (this.responseType === 'blob') {
      return data
    }
    if (headers['content-type'] === 'application/json') {
      // console.debug('response transform from: ' + data)
      // console.debug('response transform to ' + JSON.stringify(transformKeyObject(JSON.parse(data), camelCase)))

      const response = transformKeyObject(JSON.parse(data), (it) => {
        let a = memoizeCamelCase(it)

        if (a === 'datetime') {
          return 'dateTime'
        }
        if (a.endsWith('Datetime')) {
          const indexOf = a.lastIndexOf('Datetime')
          a = a.substring(0, indexOf) + 'DateTime'
        }

        return a
      })
      return response
    }
    return data
  },
  transformRequest: function (data: any, headers: AxiosRequestHeaders) {
    if (headers['Content-Type'] === 'application/json') {
      // console.debug('request from: ' + data)
      // console.debug('request to ' + JSON.stringify(engieFormatRequestTransformKey(data)))
      return JSON.stringify(engieFormatRequestTransformKey(data))
    }
    return data
  },
})

export default axiosInstance

export function engieFormatRequestTransformKey(o: unknown) {
  return transformKeyObject(o, (it) => {
    let a = memoizeSnackCase(it)

    if (a.endsWith('_date_time')) {
      const indexOf = a.lastIndexOf('_date_time')
      a = a.substring(0, indexOf) + '_datetime'
    }

    return a
  })
}

export function handleAxiosException(
  e: unknown,
  onResponseCallback: ((r: AxiosResponse) => Promise<string | undefined | null>) | string = async (r) =>
    (r.data instanceof Blob ? JSON.parse(await r.data.text()) : r.data)?.message,
  options: {
    defaultMessage?: string
  } = {}
): Promise<string> {
  if (axios.isAxiosError(e)) {
    if (e.code === 'ERR_NETWORK') {
      return Promise.resolve('Accès réseau impossible. Êtes-vous connecté à Internet ?')
    } else if (e.response) {
      if (e.response.status === 404) {
        return Promise.resolve("Ressource introuvable ou bien vous n'avez pas les droits nécessaires.")
      }
      if (e.response.status === 409) {
        return Promise.resolve(
          "On dirait quelqu'une autre personne ait modifié cette donnée pendant vos modifications. Notez vos modifications de côté, rafraîchissez la page et ressaisissez vos informations."
        )
      }
      if (e.response.status === 503 || e.response.status === 502 || e.response.status === 504) {
        return Promise.resolve('Le service semble être temporairement indisponible. Veuillez réessayer plus tard.')
      }

      if (typeof onResponseCallback === 'string') {
        return Promise.resolve(onResponseCallback)
      } else {
        return onResponseCallback(e.response).then(
          (it) => it ?? options.defaultMessage ?? "Une erreur non prévue s'est produite. Veuillez réessayer."
        )
      }
    }
    if (e.request) {
      logException(e)
      return Promise.resolve("La requête n'a pu aboutir. Veuillez vérifier votre connexion internet.")
    } else {
      logException(e)
      return Promise.resolve(
        "Une erreur s'est produite. Veuillez réessayer ou bien contacter l'administrateur du site."
      )
    }
  }

  logException(e)
  return Promise.resolve("Une erreur s'est produite. Veuillez réessayer ou bien contacter l'administrateur du site.")
}

export const generateAxiosResponse = <T>(data: T): AxiosResponse<T> => {
  return {
    data,
    status: 200,
    statusText: 'OK',
    headers: {},
    config: {
      headers: new AxiosHeaders(),
    },
  }
}
