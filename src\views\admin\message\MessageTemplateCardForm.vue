<template>
  <VCard class="content-layout">
    <VCardTitle>{{ $props.id === 0 ? 'Création' : 'Modification' }} d'un message prédéfini</VCardTitle>
    <VDivider />
    <VCardText class="content-layout__main">
      <VForm ref="formRef" :readonly="saving.loading">
        <VRow class="flex-column">
          <VCol>
            <VRow class="flex-column">
              <VCol> <VTextarea v-model="request.message" label="Message" :rules="[requiredRule]"></VTextarea> </VCol>
              <VCol> <VSwitch v-model="request.isAccountedMessage" label="Message comptabilisé" /> </VCol>
              <VCol>
                <RemoteCombobox
                  label="Raison Comptabilisation"
                  :model-value="reasonToCreate"
                  :query-for-all="
                    (s, pageable) =>
                      messageAccountingReasonApi.findAll(
                        { search: s, visible: true },
                        { ...pageable, size: 50, sort: ['reason'] }
                      )
                  "
                  :query-for-one="(v) => messageAccountingReasonApi.findOne(v)"
                  item-title="reason"
                  chips
                  @update:model-value="updateAccountingReason"
                >
                </RemoteCombobox>
              </VCol>
            </VRow>
          </VCol>
        </VRow>
      </VForm>
    </VCardText>
    <VDivider />
    <VCardActions>
      <VSpacer />
      <NjBtn :disabled="saving.loading" variant="outlined" @click="$emit('cancel')">Annuler</NjBtn>
      <NjBtn :loading="saving.loading" @click="save">Enregistrer les modifications</NjBtn>
    </VCardActions>
    <ConfirmUnsavedDataDialog v-model:unsaved-data-dialog="unsavedDataDialog" @save="save" />
  </VCard>
</template>

<script setup lang="ts">
import { messageAccountingReasonApi } from '@/api/messageAccountingReason'
import { messageTemplateApi } from '@/api/messageTemplate'
import NjBtn from '@/components/NjBtn.vue'
import { useSnackbarStore } from '@/stores/snackbar'
import {
  makeEmptyMessageTemplate,
  type MessageAccountingReason,
  type MessageTemplate,
  type MessageTemplateRequest,
} from '@/types/message'
import { VSwitch } from 'vuetify/components'
import { VForm, type VTextarea } from 'vuetify/components'
import { requiredRule } from '@/types/rule'

const props = defineProps({
  id: {
    type: Number,
    default: 0,
  },
})

const emit = defineEmits(['cancel', 'saved'])
const formRef = ref<typeof VForm | null>(null)
const request = ref<MessageTemplateRequest>({
  isAccountedMessage: false,
  message: '',
  accountingReasonId: 0,
  updateDateTime: '',
})

const saving = ref(emptyValue<MessageTemplate>())
const savingAccountingReason = ref(emptyValue<MessageAccountingReason>())

const snackbarStore = useSnackbarStore()
const save = async () => {
  const resultValidation = await formRef.value!.validate()
  if (resultValidation.valid) {
    saving.value.loading = true
    if (
      request.value.isAccountedMessage &&
      !request.value.accountingReasonId &&
      typeof reasonToCreate.value === 'string'
    ) {
      const result = await handleAxiosPromise(
        savingAccountingReason,
        messageAccountingReasonApi.create({ reason: reasonToCreate.value!, visible: true })
      )
      if (savingAccountingReason.value.error) {
        return
      }
      request.value.accountingReasonId = result.data.id
    }
    handleAxiosPromise(
      saving,
      props.id === 0 ? messageTemplateApi.create(request.value) : messageTemplateApi.update(props.id, request.value),
      {
        afterSuccess() {
          succeedSave()
          if (props.id) {
            snackbarStore.setSuccess('Modification bien enregistrée')
          } else {
            snackbarStore.setSuccess('Message préenregistré bien créé')
            setTimeout(() => {
              request.value = {
                isAccountedMessage: false,
                message: '',
                accountingReasonId: 0,
                updateDateTime: '',
              }
              reasonToCreate.value = ''
            }, 1000)
          }
          request.value.updateDateTime = saving.value.value!.updateDateTime
          emit('saved')
        },
        afterError() {
          snackbarStore.setError(saving.value.error ?? "Erreur lors de la sauvegarde de l'organisation")
          failedSave()
        },
      }
    )
  }
}

const reasonToCreate = ref<string | number>()
const updateAccountingReason = (v: any) => {
  reasonToCreate.value = v
  if (typeof v === 'string') {
    request.value.accountingReasonId = 0
  } else {
    request.value.accountingReasonId = v
  }
}

// chargement
const messageTemplate = ref(succeedValue(makeEmptyMessageTemplate()))
const { unsavedDataDialog, failedSave, succeedSave, check } = useUnsavedData(request)
watch(
  () => props.id,
  (id) => {
    if (id) {
      handleAxiosPromise(messageTemplate, messageTemplateApi.findOne(id), {
        afterSuccess: (v) => {
          succeedSave()
          request.value = {
            accountingReasonId: v.data.accountingReason?.id,
            isAccountedMessage: v.data.isAccountedMessage,
            message: v.data.message,
            updateDateTime: v.data.updateDateTime,
          }
          reasonToCreate.value = v.data.accountingReason?.id
        },
        afterError: failedSave,
      })
    } else {
      request.value = {
        isAccountedMessage: false,
        message: '',
        accountingReasonId: 0,
        updateDateTime: '',
      }
      reasonToCreate.value = ''
      succeedSave()
    }
    formRef.value?.resetValidation()
  },
  {
    immediate: true,
  }
)
defineExpose({ check })
</script>
