<template>
  <NjDisplayValue :label="label ?? ''" :color-title="colorTitle">
    <template #value>
      <div class="w-50">
        <NjDatePicker
          :model-value="modelValue"
          :rules="rules"
          @update:model-value="emit('update:model-value', $event)"
        />
      </div>
    </template>
  </NjDisplayValue>
</template>
<script lang="ts" setup>
import NjDisplayValue from './NjDisplayValue.vue'
import NjDatePicker from './NjDatePicker.vue'
import type { PropType } from 'vue'
import { type LocalDate } from '@/types/date'
import { type ValidationRule } from '@/types/rule'

defineProps({
  modelValue: {
    type: String as PropType<LocalDate | null>,
  },
  rules: {
    type: Array as PropType<ValidationRule[]>,
    default: () => [],
  },
  colorTitle: {
    type: String,
    default: '#60798b',
  },
  label: String,
})

const emit = defineEmits<{
  'update:model-value': [LocalDate | null]
}>()
</script>
