<template>
  <div class="nj-text-field nj-field">
    <label>{{ $attrs.label }}</label>
    <VTextarea
      v-bind="{ ...$attrs, label: undefined, class: undefined, style: undefined }"
      :class="fieldClass"
      :style="fieldStyle"
    >
      <template v-for="(_, slot) of $slots as {}" #[slot]="scope">
        <slot :name="slot" v-bind="scope as any" />
      </template>
      <template v-if="required" #prepend-inner>
        <div class="nj-field__required-prepend"><VIcon icon="mdi-asterisk" size="x-small" /></div>
      </template>
      <template v-else-if="recommended" #prepend-inner>
        <div class="nj-field__recommended-prepend">✦</div>
      </template>
      <template v-else #prepend-inner>
        <slot name="prepend-inner"></slot>
      </template>
    </VTextarea>
  </div>
</template>

<script setup lang="ts">
import { VTextarea } from 'vuetify/components'

defineProps<{
  required?: boolean
  recommended?: boolean
  fieldClass?: any
  fieldStyle?: any
}>()
</script>
