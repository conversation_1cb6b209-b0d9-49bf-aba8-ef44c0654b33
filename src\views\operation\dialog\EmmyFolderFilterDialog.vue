<template>
  <CardDialog v-model="activeDialog" width="70%" title="Sélectionner un ou plusieurs Dossiers EMMY">
    <VRow class="flex-column my-n2">
      <VCol cols="4">
        <VTextField v-model="search" label="Recherche" prepend-inner-icon="mdi-magnify" />
      </VCol>
      <VCol>
        <NjDataTable
          v-model:selections="localSelected"
          :page="data.value"
          :pageable="pageable"
          :headers="headers"
          checkboxes
          multi-selection
          @update:pageable="updatePageable"
        >
          <template #[`item.stepId`]="{ item }">
            <EmmyFolderStepChip :step-id="item.stepId" />
          </template>
        </NjDataTable>
      </VCol>
    </VRow>
    <template #actions>
      <NjBtn variant="outlined" @click="activeDialog = false"> Annuler </NjBtn>
      <NjBtn @click="updateFilter"> Valider </NjBtn>
    </template>
  </CardDialog>
</template>
<script lang="ts" setup>
import NjDataTable from '@/components/okta/NjDataTable.vue'
import NjBtn from '@/components/NjBtn.vue'
import type { DataTableHeader } from '@/components/okta/NjDataTable.type'
import { cloneDeep, debounce } from 'lodash'
import type { PropType } from 'vue'
import CardDialog from '@/components/CardDialog.vue'
import { emmyFolderApi, type EmmyFolderFilter } from '@/api/emmyFolder'
import type { EmmyFolder } from '@/types/emmyFolder'
import EmmyFolderStepChip from '@/views/emmyfolder/EmmyFolderStepChip.vue'

const props = defineProps({
  modelValue: Boolean,
  selected: {
    type: Array as PropType<EmmyFolder[]>,
    default: () => [],
  },
})
const emit = defineEmits(['update:model-value', 'update:selected'])
const activeDialog = computed<boolean>({
  get() {
    return props.modelValue
  },
  set(v) {
    emit('update:model-value', v)
  },
})

const localSelected = ref<EmmyFolder[]>([])

watch(
  () => props.selected,
  (v) => {
    localSelected.value = v
  },
  {
    immediate: true,
  }
)

const search = ref('')
const { data, pageable, pageFilter, updatePageable, reload } = usePagination<EmmyFolder, EmmyFolderFilter>(
  (filter, pageable) => emmyFolderApi.findAll(filter, pageable),
  {
    search: search.value,
    myFolders: null,
  },
  undefined,
  {
    lazyLoad: true,
  }
)

watch(activeDialog, (v) => {
  if (v) {
    reload()
  }
})

const headers: DataTableHeader[] = [
  {
    title: 'Numéro dossier',
    value: 'emmyCode',
  },
  {
    title: 'Nom',
    value: 'name',
  },
  {
    title: 'Nb. opérations',
    value: 'operationsCount',
    sortable: false,
  },
  {
    title: 'Etape',
    value: 'stepId',
  },
  {
    title: 'Responsable',
    value: 'creationUser',
    formater: (_, value) => (value ? displayFullnameUser(value) : ''),
  },
  {
    title: 'Validation Instruction',
    value: 'na1',
    sortable: false,
  },
  {
    title: 'Envoi PNCEE',
    value: 'pnceeSubmissionDate',
    formater: (_, value) => (value ? formatHumanReadableLocalDate(value) : ''),
  },
  {
    title: 'Délivrance PNCEE',
    value: 'pnceeIssuedDate',
    formater: (_, value) => (value ? formatHumanReadableLocalDate(value) : ''),
  },
  {
    title: 'Numéro Délivrance classique PNCEE',
    value: 'pnceeClassicIssuedNumber',
  },
  {
    title: 'Numéro Délivrance précarité PNCEE',
    value: 'pnceePrecariousnessIssuedNumber',
  },
  {
    title: 'Nb. demandés kWhc',
    value: 'na4',
    sortable: false,
  },
  {
    title: 'Mnt. dem. €',
    value: 'na5',
    sortable: false,
  },
  {
    title: 'Réf. client',
    value: 'na6',
    sortable: false,
  },
]
const updateFilter = () => {
  emit('update:model-value', false)
  emit('update:selected', cloneDeep(localSelected.value))
}

const debounceSearch = debounce((v: string | undefined) => {
  pageFilter.value.search = v!
}, 300)

watch(
  () => search.value,
  (v) => {
    if (v || v === '') {
      data.value.loading = true
      debounceSearch(v)
    }
  },
  {
    immediate: true,
  }
)
</script>
