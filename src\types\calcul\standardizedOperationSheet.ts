import type { ControlOrderExportTemplate } from '../controlOrderExportTemplate'
import type { LocalDate, LocalDateTime } from '../date'
import type { ControlTypes } from '../controlOrganism'
import type { Document } from '../document'
import type { Historisable } from '../historisation'
import type { MappingTable } from './mappingTable'
import type { ParameterFormula } from './parameterFormula'

export interface StandardizedOperationSheet extends Partial<Historisable> {
  id: number
  operationCode: string
  description: string
  parameters: ParameterFormula[]
  startDate: LocalDate
  expirationDate: LocalDate
  conventionalLifespanInYears: number
  epcBonusEligible: boolean
  mappingTables: MappingTable[]
  formula: string
  precariousnessBonusEnabled: boolean
  multipleOperation: boolean
  controlOrderStartDate?: LocalDate
  controlOrderNature?: ControlOrderNature
  controlOrderType?: ControlOrderType
  controlOrderExportTemplate: ControlOrderExportTemplate | null
  allowedControlTypes: ControlTypes[]
  requireCertifiedControlOrganism: boolean
  rgeMandatory: boolean
  substituteStandardizedOperationSheet: StandardizedOperationSheet | null
  subcontractCompulsory: boolean
  heatFund: boolean
  possibilitySyndicate: boolean
  visible: boolean
  certified: boolean
  warningText?: string
  minimumSatisfyingControlRates: MinimumControlRate[] | null
  pdfDocument?: Document
  internalPdfDocument?: Document
  usageBonusIntSheetType?: UsageBonusIntSheetType

  updateDateTime: LocalDateTime
}

export const usageBonusIntSheetTypes = [
  {
    value: 'USABLE_FOR_RES',
    title: 'Utilisable pour secteur RES',
  },
  {
    value: 'USABLE_FOR_BOILER_ROOM',
    title: 'Utilisable pour secteur TERTIAIRE',
  },
] as const
export type UsageBonusIntSheetType = (typeof usageBonusIntSheetTypes)[number]

export interface StandardizedOperationSheetRequest
  extends Omit<
    StandardizedOperationSheet,
    'id' | 'substituteStandardizedOperationSheet' | 'controlOrderExportTemplate' | 'pdfDocument' | 'internalPdfDocument'
  > {
  substituteStandardizedOperationSheetId?: number
  controlOrderExportTemplateId?: number
  pdfDocumentId?: number
  internalPdfDocumentId?: number
}

export const mapToStandardizedOperationSheetRequest = (
  value: StandardizedOperationSheet
): StandardizedOperationSheetRequest => {
  const returnValue = { ...value } as any
  delete returnValue.id
  delete returnValue.controlOrderExportTemplate
  delete returnValue.substituteStandardizedOperationSheet
  return {
    ...returnValue,
    controlOrderExportTemplateId: value.controlOrderExportTemplate?.id,
    substituteStandardizedOperationSheetId: value.substituteStandardizedOperationSheet?.id,
    pdfDocumentId: value.pdfDocument?.id,
    internalPdfDocumentId: value.internalPdfDocument?.id,
  }
}

export interface MinimumControlRate {
  startDate: LocalDate
  endDate: LocalDate
  siteRate: number | null
  contactRate: number | null
}

export const makeEmptyMinimumControlRate = (): MinimumControlRate => ({
  startDate: '',
  endDate: '',
  siteRate: null,
  contactRate: null,
})

export const makeEmptyStandardizedOperationSheet = (): StandardizedOperationSheet => ({
  id: 0,
  operationCode: '',
  mappingTables: [],
  description: '',
  parameters: [],
  expirationDate: '',
  startDate: '',
  conventionalLifespanInYears: 0,
  epcBonusEligible: false,
  formula: '',
  precariousnessBonusEnabled: false,
  multipleOperation: false,
  visible: false,
  certified: false,
  rgeMandatory: false,
  subcontractCompulsory: false,
  heatFund: false,
  possibilitySyndicate: false,
  controlOrderExportTemplate: null,
  substituteStandardizedOperationSheet: null,
  minimumSatisfyingControlRates: null,
  updateDateTime: '',
  allowedControlTypes: [],
  requireCertifiedControlOrganism: false,
})

export type ControlOrderType = 'SITE' | 'CONTACT' | 'SITE_AND_CONTACT'

export const controlOrderTypeLabel: { label: string; value: ControlOrderType }[] = [
  {
    label: 'Contrôle sur site',
    value: 'SITE',
  },
  {
    label: 'Contrôle par contact',
    value: 'CONTACT',
  },
  {
    label: 'Contrôle sur site et par contact',
    value: 'SITE_AND_CONTACT',
  },
]

export const controlOrderNatureLabel = [
  {
    value: 'HUNDRED_PERCENT',
    label: '100 %',
  },
  {
    value: 'SAMPLE',
    label: 'Echantillon',
  },
] as const

export type ControlOrderNature = (typeof controlOrderNatureLabel)[number]['value']

export interface StandardizedOperationSheetFilter {
  ids?: number[]
  validDate?: LocalDate
  search?: string
  operationGroupId?: number
  visible?: boolean
  excluded?: number[]
  hasControlOrder?: boolean
}

export interface StandardizedOperationSheetReduced {
  id: number
  operationCode: string
  startDate: LocalDate
  expirationDate: LocalDate
}

export const needControlReportInfos = (standardizedOperationSheet: StandardizedOperationSheet): boolean => {
  return (
    standardizedOperationSheet.controlOrderNature === 'HUNDRED_PERCENT' &&
    standardizedOperationSheet.controlOrderType === 'SITE'
  )
}
