<template>
  <div class="nj-select nj-field">
    <label>{{ $attrs.label }}</label>
    <VSelect v-bind="{ ...$attrs, label: undefined }">
      <template v-for="(_, slot) of $slots as {}" #[slot]="scope">
        <slot :name="slot" v-bind="scope as any" />
      </template>
      <template v-if="required" #prepend-inner>
        <div class="nj-field__required-prepend"><VIcon icon="mdi-asterisk" size="x-small" /></div>
      </template>
      <template v-else-if="recommended" #prepend-inner>
        <div class="nj-field__recommended-prepend">✦</div>
      </template>
      <template v-else #prepend-inner>
        <slot name="prepend-inner"></slot>
      </template>
    </VSelect>
  </div>
</template>

<script setup lang="ts">
defineProps<{
  required?: boolean
  recommended?: boolean
}>()
</script>
