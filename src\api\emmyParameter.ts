import type { EmmyParameter } from '@/types/emmyParameter'
import type { Page, Pageable } from '@/types/pagination'
import type { AxiosInstance, AxiosPromise } from 'axios'
import axiosInstance from '.'

class EmmyParameterApi {
  public constructor(private axios: AxiosInstance) {}

  public findOne(id: number): AxiosPromise<EmmyParameter> {
    return this.axios.get('/emmy_parameters/' + id)
  }

  public findAll(filter: { label: string }, pageable: Pageable): AxiosPromise<Page<EmmyParameter>> {
    return this.axios.get('/emmy_parameters', {
      params: { ...filter, ...pageable },
    })
  }

  public create(emmyParameter: EmmyParameter): AxiosPromise<EmmyParameter> {
    return this.axios.post('/emmy_parameters', emmyParameter)
  }

  public update(id: number, emmyParameter: EmmyParameter): AxiosPromise<EmmyParameter> {
    return this.axios.put('/emmy_parameters/' + id, emmyParameter)
  }
}

export const emmyParameterApi = new EmmyParameterApi(axiosInstance)
