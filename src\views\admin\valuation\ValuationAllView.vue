<template>
  <NjPage expend-body>
    <template #body>
      <VRow>
        <VCol cols="4">
          <VCard class="content-layout">
            <VCardTitle class="d-flex align-center content-layout__header">
              <span class="d-flex w-100 text-section-title">Types</span>
              <VProgressCircular v-show="savingOrderValuationTypes.loading" indeterminate />
            </VCardTitle>
            <VDivider />
            <VCardText class="content-layout__main pa-0">
              <VList ref="valuationTypeListRef">
                <VListItem
                  v-for="valuationType in copyValuationTypes"
                  :key="valuationType.id"
                  class="ps-2"
                  border
                  :class="{
                    'disabled-row': valuationType.endDate ? new Date(valuationType.endDate) < new Date() : false,
                    'selected-row': valuationType === currentType,
                  }"
                  @click="select(valuationType)"
                >
                  <div class="d-flex align-center">
                    <VIcon class="sortable-handle me-2">mdi-drag</VIcon>
                    <span class="d-flex w-100 text-section-title">{{ valuationType.name }}</span>
                    <NjSwitch
                      v-if="
                        (valuationType.endDate ? new Date(valuationType.endDate) > new Date() : true)
                          ? true
                          : userHasRole(userStore.currentUser, 'ADMIN_PLUS')
                      "
                      inline
                      density="compact"
                      :model-value="valuationType.endDate ? new Date(valuationType.endDate) > new Date() : true"
                      readonly
                      @click="updateValuationStatus(valuationType)"
                    />
                  </div>
                </VListItem>
              </VList>
            </VCardText>
            <VDivider />
            <VCardActions class="justify-end content-layout__footer">
              <NjBtn @click="createTypeDialog = true">Ajouter un type</NjBtn>
              <ValuationTypeDialog v-model="createTypeDialog" :type="makeEmptyValuationType()" @save="load" />
            </VCardActions>
          </VCard>
          <ValuationTypeDialog v-model="deleteDialog" :type="currentType" mode="delete" @save="load" />
          <ValuationTypeDialog v-model="activateDialog" :type="currentType" mode="activate" @save="load" />
        </VCol>
        <VCol cols="8">
          <VCard class="content-layout">
            <VCardTitle class="content-layout__header">
              <VTabs v-model="isPrecariousness">
                <VTab :value="false">Classique</VTab>
                <VTab :value="true">Précarité</VTab>
              </VTabs>
              <VDivider />
            </VCardTitle>
            <VCardText class="content-layout__main">
              <NjDataTable
                :headers="headers"
                :pageable="pageable"
                :page="data.value"
                :on-click-row="userHasRole(userStore.currentUser, 'ADMIN_PLUS') ? openEdit : undefined"
                fixed
                @update:pageable="updatePageable"
              >
                <template #[`item.type`]="{ item }">
                  {{ item.type.name }}
                </template>
              </NjDataTable>
            </VCardText>
            <VCardActions class="justify-end content-layout__footer">
              <NjBtn @click="createValuationDialog = true"> Ajouter une valorisation</NjBtn>
              <ValuationDialog
                v-model="createValuationDialog"
                :valuation="{ ...makeEmptyValuation(), type: currentType, precariousness: isPrecariousness }"
                :active-types="valuationTypes.value!"
                @save="reload"
              />
            </VCardActions>
          </VCard>
          <ValuationDialog
            v-model="edit"
            :valuation="editingValuation"
            :active-types="valuationTypes.value!"
            @save="reload"
          />
        </VCol>
      </VRow>
    </template>
  </NjPage>
</template>
<script lang="ts" setup>
import { valuationApi } from '@/api/valuation'
import { valuationTypeApi } from '@/api/valuationType'
import NjBtn from '@/components/NjBtn.vue'
import NjPage from '@/components/NjPage.vue'
import type { DataTableHeader } from '@/components/okta/NjDataTable.type'
import NjDataTable from '@/components/okta/NjDataTable.vue'
import { useSnackbarStore } from '@/stores/snackbar'
import { useUserStore } from '@/stores/user'
import type { Page } from '@/types/pagination'
import { userHasRole } from '@/types/user'
import {
  makeEmptyValuation,
  makeEmptyValuationType,
  type Valuation,
  type ValuationFilter,
  type ValuationType,
} from '@/types/valuation'
import { useSortable } from '@vueuse/integrations/useSortable'
import { clone, cloneDeep, debounce, isEqual } from 'lodash'
import ValuationDialog from './ValuationDialog.vue'
import ValuationTypeDialog from './ValuationTypeDialog.vue'

const snackbarStore = useSnackbarStore()

const currentType = ref(makeEmptyValuationType())
const valuationTypes = ref(emptyValue<Page<ValuationType>>())
const isPrecariousness = ref(false)

const { data, pageFilter, pageable, updatePageable, reload } = usePagination<Valuation, ValuationFilter>(
  (filter, pageable) => valuationApi.getAll(pageable, filter),
  {
    typeId: currentType.value.id,
    precariousness: isPrecariousness.value,
  },
  {
    page: 0,
    size: 50,
    sort: ['startDate,DESC'],
  }
)

const headers: DataTableHeader[] = [
  {
    title: 'Type',
    value: 'type',
    sortable: false,
  },
  {
    title: 'À partir de',
    value: 'startDate',
    formater: (_, value) => formatHumanReadableLocalDate(value),
  },
  {
    title: '€/MWhc',
    value: 'value',
    formater: (_, value) => formatPriceNumber(value),
    cellClass: 'text-end',
  },
]

const copyValuationTypes = ref<ValuationType[]>([])
const load = () =>
  handleAxiosPromise(valuationTypes, valuationTypeApi.getAll({}), {
    afterSuccess: (response) => {
      copyValuationTypes.value = response.data.content.concat()
      currentType.value = currentType.value.id === 0 ? valuationTypes.value.value!.content[0] : currentType.value
      pageFilter.value.typeId = currentType.value.id
    },
    afterError: () => {
      snackbarStore.setError(
        valuationTypes.value.error ?? 'Une erreur est survenue lors de la récupération des types de valorisation'
      )
    },
  })

const createTypeDialog = ref(false)
const deleteDialog = ref(false)
const activateDialog = ref(false)
const createValuationDialog = ref(false)
const edit = ref(false)
const editingValuation = ref<Valuation>(makeEmptyValuation())
const userStore = useUserStore()

const openEdit = (valuation: Valuation) => {
  editingValuation.value = clone(valuation)
  edit.value = true
}

const savingOrderValuationTypes = ref(emptyValue<ValuationType>())
const debouncedUpdateOrderValuationType = debounce(() => {
  Promise.all(
    copyValuationTypes.value.map((formValuationType, i) => {
      const valuationType = {
        ...formValuationType,
        displayOrder: i,
      }
      const a = valuationTypes.value.value!.content.find((it) => it.id === valuationType.id)
      if (!isEqual(a, valuationType)) {
        return valuationTypeApi.update(valuationType.id, valuationType)
      } else {
        return Promise.resolve()
      }
    })
  )
    .then(() => {
      valuationTypes.value.value!.content = cloneDeep(copyValuationTypes.value).map((it, i) => ({
        ...it,
        displayOrder: i,
      }))
    })
    .catch(async (e) => {
      snackbarStore.setError(await handleAxiosException(e))
    })
    .finally(() => {
      savingOrderValuationTypes.value.loading = false
    })
}, 500)

const updateOrderValuationType = () => {
  savingOrderValuationTypes.value.loading = true
  debouncedUpdateOrderValuationType()
}

const deleteType = (type: ValuationType) => {
  currentType.value = clone(type)
  deleteDialog.value = true
}

const activateType = (type: ValuationType) => {
  currentType.value = clone(type)
  activateDialog.value = true
}

const select = (type: ValuationType) => {
  currentType.value = type
  pageFilter.value.typeId = type.id
}

watch(
  () => isPrecariousness.value,
  (v) => (pageFilter.value.precariousness = v),
  {
    immediate: true,
  }
)

onMounted(load)

// Drag and Drop
const valuationTypeListRef = ref(null)
useSortable(valuationTypeListRef, copyValuationTypes, {
  handle: '.sortable-handle',
})
watch(
  copyValuationTypes,
  () => {
    if (!createTypeDialog.value && !deleteDialog.value && !activateDialog.value) {
      updateOrderValuationType()
    }
  },
  {
    deep: true,
  }
)

const updateValuationStatus = (valuationType: ValuationType) => {
  if (valuationType.endDate ? new Date(valuationType.endDate) > new Date() : true) {
    deleteType(valuationType)
  } else {
    activateType(valuationType)
  }
}
</script>
<style scoped>
.disabled-row {
  background-color: #eee;
  color: lightslategrey;
}

.selected-row {
  background-color: #0af1;
}
</style>
