<template>
  <NjPage v-bind="$attrs">
    <template #body>
      <NjDataTable
        :headers="headers"
        :pageable="pageable"
        :page="data.value"
        :selections="selection"
        class="w-100"
        @update:pageable="updatePageable"
        @update:selections="
          (event) => {
            if (edit) {
              check(() => {
                edit = false
                selection = event
                console.debug('zrezer')
                // succeedSave()
              })
            } else {
              selection = event
              // succeedSave()
            }
            historyListRef?.clear()
          }
        "
      >
      </NjDataTable>
    </template>
  </NjPage>
  <VNavigationDrawer v-if="selection[0]" location="right" style="width: 600px" permanent>
    <VCard class="content-layout">
      <VCardTitle class="d-flex content-layout__header py-0" style="min-height: 48px">
        Détails
        <VSpacer />
        <NjIconBtn color="primary" icon="mdi-pencil" rounded="0" @click="onClickPencil" />
        <NjIconBtn color="primary" icon="mdi-close" class="me-n4 rounded-0" @click="closeDrawer" />
      </VCardTitle>
      <VDivider />
      <VTabs v-model="tab" class="content-layout__header">
        <VTab value="data">Données</VTab>
        <VTab value="history">Traçabilité</VTab>
      </VTabs>
      <VDivider />
      <VWindow v-model="tab" class="content-layout__main">
        <VWindowItem value="data">
          <VCardText class="content-layout__main pa-0">
            <div class="pa-4">
              <VRow class="flex-column" dense>
                <VCol>
                  <NjDisplayValue label="Zone" :value="selection[0]?.zoneId" />
                </VCol>
                <VCol>
                  <NjDisplayValue label="Description" :value="selection[0]?.description" />
                </VCol>
                <VCol>
                  <NjDisplayValue
                    label="Référent territoire"
                    :value="displayFullnameUser(selection[0].territoryDetails?.territoryReferent)"
                  >
                    <template #value>
                      <div v-if="edit" class="w-50">
                        <RemoteAutoComplete
                          v-model="selection[0].territoryDetails.territoryReferent"
                          :query-for-one="(id) => userApi.getOne(id)"
                          :query-for-all="
                            (v: string, pageable: Pageable) => userApi.getAll(pageable, { active: true, search: v })
                          "
                          :item-title="(item: User) => displayFullnameUser(item)"
                          return-object
                          clearable
                        />
                      </div>
                    </template>
                  </NjDisplayValue>
                </VCol>
                <VCol>
                  <NjDisplayValue
                    label="Chargés de mission"
                    :value="
                      (selection[0].territoryDetails?.missionManagers ?? [])
                        .map((u) => displayFullnameUser(u))
                        .join(', ')
                    "
                  >
                    <template #value>
                      <div v-if="edit" class="w-50">
                        <RemoteAutoComplete
                          v-model="selection[0].territoryDetails.missionManagers"
                          :query-for-one="(id) => userApi.getOne(id)"
                          :query-for-all="
                            (v: string, pageable: Pageable) => userApi.getAll(pageable, { active: true, search: v })
                          "
                          :item-title="(item: User) => displayFullnameUser(item)"
                          return-object
                          clearable
                          multiple
                          chip
                        />
                      </div>
                    </template>
                  </NjDisplayValue>
                </VCol>

                <VCol v-for="(v, k) in jobTitles" :key="k">
                  <NjDisplayValue :label="v" :value="displayFullnameUser(selection[0].territoryDetails[k])">
                    <template v-if="edit" #value>
                      <div class="w-50">
                        <RemoteAutoComplete
                          v-model="selection[0].territoryDetails[k]"
                          :query-for-one="(id) => userApi.getOne(id)"
                          :query-for-all="
                            (v: string, pageable: Pageable) => userApi.getAll(pageable, { active: true, search: v })
                          "
                          :item-title="(item: User) => displayFullnameUser(item)"
                          return-object
                          clearable
                        />
                      </div>
                    </template>
                  </NjDisplayValue>
                </VCol>
              </VRow>
            </div>
          </VCardText>
        </VWindowItem>

        <VWindowItem value="history">
          <VRow dense class="flex-column my-2" style="gap: 8px">
            <NjInfiniteScroll
              ref="historyList"
              :callback="
                (p) =>
                  territoryDetailsHistoryApi.findAll(
                    { size: 10, page: p, sort: ['creationDateTime,DESC'] },
                    { territoryId: selection[0]?.id }
                  )
              "
            >
              <template #item="{ history }">
                <HistoryCard
                  :display-properties="territoryDetailsHistoryDisplayProperties"
                  :model-value="history"
                  class="mx-4"
                />
              </template>
              <template #end-data>
                <VDivider />
                <div class="px-4 py-2">Fin de l'historique</div>
              </template>
              <template #no-data>
                <div class="pa-4">Il n'y a pas d'historique pour ce territoire.</div>
              </template>
            </NjInfiniteScroll>
          </VRow>
        </VWindowItem>
      </VWindow>
      <template v-if="edit">
        <NjDivider />
        <VCardActions>
          <VSpacer />
          <NjBtn
            variant="outlined"
            @click="
              () => {
                check(() => {
                  edit = false
                })
              }
            "
          >
            Annuler
          </NjBtn>
          <NjBtn @click="save()">Enregistrer</NjBtn>
        </VCardActions>
      </template>
    </VCard>
  </VNavigationDrawer>
  <ConfirmUnsavedDataDialog v-model:unsaved-data-dialog="unsavedDataDialog" @save="save" />
</template>
<script setup lang="ts">
import type { TerritoryFilter } from '@/api/territory'
import { territoryDetailsApi } from '@/api/territoryDetails'
import { userApi } from '@/api/user'
import RemoteAutoComplete from '@/components/RemoteAutoComplete.vue'
import type { DataTableHeader } from '@/components/okta/NjDataTable.type'
import { useSnackbarStore } from '@/stores/snackbar'
import type { Pageable } from '@/types/pagination'
import type { Territory } from '@/types/territory'
import { type User, displayFullnameUser } from '@/types/user'
import { cloneDeep } from 'lodash'
import { territoryDetailsHistoryApi } from '@/api/territoryHistory'
import { territoryDetailsHistoryDisplayProperties } from '@/types/history'

const { data, pageable, updatePageable, reload } = usePaginationInQuery<Territory, TerritoryFilter>(
  (filter, pageable) => territoryApi.findAll(filter, pageable)
)

const headers: DataTableHeader<Territory>[] = [
  {
    title: 'Description',
    value: 'description',
  },
  {
    title: 'Zone',
    value: 'zoneId',
  },
  {
    title: 'Référent territoire',
    value: 'territoryDetails.territoryReferent',
    formater: (_, value: User | undefined) => displayFullnameUser(value),
  },
  {
    title: 'Chargés de mission',
    value: 'territoryDetails.missionManagers',
    formater: (_, value: User[]) => (value ?? []).map((u) => displayFullnameUser(u)).join(', '),
  },
]

const snackbarStore = useSnackbarStore()

const selection = ref<Territory[]>([])
const edit = ref(false)

const { unsavedDataDialog, failedSave, succeedSave, check } = useUnsavedData(selection)

const save = () => {
  territoryDetailsApi
    .save(selection.value[0].id, {
      territoryReferentUserId: selection.value[0].territoryDetails.territoryReferent?.id,
      missionManagerIds: selection.value[0].territoryDetails.missionManagers?.map((u) => u.id) ?? [],
      operationsAndPerformanceDirectorUserId: selection.value[0].territoryDetails.operationsAndPerformanceDirector?.id,
      operationTerritoryCfoUserId: selection.value[0].territoryDetails.operationTerritoryCfo?.id,
      networkDevelopmentDirectorUserId: selection.value[0].territoryDetails.networkDevelopmentDirector?.id,
      industryDevelopmentDirectorUserId: selection.value[0].territoryDetails.industryDevelopmentDirector?.id,
      buildingDevelopmentDirectorUserId: selection.value[0].territoryDetails.buildingDevelopmentDirector?.id,
      developmentDirectorUserId: selection.value[0].territoryDetails.developmentDirector?.id,
      territoryDirectorUserId: selection.value[0].territoryDetails.territoryDirector?.id,
    })
    .then(() => {
      snackbarStore.setSuccess('Les détails du territoire ont bien été mis à jour')
      succeedSave()
      edit.value = false
      reload()
      historyListRef.value?.reload()
    })
    .catch(async (err) => {
      snackbarStore.setError(await handleAxiosException(err))
      failedSave()
    })
}

const closeDrawer = () => {
  console.debug('TerritoryAllView#closeDrawer')
  if (edit.value) {
    check(() => {
      edit.value = false
      selection.value = []
    })
  } else {
    selection.value = []
  }
}
const onClickPencil = () => {
  console.debug('onClickPencil', edit.value)
  if (!edit.value) {
    edit.value = !edit.value
    selection.value = cloneDeep(selection.value)
    selection.value[0].territoryDetails.missionManagers ??= []
    succeedSave()
  } else {
    check(() => {
      edit.value = !edit.value
    })
  }
}

const jobTitles = {
  operationsAndPerformanceDirector: 'Directeur des opérations et performance',
  operationTerritoryCfo: 'CFO Opérationnel du Territoire',
  networkDevelopmentDirector: 'Directeur Développement Réseau',
  industryDevelopmentDirector: 'Directeur Développement Industrie',
  buildingDevelopmentDirector: 'Directeur Développement Bâtiments',
  developmentDirector: 'Directeur Développement',
  territoryDirector: 'Directeur Territoire',
}

const tab = ref()

const historyListRef = useTemplateRef('historyList')
</script>
