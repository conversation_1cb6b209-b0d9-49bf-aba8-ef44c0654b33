<template>
  <StepChip :text="text" :status="type" :messages="messages" />
</template>
<script lang="ts" setup>
import type { Operation } from '@/types/operation'
import type { PropType } from 'vue'
import { useValidateOperation } from '../validateOperationComposition'
import type { Valuation } from '@/types/valuation'
import StepChip from './StepChip.vue'

const props = defineProps({
  operation: {
    type: Object as PropType<Operation>,
    default: makeEmptyOperation,
  },
  valuations: {
    type: Array as PropType<Valuation[]>,
  },
  noAlert: Boolean,
})

const emit = defineEmits<{
  'update:type': ['success' | 'warning' | 'error' | 'cancelled']
}>()

const text = computed(() => {
  if (props.operation.stepId >= 110) {
    return 'Validée'
  }
  if (props.operation.status === 'DOING' && props.operation.stepId !== 0) {
    return props.operation.stepId.toString()
  }
  switch (props.operation.status) {
    case 'DONE':
      return 'Validée'
    case 'LOST':
      return 'Offre Perdue'
    case 'IMPROPER':
      return 'Non Conforme'
    case 'CANCELLED':
      return 'Abandonnée'
    case 'KO_PNCEE':
      return 'Refusée par le PNCEE'
    default:
      if (props.operation.stepId === 0) {
        return 'Simulation'
      }
      return props.operation.stepId.toString()
  }
})

const { operationRuleErrors, operationRuleWarnings } = useValidateOperation(
  computed(() => props.operation),
  ref()
)

const messages = computed(() => {
  return props.operation.status == 'DOING'
    ? operationRuleErrors.value.map((i) => i.toString()).concat(operationRuleWarnings.value.map((i) => i.toString()))
    : []
})

const type = computed(() => {
  if (props.operation.status !== 'DOING' || props.operation.stepId > 100) {
    return 'cancelled'
  } else if (props.noAlert) {
    return 'success'
  } else if (operationRuleErrors.value.length) {
    return 'error'
  } else if (isValuationDifferent(props.operation, props.valuations)) {
    return 'warning'
  } else if (operationRuleWarnings.value.length) {
    return 'warning'
  } else {
    return 'success'
  }
})
watch(type, (v) => emit('update:type', v), { immediate: true })
</script>
