import type { Page, Pageable } from '@/types/pagination'
import type { AxiosInstance, AxiosPromise } from 'axios'
import axiosInstance from '.'
import type { EntityDetailsHistory } from '@/types/history'

const entityDetailsUrl = '/entity_details_histories'

export interface EntityDetailsHistoryFilter {
  entityId?: string
}

class EntityDetailsHistoryApi {
  public constructor(private axios: AxiosInstance) {}

  public findAll(pageable: Pageable, filter: EntityDetailsHistoryFilter): AxiosPromise<Page<EntityDetailsHistory>> {
    return this.axios.get(entityDetailsUrl, {
      params: { ...filter, ...pageable },
    })
  }
}

export const entityDetailsHistoryApi = new EntityDetailsHistoryApi(axiosInstance)
