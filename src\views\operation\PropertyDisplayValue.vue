<template>
  <VInput :model-value="modelValue" :rules="rules" hide-details="auto">
    <VCard v-if="modelValue || finalAddress" class="w-100">
      <VCardText class="pa-2">
        <template v-if="modelValue">
          <NjDisplayValue label="Code Installation" :value="modelValue.code"></NjDisplayValue>
        </template>

        <template v-if="usingOtherAddress">
          <div class="d-flex align-center">
            <VIcon icon="mdi-alert" />Adresse et/ou Nom d'installation saisie manuellement
          </div>
        </template>

        <NjDisplayValue label="Nom" :value="finalAddress ? finalPropertyName : modelValue?.name"></NjDisplayValue>

        <template v-if="finalAddress">
          <NjDisplayValue label="Adresse" :value="formatAddressable(finalAddress)"></NjDisplayValue>
        </template>
        <template v-else-if="modelValue">
          <NjDisplayValue label="Adresse" :value="formatAddressable(modelValue)"></NjDisplayValue>
        </template>
      </VCardText>
    </VCard>
  </VInput>
</template>

<script setup lang="ts">
import { formatAddressable, type Address } from '@/types/address'
import type { Property } from '@/types/property'
import type { ValidationRule } from '@/types/rule'
import { toString } from 'lodash'
import type { PropType } from 'vue'

const props = defineProps({
  modelValue: {
    type: Object as PropType<Property | null>,
  },
  rules: {
    type: Array as PropType<Array<ValidationRule>>,
  },
  finalAddress: {
    type: Object as PropType<Address>,
  },
  finalPropertyName: {
    type: String,
    default: '',
  },
})

const usingOtherAddress = computed(() => {
  return (
    props.finalAddress &&
    (props.finalPropertyName !== props.modelValue?.name ||
      props.finalAddress?.postalCode !== props.modelValue?.postalCode ||
      props.finalAddress?.city !== props.modelValue?.city ||
      props.finalAddress?.street.trim() !==
        (toString(props.modelValue?.streetNumber) + ' ' + toString(props.modelValue?.streetName)).trim())
  )
})
</script>
