import type { Company, CompanyRequest } from '@/types/company'
import type { Entity } from '@/types/entity'
import type { Page, Pageable } from '@/types/pagination'
import type { AxiosInstance, AxiosPromise } from 'axios'
import axiosInstance from '.'

export interface CompanyFilter {
  siret?: string
  name?: string
  toObserve?: boolean
  enabled?: boolean
}

class CompanyApi {
  public constructor(private axios: AxiosInstance) {}

  public create(request: CompanyRequest): AxiosPromise<Company> {
    return this.axios.put('companies/' + request.id, request)
  }

  public getAll(pageable: Pageable, filter: CompanyFilter): AxiosPromise<Page<Company>> {
    return this.axios.get('companies', {
      params: { ...filter, ...pageable },
    })
  }

  public getOne(id: number): AxiosPromise<Entity> {
    return this.axios.get('companies/' + id)
  }

  public updateLogo(id: number, blob?: Blob): AxiosPromise<void> {
    const formData = new FormData()
    if (blob) {
      formData.append('file', blob)
    }

    return this.axios.put('companies/' + id + '/logo', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    })
  }
}

export const companyApi = new CompanyApi(axiosInstance)
