import type { LocalDateTime } from '@/types/date'
import type { Page, Pageable } from '@/types/pagination'
import type { AxiosInstance, AxiosPromise } from 'axios'
import axiosInstance from '.'

export type KafkaProcessedOffsetsFilter = Partial<{
  outOfDate: boolean
  lastProcessedDateTimeLower: LocalDateTime
}>

export interface KafkaProcessedOffsets {
  topic: string
  partition: number
  processedOffset: number
  lastProcessedDateTime?: LocalDateTime
  maxOffset: number
}

class KafkaProcessedOffsetsApi {
  public constructor(private axios: AxiosInstance) {}

  public getAll(pageable: Pageable, filter: KafkaProcessedOffsetsFilter): AxiosPromise<Page<KafkaProcessedOffsets>> {
    return this.axios.get('kafka_processed_offsets', {
      params: { ...pageable, ...filter },
    })
  }
}

export const kafkaProcessedOffsetsApi = new KafkaProcessedOffsetsApi(axiosInstance)
