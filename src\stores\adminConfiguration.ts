import type { LocalAdminConfiguration } from '@/types/adminConfiguration'
import { defineStore } from 'pinia'

export const useAdminConfigurationStore = defineStore('adminConfiguration', () => {
  const precariousnessLink = ref<LocalAdminConfiguration>()
  const expirationPeriod = ref<LocalAdminConfiguration>()
  const commitmentPeriod = ref<LocalAdminConfiguration>()
  const workPeriod = ref<LocalAdminConfiguration>()
  const vfDocumentTypeId = ref<LocalAdminConfiguration>()
  const conventionDocumentTypeId = ref<LocalAdminConfiguration>()
  const swornStatementDocumentTypeId = ref<LocalAdminConfiguration>()
  const pvDeReceptionDocumentTypeId = ref<LocalAdminConfiguration>()
  const profitabilityThreshold = ref<LocalAdminConfiguration>()
  const rgeEndofValidityAlertDayNumber = ref<LocalAdminConfiguration>()
  const cumulLink = ref<LocalAdminConfiguration>()
  const numberOfDaysForValidateStep50 = ref<LocalAdminConfiguration>()
  const validationOfStep70InControlOrderBatchAlert = ref<LocalAdminConfiguration>()
  const validateStep75To80Alert = ref<LocalAdminConfiguration>()
  const validateStep75To75CAlert = ref<LocalAdminConfiguration>()
  const validateStep75CAlert = ref<LocalAdminConfiguration>()
  const validateStep75DAlert = ref<LocalAdminConfiguration>()
  const validateStep75AAlert = ref<LocalAdminConfiguration>()
  const validateStep50FirstAlert = ref<LocalAdminConfiguration>()
  const validateStep50SecondAlert = ref<LocalAdminConfiguration>()
  const boostBonusEligibilityDocument = ref<LocalAdminConfiguration>()
  const epcEligibilityDocument = ref<LocalAdminConfiguration>()
  const operationalDashboardLink = ref<LocalAdminConfiguration>()
  const yammerLink = ref<LocalAdminConfiguration>()
  const sharepointLink = ref<LocalAdminConfiguration>()

  const haveData = ref(false)

  let timeoutId: undefined | number = undefined

  const load = async () => {
    clearTimeouts()

    return await adminconfigurationApi
      .getAll()
      .then((r) => {
        haveData.value = true
        r.data.forEach((v: LocalAdminConfiguration) => {
          if (v.type === 'NUMERIC') {
            v.valueAsInt = parseInt(v.data)
          }
          switch (v.name) {
            case 'Alerte date expiration FOS':
              expirationPeriod.value = v
              break
            case 'Alerte date engagement estimée':
              commitmentPeriod.value = v
              break
            case 'Alerte date fin de travaux estimée':
              workPeriod.value = v
              break
            case 'VF document type id':
              vfDocumentTypeId.value = v
              break
            case 'Convention document type id':
              conventionDocumentTypeId.value = v
              break
            case "Attestation sur l'honneur document type id":
              swornStatementDocumentTypeId.value = v
              break
            case 'PV de reception document type id':
              pvDeReceptionDocumentTypeId.value = v
              break
            case 'Seuil de rentabilité':
              profitabilityThreshold.value = v
              break
            case 'Alerte date de fin de validité du RGE':
              rgeEndofValidityAlertDayNumber.value = v
              break
            case 'Lien cumul':
              cumulLink.value = v
              break
            case 'compteur chrono':
              break
            case 'Lien précarité':
              precariousnessLink.value = v
              break
            case 'Alerte validation étape 50':
              numberOfDaysForValidateStep50.value = v
              break
            case 'Alerte validation étape 70 dans un lot de contrôle':
              validationOfStep70InControlOrderBatchAlert.value = v
              break
            case 'Alerte délai de validation étape 75 à 80':
              validateStep75To80Alert.value = v
              break
            case 'Alerte délai de validation étape 75 à 75C pour les opérations non satisfaisantes':
              validateStep75To75CAlert.value = v
              break
            case 'Alerte délai de validation étape 75C à 75D pour les opérations non satisfaisantes':
              validateStep75CAlert.value = v
              break
            case 'Alerte délai de validation de l étape 75D à 80 pour les opérations non satisfaisantes':
              validateStep75DAlert.value = v
              break
            case 'Alerte délai de validation de l étape 70A':
              validateStep75AAlert.value = v
              break
            case 'Document des conditions d éligibilités au coup de pouce':
              boostBonusEligibilityDocument.value = v
              break
            case 'Document des conditions d éligibilités CPE':
              epcEligibilityDocument.value = v
              break
            case "Première relance pour valider l'étape 50":
              validateStep50FirstAlert.value = v
              break
            case "Deuxième relance pour valider l'étape 50":
              validateStep50SecondAlert.value = v
              break
            case 'Lien PBI TDB operationnel':
              operationalDashboardLink.value = v
              break
            case 'Lien Yammer':
              yammerLink.value = v
              break
            case 'Lien Sharepoint':
              sharepointLink.value = v
              break
            default:
              logException(v.name)
          }
        })
        timeoutId = setTimeout(load, 30 * 60 * 1000)
      })
      .catch((e) => {
        logException(e)
        timeoutId = setTimeout(load, (haveData.value ? 30 * 60 : 5) * 1000)
      })
  }

  const clearTimeouts = () => {
    if (timeoutId !== undefined) {
      clearTimeout(timeoutId)
    }
  }

  return {
    expirationPeriod,
    commitmentPeriod,
    workPeriod,
    vfDocumentTypeId,
    profitabilityThreshold,
    rgeEndofValidityAlertDayNumber,
    cumulLink,
    load,
    clearTimeouts,
    precariousnessLink,
    numberOfDaysForValidateStep50,
    validationOfStep70InControlOrderBatchAlert,
    validateStep75To80Alert,
    validateStep75To75CAlert,
    validateStep75CAlert,
    validateStep75DAlert,
    validateStep75AAlert,
    boostBonusEligibilityDocument,
    epcEligibilityDocument,
    conventionDocumentTypeId,
    swornStatementDocumentTypeId,
    pvDeReceptionDocumentTypeId,
    validateStep50FirstAlert,
    validateStep50SecondAlert,
    operationalDashboardLink,
    yammerLink,
    sharepointLink,
  }
})
