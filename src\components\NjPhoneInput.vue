<template>
  <VTextField
    v-model="input"
    :error-messages="errorMessage"
    :rules="[() => errorMessage ?? true]"
    @update:model-value="updateValue"
  />
</template>

<script setup lang="ts">
import { type ParsedPhoneNumber, parsePhoneNumber } from 'awesome-phonenumber'

const props = withDefaults(
  defineProps<{
    modelValue?: string
    required?: boolean
    defaultRegionCode?: string
    includedRegionCodes?: string[]
  }>(),
  { required: false, defaultRegionCode: 'FR' }
)

const emit = defineEmits<{
  'update:model-value': [string]
}>()

const input = ref<string>()
const pn = shallowRef<ParsedPhoneNumber | undefined>()
const updateValue = (value?: string) => {
  if (value) {
    pn.value = parsePhoneNumber(value, { regionCode: props.defaultRegionCode })
    if (pn.value.valid) {
      if (pn.value.regionCode === props.defaultRegionCode) {
        emit('update:model-value', pn.value.number.national)
      } else {
        if (props.includedRegionCodes?.includes(pn.value.regionCode)) {
          emit('update:model-value', pn.value.number.international)
        }
      }
    }
  } else {
    pn.value = undefined
  }
}

const errorMessage = computed(() => {
  if (!pn.value) {
    return props.required ? 'Champs requis' : undefined
  } else if (!pn.value.valid) {
    return 'Numéro de téléphone invalide'
  } else if (props.includedRegionCodes?.includes(pn.value.regionCode) === false) {
    return "Doit provenir d'un de ces pays: " + props.includedRegionCodes
  } else {
    return undefined
  }
})

watch(
  () => props.modelValue,
  (v) => {
    if (v !== pn.value?.number?.input) {
      updateValue(v)
      input.value = v
    }
  },
  {
    immediate: true,
  }
)
</script>
