<template>
  <CardDialog v-model="modelValue" width="70%" title="Sélectionner une ou plusieurs organisations" no-actions>
    <template v-if="$slots['activator'] !== undefined" #activator="scope">
      <slot name="activator" v-bind="scope"></slot>
    </template>
    <EntitySelect :selected="props.selected" @previous="modelValue = false" @update:selected="updateSelected" />
  </CardDialog>
</template>
<script lang="ts" setup>
import type { PropType } from 'vue'
import type { Entity } from '@/types/entity'
import CardDialog from '@/components/CardDialog.vue'
import EntitySelect from './EntitySelect.vue'

const modelValue = defineModel<boolean>()
const props = defineProps({
  selected: Array as PropType<Entity[]>,
})

const emit = defineEmits<{
  'update:selected': [value: Entity[]]
}>()

const updateSelected = (orgs: Entity[]) => {
  emit('update:selected', orgs)
  modelValue.value = false
}
</script>
