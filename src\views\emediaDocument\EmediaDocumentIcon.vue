<template>
  <VTooltip v-if="emediaId">
    <template #activator="{ props }">
      <VIcon v-bind="props" icon="mdi-network-outline" />
    </template>
    Document sur eMedia
  </VTooltip>
  <VTooltip v-if="emediaErrors">
    <template #activator="{ props }">
      <VIcon v-bind="props" color="error" icon="mdi-alert-circle" />
    </template>
    Tentative d'envoi sur eMedia échoué (erreur : {{ emediaErrors }})
  </VTooltip>
</template>
<script setup lang="ts">
import { defineProps } from 'vue'

defineProps({ emediaId: String, emediaErrors: String })
</script>
