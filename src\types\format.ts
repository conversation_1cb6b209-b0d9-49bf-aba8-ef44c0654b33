const determineSeparation = /(\s)/g

const priceFormater = Intl.NumberFormat(undefined, { maximumFractionDigits: 2, currency: 'EUR', style: 'currency' })
export const formatPriceNumber = (v: number | undefined | null) =>
  v != null ? priceFormater.format(v).replace(determineSeparation, ' ') : undefined

const kwhcFormater = Intl.NumberFormat(undefined, { maximumFractionDigits: 2 })
export const formatKwhcNumber = (v: number | undefined | null) =>
  v != null ? kwhcFormater.format(v).replace(determineSeparation, ' ') + ' kWhc' : undefined

const numberFormater = Intl.NumberFormat(undefined, { maximumFractionDigits: 2 })
export const formatNumber = (v: number | undefined | null) =>
  v != null ? numberFormater.format(v).replace(determineSeparation, ' ') : undefined

const naturalNumberFormater = Intl.NumberFormat(undefined, { maximumFractionDigits: 0 })
export const formatNaturalNumber = (v: number | undefined | null) =>
  v != null ? naturalNumberFormater.format(v).replace(determineSeparation, ' ') : undefined

export const formatKiloNumber = (value: number | undefined | null) => {
  if (value == null) {
    return ''
  }
  if (value >= 1000000) {
    return Math.floor(value / 1000000).toLocaleString() + ' G'
  } else if (value >= 1000) {
    return Math.floor(value / 1000).toLocaleString() + ' M'
  }
  return value.toLocaleString() + ' k'
}

export const formatKiloPriceNumber = (value: number | undefined | null) => {
  if (value == null) {
    return ''
  }
  if (value >= 1000000000) {
    return Math.floor(value / 1000000000).toLocaleString() + ' G€'
  } else if (value >= 1000000) {
    return Math.floor(value / 1000000).toLocaleString() + ' M€'
  } else if (value >= 1000) {
    return Math.floor(value / 1000).toLocaleString() + ' k€'
  }
  return value.toLocaleString() + ' €'
}
