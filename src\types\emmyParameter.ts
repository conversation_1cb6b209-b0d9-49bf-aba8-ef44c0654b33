export interface EmmyParameter {
  id: number
  position: number
  label: string
  comment: string
  referenceText: string
  required: boolean
  staticValue: OperationStaticValue | null
  defaultValue: string
}

export const operationStaticValue = [
  'standardizedOperationSheetCode',
  'signedDate',
  'actualEndWorks',
  'chronoCode',
  'finalAddressStreet',
  'finalAddressPostalCode',
  'finalAddressCity',
  'finalDepartement',
  'additionalPostalAddress',
  'beneficiarySocialReason',
  'beneficiarySiren',
  'beneficiaryAddress',
  'beneficiaryPostalCode',
  'beneficiaryCity',
  'beneficiaryPhoneNumber',
  'beneficiaryEmail',
  'beneficiaryLastName',
  'beneficiaryFirstName',
  'beneficiaryCapacity',
  'cumacClassic',
  'cumacPrecarite',
  'customerOfferNumber',
  'offersDispatchDate',
  'customerFinancialIncentive',
  'installationName',
  'standardizedOperationSheetStartDate',
  'standardizedOperationSheetExpirationDate',
  'subcontractorSiret',
  'subcontractorSiren',
  'subcontractorSocialReason',
  'controlOrganismSiren',
  'controlOrganismSocialReason',
  'operationName',
  'sirenOrganisation',
  'siretOrganisation',
  'socialReasonOrganisation',
  'comment',
  'professionalSocialReason',
  'professionalSiren',
  'professionalSiret',
  'controlOrderType',
  'controlOrderStatus',
  'controlOrderAfterSalesServiceStatus',
  'controlOrderCommentary',
  'controlOrderSavActive',
  'controlOrderConformity',
  'controlOrderBatchCodeHistories',
  'emmyCodeEmmyFolder',
  'worksConformityAfterSalesServiceControlOrderDetails',
  'finalAddressStreetWithAdditionnal',
  'natureOfTheActiveIncentiveRole',
  'natureOfTheBonnification',
  'coOwnerShipSyndicateName',
  'coOwnerShipSyndicateImmatriculationNumber',
] as const

export type OperationStaticValue = (typeof operationStaticValue)[number]

export function makeEmptyEmmyParamter(): EmmyParameter {
  return {
    id: 0,
    position: 0,
    label: '',
    comment: '',
    referenceText: '',
    required: false,
    staticValue: null,
    defaultValue: '',
  }
}

type OperationStaticValueLabel = {
  [key in OperationStaticValue]: { label: string }
}

export const operationStaticValueLabel: OperationStaticValueLabel = {
  installationName: {
    label: "Nom de l'installation",
  },
  standardizedOperationSheetCode: {
    label: 'Code operation',
  },
  signedDate: {
    label: "Date d'engagement",
  },
  actualEndWorks: {
    label: 'Date de fin de travaux',
  },
  chronoCode: {
    label: 'Chrono',
  },
  finalAddressStreet: {
    label: "Rue de l'installation",
  },
  finalAddressPostalCode: {
    label: "Code postal l'installation",
  },
  finalAddressCity: {
    label: "Ville de l'installation",
  },
  finalDepartement: {
    label: "Département de l'installation",
  },
  finalAddressStreetWithAdditionnal: {
    label: "Rue de l'installation avec informations complémentaires",
  },
  additionalPostalAddress: {
    label: "Information additionel de l'adresse d'installation",
  },
  beneficiarySocialReason: {
    label: 'Raison social du bénéficiaire',
  },
  beneficiarySiren: {
    label: 'Siren du bénéficiaire',
  },
  beneficiaryAddress: {
    label: 'Adresse du bénéficiaire',
  },
  beneficiaryPostalCode: {
    label: 'Code postal du bénéficiaire',
  },
  beneficiaryCity: {
    label: 'Ville du bénéficiaire',
  },
  beneficiaryPhoneNumber: {
    label: 'Numéro de téléphone du bénéficiaire',
  },
  beneficiaryEmail: {
    label: 'Mail du bénéficiaire',
  },
  beneficiaryLastName: {
    label: 'Nom du bénéficiaire',
  },
  beneficiaryFirstName: {
    label: 'Prénom du bénéficiaire',
  },
  beneficiaryCapacity: {
    label: 'Status Légal du bénéficiaire',
  },
  cumacClassic: {
    label: "Cumac Classic de l'opération",
  },
  cumacPrecarite: {
    label: "Cumac Précarité de l'opération",
  },
  customerOfferNumber: {
    label: "Numéro de l'offre client",
  },
  offersDispatchDate: {
    label: "Date de l'offre client",
  },
  customerFinancialIncentive: {
    label: 'Incitation financière',
  },
  standardizedOperationSheetStartDate: {
    label: "Date de début de validité de la fiche d'opération",
  },
  standardizedOperationSheetExpirationDate: {
    label: "Date de fin de validité de la fiche d'opération",
  },
  subcontractorSiret: {
    label: 'SIRET du sous traitant',
  },
  subcontractorSocialReason: {
    label: 'Raison sociale du sous traitant',
  },
  controlOrganismSiren: {
    label: "SIREN de l'organisme de contrôle",
  },
  controlOrganismSocialReason: {
    label: "Raison sociale de l'organisme de contrôle",
  },
  subcontractorSiren: {
    label: 'SIREN du sous traitant',
  },
  operationName: {
    label: "Nom de l'opération",
  },
  sirenOrganisation: {
    label: "Siren de l'organisation",
  },
  siretOrganisation: {
    label: "Siret de l'organisation",
  },
  socialReasonOrganisation: {
    label: "Raison sociale de l'organisation",
  },
  professionalSocialReason: {
    label: 'Raison sociale du professionnel',
  },
  professionalSiren: {
    label: 'Siren du professionel',
  },
  professionalSiret: {
    label: 'Siret du professionel',
  },
  comment: {
    label: 'Commentaire',
  },
  controlOrderStatus: {
    label: 'Status contrôle',
  },
  controlOrderAfterSalesServiceStatus: {
    label: 'Status SAV du contrôle',
  },
  controlOrderCommentary: {
    label: 'Commentaire du contrôle',
  },
  natureOfTheActiveIncentiveRole: {
    label: 'Nature du rôle actif incitatif',
  },
  controlOrderType: {
    label: 'Arrêté contrôle type de contrôle',
  },
  controlOrderSavActive: {
    label: 'Arrêté contrôle SAV actif',
  },
  controlOrderConformity: {
    label: 'Arrêté contrôle conformité OUI/NON',
  },
  controlOrderBatchCodeHistories: {
    label: "Arrêté controle cycle de vie de l'opération",
  },
  emmyCodeEmmyFolder: {
    label: 'Dossier emmy code emmy',
  },
  worksConformityAfterSalesServiceControlOrderDetails: {
    label: 'Arrêté contrôle conformité après travaux',
  },
  natureOfTheBonnification: {
    label: 'Nature de la bonification (CPE, CDP, NGP)',
  },
  coOwnerShipSyndicateName: {
    label: 'Syndicat de copropriété',
  },
  coOwnerShipSyndicateImmatriculationNumber: {
    label: "Numéro d'immatriculation du syndicat de copropriété",
  },
}
