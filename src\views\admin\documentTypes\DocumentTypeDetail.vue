<template>
  <VRow class="flex-column flex-grow-0 mt-1">
    <VCol>
      <NjDisplayValue label="Nom" :value="documentType.name" />
    </VCol>

    <VCol>
      <NjDisplayValue label="Consignes" />
      <span style="font-size: 1rem; font-weight: 700">
        <RichTextDisplayValue :html="documentType.instructions" />
      </span>
    </VCol>

    <VCol>
      <VRow no-gutters class="flex-column">
        <VCol>
          <NjExpansionPanel title="Règles">
            <VRow class="flex-column" dense>
              <VCol>
                <NjDisplayValue label="Partageable">
                  <template #value>
                    <NjBooleanIcon :condition="documentType.shareable" />
                  </template>
                </NjDisplayValue>
              </VCol>
              <VCol>
                <NjDisplayValue
                  label="Le document doit être mis au niveau du regroupement pour les opérations dans un regroupement"
                >
                  <template #value>
                    <NjBooleanIcon :condition="documentType.requiredInOperationsGroupForOperationInOperationsGroup" />
                  </template>
                </NjDisplayValue>
              </VCol>
              <VCol>
                <NjDisplayValue label="Le document est ignoré si travaux en propre">
                  <template #value>
                    <NjBooleanIcon :condition="documentType.ignoreForSelfWorks" />
                  </template>
                </NjDisplayValue>
              </VCol>
              <VCol>
                <NjDisplayValue label="Unique">
                  <template #value>
                    <NjBooleanIcon :condition="documentType.uniqueDoc" />
                  </template>
                </NjDisplayValue>
              </VCol>
              <VCol>
                <NjDisplayValue label="Le document est obligatoire pour les filiales">
                  <template #value>
                    <NjBooleanIcon :condition="documentType.requiredForSubsidiary" />
                  </template>
                </NjDisplayValue>
              </VCol>
              <VCol>
                <NjDisplayValue label="Le document est obligatoire pour les opérations sous traitées">
                  <template #value>
                    <NjBooleanIcon :condition="documentType.requiredForSubcontracting" />
                  </template>
                </NjDisplayValue>
              </VCol>
            </VRow>
          </NjExpansionPanel>
        </VCol>
        <VCol>
          <NjExpansionPanel title="Fusion">
            <VRow class="flex-column" dense>
              <VCol>
                <NjDisplayValue type="number" label="Ordre pour fusion" :value="documentType.fusionOrder" />
              </VCol>
              <VCol>
                <NjDisplayValue label="Présent : Version Initiale">
                  <template #value>
                    <NjBooleanIcon :condition="documentType.inInitialVersion" />
                  </template>
                </NjDisplayValue>
              </VCol>
              <VCol>
                <NjDisplayValue label="Présent : Version Finale">
                  <template #value>
                    <NjBooleanIcon :condition="documentType.inFinalVersion" />
                  </template>
                </NjDisplayValue>
              </VCol>
              <VCol>
                <NjDisplayValue label="Présent : Autre Version">
                  <template #value>
                    <NjBooleanIcon :condition="documentType.inOtherVersion" />
                  </template>
                </NjDisplayValue>
              </VCol>
            </VRow>
          </NjExpansionPanel>
        </VCol>

        <VCol>
          <NjDisplayValue label="Formats Obligatoires">
            <template #value>
              <template v-if="!documentType.mustBeExcel && !documentType.mustBeWord && !documentType.mustBePdf">
                Aucun
              </template>
              <div class="d-flex" style="gap: 16px">
                <VChip v-if="documentType.mustBeExcel"> Excel </VChip>
                <VChip v-if="documentType.mustBeWord"> Word </VChip>
                <VChip v-if="documentType.mustBePdf"> PDF </VChip>
              </div>
            </template>
          </NjDisplayValue>
        </VCol>
        <VCol>
          <NjExpansionPanel title="Modèle">
            <VRow class="flex-column" dense>
              <VCol>
                <NjDisplayValue label="Modèle vierge">
                  <template #value>
                    <template v-if="documentType.template?.originalFilename">
                      <VLink @click="downloadTemplate">
                        {{ documentType.template?.originalFilename }}
                      </VLink>
                    </template>
                    <template v-else>
                      <i>Aucun</i>
                    </template>
                  </template>
                </NjDisplayValue>
              </VCol>
              <VCol>
                <NjDisplayValue label="Modèle complétable">
                  <template #value>
                    <template v-if="documentType.fillableTemplate?.originalFilename">
                      <VLink @click="downloadFillableTemplate">
                        {{ documentType.fillableTemplate?.originalFilename }}
                      </VLink>
                    </template>
                    <template v-else>
                      <i>Aucun</i>
                    </template>
                  </template>
                </NjDisplayValue>
              </VCol>
              <VCol>
                <NjBtn @click="openTokenAllViewDialog">Token</NjBtn>
                <AlertDialog title="Liste des tokens" v-bind="tokenAllViewDialog.props" no-actions>
                  <TemplateTokenAllView />
                </AlertDialog>
              </VCol>
            </VRow>
          </NjExpansionPanel>
        </VCol>
      </VRow>
    </VCol>
  </VRow>
</template>
<script lang="ts" setup>
import { useSnackbarStore } from '@/stores/snackbar'
import type { DocumentType } from '@/types/documentType'
import { VCol, VRow } from 'vuetify/components'
import TemplateTokenAllView from '../templateToken/TemplateTokenAllView.vue'
import AlertDialog from '@/components/AlertDialog.vue'

const props = defineProps({
  documentType: {
    type: Object as PropType<DocumentType>,
    default: () => makeEmptyDocumentType(),
  },
})

const snackbarStore = useSnackbarStore()

const downloadTemplate = () => {
  documentTypeApi
    .downloadTemplate(props.documentType.id)
    .then((response) => {
      downloadFile(props.documentType.template!.originalFilename, response.data)
      snackbarStore.setSuccess('Le téléchargement a réussi')
    })
    .catch(() =>
      snackbarStore.setError(
        'Le téléchargement du fichier a échoué. Aviez-vous envoyé ce fichier sur boCEE ? Si oui, veuillez nous contacter.'
      )
    )
}

const downloadFillableTemplate = () => {
  documentTypeApi
    .downloadFillableTemplate(props.documentType.id)
    .then((response) => {
      downloadFile(props.documentType.fillableTemplate!.originalFilename, response.data)
      snackbarStore.setSuccess('Le téléchargement a réussi')
    })
    .catch(() =>
      snackbarStore.setError(
        'Le téléchargement du fichier a échoué. Aviez-vous envoyé ce fichier sur boCEE ? Si oui, veuillez nous contacter.'
      )
    )
}

const tokenAllViewDialog = useConfirmAlertDialog()
const openTokenAllViewDialog = () => {
  tokenAllViewDialog.confirm()
}
</script>
