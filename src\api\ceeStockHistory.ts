import type { AxiosInstance, AxiosPromise } from 'axios'
import axiosInstance from '.'
import type { Page, Pageable } from '@/types/pagination'
import type { CeeStockEntry as CeeStockHistory } from '@/types/ceeStockEntry'

export type CeeStockHistoryFilter = Partial<{
  active: boolean
}>

export class CeeStockHistoryApi {
  public constructor(private axios: AxiosInstance) {}

  public findAll(
    operationId: number,
    pageable: Pageable,
    filter: CeeStockHistoryFilter
  ): AxiosPromise<Page<CeeStockHistory>> {
    return this.axios.get(`/operations/${operationId}/cee_stock_histories`, {
      params: { ...pageable, ...filter },
    })
  }
}

export const ceeStockEntryApi = new CeeStockHistoryApi(axiosInstance)
