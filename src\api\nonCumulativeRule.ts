import type { AxiosInstance, AxiosPromise } from 'axios'
import axiosInstance from '.'
import type { Page, Pageable } from '@/types/pagination'
import type { NonCumulativeRule, NonCumulativeRuleRequest } from '@/types/nonCumulativeRule'

export interface NonCumulativeRuleFilter {
  operationCodes?: string[]
  standardizedOperationSheetId?: number
  search?: string
}

const uri = '/non_cumulative_rules'
class NonCumulativeRuleApi {
  public constructor(private axios: AxiosInstance) {}

  public create(request: NonCumulativeRuleRequest): AxiosPromise<NonCumulativeRule> {
    return this.axios.post(uri, { ...request })
  }
  public save(id: number, request: NonCumulativeRuleRequest): AxiosPromise<NonCumulativeRule> {
    return this.axios.put(uri + '/' + id, { ...request })
  }

  public getAll(pageable: Pageable, filter: NonCumulativeRuleFilter): AxiosPromise<Page<NonCumulativeRule>> {
    return this.axios.get(uri, { params: { ...pageable, ...filter } })
  }
  public delete(id: number): AxiosPromise {
    return this.axios.delete(uri, {
      params: {
        id,
      },
    })
  }
}

export const nonCumulativeRuleApi = new NonCumulativeRuleApi(axiosInstance)
