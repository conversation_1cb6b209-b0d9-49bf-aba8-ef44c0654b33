import type { LocalDateTime } from './date'
import type { User } from './user'

export interface Beneficiary {
  id: number
  socialReason: string
  siren: string
  address: string
  postalCode: string
  city: string
  phoneNumber: string
  email: string
  lastName: string
  firstName: string
  capacity: string
  legalStatus: string
  certified: boolean
  creationUser: User
  creationDateTime: LocalDateTime
  updateUser: User
  updateDateTime: LocalDateTime
}

export interface BeneficiaryRequest
  extends Omit<Beneficiary, 'id' | 'creationUser' | 'creationDateTime' | 'updateUser'> {}

export const makeEmptyBeneficiaryRequest = (): BeneficiaryRequest => ({
  socialReason: '',
  siren: '',
  address: '',
  postalCode: '',
  city: '',
  phoneNumber: '',
  email: '',
  lastName: '',
  firstName: '',
  capacity: '',
  legalStatus: '',
  certified: false,
  updateDateTime: '',
})

export const makeEmptyBeneficiary = (): Beneficiary => ({
  id: 0,
  ...makeEmptyBeneficiaryRequest(),
  creationDateTime: '',
  creationUser: makeEmptyUser(),
  updateDateTime: '',
  updateUser: makeEmptyUser(),
})

export const mapToBeneficiaryRequest = (beneficiary: Beneficiary): BeneficiaryRequest => ({
  socialReason: beneficiary.socialReason,
  siren: beneficiary.siren,
  address: beneficiary.address,
  postalCode: beneficiary.postalCode,
  city: beneficiary.city,
  phoneNumber: beneficiary.phoneNumber,
  email: beneficiary.email,
  lastName: beneficiary.lastName,
  firstName: beneficiary.firstName,
  capacity: beneficiary.capacity,
  legalStatus: beneficiary.legalStatus,
  certified: beneficiary.certified,
  updateDateTime: beneficiary.updateDateTime,
})
export const emptyBeneficiary = makeEmptyBeneficiary()

const isPhoneNumber = (v: string) => !isNaN(parseInt(v?.split(' ').join(''))) && v.split(' ').join('').length == 10

const isAddressComplete = (b: Beneficiary | null | undefined) => {
  return b && !!b.address && !!b.city && !!b.postalCode
}

export const isBeneficiaryValid = (b: Beneficiary | null | undefined) => {
  return b && isPhoneNumber(b.phoneNumber) && isAddressComplete(b) && !!b.siren && !!b.email && b.certified
}

export const useValidateBeneficiary = (beneficiary: Ref<Beneficiary | null | undefined>) => {
  const isPhoneNumberValid = computed(
    () => beneficiary.value && beneficiary.value.phoneNumber && isPhoneNumber(beneficiary.value.phoneNumber)
  )

  const isAddressValid = computed(() => isAddressComplete(beneficiary.value))

  const isValidAtStep30 = computed(() => isBeneficiaryValid(beneficiary.value))

  return { isPhoneNumberValid, isAddressValid, isValidAtStep30 }
}
