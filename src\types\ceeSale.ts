import type { LocalDate, LocalDateTime } from './date'
import type { User } from './user'
import type { Operation } from './operation'

export interface OperationForCeeSaleDto {
  id: number
  chronoCode: string
  classicCumac: number
  precariousnessCumac: number
}

export interface CeeSale {
  id: number
  operation: OperationForCeeSaleDto
  soldClassicCumac: number
  classicCumacUnitSellingPrice: number
  classicCumacSoldAmount: number
  soldPrecariousnessCumac: number
  precariousnessCumacUnitSellingPrice: number
  precariousnessCumacSoldAmount: number
  buyer: string
  comment: string
  saleDate: LocalDate
  step100Date: LocalDate
  creationDateTime: LocalDateTime
  creationUser: User
}

export interface CeeSaleDateDto {
  saleDate: LocalDate
  salesNumber: number
  soldClassicCumac: number
  classicCumacSoldAmount: number
  soldPrecariousnessCumac: number
  precariousnessCumacSoldAmount: number
}

export interface OperationCeeSaleRequest {
  operationId: number
  soldClassicCumac: number
  soldPrecariousnessCumac: number
  buyer: string
  comment: string
  unitSellingPrice: number
  saleDate: LocalDate
}

export interface OperationCeeSaleRequestDto {
  operation: Operation
  soldClassicCumac: number
  soldPrecariousnessCumac: number
  buyer: string
  comment: string
  unitSellingPrice: number
  saleDate: LocalDate
}

export interface VolumeCeeSaleRequest {
  operationIds: number[]
  soldClassicCumac: number
  soldPrecariousnessCumac: number
  buyer: string
  comment: string
  unitSellingPrice: number
  saleDate: LocalDate
}

export function mapToOperationCeeSaleRequest(
  operationId: number,
  quantitiesSold: any,
  formSale: any
): OperationCeeSaleRequest {
  return {
    operationId: operationId,
    soldClassicCumac: quantitiesSold.soldClassicCumac,
    soldPrecariousnessCumac: quantitiesSold.soldPrecariousnessCumac,
    buyer: formSale.buyer,
    comment: formSale.comment,
    unitSellingPrice: formSale.unitSellingPrice,
    saleDate: formSale.saleDate,
  }
}

export function mapToOperationCeeSaleRequestFromDto(request: OperationCeeSaleRequestDto): OperationCeeSaleRequest {
  return {
    operationId: request.operation.id,
    soldClassicCumac: request.soldClassicCumac,
    soldPrecariousnessCumac: request.soldPrecariousnessCumac,
    buyer: request.buyer,
    comment: request.comment,
    unitSellingPrice: request.unitSellingPrice,
    saleDate: request.saleDate,
  }
}
