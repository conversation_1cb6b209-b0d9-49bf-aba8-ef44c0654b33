<template>
  <NjPage expend-body>
    <template #sub-header>
      <VRow class="flex-column" dense>
        <VCol>
          <VRow class="align-center">
            <VCol cols="3">
              <SearchInput v-model="filter.search" />
            </VCol>
            <VCol>
              <VRow>
                <span class="d-flex align-center value__label ml-2"> Engagée du </span>
                <VCol class="flex-grow-1">
                  <NjDatePicker v-model="filter.committedAfter" />
                </VCol>
                <span class="d-flex align-center value__label"> au </span>
                <VCol class="flex-grow-1">
                  <NjDatePicker v-model="filter.committedBefore" />
                </VCol>
              </VRow>
            </VCol>
            <VCol>
              <VRow>
                <span class="d-flex align-center value__label ml-2"> Terminé du </span>
                <VCol>
                  <NjDatePicker v-model="filter.endedAfter" />
                </VCol>
                <span class="d-flex align-center value__label"> au </span>
                <VCol>
                  <NjDatePicker v-model="filter.endedBefore" />
                </VCol>
              </VRow>
            </VCol>
            <VCol class="subheader-actions flex-wrap justify-end">
              <NjBtn :disabled="!selection.length" @click="addToControlOrderBatch">Ajouter à un lot de contrôle</NjBtn>
              <ControlOrderBatchDialog
                v-if="selection.length"
                v-model="controlOrderBatchDialog"
                :operation-ids="selectedOperationIds"
                :standardized-operation-sheet-id="selection[0].standardizedOperationSheet.id"
                :signed-date="selection[0].signedDate!"
                @add-in-control-order-batch="reload"
              />
              <NjBtn @click="createControlOrderBatch">Créer un lot de contrôle</NjBtn>
              <CreateControlOrderBatchDialog
                v-bind="createControlOrderBatchDialog.props"
                :filter="createControlOrderBatchOperationFilter"
              />
            </VCol>
          </VRow>
        </VCol>
        <VCol>
          <VRow>
            <VCol cols="3">
              <RemoteAutoComplete
                v-model="selectedFos"
                label="Fiche opération"
                :query-for-one="(v: number) => standardizedOperationSheetApi.findOne(v)"
                :query-for-all="(v: string) => standardizedOperationSheetApi.findAll({}, { search: v, visible: true })"
                item-title="operationCode"
                return-object
                chips
                multiple
                infinite-scroll
                @update:model-value="
                  (event) => (filter.standardizedOperationSheetIds = event.map((i: StandardizedOperationSheet) => i.id))
                "
              >
                <template #selection="{ item }">
                  <StandardizedOperationSheetChip
                    :standardized-operation-sheet="item.raw as StandardizedOperationSheet"
                  />
                </template>
                <template #item="{ item, props }">
                  <StandardizedOperationSheetItem
                    :standardized-operation-sheet="item.raw as StandardizedOperationSheet"
                    :hide-eye-off="true"
                    v-bind="props"
                  />
                </template>
              </RemoteAutoComplete>
            </VCol>

            <VCol cols="3">
              <VSelect
                v-model="filter.controlOrderTypes"
                label="Type de contrôle"
                :items="controlOrderTypeLabel"
                item-title="label"
                multiple
                class="bg-white"
              />
            </VCol>
            <VCol v-show="showResetFilter" class="subheader-actions align-center">
              <VLink
                icon="mdi-sync"
                @click="
                  () => {
                    filter = { ...defaultFilter }
                    selectedFos = []
                  }
                "
              >
                Réinitialiser les filtres
              </VLink>
            </VCol>
            <VSpacer />
            <VCol class="subheader-actions flex-wrap justify-end flex-grow-0">
              <NjBtn @click="handleColumnManager"> Personnaliser </NjBtn>
            </VCol>
          </VRow>
        </VCol>
      </VRow>
    </template>
    <template #body>
      <VRow class="w-100">
        <VCol>
          <OperationTable
            v-model:selection="selection"
            v-model:drawer="drawer"
            :pageable="pageable"
            :update-pageable="updatePageable"
            :data="data.value!"
            :hide-checkboxes="hideCheckboxes"
            @update:drawer="(event, id) => (id ? getOperationDetail(id) : undefined)"
          />
        </VCol>
      </VRow>
    </template>
    <template #drawer>
      <OperationDrawer
        :model-value="drawer === 'detail'"
        :operation="selectedOperation.value"
        :loading="selectedOperation.loading"
        @update:model-value="drawer = $event ? 'detail' : undefined"
        @update:operation="reload"
      />
    </template>
  </NjPage>
</template>
<script lang="ts" setup>
import type { Operation } from '@/types/operation'
import OperationTable from '../operation/OperationTable.vue'
import type { OperationFilter } from '@/api/operation'
import { useSnackbarStore } from '@/stores/snackbar'
import OperationDrawer from '../operation/OperationDrawer.vue'
import { debounce, isEqual } from 'lodash'
import CreateControlOrderBatchDialog from './CreateControlOrderBatchDialog.vue'
import { type ControlOrderBatch } from '@/types/controlOrder'
import { controlOrderTypeLabel, type StandardizedOperationSheet } from '@/types/calcul/standardizedOperationSheet'
import { VCol, VRow, VSelect, VSpacer } from 'vuetify/components'
import { standardizedOperationSheetApi } from '@/api/standardizedOperationSheet'
import ControlOrderBatchDialog from '../operation/dialog/ControlOrderBatchDialog.vue'

const snackbarStore = useSnackbarStore()

const makeEmptyFilter = (): OperationFilter => ({
  search: '',
  stepIds: [70],
  standardizedOperationSheetIds: [],
  committedAfter: undefined,
  committedBefore: undefined,
  controlOrderTypes: [],
  availableForControlOrder: true,
  controlOrderExportTemplateIds: [],
  endedAfter: undefined,
  endedBefore: undefined,
})

const defaultFilter = {
  ...makeEmptyFilter(),
  operationStatuses: ['DOING'],
} as OperationFilter

const filter = ref<OperationFilter>({ ...defaultFilter })

const showResetFilter = computed(() => {
  return !isEqual(filter.value, defaultFilter)
})

const { data, pageFilter, pageable, updatePageable, updateFilter, reload } = usePaginationInQuery<
  Operation,
  OperationFilter
>((filter, pageable) => operationApi.findAll(filter, pageable), {
  defaultPageFilter: { ...filter.value },
  defaultPageablePartial: {
    page: 0,
    size: 50,
  },
})

const selection = ref<Operation[]>([])
const drawer = ref<'filter' | 'detail' | 'column' | undefined>()
const selectedOperation = ref(emptyValue<Operation>())
const getOperationDetail = async (id: number) => {
  await handleAxiosPromise(selectedOperation, simulationApi.findById(id), {
    afterError: () =>
      snackbarStore.setError(
        selectedOperation.value.error ?? "Une erreur est survenue lors de la récpération de l'opération"
      ),
  })
}

const handleColumnManager = () => {
  drawer.value = drawer.value === 'column' ? undefined : 'column'
}

const debounceFilter = debounce((v: any) => updateFilter(v), 300)

watch(
  filter,
  (v) => {
    if (!isEqual(v, pageFilter.value)) {
      debounceFilter(v)
    }
  },
  {
    deep: true,
  }
)

const selectedFos = ref<StandardizedOperationSheet[]>([])

const createControlOrderBatchDialog = useConfirmAlertDialog()
const createControlOrderBatchOperationFilter = ref<OperationFilter>({})
const controlOrderBatch = ref(emptyValue<ControlOrderBatch>())
const createControlOrderBatch = async () => {
  let filter = {} as OperationFilter
  if (selection.value.length) {
    filter.operationIds = selection.value.map((simu) => simu.id)
  } else {
    filter = pageFilter.value
  }

  createControlOrderBatchOperationFilter.value = filter
  if (!(await createControlOrderBatchDialog.confirm())) {
    return
  }

  handleAxiosPromise(controlOrderBatch, controlOrderBatchApi.create({ filter: filter }), {
    afterSuccess: () => {
      snackbarStore.setSuccess(`Le lot de contrôle ${controlOrderBatch.value.value?.batchCode} à été créé`)
      reload()
      controlOrderBatchApi
        .exportExcelFile(controlOrderBatch.value.value!.id)
        .then((response) => downloadFile(`Export ${controlOrderBatch.value.value?.batchCode}.xlsx`, response.data))
        .catch(async (err) => {
          snackbarStore.setError(await handleAxiosException(err, undefined, { defaultMessage: "L'export a échoué" }))
        })
    },
    afterError: () =>
      snackbarStore.setError(controlOrderBatch.value.error ?? "Le lot de contrôle n'a pas pu être créé"),
  })
}

const controlOrderBatchDialog = ref(false)
const selectedOperationIds = computed(() => selection.value.map((i) => i.id))

const hideCheckboxes = computed(() => (item: Operation) => {
  if (!selection.value.length) {
    return false
  }
  return !(item.standardizedOperationSheet.id == selection.value[0].standardizedOperationSheet.id)
})
const addToControlOrderBatch = () => {
  controlOrderBatchDialog.value = true
}
</script>
