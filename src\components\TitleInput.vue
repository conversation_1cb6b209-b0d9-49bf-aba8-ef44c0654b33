<template>
  <VInput :rules="rules" :model-value="modelValue" hide-details class="title-input">
    <template #default="{ isValid }">
      <div :class="{ 'text-error': isValid.value === false }">
        <slot />
      </div>
    </template>
  </VInput>
</template>

<script setup lang="ts">
/* __vue_virtual_code_placeholder__ */
import type { ValidationRule } from '@/types/rule'
import type { PropType } from 'vue'

defineProps({
  modelValue: {},
  rules: {
    type: Array as PropType<ValidationRule[]>,
    default: () => [],
  },
})
</script>

<style scoped>
.title-input {
  font-size: inherit;
  font-weight: inherit;
  line-height: inherit;
}
</style>
