<template>
  <div class="h-100 content-layout">
    <VSwitch
      v-if="userStore.isAdmin"
      v-model="showDisabledDocuments"
      class="content-layout__header px-2"
      label="Voir les documents inactifs"
    />

    <template v-if="props.operation.operationsGroup">
      <div class="content-layout__main">
        <NjExpansionPanel>
          <template #title>
            Documents propre à l'opération (<template v-if="operationDocuments.loading"
              ><VProgressCircular indeterminate /></template
            ><template v-else>{{ operationDocuments.value?.totalElements }}</template
            >)
          </template>
          <OperationDocumentDataTable
            :items="allAttachedDocument.operationDocumentDataTableItems"
            :operation="operation"
            :reload-data="reload"
            @send="(id: number) => emits('send', id)"
          />
        </NjExpansionPanel>
        <NjExpansionPanel>
          <template #title>
            Documents issus du regroupement (<template v-if="operationsGroupDocuments.loading"
              ><VProgressCircular indeterminate /></template
            ><template v-else>{{ operationsGroupDocuments.value?.totalElements }}</template
            >)
          </template>
          <OperationsGroupDocumentDataTable
            :items="allAttachedDocument.operationsGroupDocumentDataTableItems"
            :operation="operation"
            hide-actions
            @send="(id: number) => emits('send', id)"
          />
        </NjExpansionPanel>
      </div>
    </template>
    <template v-else>
      <OperationDocumentDataTable
        class="content-layout__main"
        :items="allAttachedDocument.operationDocumentDataTableItems"
        :operation="operation"
        :reload-data="reload"
        @send="(id: number) => emits('send', id)"
      />
    </template>
    <OperationDocumentAllViewFooter
      :operation="operation"
      :total-elements="
        (operationDocuments.value?.totalElements ?? 0) + (operationsGroupDocuments.value?.totalElements ?? 0)
      "
      :missing-document-types="allAttachedDocument.missingDocumentTypes"
      @save-document="
        (event) => {
          emits('saveDocument', event)
          if (event.some((it) => it.type === 'success')) {
            reload()
          }
        }
      "
    />
  </div>
</template>
<script setup lang="ts">
import type { StepDocumentLinkFilter } from '@/api/documentType'
import { operationDocumentApi, type OperationDocumentFilter } from '@/api/operationDocument'
import type { OperationsGroupDocumentFilter } from '@/api/operationsGroupDocument'
import { useUserStore } from '@/stores/user'
import { useAdminConfigurationStore } from '@/stores/adminConfiguration'
import { type EnhancedDocument } from '@/types/document'
import type { DocumentType } from '@/types/documentType'
import type { Operation } from '@/types/operation'
import type { Page } from '@/types/pagination'
import type { StepDocumentLink } from '@/types/stepDocumentLink'
import type { PropType } from 'vue'
import type SendDocumentResult from '../document/sendDocumentResult'
import OperationsGroupDocumentDataTable from '../operationsGroupDocument/OperationsGroupDocumentDataTable.vue'
import OperationDocumentAllViewFooter from './OperationDocumentAllViewFooter.vue'
import OperationDocumentDataTable from './OperationDocumentDataTable.vue'
import type { DocumentDataTableItem } from './types'

const props = defineProps({
  operation: {
    type: Object as PropType<Operation>,
    required: true,
  },
})

const emits = defineEmits<{
  send: [number]
  saveDocument: [SendDocumentResult[]]
}>()

const {
  data: operationDocuments,
  reload: reloadOperationDocuments,
  pageFilter: operationDocumentPageFilter,
  updateFilter: operationDocumentUpdateFilter,
} = usePagination<EnhancedDocument, OperationDocumentFilter>(
  (filter, pageable) => operationDocumentApi.findAll(filter, pageable),
  {
    active: true as boolean | undefined,
    operationId: props.operation.id,
  },
  {
    sort: ['document.creationDateTime,DESC'],
    size: 1000,
  },
  {
    onErrorLoading: "Erreur lors du chargement des documents de l'opération",
  }
)

const {
  data: operationsGroupDocuments,
  reload: reloadOperationsGroupDocument,
  pageFilter: operationsGroupDocumentPageFilter,
  updateFilter: operationsGroupDocumentUpdateFilter,
} = usePagination<EnhancedDocument, OperationsGroupDocumentFilter>(
  (filter, pageable) => operationsGroupDocumentApi.findAll(filter, pageable),
  {
    active: true as boolean | undefined,
    operationsGroupId: props.operation.operationsGroup?.id,
    notInOperation: props.operation.id,
  },
  {
    sort: ['document.creationDateTime,DESC'],
    size: 1000,
  },
  {
    onErrorLoading: 'Erreur lors du chargement des documents du regroupement',
  }
)

const {
  data: assignedDocuments,
  reload: reloadAssignedDocuments,
  pageFilter: assignedDocumentsPageFilter,
} = usePagination<StepDocumentLink, StepDocumentLinkFilter>(
  (filter, pageable) => documentTypeApi.getAssignedDocuments(pageable, filter),
  {
    operationId: props.operation.id,
  },
  {
    size: 1000,
  }
)

const showDisabledDocuments = ref(false)
const userStore = useUserStore()
const adminConfigurationStore = useAdminConfigurationStore()

watch(showDisabledDocuments, (v) => {
  if (v) {
    operationDocumentUpdateFilter({ ...operationDocumentPageFilter.value, active: undefined })
    operationsGroupDocumentUpdateFilter({ ...operationsGroupDocumentPageFilter.value, active: undefined })
  } else {
    operationDocumentUpdateFilter({ ...operationDocumentPageFilter.value, active: true })
    operationsGroupDocumentUpdateFilter({ ...operationsGroupDocumentPageFilter.value, active: true })
  }
})

const reload = async () => {
  await reloadOperationDocuments()
  if (props.operation.operationsGroup) {
    await reloadOperationsGroupDocument()
  } else {
    operationsGroupDocuments.value = emptyValue<Page<EnhancedDocument>>()
  }
  await reloadAssignedDocuments()
}

watch(
  () => props.operation,
  () => {
    operationDocumentPageFilter.value.operationId = props.operation.id
    operationsGroupDocumentPageFilter.value.operationId = props.operation.id
    assignedDocumentsPageFilter.value.operationId = props.operation.id
  },
  {
    deep: true,
  }
)

interface DocumentFromOperation {
  operationDocumentDataTableItems: DocumentDataTableItem[]
  operationsGroupDocumentDataTableItems: DocumentDataTableItem[]
  missingDocumentTypes: DocumentType[]
}

const allAttachedDocument = computed((): DocumentFromOperation => {
  const assignedDocumentRows: StepDocumentLink[] = JSON.parse(
    JSON.stringify(assignedDocuments.value.value?.content ?? [])
  )

  const mappedRowOperationDocument: DocumentDataTableItem[] = (operationDocuments.value.value?.content ?? [])
    .filter((i) => {
      // Filtrer les documents VF pour les utilisateurs AGENCE_PLUS
      // Ne pas afficher le document VF si l'utilisateur est AGENCE_PLUS et l'opération n'est pas validée
      if (
        userStore.hasRole('AGENCE_PLUS') &&
        i.documentType.id === adminConfigurationStore.vfDocumentTypeId?.valueAsInt &&
        props.operation.status !== 'DONE'
      ) {
        return false
      }
      return true
    })
    .map((i) => {
      const assignedDocumentRowIndex = assignedDocuments.value.value?.content.findIndex(
        (r) => i.documentType.id == r.documentType.id
      )

      const assignedDocumentRow =
        assignedDocumentRowIndex != undefined && assignedDocumentRowIndex != -1
          ? assignedDocuments.value.value?.content[assignedDocumentRowIndex]
          : undefined

      if (i.active && assignedDocumentRow) {
        const removedIndex = assignedDocumentRows.findIndex((r) => i.documentType.id == r.documentType.id)

        if (removedIndex != undefined && removedIndex != -1) {
          assignedDocumentRows.splice(removedIndex, 1)
        }
      }

      return {
        id: i.id,
        emediaId: i.document.emediaId,
        emediaErrors: i.document.emediaErrors,
        documentType: i.documentType,
        requiredStepId: assignedDocumentRow?.stepId,
        creationDateTime: i.document.creationDateTime,
        format: getFileExtension(i.document.originalFilename),
        description: i.description,
        fillableTemplateId: i.documentType.fillableTemplate?.id,
        templateId: i.documentType.template?.id,
        active: i.active,
      }
    })

  const mappedRowOperationsGroupDocument: DocumentDataTableItem[] = (
    operationsGroupDocuments.value.value?.content ?? []
  )
    .filter((i) => {
      // Filtrer les documents VF pour les utilisateurs AGENCE_PLUS
      // Ne pas afficher le document VF si l'utilisateur est AGENCE_PLUS et l'opération n'est pas validée
      if (
        userStore.hasRole('AGENCE_PLUS') &&
        i.documentType.id === adminConfigurationStore.vfDocumentTypeId?.valueAsInt &&
        props.operation.status !== 'DONE'
      ) {
        return false
      }
      return true
    })
    .map((i) => {
      const assignedDocumentRowIndex = assignedDocuments.value.value?.content.findIndex(
        (r) => i.documentType.id == r.documentType.id
      )

      const assignedDocumentRow =
        assignedDocumentRowIndex != undefined && assignedDocumentRowIndex != -1
          ? assignedDocuments.value.value?.content[assignedDocumentRowIndex]
          : undefined

      if (i.active && assignedDocumentRow) {
        const removedIndex = assignedDocumentRows.findIndex((r) => i.documentType.id == r.documentType.id)

        if (removedIndex != undefined && removedIndex != -1) {
          assignedDocumentRows.splice(removedIndex, 1)
        }
      }

      return {
        id: i.id,
        emediaId: i.document.emediaId,
        emediaErrors: i.document.emediaErrors,
        documentType: i.documentType,
        requiredStepId: assignedDocumentRow?.stepId,
        creationDateTime: i.document.creationDateTime,
        format: getFileExtension(i.document.originalFilename),
        description: i.description,
        fillableTemplateId: i.documentType.fillableTemplate?.id,
        templateId: i.documentType.template?.id,
        active: i.active,
      }
    })

  assignedDocumentRows.forEach((i) => {
    const item = {
      documentType: i.documentType,
      requiredStepId: i?.stepId,
      fillableTemplateId: i.documentType.fillableTemplate?.id,
      templateId: i.documentType.template?.id,
      creationDateTime: undefined,
      format: undefined,
      description: undefined,
    }

    if (
      props.operation.operationsGroup != null &&
      i.documentType.requiredInOperationsGroupForOperationInOperationsGroup
    ) {
      mappedRowOperationsGroupDocument.push(item)
    } else {
      mappedRowOperationDocument.push(item)
    }
  })

  const missingDocumentTypes = assignedDocumentRows
    .filter((i) => i.stepId <= props.operation.stepId)
    .map((i) => i.documentType)

  const compareDocumentDataTableItem = (a: DocumentDataTableItem, b: DocumentDataTableItem) => {
    const aStepId = a?.requiredStepId ?? Number.MAX_VALUE
    const bStepId = b?.requiredStepId ?? Number.MAX_VALUE
    if (a.creationDateTime && !b.creationDateTime) {
      return bStepId <= props.operation.stepId ? 1 : -1
    } else if (!a.creationDateTime && b.creationDateTime) {
      return aStepId <= props.operation.stepId ? -1 : 1
    }
    return (a?.requiredStepId ?? Number.MAX_VALUE) - (b?.requiredStepId ?? Number.MAX_VALUE)
  }

  const operationDocumentDataTableItems = mappedRowOperationDocument.sort(compareDocumentDataTableItem)

  const operationsGroupDocumentDataTableItems = mappedRowOperationsGroupDocument.sort(compareDocumentDataTableItem)

  return {
    operationDocumentDataTableItems,
    operationsGroupDocumentDataTableItems,
    missingDocumentTypes,
  }
})

defineExpose({ reload })
</script>
