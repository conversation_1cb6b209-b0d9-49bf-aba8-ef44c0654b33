/* eslint-disable vue/one-component-per-file */
import App from '@/App.vue'
import ImplicitCallback from '@/components/ImplicitCallback.vue'
import okta from '@/plugins/okta'
import { trace } from '@/stores/analytics'
import useAdminSidebar from '@/views/adminSidebar'
import Infoview from '@/views/Infoview.vue'
import NotFoundView from '@/views/NotFoundView.vue'
import useOperationSideBar from '@/views/operationSidebar'
import useSimulationSideBar from '@/views/simulationSidebar'
import { isEqual } from 'lodash'
import { createRouter, createWebHistory, RouterView, type NavigationGuardWithThis } from 'vue-router'
import HomeView from '../views/HomeView.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/implicit/callback',
      name: 'ImplicitCallback',
      component: ImplicitCallback,
      // component: LoginCallback,
    },
    {
      path: '/',
      // name: 'home',
      component: App,
      meta: {
        requiresAuth: true,
      },
      children: [
        {
          path: '/',
          name: 'Home',
          component: HomeView,
        },
        {
          path: 'info',
          name: 'InfoView',
          component: Infoview,
        },
        {
          path: '/demande-droit',
          name: 'UserProfileRequestCreate',
          component: () => import('../views/UserProfileRequestCreate.vue'),
        },
        {
          path: 'cee-sales',
          name: 'CEESalesView',
          component: () => import('../views/ceeSales/CeeSalesView.vue'),
        },
        {
          path: 'stock-cee',
          name: 'StockCEEAllView',
          component: () => import('../views/ceeStock/CeeStockAllView.vue'),
        },
        {
          path: '/simulation',
          component: defineComponent({
            setup() {
              useSimulationSideBar()
            },
            render() {
              return h(RouterView)
            },
          }),
          children: [
            {
              path: '',
              name: 'SimulationAllView',
              component: () => import('../views/SimulationAllView.vue'),
            },
            {
              path: 'ajouter',
              name: 'CreateSimulationView',
              component: () => import('../views/CreateSimulationView.vue'),
              props: (route) => {
                return { standardizedOperationSheetId: parseInt(String(route.query.standardizedOperationSheetId)) }
              },
            },
            {
              path: ':id',
              name: 'SimulationOneView',
              component: () => import('../views/OperationOneView.vue'),
              props: (route) => {
                return { id: parseInt(firstElement(route.params.id)) }
              },
            },

            {
              path: 'businessplan',
              component: RouterView,
              children: [
                {
                  path: '',
                  name: 'BusinessPlanAllView',
                  component: () => import('../views/businessplan/BusinessPlanAllView.vue'),
                },
                {
                  path: ':id',
                  name: 'BusinessPlanOneView',
                  component: () => import('../views/businessplan/BusinessPlanOneView.vue'),
                  props: (route) => {
                    return { id: parseInt(firstElement(route.params.id)) }
                  },
                },
              ],
            },
          ],
        },
        {
          path: '/operation',
          component: defineComponent({
            setup() {
              useOperationSideBar()
            },
            render() {
              return h(RouterView)
            },
          }),
          children: [
            {
              path: '',
              name: 'OperationAllView',
              component: () => import('../views/operation/OperationAllView.vue'),
            },
            {
              path: 'regroupement',
              component: RouterView,
              children: [
                {
                  path: '',
                  name: 'OperationsGroupAllView',
                  component: () => import('../views/regroupement/OperationsGroupAllView.vue'),
                },
                {
                  path: ':id',
                  name: 'OperationsGroupOneView',
                  component: () => import('../views/regroupement/OperationsGroupOneView.vue'),
                  props: (route) => {
                    return { id: parseInt(firstElement(route.params.id)) }
                  },
                },
              ],
            },
            {
              path: 'dossier-emmy',
              component: RouterView,
              children: [
                {
                  path: '',
                  name: 'EmmyFolderAllView',
                  component: () => import('../views/emmyfolder/EmmyFolderAllView.vue'),
                },
                {
                  path: ':id',
                  name: 'EmmyFolderOneView',
                  component: () => import('../views/emmyfolder/EmmyFolderOneView.vue'),
                  props: (route) => ({ id: parseInt(firstElement(route.params.id)) }),
                },
              ],
            },
            {
              path: 'ajouter/simulation/:simulationId',
              name: 'CreateOperationView',
              component: () => import('../views/CreateOperationView.vue'),
              props: (route) => {
                return { simulationId: parseInt(firstElement(route.params.simulationId)) }
              },
            },
            {
              path: 'ajouter',
              name: 'OperationOneNewView',
              component: () => import('../views/CreateOperationView.vue'),
              props: (route) => {
                return { standardizedOperationSheetId: parseInt(String(route.query.standardizedOperationSheetId)) }
              },
            },
            {
              path: ':id',
              name: 'OperationOneView',
              component: () => import('../views/OperationOneView.vue'),
              props: (route) => {
                return { id: parseInt(firstElement(route.params.id)) }
              },
            },
            {
              path: 'arrete-controle',
              component: () => import('../views/controlorder/ControlOrderView.vue'),
              children: [
                {
                  path: '',
                  name: 'ControlOrderView',
                  component: () => import('../views/controlorder/ControlOrderOperationAllView.vue'),
                },
                {
                  path: 'lot',
                  children: [
                    {
                      path: '',
                      name: 'ControlOrderBatchAllView',
                      component: () => import('../views/controlorder/ControlOrderBatchAllView.vue'),
                    },
                    {
                      path: ':id',
                      name: 'ControlOrderBatchOneView',
                      component: () => import('../views/controlorder/ControlOrderBatchOneView.vue'),
                      props: (route) => {
                        return { id: parseInt(firstElement(route.params.id)) }
                      },
                    },
                  ],
                },
              ],
            },
            {
              path: 'osh/:tab',
              name: 'OshViewWithPart',
              component: () => import('../views/osh/OshView.vue'),
              props: (route) => {
                return { tab: firstElement(route.params.tab) }
              },
            },
            {
              path: 'osh',
              name: 'OshView',
              component: () => import('../views/osh/OshView.vue'),
            },
          ],
        },
        {
          path: 'administration',
          component: defineComponent({
            setup() {
              useAdminSidebar()
            },
            render() {
              return h(RouterView)
            },
          }),
          children: [
            {
              path: '',
              name: 'AdminView',
              redirect: { name: 'ValueManagerView' },
            },
            {
              path: 'valueList',
              component: () => import('@/views/admin/AdminValueListView.vue'),
              children: [
                {
                  path: '',
                  name: 'ValueManagerView',
                  redirect: { name: 'DocumentTypeAllView' },
                },
                {
                  path: 'documents',
                  name: 'DocumentTypeAllView',
                  component: () => import('../views/admin/documentTypes/DocumentTypeAllView.vue'),
                },
                {
                  path: 'valorisation',
                  name: 'ValuationAllView',
                  component: () => import('@/views/admin/valuation/ValuationAllView.vue'),
                },
                {
                  path: 'etape',
                  component: RouterView,
                  children: [
                    {
                      path: '',
                      name: 'StepAllView',
                      component: () => import('@/views/admin/StepAllView.vue'),
                    },
                    {
                      path: ':id',
                      name: 'StepOneView',
                      component: () => import('@/views/admin/StepOneView.vue'),
                      props: (route) => ({ id: parseInt(firstElement(route.params.id)) }),
                    },
                  ],
                },
                {
                  path: 'periode',
                  name: 'PeriodAllView',
                  component: () => import('../views/admin/PeriodAllView.vue'),
                },
                {
                  path: 'organisation',
                  name: 'EntityAllView',
                  component: () => import('../views/admin/entity/EntityAllView.vue'),
                },
                {
                  path: 'territoire',
                  name: 'TerritoryAllView',
                  component: () => import('../views/admin/TerritoryAllView.vue'),
                },
                {
                  path: 'message',
                  name: 'MessageTemplateAllView',
                  component: () => import('../views/admin/message/MessageTemplateAllView.vue'),
                },
                {
                  path: 'beneficiaires',
                  name: 'BeneficiaryAdminView',
                  component: () => import('@/views/admin/beneficiary/BeneficiaryAdminView.vue'),
                },
                {
                  path: 'sous-traitants',
                  name: 'SubcontractorAdminView',
                  component: () => import('@/views/admin/subcontractor/SubcontractorAdminView.vue'),
                },
                {
                  path: 'organismes-de-controle',
                  name: 'ControlOrganismAdminView',
                  component: () => import('@/views/admin/controlOrganism/ControlOrganismAdminView.vue'),
                },
                {
                  path: 'values',
                  name: 'AdminValueView',
                  component: () => import('@/views/AdminValueView.vue'),
                },
              ],
            },
            {
              path: 'outilCalcul',
              component: () => import('@/views/admin/outilCalcul/OutilCalculBase.vue'),
              children: [
                {
                  path: '',
                  name: 'OutilCalculView',
                  redirect: { name: 'StandardizedOperationSheetAllView' },
                },
                {
                  path: 'ficheOperation',
                  component: RouterView,
                  children: [
                    {
                      path: '',
                      name: 'StandardizedOperationSheetAllView',
                      component: () =>
                        import('../views/admin/standardizedOperationSheet/StandardizedOperationSheetAllView.vue'),
                    },
                    {
                      path: 'ajouter',
                      name: 'StandardizedOperationSheetOneNewView',
                      component: () =>
                        import('../views/admin/standardizedOperationSheet/StandardizedOperationSheetOneView.vue'),
                    },
                    {
                      path: ':id',
                      name: 'StandardizedOperationSheetOneView',
                      component: () =>
                        import('../views/admin/standardizedOperationSheet/StandardizedOperationSheetOneView.vue'),
                      props: (route) => ({ id: parseInt(firstElement(route.params.id)) }),
                    },
                  ],
                },
                {
                  path: 'precarite',
                  component: RouterView,
                  children: [
                    {
                      path: '',
                      name: 'PrecariousnessBonusSheetAllView',
                      component: () => import('@/views/admin/precariousnessBonus/PrecariousnessBonusSheetAllView.vue'),
                    },
                    {
                      path: ':id',
                      name: 'PrecariousnessBonusSheetOneView',
                      component: () => import('@/views/admin/precariousnessBonus/PrecariousnessBonusSheetOneView.vue'),
                      props: (route) => {
                        return { id: parseInt(firstElement(route.params.id)) }
                      },
                    },
                    {
                      path: 'ajouter',
                      name: 'PrecariousnessBonusSheetOneNewView',
                      component: () => import('@/views/admin/precariousnessBonus/PrecariousnessBonusSheetOneView.vue'),
                    },
                  ],
                },
                {
                  path: 'cpe',
                  name: 'EpcBonusSheet',
                  component: RouterView,
                  children: [
                    {
                      path: '',
                      name: 'EpcBonusSheetAllView',
                      component: () => import('@/views/admin/epcBonus/EpcBonusAllView.vue'),
                    },
                    {
                      path: ':id',
                      name: 'EpcBonusSheetOneView',
                      component: () => import('@/views/admin/epcBonus/EpcBonusOneView.vue'),
                      props: (route) => {
                        return { id: parseInt(firstElement(route.params.id)) }
                      },
                    },
                    {
                      path: 'ajouter',
                      name: 'EpcBonusSheetOneNewView',
                      component: () => import('@/views/admin/epcBonus/EpcBonusOneView.vue'),
                    },
                  ],
                },
                {
                  path: 'coupdepouce',
                  component: RouterView,
                  children: [
                    {
                      path: '',
                      name: 'BoostBonusSheetAllView',
                      component: () => import('../views/BoostBonusSheetAllView.vue'),
                    },
                    {
                      path: 'create',
                      name: 'BoostBonusSheetOneNewView',
                      component: () => import('../views/BoostBonusSheetOneView.vue'),
                    },
                    {
                      path: ':id',
                      name: 'BoostBonusSheetOneView',
                      component: () => import('../views/BoostBonusSheetOneView.vue'),
                      props: (route) => ({ id: parseInt(firstElement(route.params.id)) }),
                    },
                  ],
                },
                {
                  path: 'regle-non-cumul',
                  component: RouterView,
                  children: [
                    {
                      path: '',
                      name: 'NonCumulativeRuleAllView',
                      component: () => import('../views/admin/outilCalcul/NonCumulativeRuleAllView.vue'),
                    },
                  ],
                },
              ],
            },

            {
              path: 'user',
              name: 'UserAllView',
              component: () => import('@/views/admin/users/UserAllView.vue'),
            },

            {
              path: 'emmyparameter',
              name: 'EmmyParameterAllView',
              component: () => import('@/views/admin/EmmyParameterAllView.vue'),
            },
            {
              path: 'modele-document',
              component: () => import('../views/admin/convention/ConventionBase.vue'),
              children: [
                {
                  path: '',
                  name: 'ConventionManagerView',
                  redirect: { name: 'ConventionAllView' },
                },
                {
                  path: 'attestation-sur-l-honneur',
                  name: 'SwornStatementAllView',
                  component: () => import('../views/admin/swornStatement/SwornStatementPage.vue'),
                },
                {
                  path: 'conventions',
                  name: 'ConventionAllView',
                  component: () => import('../views/admin/convention/ConventionAllView.vue'),
                },
                {
                  path: 'token',
                  name: 'ConventionTokenAllView',
                  component: () => import('../views/admin/convention/ConventionTokenAllView.vue'),
                },
              ],
            },
            {
              path: 'arrete-controle',
              component: RouterView,
              children: [
                {
                  path: '',
                  name: 'ControlOrderAdministrationView',
                  component: () => import('@/views/admin/controlOrder/ControlOrderAdministrationView.vue'),
                },
                {
                  path: 'ajouter',
                  name: 'ControlOrderExportTemplateNewView',
                  component: () => import('@/views/admin/controlOrder/ControlOrderExportTemplateOneView.vue'),
                },
                {
                  path: ':id',
                  name: 'ControlOrderExportTemplatOneView',
                  component: () => import('@/views/admin/controlOrder/ControlOrderExportTemplateOneView.vue'),
                  props: (route) => ({ id: parseInt(firstElement(route.params.id)) }),
                },
              ],
            },
            {
              path: 'downloadHistory',
              name: 'DownloadHistoryAllView',
              component: () => import('@/views/admin/DownloadHistoryAllView.vue'),
            },
            {
              path: 'explorer/check',
              name: 'AppExplorerCheck',
              component: () => import('@/views/admin/explorer/AppExplorerCheck.vue'),
              props: true,
            },
            {
              path: 'explorer/:id',
              name: 'AppExplorer',
              component: () => import('@/views/admin/explorer/AppExplorer.vue'),
              props: true,
            },
            {
              path: 'mails-a-envoyer',
              name: 'RemainingMailAllView',
              component: () => import('@/views/admin/remainingMail/RemainingMailAllView.vue'),
            },
            {
              path: 'requete-profil-utilisateurs',
              name: 'UserProfileRequestAllView',
              component: () => import('@/views/admin/userProfileRequest/UserProfileRequestAllView.vue'),
            },
            {
              path: 'app-logs',
              name: 'LoggingEventsAllView',
              component: () => import('@/views/admin/loggingEvents/LoggingEventsAllView.vue'),
            },
          ],
        },

        {
          path: '/components',
          component: () => import('@/views/components/index.vue'),
          name: 'DebugComponents',
          children: [
            {
              path: 'remoteautocomplete',
              name: 'DebugComponentRemoteAutoComplete',
              component: () => import('@/views/components/ComponentRemoteAutoComplete.vue'),
            },
            {
              path: 'vdatatable',
              name: 'DebugComponentNjDataTable',
              component: () => import('@/views/components/ComponentNjDataTable.vue'),
            },
            {
              path: 'richtexteditor',
              name: 'DebugComponentRichTextEditor',
              component: () => import('@/views/components/ComponentRichTextEditor.vue'),
            },
          ],
        },
        {
          path: '/:pathMatch(.*)*',
          name: 'NotFoundView',
          component: NotFoundView,
        },
      ],
    },
  ],
})

const customAuthRedirectGuard: NavigationGuardWithThis<unknown> = async (to) => {
  if (await okta.isAuthenticated()) {
    return true
  } else if (to.name === 'ImplicitCallback') {
    return true
  } else {
    sessionStorage.setItem('okta-current-route', JSON.stringify({ name: to.name, query: to.query, params: to.params }))
    okta.signInWithRedirect({
      originalUri: to.fullPath,
    })
    return false
  }
}

router.beforeEach(customAuthRedirectGuard)

router.afterEach((to, from) => {
  if (
    (to.name !== from.name || !isEqual(to.params, from.params)) &&
    to.name !== 'ImplicitCallback' &&
    from.name !== 'ImplicitCallback'
  ) {
    trace('goPage', { name: to.name, params: to.params })
  }
})

// Workaround for https://github.com/vitejs/vite/issues/11804
// A voir si c'est intéressant ou non (pris depuis le template de base de vuetify)
router.onError((err, to) => {
  if (err?.message?.includes?.('Failed to fetch dynamically imported module')) {
    if (!localStorage.getItem('vuetify:dynamic-reload')) {
      console.log('Reloading page to fix dynamic import error')
      localStorage.setItem('vuetify:dynamic-reload', 'true')
      location.assign(to.fullPath)
    } else {
      console.error('Dynamic import error, reloading page did not fix it', err)
    }
  } else {
    console.error(err)
  }
})
router.isReady().then(() => {
  localStorage.removeItem('vuetify:dynamic-reload')
})

export default router
