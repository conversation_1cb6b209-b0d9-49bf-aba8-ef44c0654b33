<template>
  <NjPage
    :title="'Liste des opérations du dossier EMMY : ' + (emmyFolderWithSummary.value?.emmyFolder.emmyCode ?? '...')"
    expend-body
    :error-message="data.error"
    :can-go-back="{ name: 'EmmyFolderAllView' }"
  >
    <template #after-title>
      <div class="ms-4">
        <EmmyFolderStepChip :step-id="emmyFolderWithSummary.value?.emmyFolder.stepId" />
      </div>
    </template>
    <template #subtitle>
      <div class="d-flex">
        <div class="flex-grow-1">
          <b>Date de fin de travaux min.</b> :
          {{ formatHumanReadableLocalDate(emmyFolderWithSummary.value?.minActualEndWorks) }}
        </div>
        <div class="flex-grow-1">
          <b>Date de fin de travaux max.</b> :
          {{ formatHumanReadableLocalDate(emmyFolderWithSummary.value?.maxActualEndWorks) }}
          <VTooltip
            v-if="
              emmyFolderWithSummary.value &&
              emmyFolderWithSummary.value.minActualEndWorks &&
              emmyFolderWithSummary.value.maxActualEndWorks &&
              differenceInMonths(
                parseISO(emmyFolderWithSummary.value.minActualEndWorks),
                parseISO(emmyFolderWithSummary.value.maxActualEndWorks)
              ) >= 6
            "
          >
            <template #activator="{ props }">
              <VIcon icon="mdi-alert" color="warning" v-bind="props" />
            </template>
            Il y a plus de six moix entre la date minimale de fin de travaux et la date maximale. La limite de dépôts
            sera dépassée.
          </VTooltip>
        </div>
        <div class="flex-grow-1"><b>Date limite de dépôt</b> : {{ maxSendFiles }}</div>
      </div>
    </template>
    <template #header-actions>
      <NjBtn
        v-if="emmyFolderWithSummary.value!.emmyFolder.stepId <= 100"
        :disabled="!notValidatedOperations.value?.empty"
        @click="validatingStep"
      >
        Valider l'étape {{ emmyFolderWithSummary.value!.emmyFolder.stepId }}
      </NjBtn>
    </template>
    <template #sub-header>
      <VRow>
        <VCol>
          <SearchInput
            v-model:loading="data.loading"
            :model-value="pageFilter.search"
            @update:model-value="updateSearch"
          />
        </VCol>
        <VCol>
          <VMenu>
            <template #activator="{ props }">
              <NjBtn append-icon="mdi-chevron-down" variant="outlined" color="" v-bind="props">
                {{ filterLabels[currentFilter] }}
              </NjBtn>
            </template>
            <VCard>
              <VRadioGroup v-model="currentFilter">
                <VRadio v-for="f in filterKeys" :key="f" :value="f" :label="filterLabels[f]" density="default"></VRadio>
              </VRadioGroup>
            </VCard>
          </VMenu>
        </VCol>
        <VCol class="d-flex justify-end" style="gap: 16px">
          <NjBtn v-if="emmyFolderWithSummary.value?.emmyFolder.stepId == 80" @click="manageOperationsDialog = true">
            Gérer les opérations
          </NjBtn>
          <NjBtn v-if="userStore.isAdmin" @click="generateCsvDialog = true"> Générer CSV </NjBtn>
          <NjBtn
            v-if="emmyFolderWithSummary.value?.emmyFolder.stepId == 80 && userStore.isAdmin"
            :disabled="!!notValidatedOperations.value?.empty"
            @click="importEmmyDialog = true"
          >
            Valider l'import EMMY
          </NjBtn>
        </VCol>
      </VRow>
    </template>
    <template #body>
      <VRow class="w-100 flex-nowrap">
        <VCol :cols="openDrawer ? '12' : '7'">
          <NjDataTable
            v-model:selections="operationSelection"
            :headers="headers"
            :pageable="pageable"
            :page="data.value!"
            :disabled-row="(item: Operation) => !!item.validateImportInEmmyDateTime"
            :on-click-row="clickRow"
            :clicked-row="clickedRow"
            checkboxes
            multi-selection
            fixed
            @update:pageable="updatePageable"
          >
            <template #[`item.emmyLotId`]="{ item }">
              <VSelect
                v-model="item.emmyLotId"
                :disabled="!!item.validateImportInEmmyDateTime && emmyFolderWithSummary.value?.emmyFolder.stepId == 80"
                style="width: 164px"
                :items="lots"
                @update:model-value="updateEmmyLotId(item.id, $event)"
                @click.stop
              >
              </VSelect>
            </template>
            <template #[`item.actualEndWorksDate`]="{ item }">
              {{ formatHumanReadableLocalDate(item.actualEndWorksDate!) }}
            </template>
          </NjDataTable>
        </VCol>
        <VCol v-show="!openDrawer" cols="5">
          <VCard class="h-100 content-layout">
            <VCardTitle class="content-layout__header pa-0">
              <VTabs v-model="tab">
                <VTab>Dossier EMMY</VTab>
                <VTab>Documents associés</VTab>
              </VTabs>
            </VCardTitle>
            <VDivider />
            <VCardText class="content-layout__main pa-0">
              <VWindow v-model="tab" class="h-100">
                <VWindowItem>
                  <NjExpansionPanel>
                    <template #title>
                      <div class="d-flex align-center">
                        Dossier EMMY
                        <VSpacer />
                        <NjIconBtn
                          v-if="userStore.isAdmin"
                          :icon="edit ? 'mdi-close' : 'mdi-pencil'"
                          color="primary"
                          @click.stop="toggleEdit"
                        ></NjIconBtn>
                      </div>
                    </template>
                    <NjDisplayValue
                      v-if="!edit"
                      label="Nom"
                      :value="emmyFolderWithSummary.value?.emmyFolder.name"
                    ></NjDisplayValue>
                    <VTextField
                      v-else
                      v-model="emmyFolderForm!.name"
                      label="Nom"
                      :disabled="editing.loading"
                      :rules="[requiredRule]"
                    />
                    <NjDisplayValue
                      v-if="!edit"
                      label="Numéro"
                      :value="emmyFolderWithSummary.value?.emmyFolder.emmyCode"
                    >
                    </NjDisplayValue>
                    <VTextField
                      v-else
                      v-model="emmyFolderForm!.emmyCode"
                      label="Code EMMY"
                      :disabled="editing.loading"
                      :rules="[requiredRule]"
                      class="mt-2"
                    />
                    <template v-if="(emmyFolderWithSummary.value?.emmyFolder.stepId ?? 0) >= 110">
                      <NjDisplayValue
                        v-if="!edit"
                        label="Numéro de délivrance classique"
                        :value="emmyFolderWithSummary.value?.emmyFolder.pnceeClassicIssuedNumber"
                      >
                      </NjDisplayValue>
                      <VTextField
                        v-else
                        v-model="emmyFolderForm!.pnceeClassicIssuedNumber"
                        label="Numéro de délivrance classique"
                        :disabled="editing.loading"
                        class="mt-2"
                      />
                      <NjDisplayValue
                        v-if="!edit"
                        label="Numéro de délivrance précarité"
                        :value="emmyFolderWithSummary.value?.emmyFolder.pnceePrecariousnessIssuedNumber"
                      >
                      </NjDisplayValue>
                      <VTextField
                        v-else
                        v-model="emmyFolderForm!.pnceePrecariousnessIssuedNumber"
                        label="Numéro de délivrance précarité"
                        :disabled="editing.loading"
                        class="mt-2"
                      />
                    </template>

                    <NjDisplayValue
                      label="Nombre d'opérations"
                      :value="totalSummary?.numberOfOperation.toLocaleString()"
                    >
                    </NjDisplayValue>
                  </NjExpansionPanel>

                  <VDivider />

                  <NjExpansionPanel title="kWhc Précarité et Classique">
                    <VRow class="flex-column" dense>
                      <VCol>
                        <NjDisplayValue
                          label="Nbre kWhc Class. dem"
                          :value="formatNumber(totalSummary?.classicCumacSum ?? 0)"
                        />
                      </VCol>
                      <VCol>
                        <NjDisplayValue
                          label="Nbre kWhc Préc. dem"
                          :value="formatNumber(totalSummary?.precariousnessCumacSum ?? 0)"
                        />
                      </VCol>
                      <VCol>
                        <NjDisplayValue
                          label="Nbre kWhc total dem"
                          :value="
                            formatNumber(
                              (totalSummary?.precariousnessCumacSum ?? 0) + (totalSummary?.classicCumacSum ?? 0)
                            )
                          "
                        />
                      </VCol>
                      <VCol>
                        <!-- eslint-disable-next-line vue/valid-v-for -->
                        <NjDisplayValue
                          v-for="(_, i) in 4"
                          :label="lots[i].title + ' (' + (reorganisedBatchSummaries[i]?.numberOfOperation ?? 0) + ')'"
                        >
                          <template #value>
                            <template v-if="reorganisedBatchSummaries[i]?.numberOfOperation">
                              Classique : {{ reorganisedBatchSummaries[i]?.classicCumacSum.toLocaleString() }} Précarité
                              :
                              {{ reorganisedBatchSummaries[i]?.precariousnessCumacSum.toLocaleString() }}
                            </template>
                            <template v-else>--</template>
                          </template>
                        </NjDisplayValue>
                      </VCol>
                    </VRow>
                  </NjExpansionPanel>

                  <VDivider v-if="edit" />
                  <div v-if="edit" class="mt-4 content-layout__footer d-flex justify-end" style="gap: 8px">
                    <NjBtn variant="outlined" @click="cancel">Annuler</NjBtn>
                    <NjBtn :loading="editing.loading" @click="update">Enregistrer</NjBtn>
                  </div>
                </VWindowItem>
                <VWindowItem :class="tab === 1 ? 'content-layout' : ''" class="pa-3">
                  <VRow class="flex-column content-layout">
                    <VCol v-if="userStore.isAdmin" class="content-layout__header">
                      <VSwitch v-model="showDisabledDocuments" label="Voir les documents inactifs" />
                    </VCol>
                    <VCol class="content-layout__main content-layout">
                      <EmmyFolderDocumentAllView
                        :id="props.id"
                        ref="emmyFolderDocumentAllViewRef"
                        :filter="{ emmyFolderId: props.id }"
                        actions
                        :show-disabled-documents="showDisabledDocuments"
                        class="h-100"
                      />
                    </VCol>
                    <VCol class="content-layout__footer">
                      <NjBtn prepend-icon="mdi-plus-circle-outline" @click="addDocumentDialog = true"> Ajouter </NjBtn>
                      <SubmitDocumentDialog
                        v-model="addDocumentDialog"
                        :emmy-folder-id="props.id"
                        @save-document="
                          (event) => {
                            if (event.some((it) => it.type === 'success')) {
                              emmyFolderDocumentAllViewRef?.reload()
                            }
                          }
                        "
                      />
                    </VCol>
                  </VRow>
                </VWindowItem>
              </VWindow>
            </VCardText>
          </VCard>
        </VCol>
      </VRow>
      <ManageOperationInEmmyFolderDialog
        v-if="emmyFolderWithSummary.value?.emmyFolder.id"
        ref="manageOperationInEmmyFolderDialogRef"
        v-model="manageOperationsDialog"
        :emmy-folder="emmyFolderWithSummary.value!.emmyFolder"
      >
        <template #actions>
          <NjBtn variant="outlined" @click="handleCancelManageOperation">Annuler</NjBtn>
          <NjBtn
            @click="
              () => {
                manageOperationsDialog = false
                reloadEmmyFolder(id)
              }
            "
            >Valider</NjBtn
          >
        </template>
      </ManageOperationInEmmyFolderDialog>
      <CardDialog v-model="generateCsvDialog" width="55%" title="Générer CSV" :persistent="generatingCSV">
        <NjDataTable
          v-model:selections="selection"
          :headers="headerCsv"
          :items="batchSummaries.value"
          checkboxes
          multi-selection
        >
          <template #[`item.batchId`]="{ item }">
            {{ lots.find((i) => i.value == item.batchId)?.title }}
          </template>
        </NjDataTable>
        <template #actions>
          <NjBtn variant="outlined" @click="generateCsvDialog = false"> Annuler </NjBtn>
          <NjBtn :disabled="!selection.length" :loading="generatingCSV" @click="generateCSV">Générer CSV</NjBtn>
        </template>
      </CardDialog>

      <CardDialog v-model="importEmmyDialog" width="55%" title="Valider l'import EMMY">
        <NjDataTable
          v-model:selections="selection"
          :headers="headerCsv"
          :items="batchSummaries.value!"
          checkboxes
          multi-selection
        >
          <template #[`item.batchId`]="{ item }">
            {{ lots.find((i) => i.value == item.batchId)?.title }}
          </template>
        </NjDataTable>
        <template #actions>
          <NjBtn variant="outlined" @click="importEmmyDialog = false"> Annuler </NjBtn>
          <NjBtn :disabled="!selection.length" @click="validateImport">Valider l'import EMMY</NjBtn>
        </template>
      </CardDialog>
      <CardDialog v-model="updatingError" title="Rapport d'erreur" width="40%">
        Une erreur est survenue lors du traitement
        {{ errorReports.length === 1 ? ' du lot suivant' : ' des lots suivants' }} :
        <div v-for="report in errorReports" :key="report">
          <span class="nj-display-value__value">{{ lots.find((i) => i.value == report.batchId)?.title }}</span>
          , avec pour message :<br />
          <div class="d-flex flex-column">
            <span v-for="(message, index) in report.messages" :key="index" class="value__value">
              {{ message.message }}</span
            >
          </div>
        </div>
        <template #actions>
          <NjBtn @click="updatingError = false"> Ok </NjBtn>
        </template>
      </CardDialog>
      <NextStepDialog
        v-if="emmyFolderWithSummary.value"
        v-model="validateStepDialog"
        :step-id="emmyFolderWithSummary.value.emmyFolder.stepId"
        :hide-divider="true"
      >
        <template #informations>
          <VRow class="flex-column pa-2 ma-0" dense>
            <VCol>
              <NjDisplayValue label="Dossier EMMY" :value="emmyFolderWithSummary.value.emmyFolder.emmyCode" />
            </VCol>
            <VCol v-if="emmyFolderWithSummary.value.emmyFolder.stepId >= 90">
              <VDivider />
            </VCol>
            <VCol v-if="emmyFolderWithSummary.value.emmyFolder.stepId == 90">
              <VForm ref="validateStep90Form">
                <NjDisplayValue label="Date d'envoi au PNCEE">
                  <template #value>
                    <div class="w-50">
                      <NjDatePicker v-model="pnceeSubmissionDate" :rules="[requiredRule]" />
                    </div>
                  </template>
                </NjDisplayValue>
              </VForm>
            </VCol>
            <VCol v-else-if="emmyFolderWithSummary.value.emmyFolder.stepId > 90">
              <NjDisplayValue
                label="Date d'envoi au PNCEE"
                :value="formatHumanReadableLocalDate(emmyFolderWithSummary.value.emmyFolder.pnceeSubmissionDate!)"
              />
            </VCol>
            <VCol v-if="emmyFolderWithSummary.value.emmyFolder.stepId > 90">
              <VDivider />
            </VCol>
            <VCol v-if="emmyFolderWithSummary.value.emmyFolder.stepId == 100">
              <VForm ref="validateStep100Form">
                <VRow class="flex-column">
                  <VCol>
                    <NjDisplayValue label="Date décision délivrance PNCEE">
                      <template #value>
                        <div class="w-50">
                          <NjDatePicker v-model="pnceeIssuedDate" :rules="[requiredRule]" />
                        </div>
                      </template>
                    </NjDisplayValue>
                  </VCol>
                  <VCol class="py-0">
                    <VDivider />
                  </VCol>
                  <VCol>
                    <NjDisplayValue label="Numéro de délivrance classique PNCEE">
                      <template #value>
                        <div class="w-50">
                          <VTextField v-model="pnceeClassicIssuedNumber" prefix="CL" />
                        </div>
                      </template>
                    </NjDisplayValue>
                  </VCol>
                  <VCol>
                    <NjDisplayValue label="Numéro de délivrance précarité PNCEE">
                      <template #value>
                        <div class="w-50">
                          <VTextField v-model="pnceePrecariousnessIssuedNumber" prefix="PR" />
                        </div>
                      </template>
                    </NjDisplayValue>
                  </VCol>
                  <VCol>
                    <VInput
                      :model-value="pnceeClassicIssuedNumber || pnceePrecariousnessIssuedNumber"
                      :rules="[requiredRuleGenerator('Au moins un des deux numéros de délivrance est requis')]"
                    />
                  </VCol>
                </VRow>
              </VForm>
            </VCol>
          </VRow>
          <template v-if="missingDocuments.value?.totalElements">
            <NjDivider />
            <NjExpansionPanel title="Document requis">
              <GenericDocumentSubmit
                ref="genericDocumentSubmit"
                :emmy-folder-id="props.id"
                mode="nextStep"
                :missing-documents="missingDocuments.value.content"
                @document-save="afterSubmitDocumentInNextStepDialog"
              />
            </NjExpansionPanel>
          </template>
        </template>
        <template #actions>
          <VCardActions>
            <VSpacer />
            <NjBtn variant="outlined" :disabled="updating" @click="validateStepDialog = false"> Annuler </NjBtn>
            <NjBtn :loading="updating" @click="validateStep"> Valider </NjBtn>
          </VCardActions>
        </template>
      </NextStepDialog>

      <ConfirmUnsavedDataDialog
        v-model:unsaved-data-dialog="unsavedDataDialog"
        @confirm="clearChanges"
        @save="update"
      />
    </template>

    <template #drawer>
      <OperationDrawer
        ref="operationDrawerRef"
        v-model="openDrawer"
        :operation="selectedOperation.value"
        @update:operation="onUpdateOperation"
        @cancel-operation-k-o-p-n-c-e-e="reload"
      />

      <CommentaireDialog
        v-model="commentaireDialog.props.modelValue"
        v-model:mandatory-message="mandatoryMessage"
        :operation="operationForMessage"
        :no-link="!!mandatoryMessage"
        :advised-recipient="advisedRecipient"
        @send="handleSentMessage"
        @close="commentaireDialog.props['onClick:negative']"
      />
    </template>
  </NjPage>
</template>
<script setup lang="ts">
import { emmyFolderApi, type EmmyFolderRequest } from '@/api/emmyFolder'
import type { OperationFilter } from '@/api/operation'
import NjBtn from '@/components/NjBtn.vue'
import type { DataTableHeader } from '@/components/okta/NjDataTable.type'
import NjDataTable from '@/components/okta/NjDataTable.vue'
import { useSnackbarStore } from '@/stores/snackbar'
import { formatHumanReadableLocalDate, type LocalDate } from '@/types/date'
import {
  makeEmptyEmmyFolder,
  type EmmyFolder,
  type EmmyFolderBatchSummary,
  type EmmyFolderWithSummary,
} from '@/types/emmyFolder'
import type { Operation } from '@/types/operation'
import { requiredRule, requiredRuleGenerator } from '@/types/rule'
import { add, sub, differenceInMonths, format, parseISO } from 'date-fns'
import { ref, unref, watch } from 'vue'
import { VDivider, VIcon, VRadio, VRadioGroup, VSelect, VTooltip } from 'vuetify/components'
import ManageOperationInEmmyFolderDialog from './ManageOperationInEmmyFolderDialog.vue'
import EmmyFolderDocumentAllView from './documents/EmmyFolderDocumentAllView.vue'
import NextStepDialog from '../dialog/NextStepDialog.vue'
import type { VForm } from 'vuetify/components/VForm'
import OperationDrawer from '../operation/OperationDrawer.vue'
import { useUserStore } from '@/stores/user'
import type { Page } from '@/types/pagination'
import type { DocumentType } from '@/types/documentType'
import GenericDocumentSubmit from '../document/GenericDocumentSubmit.vue'
import { formatNumber } from '@/types/format'
import { cloneDeep } from 'lodash'
import CommentaireDialog from '../operation/dialog/CommentaireDialog.vue'
import EmmyFolderStepChip from './EmmyFolderStepChip.vue'
import SubmitDocumentDialog from '../document/SubmitDocumentDialog.vue'
import { missingDocumentTypeIdsKey } from '../dashboard/keys'

const props = defineProps<{
  id: number
}>()

const headers: DataTableHeader<Operation>[] = [
  {
    title: 'Lot',
    value: 'emmyLotId',
  },
  {
    title: 'Nom',
    value: 'operationName',
  },
  {
    title: 'Etape',
    value: 'stepId',
  },
  {
    title: 'Chrono',
    value: 'chronoCode',
  },
  {
    title: 'Code',
    value: 'standardizedOperationSheet.operationCode',
  },
  {
    title: 'Fin travaux',
    value: 'actualEndWorksDate',
  },
  {
    title: 'Envoi EMMY',
    value: 'validateImportInEmmyDateTime',
    formater: (item) => (item.validateImportInEmmyDateTime ? 'OUI' : 'NON'),
  },
  {
    title: 'Date de génération du CSV',
    value: 'exportToCsvDateTime',
    formater: (_, value) => formatHumanReadableLocalDateTime(value),
  },
]

const tab = ref(0)
const operationSelection = ref<Operation[]>([])
const updatingError = ref(false)
const errorReports = ref<any[]>([])
const showDisabledDocuments = ref(false)

const { data, pageFilter, pageable, updatePageable, reload, updateFilter } = usePaginationInQuery<
  Operation,
  OperationFilter
>((filter, pageable) => operationApi.findAll(filter, pageable), {
  saveFiltersName: 'EmmyFolderOneView',
  defaultPageFilter: {
    emmyFolderId: props.id,
  },
})

const userStore = useUserStore()
const openDrawer = ref(false)
const selectedOperation = ref(emptyValue<Operation>())
const clickedRow = ref<Operation>()
const clickRow = async (item: Operation) => {
  check(() => {
    edit.value = false
    operationDrawerRef.value!.check(async () => {
      clickedRow.value = item
      if (item.id !== selectedOperation.value.value?.id) {
        await handleAxiosPromise(selectedOperation, simulationApi.findById(item.id), {
          afterError: () =>
            snackbarStore.setError(
              selectedOperation.value.error ?? "Une erreur est survenue lors de la récupération de l'opération"
            ),
        })
        openDrawer.value = true
        return
      }
      openDrawer.value = !openDrawer.value
    })
  })
}

const loadMissingDocuments = () => {
  return handleAxiosPromise(
    missingDocuments,
    documentTypeApi.getAll({}, { missingToValidateEmmyFolder: props.id })
  ).then((it) => {
    return it.data.content
  })
}
const instructionsDisplay = ref<Record<string, boolean>>({})
const missingDocuments = ref(emptyValue<Page<DocumentType>>())
provide(missingDocumentTypeIdsKey, {
  missingDocumentTypes: computed(() => missingDocuments.value.value?.content ?? []),
  reload: loadMissingDocuments,
})

const onUpdateOperation = () => {
  reload()
  reloadNotValidatedOperations()
}

watch(openDrawer, (v) => {
  if (!v) {
    clickedRow.value = undefined
  }
})

watch(pageFilter, updateFilter, { deep: true })

const emmyFolderWithSummary = ref(
  succeedValue<EmmyFolderWithSummary>({
    emmyFolder: makeEmptyEmmyFolder(),
    emmyFolderBatchSummaries: [],
    minActualEndWorks: '',
    maxActualEndWorks: '',
  })
)
const emmyFolderForm = ref<EmmyFolder>()
const maxSendFiles = computed(() => {
  if (emmyFolderWithSummary.value.value?.minActualEndWorks) {
    return format(
      sub(add(parseISO(emmyFolderWithSummary.value.value?.minActualEndWorks), { months: 12 }), { days: 1 }),
      'dd/MM/yyyy'
    )
  }
  return ''
})
const reloadEmmyFolder = async (id: number) => {
  await handleAxiosPromise(emmyFolderWithSummary, emmyFolderApi.findById(id))
  emmyFolderForm.value = cloneDeep(emmyFolderWithSummary.value.value!.emmyFolder)
  succeedSave()
}
watch(
  () => props.id,
  (v) => {
    if (v) {
      reloadEmmyFolder(props.id)
    } else {
      emmyFolderWithSummary.value = succeedValue({
        emmyFolder: makeEmptyEmmyFolder(),
        emmyFolderBatchSummaries: [],
        maxActualEndWorks: '',
        minActualEndWorks: '',
      })
    }
  },
  {
    immediate: true,
  }
)
const totalSummary = computed((): Omit<EmmyFolderBatchSummary, 'batchId'> | undefined => {
  return emmyFolderWithSummary.value.value?.emmyFolderBatchSummaries?.reduce(
    (v, acc) => ({
      classicCumacSum: v.classicCumacSum + acc.classicCumacSum,
      precariousnessCumacSum: v.precariousnessCumacSum + acc.precariousnessCumacSum,
      numberOfOperation: v.numberOfOperation + acc.numberOfOperation,
    }),
    { numberOfOperation: 0, classicCumacSum: 0, precariousnessCumacSum: 0 }
  )
})
const reorganisedBatchSummaries = computed(() => {
  const result: EmmyFolderBatchSummary[] = []
  emmyFolderWithSummary.value.value?.emmyFolderBatchSummaries.forEach((el) => {
    result[el.batchId] = el
  })
  return result
})

const manageOperationsDialog = ref(false)
const manageOperationInEmmyFolderDialogRef = ref<typeof ManageOperationInEmmyFolderDialog>()
watch(manageOperationsDialog, (v) => {
  if (!v) {
    reload()
  }
})

const handleCancelManageOperation = async () => {
  await Promise.all(
    manageOperationInEmmyFolderDialogRef.value!.updatedOperation.addedOperations.map((operationId: number) => {
      return emmyFolderApi.removeOperation(emmyFolderWithSummary.value.value!.emmyFolder.id, operationId)
    })
  )
  await Promise.all(
    manageOperationInEmmyFolderDialogRef.value!.updatedOperation.removedOperation.map((operationId: number) => {
      return emmyFolderApi.addOperation(emmyFolderWithSummary.value.value!.emmyFolder.id, {
        operationIds: [operationId],
      })
    })
  )
  manageOperationsDialog.value = false
}

// Search
const updateSearch = (v: string) => {
  const filter = {
    ...unref(pageFilter),
    search: v,
  }
  updateFilter(filter)
}

const lots = [
  { value: 0, title: 'Aucun lot' },
  { value: 1, title: 'Lot 1' },
  { value: 2, title: 'Lot 2' },
  { value: 3, title: 'Lot 3' },
]

const updateEmmyLotId = (operationId: number, emmyLotId: number) => {
  //data.value.loading = true
  if (operationSelection.value.find((ope) => ope.id === operationId)) {
    Promise.all(
      operationSelection.value.map((ope) =>
        operationApi
          .setEmmyLotId(ope.id, emmyLotId)
          .then(() => {
            ope.emmyLotId = emmyLotId
          })
          .catch(async (e) => {
            data.value.error = await handleAxiosException(e)
          })
      )
    )
      .then(() => (operationSelection.value = []))
      .finally(() => reloadEmmyFolder(props.id))
    return
  }
  operationApi
    .setEmmyLotId(operationId, emmyLotId)
    .then(() => {
      reloadEmmyFolder(props.id)
    })
    .catch(async (e) => {
      data.value.error = await handleAxiosException(e)
    })
}

const generateCsvDialog = ref(false)
const headerCsv = [
  {
    title: 'Préparation',
    value: 'batchId',
  },
  {
    title: "Nombre d'opérations",
    value: 'numberOfOperation',
    formater: (_: any, value: number) => formatNumber(value),
    cellClass: 'text-right justify-end',
  },
  {
    title: 'Nb. Dem. Class.kWhc',
    value: 'classicCumacSum',
    formater: (_: any, value: number) => formatNumber(value),
    cellClass: 'text-right justify-end',
  },
  {
    title: 'Nb. Dem. Préc. kWhc',
    value: 'precariousnessCumacSum',
    formater: (_: any, value: number) => formatNumber(value),
    cellClass: 'text-right justify-end',
  },
]

const snackbarStore = useSnackbarStore()

const batchSummaries = ref(emptyValue<EmmyFolderBatchSummary[]>())
watch(generateCsvDialog, (v) => {
  if (v) {
    handleAxiosPromise(
      batchSummaries,
      emmyFolderApi.findAllBatchSummary(props.id, {
        onlyNotValidatedInEmmy: emmyFolderWithSummary.value.value!.emmyFolder.stepId == 80,
      })
    )
  } else {
    selection.value = []
  }
})
const selection = ref<EmmyFolderBatchSummary[]>([])
const generatingCSV = ref(false)
const generateCSV = () => {
  generatingCSV.value = true
  Promise.allSettled(
    selection.value.map((batch) =>
      emmyFolderApi
        .exportBatchToCSV(props.id, batch.batchId, {
          onlyNotValidatedInEmmy: emmyFolderWithSummary.value.value!.emmyFolder.stepId == 80,
        })
        .then((response) =>
          downloadFile(
            emmyFolderWithSummary.value.value?.emmyFolder.name +
              ' ' +
              lots.find((item) => item.value == batch.batchId)?.value +
              '.csv',
            response.data
          )
        )
    )
  )
    .then((response) => {
      if (response.filter((res) => res.status === 'rejected').length > 0) {
        errorReports.value = []
        response.forEach(async (error, index) => {
          if (error.status === 'rejected') {
            errorReports.value.push({
              batchId: selection.value[index].batchId,
              messages: JSON.parse(await (error as any).reason.response.data.text()).errors ?? [
                'Une erreur est survenue lors de la génération du fichier csv',
              ],
            })
          }
        })
        generateCsvDialog.value = false
        updatingError.value = true
      } else {
        snackbarStore.setSuccess(
          `${
            selection.value.length === 1
              ? 'La génération du fichier csv a réussi'
              : 'La génération des fichiers csv a réussi'
          }`
        )
        generateCsvDialog.value = false
      }
      reload()
    })
    .finally(() => {
      generatingCSV.value = false
    })
}

const importEmmyDialog = ref(false)
watch(importEmmyDialog, (v) => {
  if (v) {
    handleAxiosPromise(
      batchSummaries,
      emmyFolderApi.findAllBatchSummary(props.id, {
        onlyNotValidatedInEmmy: true,
      })
    )
  } else {
    selection.value = []
  }
})
const validateImport = () => {
  Promise.allSettled(selection.value.map((batch) => emmyFolderApi.validateBatch(props.id, batch.batchId))).then(
    async (response) => {
      if (response.filter((res) => res.status === 'rejected').length > 0) {
        errorReports.value = []
        response.forEach(async (error, index) => {
          if (error.status === 'rejected') {
            errorReports.value.push({
              batchId: selection.value[index].batchId,
              message: handleAxiosException((error as any).reason, undefined, {
                defaultMessage: 'Une erreur est survenue lors de la validation',
              }),
            })
          }
        })
        importEmmyDialog.value = false
        updatingError.value = true
      } else {
        snackbarStore.setSuccess(
          `${selection.value.length === 1 ? 'La validation du lot a réussi' : 'La validation des lots a réussi'}`
        )
        reload()
        if (selectedOperation.value.value) {
          await handleAxiosPromise(selectedOperation, simulationApi.findById(selectedOperation.value.value.id), {
            afterError: () =>
              snackbarStore.setError(
                selectedOperation.value.error ?? "Une erreur est survenue lors de la récupération de l'opération"
              ),
          })
        }
        importEmmyDialog.value = false
      }
      reloadEmmyFolder(props.id)
    }
  )
}

const filterKeys = ['none', 'lot_0', 'lot_1', 'lot_2', 'lot_3', 'valid', 'anomaly'] as const
const filterLabels: Record<(typeof filterKeys)[number], string> = {
  anomaly: 'Anomalie',
  lot_0: 'Aucun lot',
  lot_1: 'Lot 1',
  lot_2: 'Lot 2',
  lot_3: 'Lot 3',
  valid: 'Validés',
  none: 'Tous',
}
const currentFilter = ref<(typeof filterKeys)[number]>('none')
watch(currentFilter, () => {
  const newFilter: OperationFilter = {
    emmyFolderId: props.id,
  }

  switch (currentFilter.value) {
    case 'lot_0':
      newFilter.emmyLotIds = [0]
      break
    case 'lot_1':
      newFilter.emmyLotIds = [1]
      break
    case 'lot_2':
      newFilter.emmyLotIds = [2]
      break
    case 'lot_3':
      newFilter.emmyLotIds = [3]
      break
    case 'valid':
      newFilter.emmyValidated = true
      break
    case 'anomaly':
      // TODO
      break
    default:
      break
  }
  updateFilter(newFilter)
})

const oneElementPageable = {
  page: 0,
  size: 1,
}

const { data: notValidatedOperations, reload: reloadNotValidatedOperations } = usePagination<
  Operation,
  OperationFilter
>(
  (filter, pageable) => operationApi.findAll(filter, pageable),
  {
    emmyFolderId: props.id,
    emmyValidated: false,
    stepIds: [80],
  },
  { ...oneElementPageable }
)

watch([manageOperationsDialog, importEmmyDialog], (v, oldV) => {
  if ((!v[0] && oldV[0]) || (!v[1] && oldV[1])) {
    reloadNotValidatedOperations()
  }
})

const validateStepDialog = ref(false)
const pnceeSubmissionDate = ref<LocalDate | null>()
const validateStep90Form = ref<typeof VForm | null>(null)
const pnceeIssuedDate = ref<LocalDate | null>(null)
const pnceeClassicIssuedNumber = ref<string | null>(null)
const pnceePrecariousnessIssuedNumber = ref<string | null>(null)

const validateStep100Form = ref<typeof VForm | null>(null)
const operationDrawerRef = ref<typeof OperationDrawer | null>(null)

const emmyFolderDocumentAllViewRef = ref<typeof EmmyFolderDocumentAllView | null>(null)
const afterSubmitDocumentInNextStepDialog = () => {
  loadMissingDocuments()
  emmyFolderDocumentAllViewRef.value?.reload()
}

const validatingStep = () => {
  check(() => {
    instructionsDisplay.value = {}
    loadMissingDocuments()
    validateStepDialog.value = true
    missingDocuments.value.value?.content.forEach((type) => (instructionsDisplay.value[type.name] = false))
  })
}

const mandatoryMessage = ref('')
const advisedRecipient = ref<string[]>([])
const commentaireDialog = useConfirmAlertDialog()
const operationForMessage = ref<Operation>()
const handleSentMessage = () => {
  commentaireDialog.props['onClick:positive']()
}
const updating = ref(false)

const validateStep = async () => {
  const request = mapToRequest(emmyFolderWithSummary.value.value!.emmyFolder)
  request.stepId += 10

  if (request.stepId == 100 && !(await validateStep90Form!.value!.validate()).valid) {
    return
  }

  if (request.stepId == 110 && !(await validateStep100Form!.value!.validate()).valid) {
    return
  }

  if (request.stepId === 110) {
    const sosPage = (
      await standardizedOperationSheetApi.findAll({}, { search: 'SPECIFIQUE' }).catch(async (e) => {
        snackbarStore.setError(await handleAxiosException(e))
        throw e
      })
    ).data.content[0]

    const ops = await operationApi
      .findAll(
        {
          emmyFolderId: props.id,
          standardizedOperationSheetIds: [sosPage.id],
        },
        {
          size: 1000,
        }
      )
      .catch(async (e) => {
        snackbarStore.setError(await handleAxiosException(e))
        throw e
      })

    for (const it of ops.data.content) {
      mandatoryMessage.value = dafMail(it!, true)
      advisedRecipient.value = it.entity.entityDetails.effectiveDafMail
      operationForMessage.value = it

      if (!(await commentaireDialog.confirm())) {
        return
      }
      mandatoryMessage.value = ''
      advisedRecipient.value = []
    }
  }

  updating.value = true

  // promisableValidateStep30.value = loadingValue()
  if (genericDocumentSubmitRef.value) {
    const sendDocumentsResponse = (await genericDocumentSubmitRef.value?.uploadFiles()) ?? []
    if (sendDocumentsResponse !== false && sendDocumentsResponse.some((it) => it.type === 'success')) {
      afterSubmitDocumentInNextStepDialog()
    }
    if (sendDocumentsResponse === false || sendDocumentsResponse.some((it) => it.type === 'error')) {
      // promisableValidateStep30.value.loading = false
      return
    }
  }

  await emmyFolderApi
    .save(props.id, request)
    .then(() => {
      snackbarStore.setSuccess("Passage d'étape effectué avec succès")
      validateStepDialog.value = false
      reloadEmmyFolder(props.id)
      reload()
    })
    .catch(async (err) => {
      snackbarStore.setError(await handleAxiosException(err))
    })
    .finally(() => {
      updating.value = false
    })
}

const edit = ref(false)
const { check, succeedSave, unsavedDataDialog, clearChanges } = useUnsavedData(emmyFolderWithSummary)
const toggleEdit = () => {
  check(() => {
    edit.value = !edit.value
  })
}

const mapToRequest = (emmyFolder: EmmyFolder): EmmyFolderRequest => {
  let classicIssuedNumber
  if (pnceeClassicIssuedNumber.value) {
    classicIssuedNumber = pnceeClassicIssuedNumber.value.startsWith('CL')
      ? pnceeClassicIssuedNumber.value.substring(2)
      : pnceeClassicIssuedNumber.value
  } else {
    classicIssuedNumber = emmyFolder.pnceeClassicIssuedNumber
  }
  let precariousnessIssuedNumber
  if (pnceePrecariousnessIssuedNumber.value) {
    precariousnessIssuedNumber = pnceePrecariousnessIssuedNumber.value.startsWith('PR')
      ? pnceePrecariousnessIssuedNumber.value.substring(2)
      : pnceePrecariousnessIssuedNumber.value
  } else {
    precariousnessIssuedNumber = emmyFolder.pnceePrecariousnessIssuedNumber
  }

  return {
    name: emmyFolder.name,
    emmyCode: emmyFolder.emmyCode,
    stepId: emmyFolder.stepId,
    pnceeSubmissionDate: pnceeSubmissionDate.value ?? emmyFolder.pnceeSubmissionDate,
    pnceeIssuedDate: pnceeIssuedDate.value ?? emmyFolder.pnceeIssuedDate,
    pnceeClassicIssuedNumber: classicIssuedNumber,
    pnceePrecariousnessIssuedNumber: precariousnessIssuedNumber,
  }
}
const editing = ref(emptyValue<EmmyFolder>())
const update = async () => {
  handleAxiosPromise(editing, emmyFolderApi.save(props.id, mapToRequest(emmyFolderForm.value!)))
    .then(() => {
      snackbarStore.setSuccess('Dossier emmy mise à jour avec succès')
      emmyFolderWithSummary.value.value!.emmyFolder = cloneDeep(emmyFolderForm.value!)
      succeedSave()
      edit.value = false
    })
    .catch(async (e) => {
      snackbarStore.setError(await handleAxiosException(e))
    })
}
const cancel = () => {
  toggleEdit()
}

const genericDocumentSubmitRef = useTemplateRef('genericDocumentSubmit')
const addDocumentDialog = ref(false)
</script>
