import type { LocalDate, LocalDateTime } from './date'
import type { User } from './user'

export interface ControlOrganism {
  id: number
  socialReason: string
  siren: string
  address: string
  postalCode: string
  city: string
  certified: boolean
  controlType: ControlTypes | undefined
  contactName: string
  contactPhoneNumber: string
  contactEmail: string
  startDate: LocalDate
  endDate: LocalDate
  creationUser: User
  creationDateTime: LocalDateTime
  updateUser: User
  updateDateTime: LocalDateTime
}

export const controlTypeLabel: {
  label: string
  value: string
}[] = [
  {
    label: 'CPE',
    value: 'CPE',
  },
  {
    label: 'Arreté contrôle',
    value: 'ARRETE_CONTROLE',
  },
  {
    label: 'Calorifuge',
    value: 'CALORIFUGE',
  },
  {
    label: 'Audit energetique',
    value: 'AUDIT_ENERGETIQUE',
  },
  {
    label: 'Autres',
    value: 'AUTRES',
  },
]

export type ControlTypes = (typeof controlTypeLabel)[number]['value']

export const getControlTypeLabel = (type: ControlTypes | undefined) => {
  return controlTypeLabel.find((i) => i.value == type)?.label
}

export function makeEmptyControlOrganism(): ControlOrganism {
  return {
    id: 0,
    socialReason: '',
    siren: '',
    address: '',
    postalCode: '',
    city: '',
    contactName: '',
    contactPhoneNumber: '',
    contactEmail: '',
    certified: false,
    controlType: undefined,
    creationDateTime: '',
    creationUser: makeEmptyUser(),
    updateDateTime: '',
    updateUser: makeEmptyUser(),
    startDate: '',
    endDate: '',
  }
}

export interface ControlOrganismFilter {
  search?: string
}
