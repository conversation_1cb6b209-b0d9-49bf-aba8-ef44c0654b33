import type { ControlOrganism, ControlOrganismFilter } from '@/types/controlOrganism'
import type { Page, Pageable } from '@/types/pagination'
import type { AxiosInstance, AxiosPromise } from 'axios'
import axiosInstance from '.'

class ControlOrganismApi {
  public constructor(private axios: AxiosInstance) {}

  public create(controlOrganism: ControlOrganism): AxiosPromise<ControlOrganism> {
    return this.axios.post('/control_organisms', controlOrganism)
  }

  public update(id: number, controlOrganism: ControlOrganism): AxiosPromise<ControlOrganism> {
    return this.axios.put(`/control_organisms/${id}`, controlOrganism)
  }

  public getAll(pageable: Pageable, filter: ControlOrganismFilter): AxiosPromise<Page<ControlOrganism>> {
    return this.axios.get('/control_organisms', {
      params: { ...pageable, ...filter },
    })
  }

  public getOne(id: number): AxiosPromise<ControlOrganism> {
    return this.axios.get(`/control_organisms/${id}`)
  }
}

export const controlOrganismApi = new ControlOrganismApi(axiosInstance)
