<template>
  <VRow>
    <VCol v-if="subcontractors.loading" class="d-flex align-center">
      <VProgressCircular class="ms-4" indeterminate />
    </VCol>
    <VCol v-else>
      <NjDataTable
        :headers="subcontractorHeader"
        :selections="props.selections"
        :pageable="pageable"
        :page="data.value!"
        :fixed="fixed"
        @update:selections="updateModelValue"
        @update:pageable="updatePageable"
      >
        <template #[`item.certified`]="{ item }">
          <VIcon v-show="item.certified" color="#28B750" icon="mdi-shield-check-outline" />
        </template>
      </NjDataTable>
    </VCol>
  </VRow>
</template>
<script setup lang="ts">
import type { PropType } from 'vue'
import type { Subcontractor, SubcontractorFilter } from '@/types/subcontractor'
import { subcontractorApi } from '@/api/subcontractor'
import { usePagination, type Page } from '@/types/pagination'
import type { DataTableHeader } from '@/components/okta/NjDataTable.type'
import { debounce } from 'lodash'

const props = defineProps({
  selections: {
    type: Object as PropType<Subcontractor[]>,
  },
  search: {
    type: String,
  },
  fixed: Boolean,
})

const emit = defineEmits(['update:selections', 'update:loading'])

const defaultSort =
  (props.selections ?? []).length > 0
    ? ['"ids:' + props.selections?.map((b) => b.id).join(';') + '",DESC', 'certified,DESC']
    : ['certified,DESC']

const updateModelValue = (value: Subcontractor[]) => {
  emit('update:selections', value)
}

const subcontractors = ref(emptyValue<Page<Subcontractor>>())

const { data, pageable, pageFilter, updatePageable, updateFilter, reload } = usePagination<
  Subcontractor,
  SubcontractorFilter
>(
  (filter, pageable) => subcontractorApi.findAll(pageable, filter),
  {},
  {
    page: 0,
    size: 100,
    sort: defaultSort,
  }
)
const subcontractorHeader: DataTableHeader[] = [
  {
    title: 'Raison sociale',
    value: 'socialReason',
  },
  {
    title: 'Siret',
    value: 'siret',
  },
  {
    title: 'Adresse',
    value: 'address.street',
  },
  {
    title: 'Code postal',
    value: 'address.postalCode',
  },
  {
    title: 'Ville',
    value: 'address.city',
  },
  {
    title: 'Certifié',
    value: 'certified',
  },
  {
    title: 'Certifié par',
    value: 'certifyingUser',
    formater: (item) => (item.certified ? displayFullnameUser(item.updateUser) : ''),
    sortable: false,
  },
  {
    title: 'Certifié le',
    value: 'certifiedDateTime',
    formater: (item) => (item.certified ? formatHumanReadableLocalDateTime(item.updateDateTime) : ''),
    sortable: false,
  },
  {
    title: 'Créateur',
    value: 'creationUser',
    formater: (_, value) => displayFullnameUser(value),
  },
  {
    title: 'Date de création',
    value: 'creationDateTime',
    formater: (_, value) => (value ? formatHumanReadableLocalDateTime(value) : ''),
  },
  {
    title: 'Mis à jour par',
    value: 'updateUser',
    formater: (_, value) => displayFullnameUser(value),
  },
  {
    title: 'Date de mis à jour',
    value: 'updateDateTime',
    formater: (_, value) => (value ? formatHumanReadableLocalDateTime(value) : ''),
  },
]

const debounceSearch = debounce((v: string | undefined) => {
  updateFilter({ ...pageFilter.value, search: v })
}, 300)

watch(
  () => props.search,
  (v) => {
    data.value.loading = true
    debounceSearch(v)
  }
)

watch(
  () => data.value.loading,
  (v) => {
    emit('update:loading', v)
  }
)

onMounted(() => {
  if (props.search) {
    pageFilter.value.search = props.search
  }
})

defineExpose({ reload })
</script>
