<template>
  <NjExpansionPanel>
    <template #title>
      <div class="d-flex align-center fill-width">
        Paramètres
        <VSpacer />
        <NjIconBtn icon="mdi-plus" size="small" color="primary" @click.stop="addParameter"></NjIconBtn>
      </div>
    </template>
    <VRow v-for="(arg, i) in props.modelValue.parameters" :key="i">
      <VCol>
        <VCard>
          <VCardText class="align-center">
            <ParameterCalculEditor
              :model-value="props.modelValue.parameters[i]"
              :disabled="readonly"
              @update:model-value="updateParameter(i, $event)"
              @delete="deleteParameter(i)"
            />
          </VCardText>
        </VCard>
      </VCol>
    </VRow>
  </NjExpansionPanel>
  <NjExpansionPanel>
    <template #title>
      <div class="d-flex align-center fill-width">
        Tables de correspondances
        <VSpacer />
        <NjIconBtn icon="mdi-plus" size="small" color="primary" @click.stop="addMappingTable" />
      </div>
    </template>
    <VRow v-for="(mappingTable, iMappingTable) in props.modelValue.mappingTables" :key="iMappingTable">
      <VCol>
        <VDivider />
        <MappingTableEditor
          :parameters="choiceParameter"
          :i-mapping-tables="iMappingTable"
          :model-value="props.modelValue.mappingTables[iMappingTable]"
          :disabled="readonly"
          @update:model-value="updateMappingTable(iMappingTable, $event)"
          @delete="deleteMappingTable(iMappingTable)"
        />
      </VCol>
    </VRow>
  </NjExpansionPanel>
  <NjExpansionPanel title="Formule">
    <FormulaInput
      :model-value="props.modelValue.formula"
      :rules="[requiredRule]"
      :readonly="readonly"
      @update:model-value="updateValue('formula', $event)"
    />
  </NjExpansionPanel>
  <NjExpansionPanel title="Aperçu">
    <BoostBonusSheetCalculator
      v-model="paramPreview"
      result-title="Résultat"
      :calcul-formula="props.modelValue"
      mode="preview"
      :disabled="readonly"
    />
  </NjExpansionPanel>
</template>
<script setup lang="ts">
import { generateMappingTableCombinaisons, makeEmptyMappingTable, type MappingTable } from '@/types/calcul/mappingTable'
import { makeEmptyParameterFormula, type ParameterFormula } from '@/types/calcul/parameterFormula'
import type { CalculFormula } from '@/types/calculFormula'
import type { PropType } from 'vue'
import { requiredRule } from '@/types/rule'
import FormulaInput from './FormulaInput.vue'
import MappingTableEditor from './MappingTableEditor.vue'
import ParameterCalculEditor from './ParameterCalculEditor.vue'
import { VCard, VCardText } from 'vuetify/components'

const props = defineProps({
  modelValue: {
    type: Object as PropType<CalculFormula>,
    required: true,
  },
  readonly: Boolean,
})

const emit = defineEmits(['update:model-value'])
onMounted(() => {
  paramPreview.value = initParamPreview()
})
watch(
  () => props.modelValue,
  (v) => {
    for (const mappingTable of v.mappingTables) {
      combinaisonsValues.value[mappingTable.id] = generateMappingTableCombinaisons(mappingTable, v.parameters)
    }
  }
)

function updateValue(column: string, value: unknown) {
  emit('update:model-value', { ...props.modelValue, [column]: value })
}

const addParameter = () => {
  const parameters = props.modelValue.parameters.concat(makeEmptyParameterFormula())
  updateValue('parameters', parameters)
}
const updateParameter = (index: number, value: ParameterFormula) => {
  const parameters = props.modelValue.parameters
  parameters[index] = value
}

const deleteParameter = (index: number) => {
  const parameters = props.modelValue.parameters.slice(0, index).concat(props.modelValue.parameters.slice(index + 1))
  updateValue('parameters', parameters)
}

const addMappingTable = () => {
  const mappingTable = props.modelValue.mappingTables.concat(makeEmptyMappingTable())
  updateValue('mappingTables', mappingTable)
}

const choiceParameter = computed(() => {
  return props.modelValue.parameters.filter((it) => it.type === 'CHOICE')
})

const updateMappingTable = (iMappingTable: number, mappingTable: MappingTable) => {
  const mappingTables = props.modelValue.mappingTables
  mappingTables[iMappingTable] = mappingTable
}

const deleteMappingTable = (index: number) => {
  const mappingTables = props.modelValue.mappingTables
    .slice(0, index)
    .concat(props.modelValue.mappingTables.slice(index + 1))
  updateValue('mappingTables', mappingTables)
}

const combinaisonsValues = ref<Record<string, string[][]>>({})

// formula parsing
const initParamPreview = () => {
  const result: any = {}
  for (const parameter of props.modelValue.parameters) {
    result[parameter.id] = ''
  }
  return result
}
const paramPreview = ref({})
</script>
