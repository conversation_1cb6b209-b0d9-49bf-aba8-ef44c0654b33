import type { Entity } from '@/types/entity'
import { add, isAfter } from 'date-fns'
import { defineStore } from 'pinia'

export const useEntityStore = defineStore('entity', () => {
  let store: Record<string, { entityPromise: Promise<Readonly<Entity>>; fetchDate: Date }> = {}

  const findById = async (id: string | null | undefined): Promise<Readonly<Entity>> => {
    if (!id) {
      return Promise.reject('not found')
    }

    const optionalEntityPromise = store[id]

    if (optionalEntityPromise && isAfter(add(optionalEntityPromise.fetchDate, { minutes: 60 }), new Date())) {
      return optionalEntityPromise.entityPromise
    }

    const promise = entityApi.getOne(id).then((it) => it.data)
    store[id] = { entityPromise: promise, fetchDate: new Date() }
    promise.catch((e) => {
      delete store[id]
      throw e
    })

    return store[id].entityPromise
  }

  const clear = () => {
    store = {}
  }

  const clearOne = (id: string) => {
    delete store[id]
  }

  return { getOne: findById, clear, clearOne: clearOne }
})
