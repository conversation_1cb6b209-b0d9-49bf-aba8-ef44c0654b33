<template>
  <NjPage title="Logs applicatifs" :error-message="data.error" :loading="data.loading" expend-body>
    <template #sub-header>
      <VRow>
        <VCol cols="3">
          <SearchInput
            v-model:loading="data.loading"
            :model-value="pageFilter.search"
            @update:model-value="updateFilterByFieldname('search', $event)"
          />
        </VCol>
        <VCol cols="3">
          <VSelect
            label="Niveau"
            :model-value="pageFilter.level"
            :items="['ERROR', 'WARN', 'INFO', 'DEBUG']"
            clearable
            @update:model-value="(event) => updateFilterByFieldname('level', event)"
          />
        </VCol>
      </VRow>
    </template>
    <template #body>
      <VRow class="w-100">
        <VCol>
          <NjDataTable
            :headers="headers"
            :pageable="pageable"
            :page="data.value!"
            fixed
            :page-sizes="[-1, 50, 100, 250]"
            @update:pageable="updatePageable"
          >
            <!-- eslint-disable-next-line vue/valid-v-slot -->
            <template #item.actions="{ item }">
              <NjIconBtn
                :disabled="stacktraceDialog.loading && stacktraceDialog.eventId !== item.eventId"
                :loading="stacktraceDialog.loading && stacktraceDialog.eventId === item.eventId"
                icon="mdi-code-block-braces"
                @click="showStackTrace(item)"
              />
            </template>
          </NjDataTable>
        </VCol>
      </VRow>
      <VDialog v-model="stacktraceDialog.active">
        <VCard class="pa-4">
          <pre><template v-for="l in stacktraceDialog.stacktraces">{{ l.traceLine + '\n' }}</template></pre>
        </VCard>
      </VDialog>
    </template>
  </NjPage>
</template>
<script lang="ts" setup>
import type { LoggingEvent, LoggingEventException, LoggingEventFilter } from '@/api/loggingEvent'
import NjPage from '@/components/NjPage.vue'
import type { DataTableHeader } from '@/components/okta/NjDataTable.type'
import NjDataTable from '@/components/okta/NjDataTable.vue'
import { useSnackbarStore } from '@/stores/snackbar'
import { format, fromUnixTime } from 'date-fns'
import { VSelect } from 'vuetify/components'

const search = ref<string>()
const snackbarStore = useSnackbarStore()
const { data, pageable, pageFilter, updatePageable, updateFilter, updateFilterByFieldname } = usePaginationInQuery<
  LoggingEvent,
  LoggingEventFilter
>(
  (filter, pageable) => {
    return loggingEventApi.findAll(filter, pageable)
  },
  {
    defaultPageFilter: {
      // search: search.value,
      // territoryIds: [],
      // level: ''
    },
    saveFiltersName: 'LoggingEventsAllView',
    queryToFilterMapper(query) {
      return {
        ...query,
        territoryIds: mapQueryToTable(query.territoryIds, true),
      }
    },
  }
)

const headers: DataTableHeader<LoggingEvent>[] = [
  {
    title: 'ID',
    value: 'eventId',
  },
  {
    title: 'Date',
    value: 'timestmp',
    formater: (_, value) => {
      return format(fromUnixTime(value / 1000), dateTimeHumanFormat)
    },
  },
  {
    title: 'Action',
    value: 'actions',
  },
  {
    title: 'Level',
    value: 'levelString',
  },
  {
    title: 'LoggerName',
    value: 'loggerName',
  },
  {
    title: 'Message',
    value: 'formattedMessage',
    maxLength: 254,
  },
  {
    title: 'Class',
    value: 'caller',
    formater(item) {
      return item.callerClass + '#' + item.callerMethod + ':' + item['callerLine']
    },
    maxLength: 100,
  },
  {
    title: 'arg0',
    value: 'arg0',
  },
  {
    title: 'arg1',
    value: 'arg1',
  },
  {
    title: 'arg2',
    value: 'arg2',
  },
  {
    title: 'arg3',
    value: 'arg3',
  },
  {
    title: 'Reference Flag',
    value: 'referenceFlag',
  },
]

watch(search, (v) => {
  const filter = {
    ...unref(pageFilter),
    search: v,
  }
  updateFilter(filter)
})

const stacktraceDialog = ref({
  active: false,
  stacktraces: [] as LoggingEventException[],
  eventId: -1,
  loading: false,
})
const showStackTrace = (item: LoggingEvent) => {
  stacktraceDialog.value.loading = true
  stacktraceDialog.value.eventId = item.eventId
  loggingEventApi
    .getStacktrace(item.eventId)
    .then((it) => {
      if (it.data.length > 0) {
        stacktraceDialog.value.stacktraces = it.data
        stacktraceDialog.value.active = true
      } else {
        snackbarStore.state = {
          color: 'info',
          message: "Il n'y a pas de stacktrace pour cette évènement.",
          modelValue: true,
          timeout: 5000,
        }
      }
    })
    .finally(() => {
      stacktraceDialog.value.loading = false
    })
}
</script>
