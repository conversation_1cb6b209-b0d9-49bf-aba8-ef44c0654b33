<template>
  <VNavigationDrawer location="right" width="500" permanent>
    <VCard class="content-layout">
      <VCardTitle class="d-flex align-center content-layout__header">
        <span class="d-flex w-100"> Filtres </span>
        <NjIconBtn icon="mdi-window-close" variant="flat" color="primary" @click="emit('update:model-value')" />
      </VCardTitle>
      <VDivider />
      <VCardText class="content-layout__main">
        <VRow class="flex-column" dense>
          <VCol>
            <VSelect
              v-model="filter.periodIds"
              label="Périodes"
              :items="periodsStore.periods"
              item-title="name"
              item-value="id"
              multiple
            />
          </VCol>
          <VCol>
            <RemoteAutoComplete
              v-model="selectedBeneficiaries"
              label="Bénéficiaires"
              :query-for-one="(id) => beneficiaryApi.findOne(id)"
              :query-for-all="(s, pageable) => beneficiaryApi.findAll(pageable, { search: s })"
              :item-title="(item) => (item.lastName ?? '') + ' ' + (item?.socialReason ?? '')"
              chips
              closable-chips
              multiple
              return-object
              infinite-scroll
            />
          </VCol>
          <VCol>
            <VSelect v-model="filter.status" label="Processus" :items="operationStatusLabel" multiple />
          </VCol>
          <VCol>
            <VDivider />
          </VCol>
          <VCol>
            <div class="d-flex align-center fill-width">
              <div class="text-section-title">Organisations</div>
              <VSpacer />
              <VLink
                size="small"
                icon="mdi-format-list-bulleted"
                style="font-weight: initial; font-size: initial"
                @click="orgFilter = true"
              >
                Organisations
              </VLink>
            </div>
          </VCol>
          <VCol v-if="selectedEntities.length">
            <VList>
              <VListItem v-for="org in selectedEntities" :key="org.id" density="compact" class="py-0">
                {{ org.name }}
                <template #append>
                  <NjIconBtn icon="mdi-delete-outline" @click="removeEntity(org)" />
                </template>
              </VListItem>
            </VList>
          </VCol>
        </VRow>
      </VCardText>
    </VCard>
    <EntityFilterDialog v-model="orgFilter" v-model:selected="selectedEntities" />
  </VNavigationDrawer>
</template>
<script lang="ts" setup>
import { beneficiaryApi } from '@/api/beneficiary'
import type { OperationsGroupFilter } from '@/api/operationsGroup'
import RemoteAutoComplete from '@/components/RemoteAutoComplete.vue'
import { usePeriodsStore } from '@/stores/periods'
import { type Beneficiary } from '@/types/beneficiary'
import type { Entity } from '@/types/entity'
import { cloneDeep, isEqual } from 'lodash'
import type { PropType } from 'vue'
import EntityFilterDialog from '../operation/dialog/EntityFilterDialog.vue'
import { VCol, VSelect } from 'vuetify/components'
import { operationStatusLabel } from '@/types/operation'

const props = defineProps({
  originalFilter: {
    type: Object as PropType<OperationsGroupFilter>,
    default: () => {},
  },
})

const emit = defineEmits<{
  'update:original-filter': [filter: OperationsGroupFilter]
  'update:model-value': []
}>()

const orgFilter = ref(false)
const filter = ref(cloneDeep(props.originalFilter))
const selectedBeneficiaries = ref<Beneficiary[]>([])
const selectedEntities = ref<Entity[]>([])

const periodsStore = usePeriodsStore()

const load = async () => {
  initFilter()
}

const initFilter = async () => {
  if ((props.originalFilter.beneficiaryIds?.length ?? 0) > 0) {
    beneficiaryApi
      .findAll({}, { ids: props.originalFilter.beneficiaryIds })
      .then((response) => (selectedBeneficiaries.value = response.data.content))
  } else {
    selectedBeneficiaries.value = []
  }

  if ((props.originalFilter.entityNavFullIds?.length ?? 0) > 0) {
    entityApi
      .getAll({ startWithNavFullIds: props.originalFilter.entityNavFullIds }, {})
      .then((response) => (selectedEntities.value = response.data.content))
  } else {
    selectedEntities.value = []
  }
}

watch(selectedBeneficiaries, (v) => {
  if (v && v.length > 0) {
    filter.value.beneficiaryIds = v.map((b) => b.id)
  } else {
    filter.value.beneficiaryIds = []
  }
})

watch(
  () => props.originalFilter,
  (v) => {
    if (v && !isEqual(v, filter.value)) {
      filter.value = cloneDeep(v)
      if ((props.originalFilter.entityNavFullIds?.length ?? 0) === 0) {
        selectedEntities.value = []
      }
    }
  },
  {
    deep: true,
  }
)

watch(selectedEntities, (v) => {
  filter.value.entityNavFullIds = v.map((i) => i.navFullId)
})

const removeEntity = (entity: Entity) => {
  selectedEntities.value = selectedEntities.value.filter((item) => item.id != entity.id)
}

watch(
  filter,
  (v) => {
    if (v) {
      emit('update:original-filter', filter.value)
    }
  },
  {
    deep: true,
  }
)

onMounted(load)
</script>
