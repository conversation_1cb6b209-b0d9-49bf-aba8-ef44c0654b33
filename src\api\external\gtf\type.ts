import type { Page, Pageable } from '@/types/pagination'
import type { AxiosResponse } from 'axios'
import { keyBy, mapValues } from 'lodash'

export type FieldInstructionFilter = {
  $gte?: string | number
  $gt?: string | number
  $lte?: string | number
  $lt?: string | number
  $like?: string
  $instr?: string
  $eq?: any
  $ne?: any
  $null?: null
  $notnull?: null
}

export type FieldFilter = string | number | FieldInstructionFilter
export type ComplexFieldFilter =
  | FieldFilter
  | {
      $and?: ComplexFieldFilter[]
    }
  | {
      $or?: ComplexFieldFilter[]
    }
// | OrderFieldFilterArray

// export type OrderFieldFilterArray = Record<string, FieldFilter>[]
export type FieldFilterSet<K extends keyof any> = Partial<Record<K, ComplexFieldFilter>>

export type ComplexFilterFilterSet<K extends keyof any> = {
  $and?: ComplexFilterFilterSet<K>[]
} & {
  $or?: ComplexFilterFilterSet<K>[]
} & FieldFilterSet<K>

export type OrderByField<K extends keyof any> = Partial<Record<K, 'ASC' | 'DESC'>>
export interface OrdsQueryParams<T> {
  q: { $orderby?: OrderByField<keyof T> } & ComplexFilterFilterSet<keyof T>
  limit?: number
  offset?: number
}

export interface GtfPage<T> {
  items: T[]
  limit: number
  offset: number
  hasMore: boolean
  count: number
}

export interface GtfPageable {
  offset?: number
  limit?: number
}

export const mapGtfPagToPage = <T>(gtfPage: GtfPage<T>): Page<T> => {
  const temporaryTotal = gtfPage.count + gtfPage.offset
  return {
    ...gtfPage,
    content: gtfPage.items,
    empty: gtfPage.count === 0,
    first: gtfPage.offset === 0,
    last: gtfPage.hasMore === false,
    number: gtfPage.offset / gtfPage.count,
    size: gtfPage.count,
    sort: {
      empty: false,
      sorted: false,
      unsorted: false,
    },
    totalElements: temporaryTotal + (gtfPage.hasMore ? 1 : 0),
    totalPages: Math.ceil((temporaryTotal + (gtfPage.hasMore ? 1 : 0)) / gtfPage.limit),
  }
}

export const mapAxiosResponse = <T>(r: AxiosResponse<GtfPage<T>>): AxiosResponse<Page<T>> => {
  return {
    ...r,
    data: mapGtfPagToPage(r.data),
  }
}

export const mapPageableToGtfPageable = (pageable: Pageable | undefined): GtfPageable =>
  pageable === undefined
    ? {}
    : {
        limit: pageable.size,
        offset: (pageable.page ?? 0) * (pageable.size ?? 0),
      }

export const mapPageableToOrderByGtfPageable = (sort: Pageable['sort']): OrderByField<string> => {
  return mapValues(
    keyBy(
      sort?.map((it) => {
        const splitted = it.split(',')
        return [splitted[0], splitted[1] ?? 'ASC'] as [string, 'ASC' | 'DESC']
      }) ?? [],
      0
    ),
    1
  )
}
