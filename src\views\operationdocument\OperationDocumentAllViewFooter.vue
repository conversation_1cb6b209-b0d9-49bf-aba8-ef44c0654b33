<template>
  <VDivider />
  <VRow class="pa-2 value__label content-layout__footer">
    <VCol align-self="center" class="flex-grow-0 text-no-wrap"> Documents ajoutés: {{ totalElements }} </VCol>
    <VCol>
      <NjBtn
        v-if="props.totalElements"
        border="0"
        prepend-icon="mdi-download"
        variant="outlined"
        :loading="downloadAllDocumentsLoading"
        @click="downloadAllDocuments"
      >
        Tout télécharger
      </NjBtn>
      <template v-if="displayMergeButton">
        <NjBtn border="0" prepend-icon="mdi-file" variant="outlined" @click="mergePdfDialog = true">
          Fusionner les Pdf
        </NjBtn>
        <MergePdfDialog
          v-model="mergePdfDialog"
          :operation-id="operation.id"
          :operation-name="operation.operationName"
        />
      </template>
      <NjBtn
        v-if="displayExportControlOrderFileButton"
        prepend-icon="mdi-download"
        :loading="exportControlOrderFileLoading"
        variant="outlined"
        border="0"
        @click="exportControlOrderFile"
      >
        Télécharger la synthèse
      </NjBtn>
    </VCol>
    <VCol class="flex-grow-0">
      <NjBtn
        v-if="(operation.stepId ?? 0) < 60 || userStore.isSiege"
        prepend-icon="mdi-plus-circle-outline"
        @click="addDocumentDialog = true"
      >
        Ajouter
      </NjBtn>
      <SubmitDocumentDialog
        v-model="addDocumentDialog"
        :missing-documents="missingDocumentTypes"
        :operation-id="props.operation.id"
        :operations-group-id="props.operation.operationsGroup?.id"
        display-missing-document-in-info-alert
        @save-document="emits('saveDocument', $event)"
      />
    </VCol>
  </VRow>
</template>
<script setup lang="ts">
import type { Operation } from '@/types/operation'
import MergePdfDialog from '../operation/dialog/MergePdfDialog.vue'
import { useUserStore } from '@/stores/user'
import { useSnackbarStore } from '@/stores/snackbar'
import type { PropType } from 'vue'
import type { DocumentType } from '@/types/documentType'
import SubmitDocumentDialog from '../document/SubmitDocumentDialog.vue'
import type SendDocumentResult from '../document/sendDocumentResult'

const props = defineProps({
  operation: {
    type: Object as PropType<Operation>,
    required: true,
  },
  totalElements: Number,
  missingDocumentTypes: { type: Array as PropType<DocumentType[]>, default: [] as DocumentType[] },
})

const emits = defineEmits<{
  saveDocument: [SendDocumentResult[]]
}>()
const displayExportControlOrderFileButton = computed(
  () =>
    props.operation.stepId == 50 &&
    props.operation.standardizedOperationSheet.controlOrderStartDate &&
    props.operation.standardizedOperationSheet.controlOrderStartDate <= props.operation.signedDate! &&
    props.operation.standardizedOperationSheet.controlOrderNature == 'HUNDRED_PERCENT'
)
const exportControlOrderFileLoading = ref(false)
const exportControlOrderFile = () => {
  exportControlOrderFileLoading.value = true
  operationApi
    .exportToControlOrderExcelFile(props.operation.id)
    .then((r) => {
      downloadFile(`Synthèse arreté controle ${props.operation.chronoCode}`, r.data)
      snackbarStore.setSuccess('La synthèse a bien été téléchargée')
    })
    .catch(async (error) => {
      snackbarStore.setError(await handleAxiosException(error, JSON.parse(await error.response.data.text()).message))
    })
    .finally(() => {
      exportControlOrderFileLoading.value = false
    })
}

const mergePdfDialog = ref(false)
const userStore = useUserStore()
const snackbarStore = useSnackbarStore()

const displayMergeButton = computed(
  () =>
    ((props.operation.stepId ?? 0) > 40 && userStore.isSiege) ||
    ((props.operation.stepId ?? 0) == 50 && userStore.hasRole('AGENCE_PLUS', 'SUPPORT_AGENCE_PLUS'))
)

const downloadAllDocumentsLoading = ref(false)
const downloadAllDocuments = () => {
  downloadAllDocumentsLoading.value = true
  operationDocumentApi
    .downloadAll(props.operation.id)
    .then(async (response) => {
      await awaitResponse(response.data)
        .then(async (response) => {
          downloadFile(
            'documents-' + props.operation.chronoCode + '.zip',
            (await responseHandlerApi.download(response.uuid)).data
          )
          snackbarStore.setSuccess('Le téléchargement des documents a réussi')
        })
        .catch((response) => {
          snackbarStore.setError(response.errorMessage ?? 'Le téléchargement des documents a échoué')
        })
    })
    .catch(async (err) => snackbarStore.setError(await handleAxiosException(err)))
    .finally(() => {
      downloadAllDocumentsLoading.value = false
    })
}

const addDocumentDialog = ref(false)
</script>
