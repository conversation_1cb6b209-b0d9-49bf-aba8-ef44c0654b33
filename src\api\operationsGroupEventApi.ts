import type { Page, Pageable } from '@/types/pagination'
import type { AxiosInstance, AxiosPromise } from 'axios'
import axiosInstance from '.'
import type { OperationsGroupEvent } from '@/types/operationsGroupEvent'

export interface OperationsGroupEventFilter {
  top?: boolean
}
class OperationsGroupEventApi {
  public constructor(private axios: AxiosInstance) {}

  public findAll(
    operationsGroupsId: number,
    filter: OperationsGroupEventFilter,
    pageable: Pageable
  ): AxiosPromise<Page<OperationsGroupEvent>> {
    return this.axios.get(`/operations_groups/${operationsGroupsId}/events`, {
      params: { ...filter, ...pageable },
    })
  }
}
export const operationsGroupEventApi = new OperationsGroupEventApi(axiosInstance)
