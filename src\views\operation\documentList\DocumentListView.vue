<template>
  <VRow class="flex-column">
    <VCol>
      <DocumentDisplayList :documents="operationDocumentPage" type="OperationDocument" />
    </VCol>
    <VCol v-if="props.operationsGroupId">
      <span>Document issus du regroupement:</span>
      <DocumentDisplayList :documents="operationsGroupDocumentPage" type="OperationsGroupDocument" />
    </VCol>
  </VRow>
</template>
<script setup lang="ts">
import type { EnhancedDocument } from '@/types/document'
import { operationDocumentApi } from '@/api/operationDocument'
import { operationsGroupDocumentApi } from '@/api/operationsGroupDocument'
import { type Page } from '@/types/pagination'
import DocumentDisplayList from './DocumentDisplayList.vue'

const props = defineProps({
  operationId: Number,
  operationsGroupId: Number,
})

const operationDocumentPage = ref(emptyValue<Page<EnhancedDocument>>())
const operationsGroupDocumentPage = ref(emptyValue<Page<EnhancedDocument>>())

watch(
  [() => props.operationsGroupId, () => props.operationId],
  (v) => {
    if (v[1]) {
      handleAxiosPromise(
        operationDocumentPage,
        operationDocumentApi.findAll(
          {
            active: true,
            operationId: props.operationId,
          },
          {
            page: 0,
            size: 1000,
          }
        )
      )
    }
    if (v[0] && v[1]) {
      handleAxiosPromise(
        operationsGroupDocumentPage,
        operationsGroupDocumentApi.findAll(
          {
            active: true,
            operationsGroupId: v[0],
            notInOperation: props.operationId,
          },
          {
            page: 0,
            size: 1000,
          }
        )
      )
    }
  },
  {
    immediate: true,
  }
)
</script>
