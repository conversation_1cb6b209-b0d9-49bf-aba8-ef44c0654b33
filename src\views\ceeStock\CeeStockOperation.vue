<template>
  <VRow class="flex-column content-layout" dense v-bind="$attrs">
    <VCol class="content-layout__header">
      <VRow>
        <VCol>
          <VCheckbox
            :model-value="withEmmyFolder"
            label="Restreindre aux réservations avec dossiers EMMY"
            @update:model-value="emits('update:withEmmyFolder', $event ? true : null)"
          />
        </VCol>
        <VCol>
          <VCheckbox
            :model-value="withCorrections"
            label="Restreindre au réservations corrigées"
            @update:model-value="emits('update:withCorrections', $event ? true : null)"
          />
        </VCol>
        <VCol class="flex-grow-0">
          <NjBtn @click="filterDrawer = !filterDrawer">Filtres</NjBtn>
        </VCol>
      </VRow>
    </VCol>
    <VCol>
      <NjDataTable
        :headers="headers"
        :page="ceeStockOperations.value!"
        :pageable="pageableCeeStockOperations"
        :selections="selections"
        :loading="ceeStockOperations.loading"
        fixed
        @update:pageable="updatePageableCeeStockOperations"
        @update:selections="(item: CeeStockEntryDto[]) => emits('update:selections', item)"
      >
        <template #[`item.worksStatus`]="{ item }">
          <VProgressCircular v-show="worksMap.loading" indeterminate />
          <template v-if="!worksMap.loading && !worksMap.error">
            {{
              [item.worksId1, item.worksId2, item.worksId3]
                .filter((s) => s != null)
                .map((it) =>
                  worksMap.value?.[it]?.closure_date
                    ? 'Fini le ' + formatHumanReadableLocalDate(worksMap.value?.[it]?.closure_date)
                    : worksMap.value?.[it]?.cancellation_date
                      ? 'Annulé le ' + formatHumanReadableLocalDate(worksMap.value?.[it]?.cancellation_date)
                      : 'En cours'
                )
                .join('/')
            }}
          </template>
        </template>
        <template #[`item.action`]="{ item }">
          <NjIconBtn
            rounded="0"
            icon="mdi-eye-outline"
            @click.stop="
              () => {
                operationForAccountingEntryDialog = item
                updateFilterByFieldnameAccountingEntries('operationId', item.operationId)
                accountingEntryDialog = true
              }
            "
          ></NjIconBtn>
        </template>
      </NjDataTable>
      <CardDialog
        v-model="accountingEntryDialog"
        width="80%"
        height="80%"
        :title="'Pièce comptable pour l\'opération: ' + operationForAccountingEntryDialog?.chronoCode"
        no-actions
      >
        <div class="content-layout w-100 h-100">
          <NjDataTable
            class="content-layout content-layout__main"
            :headers="accountingEntryHeaders"
            :page="accountingEntries.value!"
            :pageable="accountingEntriesPageable"
            :page-filter="pageFilterAccountingEntries"
            :loading="accountingEntries.loading"
            @update:pageable="updatePageableAccountingEntries"
          />
        </div>
      </CardDialog>
    </VCol>
  </VRow>
  <VNavigationDrawer v-model="filterDrawer" location="right" width="600" permanent>
    <VCard class="content-layout">
      <VCardTitle class="d-flex align-center content-layout__header">
        <span class="d-flex w-100"> Filtres </span>
        <NjIconBtn icon="mdi-window-close" variant="flat" color="primary" rounded="0" @click="filterDrawer = false" />
      </VCardTitle>
      <VDivider />
      <VCardText class="content-layout__main">
        <VRow class="flex-column" dense>
          <VCol>
            <VSelect v-model="pageFilter.stepIds" label="Étapes" :items="stepsStore.steps" multiple>
              <template #selection="{ item }">{{ item.title }}</template>
              <template #item="{ item }">
                <VListItem @click="updateStepIds(item.raw.id)">
                  <template #prepend>
                    <VCheckboxBtn :model-value="(pageFilter.stepIds ?? []).includes(item.raw.id)"></VCheckboxBtn>
                  </template>
                  {{ item.raw.id }} - {{ item.raw.name }}
                </VListItem>
              </template>
            </VSelect>
          </VCol>
          <VCol>
            <VSelect
              v-model="pageFilter.periodIds"
              label="Périodes"
              :items="periodsStore.periods"
              item-title="name"
              item-value="id"
              multiple
            />
          </VCol>
          <VCol>
            <VSelect
              v-model="pageFilter.valuationTypeIds"
              label="Type d'affaires"
              :items="activeValuationTypes"
              item-title="name"
              item-value="id"
              multiple
            />
          </VCol>
          <VCol>
            <VSelect v-model="pageFilter.operationStatuses" label="Processus" :items="operationStatus" multiple>
              <template #selection="{ item }">{{ mapToReadableStatus(item.raw) }}</template>
              <template #item="{ item }">
                <VListItem @click="updateCommercialStatuses(item.raw)">
                  <template #prepend>
                    <VCheckboxBtn :model-value="pageFilter.operationStatuses!.includes(item.raw)"></VCheckboxBtn>
                  </template>
                  {{ mapToReadableStatus(item.raw) }}
                </VListItem>
              </template>
            </VSelect>
          </VCol>
          <VCol>
            <VSelect
              v-model="pageFilter.commercialStatuses"
              label="Statut offres commerciales"
              :items="commercialStatusTitle"
              multiple
            />
          </VCol>
          <VCol>
            <RemoteAutoComplete
              v-model="selectedBeneficiaries"
              label="Bénéficiaires"
              :query-for-one="(id) => beneficiaryApi.findOne(id)"
              :query-for-all="(s, pageable) => beneficiaryApi.findAll(pageable, { search: s })"
              :item-title="(item) => (item.lastName ?? '') + ' ' + (item?.socialReason ?? '')"
              chips
              closable-chips
              multiple
              return-object
              infinite-scroll
            />
          </VCol>
          <VCol>
            <RemoteAutoComplete
              v-model="selectedInstructors"
              label="Instructeur"
              :query-for-one="(id) => userApi.getOne(id)"
              :query-for-all="
                (s, pageable) => userApi.getAll(pageable, { search: s, roles: ['INSTRUCTEUR'], active: true })
              "
              :item-title="(item: User) => displayFullnameUser(item)"
              chips
              closable-chips
              multiple
              return-object
              infinite-scroll
            />
          </VCol>
          <VCol>
            <RemoteAutoComplete
              v-model="selectedOperationsGroup"
              label="Regroupement"
              :query-for-one="(id) => operationsGroupApi.findById(id)"
              :query-for-all="(s, pageable) => operationsGroupApi.findAll({ search: s }, pageable)"
              :item-title="(item) => item.name"
              chips
              closable-chips
              multiple
              return-object
              infinite-scroll
            />
          </VCol>
          <VCol>
            <RemoteAutoComplete
              v-model="selectedEmmyFolders"
              label="Dossier EMMY"
              :query-for-one="(id) => emmyFolderApi.findById(id).then((it) => ({ ...it, data: it.data.emmyFolder }))"
              :query-for-all="(s, pageable) => emmyFolderApi.findAll({ search: s }, pageable)"
              :item-title="(item) => item.emmyCode + ' - ' + item.name"
              chips
              closable-chips
              multiple
              return-object
              infinite-scroll
            />
          </VCol>
          <VCol>
            <RemoteAutoComplete
              label="Territoire"
              :model-value="pageFilter.territoryIds?.[0]"
              :query-for-one="(v: number) => territoryApi.findOne(v)"
              :query-for-all="
                (v: string, pageable: Pageable) =>
                  territoryApi.findAll({ search: v }, { ...pageable, sort: ['description,ASC'] })
              "
              :item-title="formatTerritory"
              item-value="id"
              infinite-scroll
              chips
              closable-chips
              clearable
              @update:model-value="pageFilter.territoryIds = $event ? [parseInt($event)] : undefined"
            />
          </VCol>
        </VRow>
      </VCardText>
    </VCard>
  </VNavigationDrawer>
</template>
<script setup lang="ts">
import type { AccountingEntryFilter } from '@/api/accountingEntry'
import { beneficiaryApi } from '@/api/beneficiary'
import { ceeStockOperationApi, type CeeStockOperationFilter } from '@/api/ceeStockOperation'
import { emmyFolderApi } from '@/api/emmyFolder'
import { gtfTraApi, type GtfTraWorkDto } from '@/api/external/gtf/gtfTra'
import { operationsGroupApi } from '@/api/operationsGroup'
import { territoryApi } from '@/api/territory'
import { userApi } from '@/api/user'
import NjBtn from '@/components/NjBtn.vue'
import type { DataTableHeader } from '@/components/okta/NjDataTable.type'
import NjDataTable from '@/components/okta/NjDataTable.vue'
import { usePeriodsStore } from '@/stores/periods'
import { useSnackbarStore } from '@/stores/snackbar'
import { useStepsStore } from '@/stores/steps'
import { useUserStore } from '@/stores/user'
import { useValuationTypesStore } from '@/stores/valuationTypes'
import type { AccountingEntryOperationDto } from '@/types/accountingEntry'
import type { Beneficiary } from '@/types/beneficiary'
import type { CeeStockEntryDto } from '@/types/ceeStockEntry'
import type { LocalDate } from '@/types/date'
import { formatHumanReadableLocalDate } from '@/types/date'
import { type EmmyFolder } from '@/types/emmyFolder'
import { mapToReadableStatus, type OperationStatus, operationStatus } from '@/types/operation'
import type { OperationsGroup } from '@/types/operationsGroup'
import type { Pageable } from '@/types/pagination'
import { commercialStatusTitle } from '@/types/steps'
import { formatTerritory } from '@/types/territory'
import { displayFullnameUser, type User } from '@/types/user'
import { keyBy } from 'lodash'
import type { PropType } from 'vue'
import { dafSiegeCeeStockOperationHeaders, defaultCeeStockOperationHeaders } from './ceeStockOperationHeader'

const props = defineProps({
  entityIds: {
    type: Array as PropType<string[]>,
    required: true,
  },
  startDate: String as PropType<LocalDate>,
  endDate: String as PropType<LocalDate>,
  search: String,
  selections: Array as PropType<CeeStockEntryDto[]>,
  withEmmyFolder: Boolean,
  withCorrections: Boolean,
})

const emits = defineEmits<{
  'update:selections': [item: CeeStockEntryDto[]]
  'update:withEmmyFolder': [item: boolean | null]
  'update:withCorrections': [item: boolean | null]
}>()
const snackbarStore = useSnackbarStore()
const userStore = useUserStore()
const periodsStore = usePeriodsStore()

const headers = computed(() => {
  return userStore.hasRole('DAF_SIEGE') ? dafSiegeCeeStockOperationHeaders : defaultCeeStockOperationHeaders
})

const {
  data: ceeStockOperations,
  pageable: pageableCeeStockOperations,
  updatePageable: updatePageableCeeStockOperations,
  updateFilter,
  pageFilter,
} = usePagination<CeeStockEntryDto, CeeStockOperationFilter>(
  (filter, pageable) => {
    if (userStore.hasRole('DAF_SIEGE')) {
      return ceeStockOperationApi.findAllWithAccountingEntrySummary(pageable, { ...filter, entityIds: props.entityIds })
    }
    return ceeStockOperationApi.findAll(pageable, { ...filter, entityIds: props.entityIds })
  },
  {
    startDate: props.startDate,
    endDate: props.endDate,
    search: props.search,
    withCorrections: props.withCorrections,
    withEmmyFolder: props.withEmmyFolder,
    operationStatuses: [],
  },
  {}
)

watch(
  ceeStockOperations,
  (v) => {
    if (props.selections && props.selections[0]) {
      const newSelected = v.value?.content.find((item) => item.operationId == props.selections![0].operationId)
      if (newSelected) {
        emits('update:selections', [newSelected])
      } else {
        emits('update:selections', [])
      }
    }
  },
  {
    deep: true,
  }
)

watch(
  () => [props.entityIds, props.startDate, props.endDate, props.search, props.withEmmyFolder, props.withCorrections],
  (v) => {
    updateFilter({
      ...unref(pageFilter),
      entityIds: v[0] as string[],
      startDate: v[1] as LocalDate,
      endDate: v[2] as LocalDate,
      search: v[3] as string,
      withEmmyFolder: v[4] as boolean | null,
      withCorrections: v[5] as boolean | null,
    })
  },
  {
    deep: true,
  }
)

const worksMap = ref(emptyValue<Record<number, GtfTraWorkDto>>())
watch(
  () => ceeStockOperations.value.value?.content,
  (v) => {
    const ids = v
      ?.map((it) => [it.worksId1, it.worksId2, it.worksId3])
      .flat()
      .filter((it) => it != null)

    if (ids?.length) {
      worksMap.value.loading = true
      worksMap.value.error = undefined
      gtfTraApi
        .getAllWorks(
          {
            ids,
          },
          {
            size: ids.length,
          }
        )
        .then((d) => {
          worksMap.value.value = keyBy(d.data.items, 'id')
        })
        .catch(async (e) => {
          worksMap.value.error = await handleAxiosException(e)
          snackbarStore.setError("Nous n'avons pas pu récupéré l'état des chantiers des opérations")
        })
        .finally(() => {
          worksMap.value.loading = false
        })
    }
  }
)

const accountingEntryDialog = ref(false)
const operationForAccountingEntryDialog = ref<CeeStockEntryDto | null>(null)

const accountingEntryHeaders: DataTableHeader<AccountingEntryOperationDto>[] = [
  {
    title: 'Numéro de pièce',
    value: 'accountingEntryOperationId.accountingEntry.sapCode',
    formater: (item) => item.accountingEntry.sapCode,
  },
  {
    title: "Date d'entrée",
    value: 'accountingEntryOperationId.accountingEntry.creationDate',
    formater: (item) => formatHumanReadableLocalDate(item.accountingEntry.creationDate),
  },
  {
    title: 'Type de pièce',
    value: 'accountingEntryOperationId.accountingEntry.accountingEntryType',
    formater: (item) => accountingEntryTypes.find((i) => i.value == item.accountingEntry.accountingEntryType)?.label,
  },
  {
    title: 'Cumac classic',
    value: 'classicCumac',
    formater: (_, value) => formatKwhcNumber(value),
  },
  {
    title: 'Cumac précarité',
    value: 'precariousnessCumac',
    formater: (_, value) => formatKwhcNumber(value),
  },
]

const {
  data: accountingEntries,
  pageable: accountingEntriesPageable,
  updatePageable: updatePageableAccountingEntries,
  pageFilter: pageFilterAccountingEntries,
  updateFilterByFieldname: updateFilterByFieldnameAccountingEntries,
} = usePagination<AccountingEntryOperationDto, AccountingEntryFilter>(
  (filter, pageable) => {
    return accountingEntryApi.getAll(pageable, filter)
  },
  {},
  {
    sort: ['accountingEntryOperationId.accountingEntry.creationDate,DESC'],
  },
  {
    lazyLoad: true,
  }
)

const filterDrawer = ref(false)
const selectedBeneficiaries = ref<Beneficiary[]>([])
const selectedOperationsGroup = ref<OperationsGroup[]>([])
const selectedInstructors = ref<User[]>([])
const selectedEmmyFolders = ref<EmmyFolder[]>([])
const { activeValuationTypes } = useValuationTypesStore()

const updateCommercialStatuses = (status: OperationStatus) => {
  if (pageFilter.value.operationStatuses!.includes(status)) {
    pageFilter.value.operationStatuses!.splice(
      pageFilter.value.operationStatuses!.findIndex((id) => id === status),
      1
    )
  } else {
    pageFilter.value.operationStatuses!.push(status)
    pageFilter.value.operationStatuses!.sort()
  }
}

watch(selectedInstructors, (v) => {
  if (v && v.length > 0) {
    pageFilter.value.instructorIds = v.map((b) => b.id)
  } else {
    pageFilter.value.instructorIds = []
  }
})

watch(selectedBeneficiaries, (v) => {
  if (v && v.length > 0) {
    pageFilter.value.beneficiaryIds = v.map((b) => b.id)
  } else {
    pageFilter.value.beneficiaryIds = []
  }
})

watch(selectedOperationsGroup, (v) => {
  if (v && v.length > 0) {
    pageFilter.value.operationsGroupIds = v.map((b) => b.id)
  } else {
    pageFilter.value.operationsGroupIds = []
  }
})

watch(selectedEmmyFolders, (v) => {
  if (v && v.length > 0) {
    pageFilter.value.emmyFolderIds = v.map((b) => b.id)
  } else {
    pageFilter.value.emmyFolderIds = []
  }
})
const stepsStore = useStepsStore()

const updateStepIds = (stepId: number) => {
  if (pageFilter.value.stepIds && pageFilter.value.stepIds.includes(stepId)) {
    pageFilter.value.stepIds!.splice(
      pageFilter.value.stepIds!.findIndex((id) => id === stepId),
      1
    )
  } else if (pageFilter.value.stepIds) {
    pageFilter.value.stepIds.push(stepId)
    pageFilter.value.stepIds.sort()
  } else {
    pageFilter.value.stepIds = [stepId]
  }
}

defineExpose({
  ceeStockOperationFilter: pageFilter,
})
</script>
