<template>
  <CardDialog
    :model-value="showConfirmAddOperationDialog.active"
    :title="
      showConfirmAddOperationDialog.operation.operationName +
      '-' +
      showConfirmAddOperationDialog.operation.standardizedOperationSheet.operationCode +
      ' - ' +
      showConfirmAddOperationDialog.operation.chronoCode
    "
    max-width="640px"
    @update:model-value="
      emit('update:showConfirmAddOperationDialog', { ...showConfirmAddOperationDialog, active: false })
    "
  >
    <VRow class="flex-column mb-n1" dense>
      <VCol>
        <VAlert type="info" variant="tonal" class="rounded-0 border-1-primary">
          Si vous rencontrez des erreurs dans les kWhc obtenus/demandés les valeurs sont à corriger directement dans la
          fiche de calcul.
        </VAlert>
      </VCol>
      <VCol>
        <NjDisplayValue
          label="Offre commerciale"
          :value="
            commercialStatusTitle.find(
              (status) => status.value === showConfirmAddOperationDialog.operation.commercialStatus
            )?.title
          "
        />
      </VCol>
      <VDivider class="mx-1" />
      <VCol>
        <DetailValuationDisplayValue
          v-slot="slotProps"
          :operation="showConfirmAddOperationDialog.operation"
          simulate-step60
        >
          <VRow class="flex-column" dense>
            <VCol>
              <NjExpansionPanel title="CEE Classique en kWhc">
                <VRow class="flex-column" dense>
                  <VCol>
                    <NjDisplayValue
                      label="kWhc"
                      :value="
                        'Réservés: ' +
                        formatNumber(showConfirmAddOperationDialog.operation.reservedClassicCumac) +
                        ' Obtenus/Demandés: ' +
                        formatNumber(showConfirmAddOperationDialog.operation.classicCumac)
                      "
                    />
                  </VCol>
                  <VCol>
                    <NjDisplayValue
                      label="Valorisation en €/kWhc"
                      :value="
                        'Réservés: ' +
                        formatNumber(showConfirmAddOperationDialog.operation.reservedClassicValuationValue ?? 0) +
                        ' Obtenus/Demandés: ' +
                        formatNumber(slotProps.classicValuation.value ?? 0)
                      "
                    />
                  </VCol>
                  <VCol>
                    <NjDisplayValue
                      label="Montant en €"
                      :value="
                        'Réservés: ' +
                        formatNumber(
                          ((showConfirmAddOperationDialog.operation.reservedClassicValuationValue ?? 0) *
                            showConfirmAddOperationDialog.operation.reservedClassicCumac) /
                            1000
                        ) +
                        ' Obtenus/Demandés: ' +
                        formatNumber(slotProps.classicValuation.total ?? 0)
                      "
                    />
                  </VCol>
                </VRow>
              </NjExpansionPanel>
            </VCol>
            <VDivider class="mx-1" />
            <VCol>
              <NjExpansionPanel title="CEE Précarité en kWhc">
                <VRow class="flex-column" dense>
                  <VCol>
                    <NjDisplayValue
                      label="kWhc"
                      :value="
                        'Réservés: ' +
                        formatNumber(showConfirmAddOperationDialog.operation.reservedPrecariousnessCumac ?? 0) +
                        ' Obtenus/Demandés: ' +
                        formatNumber(showConfirmAddOperationDialog.operation.precariousnessCumac ?? 0)
                      "
                    />
                  </VCol>
                  <VCol>
                    <NjDisplayValue
                      label="Valorisation en €/kWhc"
                      :value="
                        'Réservés: ' +
                        formatNumber(
                          showConfirmAddOperationDialog.operation.reservedPrecariousnessValuationValue ?? 0
                        ) +
                        ' Obtenus/Demandés: ' +
                        formatNumber(slotProps.precariousnessValuation.value ?? 0)
                      "
                    />
                  </VCol>
                  <VCol>
                    <NjDisplayValue
                      label="Montant en €"
                      :value="
                        'Réservés: ' +
                        formatNumber(
                          ((showConfirmAddOperationDialog.operation.reservedPrecariousnessValuationValue ?? 0) *
                            showConfirmAddOperationDialog.operation.reservedPrecariousnessCumac) /
                            1000
                        ) +
                        ' Obtenus/Demandés: ' +
                        formatNumber(slotProps.precariousnessValuation.total ?? 0)
                      "
                    />
                  </VCol>
                </VRow>
              </NjExpansionPanel>
            </VCol>
          </VRow>
        </DetailValuationDisplayValue>
      </VCol>
    </VRow>

    <template #actions>
      <NjBtn
        variant="outlined"
        @click="emit('update:showConfirmAddOperationDialog', { ...showConfirmAddOperationDialog, active: false })"
        >Précédent</NjBtn
      >
      <NjBtn :to="{ name: 'OperationOneView', params: { id: showConfirmAddOperationDialog.operation.id } }"
        >Voir l'opération</NjBtn
      >
      <NjBtn @click="addOperation">Ajouter</NjBtn>
    </template>
  </CardDialog>
</template>
<script lang="ts" setup>
import type { Operation } from '@/types/operation'
import type { PropType } from 'vue'
import { commercialStatusTitle } from '@/types/steps'
import { formatNumber } from '@/types/format'
import DetailValuationDisplayValue from '../operation/DetailValuationDisplayValue.vue'

defineProps({
  showConfirmAddOperationDialog: {
    type: Object as PropType<{
      active: boolean
      operation: Operation
    }>,
    required: true,
  },
  addOperation: Function as PropType<() => void>,
})

const emit = defineEmits<{
  'update:showConfirmAddOperationDialog': [
    value: {
      active: boolean
      operation: Operation
    },
  ]
}>()
</script>
