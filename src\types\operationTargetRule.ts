import type { LocalDate } from './date'

export interface OperationTargetRule {
  operationCodeTarget: string
  target: string
  exclusion: string
  bonusType: BonusType | null
  minSignedDate?: LocalDate
  maxSignedDate?: LocalDate
}

export const makeEmptyOperationTargetRule = (): OperationTargetRule => ({
  operationCodeTarget: '',
  target: ';;',
  exclusion: ';;',
  bonusType: null,
})

export const bonusTypeLabel: {
  value: string
  label: string
}[] = [
  {
    label: 'CPE',
    value: 'EPC',
  },
  {
    label: 'Coup de pouce',
    value: 'BOOST',
  },
  {
    label: 'Précarité',
    value: 'PRECARIOUSNESS',
  },
]

export type BonusType = (typeof bonusTypeLabel)[number]['value']
