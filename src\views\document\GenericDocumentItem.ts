import type {
  ControlOrderBatchDocumentRequest,
  EmmyFolderDocumentRequest,
  OperationDocumentRequest,
  OperationsGroupDocumentRequest,
} from '@/types/document'

export type EnhancedDocumentRequest =
  | OperationDocumentRequest
  | OperationsGroupDocumentRequest
  | EmmyFolderDocumentRequest
  | ControlOrderBatchDocumentRequest

export default interface GenericSubmitDocumentItem {
  file: File
  localId: number
  error?: string
  loading?: true
  success?: true
  document: EnhancedDocumentRequest
}
