import type { Step } from '@/types/steps'
import type { AxiosInstance, AxiosPromise } from 'axios'
import axiosInstance from '.'

const stepUri = '/steps'
class StepsApi {
  public constructor(private axios: AxiosInstance) {}

  public getAll(): AxiosPromise<Step[]> {
    return this.axios.get(stepUri)
  }

  public getById(id: number): AxiosPromise<Step> {
    return this.axios.get(stepUri + '/' + id)
  }

  public update(id: number, step: Step): AxiosPromise<void> {
    return this.axios.put(stepUri + '/' + id, step)
  }
}

export const stepsApi = new StepsApi(axiosInstance)
