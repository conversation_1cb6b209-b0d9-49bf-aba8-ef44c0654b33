import type { Document } from '../document'

export interface ConventionTemplate {
  id: number
  name: string
  type: ConventionType
  document: Document | null
}

export type ConventionType = 'SINGLE_OPERATION' | null
export const conventionTypeValues = [
  'SINGLE_OPERATION',
  'OPERATION_GROUP_SINGLE_PROPERTY',
  'OPERATION_GROUP_SINGLE_OPERATION_SHEET',
  'OPERATION_GROUP_MULTIPLE_PROPERTY_MULTIPLE_OPERATION_SHEET',
  'SINGLE_OPERATION_TRIPARTITE',
  'OPERATION_GROUP_SINGLE_PROPERTY_TRIPARTITE',
  'OPERATION_GROUP_SINGLE_OPERATION_SHEET_TRIPARTITE',
  'OPERATION_GROUP_MULTIPLE_PROPERTY_MULTIPLE_OPERATION_SHEET_TRIPARTITE',
  null,
]

export const conventionTypeValuesLabels = [
  {
    type: 'SINGLE_OPERATION',
    label: 'Simple opération',
  },
  {
    type: 'OPERATION_GROUP_SINGLE_PROPERTY',
    label: 'Regroupement une seul installation',
  },
  {
    type: 'OPERATION_GROUP_SINGLE_OPERATION_SHEET',
    label: "Regroupement un seul type d'opération",
  },
  {
    type: 'OPERATION_GROUP_MULTIPLE_PROPERTY_MULTIPLE_OPERATION_SHEET',
    label: "Regroupement plusieurs installations et types d'opération",
  },
  {
    type: 'SINGLE_OPERATION_TRIPARTITE',
    label: 'Simple opération et tripartite',
  },
  {
    type: 'OPERATION_GROUP_SINGLE_PROPERTY_TRIPARTITE',
    label: 'Regroupement une seule installation et tripartite',
  },
  {
    type: 'OPERATION_GROUP_SINGLE_OPERATION_SHEET_TRIPARTITE',
    label: "Regroupement un seul type d'opération et tripartite",
  },
  {
    type: 'OPERATION_GROUP_MULTIPLE_PROPERTY_MULTIPLE_OPERATION_SHEET_TRIPARTITE',
    label: "Regroupement plusieurs installations et types d'opération et tripartite",
  },
]

export interface ConventionTemplateRequest extends Omit<ConventionTemplate, 'id' | 'present' | 'document'> {
  documentId?: number
}

export const makeEmptyConventionTemplate = (): ConventionTemplate => ({
  id: 0,
  name: '',
  type: null,
  document: null,
})

export const mapToConventionTemplateRequest = (template: ConventionTemplate): ConventionTemplateRequest => ({
  name: template.name,
  type: template.type,
  documentId: template.document?.id,
})
