<template>
  <VInput :model-value="modelValue" :rules="rules" hide-details="auto">
    <VCard v-if="modelValue" class="w-100">
      <VCardText class="pa-2">
        <VRow class="flex-column" no-gutters>
          <VCol>
            <NjDisplayValue label="Utilisateur" :value="displayFullnameUser(modelValue!)" />
          </VCol>
          <VCol>
            <NjDisplayValue label="GID" :value="modelValue.gid" />
          </VCol>
          <VCol>
            <NjDisplayValue label="Email" :value="modelValue.email" />
          </VCol>
        </VRow>
      </VCardText>
    </VCard>
    <div v-else-if="noData">
      <i>{{ noData }}</i>
    </div>
  </VInput>
</template>
<script lang="ts" setup>
import type { ValidationRule } from '@/types/rule'
import { displayFullnameUser, type User } from '@/types/user'
import type { PropType } from 'vue'

defineProps({
  modelValue: {
    type: Object as PropType<User | null>,
  },
  rules: {
    type: Array as PropType<Array<ValidationRule>>,
  },
  noData: {
    type: String,
    default: '',
  },
})
</script>
