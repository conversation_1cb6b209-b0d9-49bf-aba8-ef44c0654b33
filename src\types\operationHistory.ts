import { useSnackbarStore } from '@/stores/snackbar'
import { useStepsStore } from '@/stores/steps'
import type { LocalDateTime } from './date'
import type { OperationStepHistory } from './operation'
import type { User } from './user'

export const useOperationHistory = (
  props: Ref<{ stepId: number; id: number; creationDateTime?: LocalDateTime; creationUser?: User }>
) => {
  const stepsStore = useStepsStore()
  const snackbarStore = useSnackbarStore()

  const operationStepHistoryList = ref(emptyValue<OperationStepHistory[]>())

  watch(
    [() => props.value.id, () => props.value.stepId],
    async ([id]) => {
      if (id) {
        await handleAxiosPromise(operationStepHistoryList, operationApi.getHistory(id), {
          afterError: () =>
            snackbarStore.setError(
              operationStepHistoryList.value.error ?? "Une erreur est survenue lors de la récupération de l'historique"
            ),
        })
        operationStepHistoryList.value.value?.sort((hist1, hist2) =>
          new Date(hist1.creationDateTime) < new Date(hist2.creationDateTime)
            ? 1
            : new Date(hist1.creationDateTime) > new Date(hist2.creationDateTime)
              ? -1
              : 0
        )
      }
    },
    {
      immediate: true,
    }
  )

  const history = computed(() => {
    if (operationStepHistoryList.value.value) {
      let step = 10
      const history: Record<number, OperationStepHistory | undefined> = {}
      while (step < props.value.stepId) {
        history[step] = computeStepHistory(step)
        step += 10
      }
      return history
    }
    return {}
  })

  const computeStepHistory = (stepId: number): OperationStepHistory | undefined => {
    let step = stepId
    while (step > 0) {
      const possibleHistory = operationStepHistoryList.value.value!.filter((histo) => histo.step.id === step) ?? []
      if (possibleHistory.length !== 0) {
        return possibleHistory[0]
      }
      step -= 10
    }
    return undefined
  }

  const steps = ref([10, 20, 30, 40, 50, 60, 70, 80, 90, 100, 110])

  return { history, stepsStore, operationStepHistoryList, steps }
}
