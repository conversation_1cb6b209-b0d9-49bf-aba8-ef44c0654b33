<template>
  <VRow class="flex-column">
    <VCol v-if="mode === 'nextStep' && missingDocuments.length">
      <VRow v-for="(document, index) in missingDocuments" :key="index" dense>
        <VCol v-if="isFileDragging">
          <VCard
            variant="outlined"
            rounded="1"
            color="info"
            class="drop-file-zone"
            :class="{ 'is-dragging-over': fileOverSomeDocumentTypeId === document.id }"
            @dragenter.prevent
            @dragover.prevent="updateFileTypeOver(document.id)"
            @dragleave.prevent="updateFileTypeOver(undefined)"
            @drop.prevent="onDropOnDocumentType(document, $event)"
          >
            Déposer ici un document "{{ document.name }}"
          </VCard>
        </VCol>
        <template v-else>
          <SubmitDocumentHelper :operations-group-id="props.operationsGroupId" :missing-document-types="document" />
        </template>
      </VRow>
    </VCol>
    <VCol>
      <NjFileInput v-show="!hideInputFile" multiple @new-files="onNewFileFromNjFileInput" />
    </VCol>
    <VCol v-if="mode === 'default' && missingDocuments.length">
      <ErrorAlert
        type="info"
        :message="'Documents manquants pour valider l\'étape :\n' + missingDocuments.map((i) => i.name).join(' - ')"
      >
      </ErrorAlert>
    </VCol>
    <VCol v-show="!!documentItems.length">
      <VForm ref="formRef" class="w-100">
        <SubmittableList
          :operation-id="operationId"
          :operations-group-id="operationsGroupId"
          :control-order-batch-id="controlOrderBatchId"
          :emmy-folder-id="emmyFolderId"
          :requests="documentItems"
          :missing-documents="remainingDocumentTypes"
          @remove="remove"
          @update-description="documentItems[$event.index].document.description = $event.value"
          @update-document-type-id="documentItems[$event.index].document.documentTypeId = $event.value"
        />
      </VForm>
    </VCol>
  </VRow>
</template>
<script setup lang="ts">
import ErrorAlert from '@/components/ErrorAlert.vue'
import NjFileInput from '@/components/NjFileInput.vue'
import {
  makeEmptyOperationDocumentRequest,
  type ControlOrderBatchDocumentRequest,
  type EmmyFolderDocumentRequest,
  type EnhancedDocument,
  type OperationDocumentRequest,
  type OperationsGroupDocumentRequest,
} from '@/types/document'
import type { DocumentType } from '@/types/documentType'
import SubmitDocumentHelper from '@/views/document/SubmitDocumentHelper.vue'
import SubmittableList from '@/views/document/SubmittableList.vue'
import { useEventListener } from '@vueuse/core'
import type { AxiosPromise } from 'axios'
import { debounce } from 'lodash'
import type { PropType } from 'vue'
import type { VForm } from 'vuetify/components'
import { missingDocumentTypeIdsKey } from '../dashboard/keys'
import type GenericSubmitDocumentItem from './GenericDocumentItem'
import type SendDocumentResult from './sendDocumentResult'

const props = defineProps({
  hideInputFile: Boolean,

  operationId: Number,
  operationsGroupId: Number,
  emmyFolderId: Number,
  controlOrderBatchId: Number,
  mode: {
    type: String as PropType<'default' | 'nextStep'>,
    default: 'default',
  },
})

const sentDocumentTypes = ref<DocumentType[]>([])
const { missingDocumentTypes: missingDocuments } = inject(missingDocumentTypeIdsKey, {
  missingDocumentTypes: ref([]),
  reload: () => {
    return Promise.resolve([])
  },
})
const remainingDocumentTypes = computed(() => {
  return missingDocuments.value.filter(
    (d) =>
      !sentDocumentTypes.value.some((s) => s.id === d.id) &&
      !documentItems.value.some((i) => i.document.documentTypeId === d.id)
  )
})

const documentItems = ref<GenericSubmitDocumentItem[]>([])

const remove = (index: number) => {
  documentItems.value.splice(index, 1)
}

// Drag&Drop
const isFileDragging = ref(false)

useEventListener(document, 'dragover', (e) => {
  if (!isFileDragging.value) {
    const dt = e.dataTransfer
    if (dt?.types && (dt.types.indexOf ? dt.types.indexOf('Files') != -1 : dt.types.includes('Files'))) {
      isFileDragging.value = true
    }
  }
})

useEventListener(document, 'dragleave', () => {
  if (isFileDragging.value) {
    isFileDragging.value = false
  }
})

useEventListener(document, 'drop', () => {
  if (isFileDragging.value) {
    isFileDragging.value = false
  }
})

const onDropOnDocumentType = (d: DocumentType, e: DragEvent) => {
  updateFileTypeOver(undefined)
  if (!e.dataTransfer?.files.length) {
    return
  }
  addFiles(d, e.dataTransfer?.files)
}

const fileLocalIdIncrement = ref(1)
const addFiles = (d: DocumentType | undefined, fileList: FileList) => {
  // if (!addFileDialog.value) {
  // files.value = []
  // requests.value = []
  // responses.value = []
  // }

  const callback = createDocumentRequest(d)

  documentItems.value.push(
    ...Array.from(fileList).map(
      (f): GenericSubmitDocumentItem => ({
        file: f,
        localId: fileLocalIdIncrement.value++,
        document: callback(f),
      })
    )
  )
}

const createDocumentRequest = (d: DocumentType | undefined) => (file: File) => {
  return props.operationId
    ? {
        ...makeEmptyOperationDocumentRequest(),
        operationId: props.operationId,
        documentTypeId: d?.id,
        description: file.name,
      }
    : props.operationsGroupId
      ? {
          ...makeEmptyOperationsGroupDocumentRequest(),
          operationsGroupId: props.operationsGroupId,
          documentTypeId: d?.id,
          description: file.name,
        }
      : props.emmyFolderId
        ? ({
            ...makeEmptyEmmyFolderDocumentRequest(),
            emmyFolderId: props.emmyFolderId,
            documentTypeId: d?.id,
            description: file.name,
          } as EmmyFolderDocumentRequest)
        : ({
            ...makeEmptyControlOrderBatchDocumentRequest(),
            controlOrderBatchId: props.controlOrderBatchId,
            documentTypeId: d?.id,
            description: file.name,
          } as ControlOrderBatchDocumentRequest)
}

const fileOverSomeDocumentTypeId = ref<number>()
const updateFileTypeOver = debounce((v: number | undefined) => {
  if (v !== fileOverSomeDocumentTypeId.value) {
    fileOverSomeDocumentTypeId.value = v
  }
})

const onNewFileFromNjFileInput = (files: FileList) => {
  addFiles(undefined, files)
}

const formRef = useTemplateRef('formRef')
const uploadFiles: () => Promise<false | Array<SendDocumentResult>> = async () => {
  if ((await formRef.value?.validate())?.valid) {
    const uploadPromises = documentItems.value.map(async (r): Promise<SendDocumentResult> => {
      const file = r.file
      if (r.success) {
        return Promise.resolve({
          type: 'success',
          msg: r.file.name + ' a déjà été téléchargé',
        })
      } else {
        r.loading = true
        const promise = makeUploadPromise(r.document, file)
        if (promise) {
          return promise
            .then((res): SendDocumentResult => {
              r.success = true
              return {
                type: 'success',
                msg: res.data.document.originalFilename + ' a bien été téléchargé',
                enhancedDocument: res.data,
              }
            })
            .catch(async (err): Promise<SendDocumentResult> => {
              const msg = await handleAxiosException(err)
              r.error = msg
              return {
                type: 'error',
                msg,
              }
            })
            .finally(() => {
              r.loading = undefined
            })
        } else {
          return Promise.resolve({
            type: 'error',
            msg: `Impossible de trouver le bon endpoint pour le téléchargement du document ${file.name}`,
          })
        }
      }
    })

    let responses: SendDocumentResult[] = []
    try {
      responses = (await Promise.allSettled(uploadPromises)).map((it) =>
        it.status === 'fulfilled'
          ? it.value
          : {
              type: 'error',
              msg: 'Une erreur est survenue lors du téléchargement du document',
            }
      )
    } finally {
      return responses
    }
  } else {
    return false
  }
}

const makeUploadPromise = (
  r:
    | OperationDocumentRequest
    | OperationsGroupDocumentRequest
    | EmmyFolderDocumentRequest
    | ControlOrderBatchDocumentRequest,
  file: File
): AxiosPromise<EnhancedDocument> | null => {
  if ((r as OperationDocumentRequest)?.operationId) {
    return operationDocumentApi.uploadFile(r as OperationDocumentRequest, file)
  } else if ((r as OperationsGroupDocumentRequest)?.operationsGroupId) {
    return operationsGroupDocumentApi.uploadFile(r as OperationsGroupDocumentRequest, file)
  } else if ((r as EmmyFolderDocumentRequest)?.emmyFolderId) {
    return emmyFolderDocumentApi.uploadFile(r as EmmyFolderDocumentRequest, file)
  } else if ((r as ControlOrderBatchDocumentRequest)?.controlOrderBatchId) {
    return controlOrderBatchDocumentApi.uploadFile(r as ControlOrderBatchDocumentRequest, file)
  }
  return null
}

defineExpose({
  uploadFiles,
  reset: () => {
    documentItems.value = []
    fileLocalIdIncrement.value = 1
  },
})
</script>

<style scoped>
.drop-file-zone {
  border: 1px dashed #007acd;
  background-color: #e7eefc;
  padding: 4px;
  transition: all 0.2s linear;
}

.is-dragging-over {
  scale: 1.02;
  background-color: white !important;
}
</style>
