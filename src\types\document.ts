import type { ControlOrderBatch } from './controlOrder'
import type { LocalDateTime } from './date'
import type { DocumentType } from './documentType'
import type { EmmyFolder } from './emmyFolder'
import type { Operation } from './operation'
import type { OperationsGroup } from './operationsGroup'
import type { User } from './user'

export interface Document {
  id: number
  creationUser: User
  creationDateTime: LocalDateTime
  originalFilename: string
  mediaType: string
  emediaErrors?: string
  emediaId?: string
}

export const makeEmptyDocument = (): Document => ({
  id: 0,
  creationUser: makeEmptyUser(),
  creationDateTime: '',
  originalFilename: '',
  mediaType: '',
})

export interface EnhancedDocument {
  id: number
  documentType: DocumentType
  document: Document
  description: string
  active: boolean
  operation?: Operation
  operationGroup?: OperationsGroup
  emmyFolder?: EmmyFolder
  controlOrderBatch?: ControlOrderBatch
}

export interface OperationDocumentRequest extends Omit<EnhancedDocument, 'id' | 'documentType' | 'document'> {
  documentTypeId?: number
  operationId: number
}

export const makeEmptyOperationDocumentRequest = (): OperationDocumentRequest => ({
  operationId: 0,
  documentTypeId: undefined,
  description: '',
  active: true,
})

export interface OperationsGroupDocumentRequest extends Omit<EnhancedDocument, 'id' | 'document' | 'documentType'> {
  documentTypeId?: number
  operationsGroupId: number
}

export const makeEmptyOperationsGroupDocumentRequest = (): OperationsGroupDocumentRequest => ({
  operationsGroupId: 0,
  documentTypeId: undefined,
  description: '',
  active: true,
})
export interface EmmyFolderDocumentRequest extends Omit<EnhancedDocument, 'id' | 'documentType' | 'document'> {
  documentTypeId?: number
  emmyFolderId: number
}

export const makeEmptyEmmyFolderDocumentRequest = (): EmmyFolderDocumentRequest => ({
  emmyFolderId: 0,
  documentTypeId: undefined,
  description: '',
  active: true,
})

export interface ControlOrderBatchDocumentRequest extends Omit<EnhancedDocument, 'id' | 'documentType' | 'document'> {
  documentTypeId?: number
  controlOrderBatchId: number
}

export const makeEmptyControlOrderBatchDocumentRequest = (): ControlOrderBatchDocumentRequest => ({
  controlOrderBatchId: 0,
  documentTypeId: undefined,
  description: '',
  active: true,
})

export interface DocumentLocation {
  documentType: DocumentType
  location: 'OPERATIONS_GROUP' | 'OPERATION'
}

export interface UsedDocument {
  operationDocumentId?: number
  operationGroupDocumentId?: number
  documentType: DocumentType
  description: string
  document: Document
  active: boolean
}

export interface MergeDocumentToPdfRequest {
  operationDocumentIds: number[]
  operationsGroupDocumentIds: number[]
}
