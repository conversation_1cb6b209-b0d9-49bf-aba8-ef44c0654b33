import type { AxiosInstance, AxiosPromise } from 'axios'
import axiosInstance from '.'
import type { Page, Pageable } from '@/types/pagination'
import type { CeeStockMonthly } from '@/types/ceeStockMonthly'
import type { LocalDate } from '@/types/date'
export interface CeeStockMonthlyFilter {
  startDate?: LocalDate
  endDate?: LocalDate
  entityIds?: string[]
}
export class CeeStockMonthlyApi {
  public constructor(private axios: AxiosInstance) {}

  public findAll(pageable: Pageable, filter: CeeStockMonthlyFilter): AxiosPromise<Page<CeeStockMonthly>> {
    return this.axios.get(`/entities/cee_stock_monthlies`, {
      params: { ...pageable, ...filter },
    })
  }
}

export const ceeStockMonthlyApi = new CeeStockMonthlyApi(axiosInstance)
