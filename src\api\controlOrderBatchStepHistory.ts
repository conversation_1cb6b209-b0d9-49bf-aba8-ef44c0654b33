import type { AxiosInstance, AxiosPromise } from 'axios'
import axiosInstance from '.'
import type { ControlOrderBatchStepHistory } from '@/types/controlOrderBatchStepHistory'

const controlOrderBatchUri = 'control_order_batches'
class ControlOrderBatchStepHistoryApi {
  public constructor(private axios: AxiosInstance) {}

  public findAllByControlOrderBatchId(controlOrderBatchId: number): AxiosPromise<ControlOrderBatchStepHistory[]> {
    return this.axios.get(controlOrderBatchUri + '/' + controlOrderBatchId + '/step_histories')
  }
}
export const controlOrderBatchStepHistoryApi = new ControlOrderBatchStepHistoryApi(axiosInstance)
