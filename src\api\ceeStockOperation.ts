import type { AxiosInstance, AxiosPromise } from 'axios'
import axiosInstance from '.'
import type { Page, Pageable } from '@/types/pagination'
import type { LocalDate } from '@/types/date'
import type { CeeStockEntryDto, CeeStockOperationWithAccountingEntrySummary } from '@/types/ceeStockEntry'
import type { OperationStatus } from '@/types/operation'
import type { CommercialStatus } from '@/types/steps'

export type CeeStockOperationFilter = Partial<{
  search: string
  entityIds: string[]
  startDate: LocalDate
  endDate: LocalDate
  withEmmyFolder: boolean | null
  withCorrections: boolean | null
  stepIds: number[]
  periodIds: number[]
  valuationTypeIds: number[]
  operationStatuses: OperationStatus[]
  commercialStatuses: CommercialStatus[]
  beneficiaryIds: number[]
  instructorIds: number[]
  operationsGroupIds: number[]
  territoryIds: number[]
  standardizedOperationSheetIds: number[]
  emmyFolderIds: number[]
}>
export class CeeStockOperationApi {
  public constructor(private axios: AxiosInstance) {}

  public findAll(pageable: Pageable, filter: CeeStockOperationFilter): AxiosPromise<Page<CeeStockEntryDto>> {
    return this.axios.get(`/cee_stocks/operations`, {
      params: { ...pageable, ...filter },
    })
  }

  public export(filter: CeeStockOperationFilter) {
    return this.axios.get('/cee_stocks/operations/export', {
      responseType: 'blob',
      params: { ...filter },
    })
  }

  public findAllWithAccountingEntrySummary(
    pageable: Pageable,
    filter: CeeStockOperationFilter
  ): AxiosPromise<Page<CeeStockOperationWithAccountingEntrySummary>> {
    return this.axios.get(`/cee_stocks/operations_with_accounting_entry_summary`, {
      params: { ...pageable, ...filter },
    })
  }

  public exportWithAccountingEntrySummary(filter: CeeStockOperationFilter) {
    return this.axios.get('/cee_stocks/operations_with_accounting_entry_summary/export', {
      responseType: 'blob',
      params: { ...filter },
    })
  }
}

export const ceeStockOperationApi = new CeeStockOperationApi(axiosInstance)
