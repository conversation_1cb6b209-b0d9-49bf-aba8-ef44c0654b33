import type { AxiosInstance, AxiosPromise } from 'axios'
import type { Page, Pageable } from '@/types/pagination'
import type { ConventionTemplate, ConventionTemplateRequest } from '@/types/convention/conventionTemplate'
import axiosInstance from '.'

const conventionTemplateUri = '/convention_templates'

class ConventionTemplateApi {
  public constructor(private axios: AxiosInstance) {}

  public findAll(pageable: Pageable): AxiosPromise<Page<ConventionTemplate>> {
    return this.axios.get(conventionTemplateUri, {
      params: pageable,
    })
  }

  public findOne(id: number) {
    return this.axios.get(conventionTemplateUri + '/' + id)
  }

  public async create(request: ConventionTemplateRequest, file?: File) {
    const formData = new FormData()
    if (file) {
      formData.append('file', file, file.name)
      const hash = await hashFile(file)
      formData.append('fileHash', hash)
    }

    const jsonRequest = JSON.stringify(engieFormatRequestTransformKey(request))
    const blob = new Blob([jsonRequest], {
      type: 'application/json',
    })
    formData.append('request', blob)

    return this.axios.post(conventionTemplateUri, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    })
  }

  public async update(id: number, request: ConventionTemplateRequest, file?: File) {
    const formData = new FormData()
    if (file) {
      formData.append('file', file, file.name)
      const hash = await hashFile(file)
      formData.append('fileHash', hash)
    }

    const jsonRequest = JSON.stringify(engieFormatRequestTransformKey(request))
    const blob = new Blob([jsonRequest], {
      type: 'application/json',
    })
    formData.append('request', blob)

    return this.axios.put(conventionTemplateUri + '/' + id, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    })
  }

  public preview(id: number): AxiosPromise<any> {
    return this.axios.post(conventionTemplateUri + '/' + id + '/preview', null, {
      responseType: 'blob',
    })
  }

  public download(id: number) {
    return this.axios.get(conventionTemplateUri + '/' + id + '/file', {
      responseType: 'blob',
    })
  }
}

export const conventionTemplateApi = new ConventionTemplateApi(axiosInstance)
