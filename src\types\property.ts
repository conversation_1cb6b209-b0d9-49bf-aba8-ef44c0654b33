import type { GtfGdePropertyDto } from '@/api/external/gtf/gtfGde'
import type { AddressWithNumber } from './address'
import type { LocalDateTime } from './date'
import type { Entity } from './entity'

export interface Property extends AddressWithNumber {
  id: number
  code: string
  entity?: Entity
  name: string
  buildingName?: string
  enabled: boolean
  updateDateTime?: LocalDateTime
  latitude?: number | null
  longitude?: number | null
  teamEntityId: string | null
  teamEntityNavFullId: string | null
}

export const makeEmptyProperty = (): Property => ({
  id: 0,
  code: '',
  name: '',
  buildingName: '',
  streetNumber: undefined,
  streetName: '',
  postalCode: '',
  city: '',
  country: '',
  enabled: true,
  updateDateTime: '',
  teamEntityId: null,
  teamEntityNavFullId: null,
})

export const emptyProperty = makeEmptyProperty()

const formatPostalCode = (v?: string) => {
  if (v) {
    if (v.toString().length == 4) {
      return '0' + v.toString()
    }
    return v.toString()
  }
  return ''
}

export const mapGtfGdePropertyDtoToProperty = (dto: GtfGdePropertyDto, entity?: Entity): Property => {
  return {
    buildingName: dto.building,
    city: dto.city,
    code: dto.code,
    id: dto.id,
    country: dto.country,
    enabled: true,
    name: dto.name,
    entity: entity ?? {
      ...makeEmptyEntity(),
      id: dto.parent_entity_id,
    },
    postalCode: formatPostalCode(dto.postal_code),
    streetName: dto.road_name,
    streetNumber: dto.road_number,
    latitude: dto.latitude,
    longitude: dto.longitude,
    additionalPostalAddress: dto.additional_address,
    teamEntityId: dto.parent_entity_id,
    teamEntityNavFullId: dto.parent_entity_nav_full_id,
  }
}
