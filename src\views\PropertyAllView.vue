<template>
  <VRow class="flex-column h-100">
    <VCol v-if="data.error" class="d-flex align-center">
      <ErrorAlert :message="data.error" />
    </VCol>
    <!-- <VCol class="d-flex align-center" v-if="data.loading">
      <VProgressCircular class="ms-4" indeterminate />
    </VCol> -->
    <VCol>
      <NjDataTable
        :headers="headers"
        :selections="props.selections"
        :pageable="pageable"
        :page="data.value!"
        :disabled-row="(item) => !item.entity || !item.entity.entityDetails.visible"
        fixed
        hide-total
        @update:selections="updateModelValue"
        @update:pageable="updatePageable"
      >
      </NjDataTable>
    </VCol>
  </VRow>
</template>
<script setup lang="ts">
import { gtfGdeApi, type GtfGdePropertyDto, type GtfGdePropertyFilter } from '@/api/external/gtf/gtfGde'
import { mapAxiosResponse } from '@/api/external/gtf/type'
import type { DataTableHeader } from '@/components/okta/NjDataTable.type'
import type { Property } from '@/types/property'
import { type Entity } from '@/types/entity'
import { usePagination, type Page, type Pageable } from '@/types/pagination'
import type { AxiosResponse } from 'axios'
import { debounce, isEqual, uniq } from 'lodash'
import type { PropType } from 'vue'

const props = defineProps({
  selections: {
    type: Object as PropType<Property[]>,
  },
  search: {
    type: String,
  },
  navFullId: {
    type: Array as PropType<string[]>,
  },
})

const emit = defineEmits(['update:selections', 'update:loading'])
const updateModelValue = (value: Property[]) => {
  emit('update:selections', value)
}

const convertNameHeader: Partial<Record<keyof Property, keyof GtfGdePropertyDto>> = {
  streetName: 'road_name',
  postalCode: 'postal_code',
  city: 'city',
}
const mapSortName = (pageable: Pageable): Pageable => ({
  ...pageable,
  sort: pageable.sort?.map((it) => {
    const a = it.split(',') as [keyof Property, 'ASC' | 'DESC']
    return [convertNameHeader[a[0]], a[1]].filter((it) => it).join(',')
  }),
})

const { data, pageable, pageFilter, updatePageable, updateFilter } = usePagination<Property, GtfGdePropertyFilter>(
  (filter, pageable) => {
    if ((filter.parentEntityIds?.length ?? 0) === 0) {
      return Promise.resolve({
        data: makeEmptyPage(),
      } as AxiosResponse<Page<Property>>)
    } else {
      return gtfGdeApi
        .getAllProperties(filter, mapSortName(pageable))
        .then(mapAxiosResponse)
        .then(async (response): Promise<AxiosResponse<Page<Property>>> => {
          const entities = await entityApi.getAll(
            {
              ids: uniq(response.data.content.map((i) => i.parent_entity_nav_full_id.substring(12, 15))),
            },
            {
              ...pageable,
              page: 0,
            }
          )

          return {
            ...response,
            data: {
              ...response.data,
              content: response.data.content.map((i) =>
                mapGtfGdePropertyDtoToProperty(
                  i,
                  entities.data.content.find((entity) => i.parent_entity_nav_full_id.substring(12, 15) == entity.id)
                )
              ),
            },
          }
        })
    }
  },
  {
    enabled: true,
  },
  {
    page: 0,
    size: 20,
    sort: [],
  }
)

const headers: DataTableHeader<GtfGdePropertyDto>[] = [
  {
    title: 'Numéro installation',
    value: 'code',
    sortable: false,
  },
  {
    title: 'Nom',
    value: 'name',
    sortable: false,
  },
  {
    title: 'Nom Immeuble',
    value: 'buildingName',
    sortable: false,
  },
  {
    title: 'N°',
    value: 'streetNumber',
    sortable: false,
  },
  {
    title: 'Rue',
    value: 'streetName',
    sortable: false,
  },
  {
    title: 'CP',
    value: 'postalCode',
    sortable: false,
  },
  {
    title: 'Ville',
    value: 'city',
    sortable: false,
  },
  {
    title: 'Organisation',
    value: 'entity',
    formater: (_, value: Entity) => `${value.name}`,
    sortable: false,
  },
]

const debounceSearch = debounce((v: string | undefined) => {
  updateFilter({ ...pageFilter.value, search: v })
}, 300)

watch(
  () => props.search,
  (v) => {
    data.value.loading = true
    debounceSearch(v)
  }
)
watch(
  () => props.navFullId,
  (v, oldV) => {
    if (!isEqual(v, oldV)) {
      updateFilter({ ...pageFilter.value, parentEntityIds: v })
    }
  },
  {
    immediate: true,
  }
)

watch(
  () => data.value.loading,
  (v) => {
    emit('update:loading', v)
  },
  {
    immediate: true,
  }
)
</script>
