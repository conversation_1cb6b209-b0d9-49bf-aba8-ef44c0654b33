<template>
  <div style="display: flex; justify-content: end; align-items: center; gap: 8px">
    <NjBtn
      v-if="displaySendButton"
      variant="outlined"
      prepend-icon="mdi-send"
      density="comfortable"
      @click="emits('send', item.documentType.id)"
    >
      Envoyer
    </NjBtn>
    <FillableTemplateActivator v-if="displayFillableTemplateButtonFirst" :operation="operation" :item="item">
      <template #activator="{ download }">
        <NjBtn
          variant="outlined"
          prepend-icon="mdi-download"
          border="0"
          :with-menu="!!item.templateId"
          @click="download"
        >
          <div class="me-n8">
            Modèle prérempli
            <VMenu>
              <template #activator="{ props }">
                <NjIconBtn
                  class="ml-0"
                  :icon="props['aria-expanded'] === 'true' ? 'mdi-chevron-up' : 'mdi-chevron-down'"
                  v-bind="props"
                  rounded="0"
                  height="40px"
                ></NjIconBtn>
              </template>
              <VList>
                <VListItem
                  prepend-icon="mdi-download"
                  :loading="downloadTemplateLoading"
                  base-color="primary"
                  @click.stop="downloadTemplate"
                >
                  <template #prepend>
                    <VIcon icon="mdi-download" color="primary" />
                  </template>
                  Modèle vierge
                </VListItem>
              </VList>
            </VMenu>
          </div>
        </NjBtn>
      </template>
    </FillableTemplateActivator>
    <VMenu v-if="displayAction && ((operation?.stepId ?? 0) < 60 || userStore.isSiege)" :close-on-content-click="false">
      <template #activator="{ props }">
        <NjIconBtn icon="mdi-dots-horizontal" border="0" color="primary" variant="square-outline" v-bind="props" />
      </template>
      <VList class="d-flex flex-column">
        <FillableTemplateActivator
          v-if="(displaySendButton || !isGenerateButtonFocused) && canGenerateDocuments"
          :operation="operation"
          :item="item"
        >
          <template #activator="{ download }">
            <VListItem color="primary" base-color="primary" prepend-icon="mdi-download" @click="download"
              >Modèle prérempli</VListItem
            >
          </template>
        </FillableTemplateActivator>
        <VListItem
          v-if="item.templateId && !displayFillableTemplateButtonFirst"
          base-color="primary"
          prepend-icon="mdi-download"
          :loading="downloadTemplateLoading"
          @click="downloadTemplate"
        >
          Modèle vierge
        </VListItem>
        <CardDialog v-model="replaceDocumentDialog" width="40%" title="Remplacer le document" :closeable="false">
          <template #activator>
            <VListItem
              v-if="item.active"
              base-color="primary"
              prepend-icon="mdi-swap-horizontal"
              @click="replaceDocument(item)"
            >
              Remplacer
            </VListItem>
          </template>
          <VRow class="flex-column my-n2">
            <VCol>
              <NjFileInput
                @new-files="
                  (event) => {
                    replaceFile = []
                    replaceFile.push(...event)
                  }
                "
              />
            </VCol>
            <VCol>
              <NjDisplayValue
                v-if="replaceFile.length > 0"
                :label="oldDocument?.documentType.name ?? ''"
                :value="replaceFile[0].name"
              />
            </VCol>
          </VRow>
          <template #actions>
            <NjBtn variant="outlined" @click="replaceDocumentDialog = false">Annuler</NjBtn>
            <NjBtn @click="submitReplaceDocument"> Valider </NjBtn>
          </template>
        </CardDialog>
        <VListItem v-if="item.active" base-color="primary" prepend-icon="mdi-cancel" @click="desactivateDocument(item)">
          Désactiver
        </VListItem>
        <template v-if="userStore.isAdmin && item.id">
          <CardDialog v-model="editDocumentDialog" width="30%" title="Modifier le type de document" :closeable="false">
            <template #activator>
              <VListItem base-color="primary" prepend-icon="mdi-pencil" @click="editDocument(item)">
                Modifier le type
              </VListItem>
            </template>
            <RemoteAutoComplete
              v-model="operationDocumentRequest!.documentTypeId"
              :query-for-all="queryDocumentTypeNotInOperation(operationDocument?.id ?? 0)"
              :query-for-one="(id) => documentTypeApi.getOne(id)"
              item-title="name"
              infinite-scroll
            />
            <template #actions>
              <NjBtn variant="outlined" @click="editDocumentDialog = false">Annuler</NjBtn>
              <NjBtn @click="saveDocument"> Valider </NjBtn>
            </template>
          </CardDialog>

          <VListItem base-color="primary" prepend-icon="mdi-delete-outline" @click="deleteDocument(item)">
            Supprimer
          </VListItem>
          <AlertDialog v-bind="deleteDocumentDialog.props" title="Supprimer le document" max-width="640px">
            Êtes-vous sûr de vouloir supprimer le document?
          </AlertDialog>
        </template>
      </VList>
    </VMenu>
  </div>
</template>
<script setup lang="ts">
import { documentTypeApi } from '@/api/documentType'
import CardDialog from '@/components/CardDialog.vue'
import NjBtn from '@/components/NjBtn.vue'
import NjFileInput from '@/components/NjFileInput.vue'
import NjIconBtn from '@/components/NjIconBtn.vue'
import { useAdminConfigurationStore } from '@/stores/adminConfiguration'
import { trace } from '@/stores/analytics'
import { useSnackbarStore } from '@/stores/snackbar'
import { useUserStore } from '@/stores/user'
import type { OperationDocumentRequest } from '@/types/document'
import { CONTROL_REPORT_DOCUMENT_TYPE_ID } from '@/types/documentType'
import type { Operation } from '@/types/operation'
import type { Pageable } from '@/types/pagination'
import { VIcon, VMenu } from 'vuetify/components'
import FillableTemplateActivator from './FillableTemplateActivator.vue'
import type { DocumentDataTableItem } from './types'

const props = defineProps({
  item: { type: Object as PropType<DocumentDataTableItem>, required: true },
  operation: Object as PropType<Operation>,
})

const emits = defineEmits<{
  send: [number]
  replaceDocument: [void]
  desactivateDocument: [void]
  deleteDocument: [void]
  editDocument: [void]
}>()

const { swornStatementDocumentTypeId, pvDeReceptionDocumentTypeId, conventionDocumentTypeId, vfDocumentTypeId } =
  useAdminConfigurationStore()

const userStore = useUserStore()
const snackbarStore = useSnackbarStore()

const displaySendButton = computed(
  (): boolean =>
    !!(
      props.operation &&
      props.item.id &&
      ((vfDocumentTypeId?.valueAsInt == props.item.documentType.id &&
        (props.operation.stepId >= 110 || (props.operation.controlOrderDetails && props.operation.stepId >= 70))) ||
        (swornStatementDocumentTypeId?.valueAsInt == props.item.documentType.id && props.operation.stepId == 50) ||
        (pvDeReceptionDocumentTypeId?.valueAsInt == props.item.documentType.id && props.operation.stepId == 50) ||
        (CONTROL_REPORT_DOCUMENT_TYPE_ID == props.item.documentType.id &&
          props.operation.stepId == 80 &&
          userStore.hasRole('AGENCE_PLUS', 'ADMIN', 'ADMIN_PLUS')) ||
        (conventionDocumentTypeId?.valueAsInt == props.item.documentType.id &&
          !props.operation?.operationsGroup &&
          props.operation.stepId == 30 &&
          userHasRole(
            userStore.currentUser,
            'AGENCE_PLUS',
            'SUPPORT_AGENCE_PLUS',
            'TERRITOIRE',
            'ADMIN',
            'ADMIN_PLUS'
          )))
    )
)
const canGenerateDocuments = computed((): boolean => {
  return !!(
    props.item.documentType.id === conventionDocumentTypeId?.valueAsInt ||
    props.item.documentType.id === swornStatementDocumentTypeId?.valueAsInt ||
    props.item.documentType.fillableTemplate
  )
})
const isGenerateButtonFocused = computed((): boolean => {
  return props.item.documentType.id === conventionDocumentTypeId?.valueAsInt
    ? props.operation?.stepId === 30
    : props.item.requiredStepId === props.operation?.stepId && !props.item.creationDateTime
})

const canActOnDocuments = computed(() => {
  return (props.operation?.stepId ?? 0) < 60 || userStore.isSiege
})
const displayFillableTemplateButtonFirst = computed(() => {
  return (
    canActOnDocuments.value && !displaySendButton.value && canGenerateDocuments.value && isGenerateButtonFocused.value
  )
})
const displayAction = computed(
  (): boolean =>
    !!(
      displaySendButton.value ||
      props.item.active ||
      (props.item.templateId && !displayFillableTemplateButtonFirst.value) ||
      props.item.id
    ) && canActOnDocuments.value
)

const downloadTemplateLoading = ref(false)
const downloadTemplate = () => {
  downloadTemplateLoading.value = true
  trace('downloadTemplate', {
    documentType: { id: props.item.documentType.id, name: props.item.documentType.name },
    operation: {
      id: props.operation?.id,
      stepId: props.operation?.stepId,
    },
    standardizedOperationSheet: {
      id: props.operation?.standardizedOperationSheet.id,
      operationCode: props.operation?.standardizedOperationSheet.operationCode,
    },
  })

  documentTypeApi
    .downloadTemplate(props.item.documentType.id)
    .then((response) => {
      downloadFile(props.item.documentType.template!.originalFilename, response.data)
      snackbarStore.setSuccess('Le téléchargement a réussi')
    })
    .catch(() =>
      snackbarStore.setError(
        'Le téléchargement du fichier a échoué. Aviez-vous envoyé ce fichier sur boCEE ? Si oui, veuillez nous contacter.'
      )
    )
    .finally(() => {
      downloadTemplateLoading.value = false
    })
}

const deleteDocumentDialog = useConfirmAlertDialog()
const deleteDocument = async (item: DocumentDataTableItem) => {
  if (await deleteDocumentDialog.confirm()) {
    trace('deleteDocument', {
      operationDocument: {
        id: item.id,
      },
      operation: {
        id: props.operation?.id,
        stepId: props.operation?.stepId,
      },
    })
    operationDocumentApi
      .delete(item.id!)
      .then(() => {
        emits('deleteDocument')
        snackbarStore.setSuccess('Le document à été supprimé')
      })
      .catch(() => {
        snackbarStore.setError('Une erreur est survenue lors de la suppression du document')
      })
  }
}

const desactivateDocument = (item: DocumentDataTableItem) => {
  const request: OperationDocumentRequest = {
    operationId: props.operation?.id!,
    documentTypeId: item.documentType.id,
    description: item.description!,
    active: false,
  }
  trace('disableDocument', {
    operationDocument: {
      id: item.id,
    },
    operation: {
      id: props.operation?.id,
      stepId: props.operation?.stepId,
    },
  })
  operationDocumentApi
    .update(item.id!, request)
    .then(() => {
      emits('desactivateDocument')

      snackbarStore.setSuccess('Le document à été désactivé')
    })
    .catch(async (err) => {
      snackbarStore.setError(await handleAxiosException(err))
    })
}

//remplacer dialog
const replaceDocumentDialog = ref(false)
const oldDocument = ref<DocumentDataTableItem>()
const replaceFile = ref<File[]>([])
const replaceDocument = (old: DocumentDataTableItem) => {
  oldDocument.value = old
  replaceDocumentDialog.value = true
}

const promisableReplaceOperationDocument = ref(emptyValue<DocumentDataTableItem>())

const submitReplaceDocument = () => {
  if (!oldDocument.value) {
    return
  }
  trace('replaceDocument', {
    operationDocument: {
      id: oldDocument.value!.id,
    },
    operation: {
      id: props.operation?.id,
    },
  })
  const request: OperationDocumentRequest = {
    operationId: props.operation!.id,
    documentTypeId: oldDocument.value.documentType.id,
    description: oldDocument.value.description ?? '',
    active: true,
  }
  handleAxiosPromise(
    promisableReplaceOperationDocument,
    operationDocumentApi.uploadFile(request, replaceFile.value[0], oldDocument.value.id),
    {
      afterSuccess: () => {
        snackbarStore.setSuccess('Le document a été remplacé')
        emits('replaceDocument')
        replaceDocumentDialog.value = false
        replaceFile.value = []
      },
      afterError: () => snackbarStore.setError("Erreur le document n'a pas pu être remplacé"),
    }
  )
}

const editDocumentDialog = ref(false)
const operationDocumentRequest = ref<OperationDocumentRequest>()
const operationDocument = ref<DocumentDataTableItem>()

const editDocument = (doc: DocumentDataTableItem) => {
  operationDocument.value = doc
  operationDocumentRequest.value = {
    operationId: props.operation!.id,
    documentTypeId: doc.documentType.id,
    description: doc.description ?? '',
    active: doc.active!,
  }
  editDocumentDialog.value = true
}

const saveDocument = () => {
  if (!operationDocument.value) {
    return
  }

  trace('modifyDocument', {
    operationDocument: {
      id: operationDocument.value!.id,
    },
    operation: {
      id: props.operation?.id,
    },
  })

  operationDocumentApi
    .update(operationDocument.value!.id!, operationDocumentRequest.value!)
    .then(() => {
      snackbarStore.setSuccess('Le document à été mis à jour')
      editDocumentDialog.value = false
      emits('editDocument')
    })
    .catch(async (err) => {
      snackbarStore.setError(await handleAxiosException(err))
    })
}

const queryDocumentTypeNotInOperation = (operationId: number) => (search: string, pageable: Pageable) => {
  return documentTypeApi.getAll(pageable, {
    search: search,
    notActiveInOperation: operationId,
  })
}
</script>
