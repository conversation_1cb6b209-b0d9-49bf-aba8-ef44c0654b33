<template>
  <CardDialog
    :model-value="props.modelValue"
    width="50%"
    title="Préparer l'attestation sur l'honneur"
    :closeable="false"
    @update:model-value="emits('update:model-value', $event)"
  >
    <VForm ref="formRef">
      <VRow class="flex-column">
        <VCol>
          <VAlert type="info" variant="outlined">
            Le document
            <b>{{ `Attestation sur l'honneur - ${operation?.chronoCode}.docx` }}</b>
            est disponible. Merci de vérifier les informations avant signature.
          </VAlert>
        </VCol>
        <VCol>
          <NjDisplayValue
            v-if="operation?.precariousnessBonusParameterValues?.cas == 1"
            label="Veuillez choisir si bailleur social ou Quartier Prioritaire de la ville?"
          >
            <template #value>
              <VSelect
                v-model="precariousnessCase2FrameD"
                :items="precariousnessCase2FrameDValueLabel"
                :rules="[requiredRule]"
              />
            </template>
          </NjDisplayValue>
        </VCol>
      </VRow>
    </VForm>

    <template #actions>
      <NjBtn variant="outlined" @click="emits('update:model-value', false)">Annuler</NjBtn>
      <NjBtn :loading="downloadingSwornStatement" @click="createSwornStatement">Téléchargement</NjBtn>
    </template>
  </CardDialog>
</template>
<script setup lang="ts">
import { type PropType } from 'vue'
import { type Operation } from '@/types/operation'
import { useSnackbarStore } from '@/stores/snackbar'
import { requiredRule } from '@/types/rule'
import { precariousnessCase2FrameDValueLabel } from '@/types/swornStatementTemplateFrame'
import NjDisplayValue from '@/components/NjDisplayValue.vue'
import { VCol, VForm } from 'vuetify/components'
import { useAdminConfigurationStore } from '@/stores/adminConfiguration'
import { trace } from '@/stores/analytics'

const props = defineProps({
  modelValue: Boolean,
  operation: Object as PropType<Operation>,
})
const emits = defineEmits<{
  'update:model-value': [boolean]
}>()

const snackbarStore = useSnackbarStore()

const downloadingSwornStatement = ref(false)
const precariousnessCase2FrameD = ref(null)

const formRef = ref<typeof VForm | null>(null)

const adminStore = useAdminConfigurationStore()

const createSwornStatement = async () => {
  if (!props.operation) {
    return
  }
  if (!(await formRef.value!.validate()).valid) {
    return
  }

  downloadingSwornStatement.value = true

  trace('downloadFilledTemplate', {
    documentType: { id: adminStore.swornStatementDocumentTypeId?.valueAsInt, name: 'AH' },
    operation: {
      id: props.operation?.id,
      stepId: props.operation?.stepId,
    },
    standardizedOperationSheet: {
      id: props.operation?.standardizedOperationSheet.id,
      operationCode: props.operation?.standardizedOperationSheet.operationCode,
    },
  })

  swornStatementApi
    .create(props.operation?.id, precariousnessCase2FrameD.value)
    .then((response) => {
      downloadFile(`Attestation sur l'honneur - ${props.operation?.chronoCode}.docx`, response.data)
      snackbarStore.setSuccess("L'édition de l'attestation sur l'honneur a réussi")
      emits('update:model-value', false)
    })
    .catch(async (err) => snackbarStore.setError(await handleAxiosException(err)))
    .finally(() => (downloadingSwornStatement.value = false))
}
</script>
