<template>
  <VRow class="flex-column" dense>
    <template v-if="mode === 'display'">
      <VCol v-for="(parameter, index) in requiredParameters" :key="index">
        <NjDisplayValue :label="parameter.label" :value="props.modelValue[parameter.id]" />
      </VCol>

      <VCol v-for="(parameter, index) in optionalParameters" :key="index">
        <NjDisplayValue :label="parameter.label" :value="props.modelValue[parameter.id]" />
      </VCol>
    </template>
    <template v-else>
      <VCol v-for="(parameter, index) in requiredParameters" :key="index">
        <NjTextField
          v-if="parameter.type === 'NUMBER'"
          :label="parameter.label"
          :model-value="props.modelValue[parameter.id]"
          :rules="numberRules"
          type="number"
          required
          @update:model-value="updateValue(parameter.id, $event)"
        />
        <NjSelect
          v-else-if="parameter.data"
          :label="parameter.label"
          :items="parameter.data.split(';')"
          :model-value="props.modelValue[parameter.id]"
          :rules="choiceRules"
          required
          @update:model-value="updateValue(parameter.id, $event)"
        />
      </VCol>
      <VCol v-for="(parameter, index) in optionalParameters" :key="index">
        <NjTextField
          v-if="parameter.type === 'NUMBER'"
          :label="parameter.label"
          :model-value="props.modelValue[parameter.id]"
          :rules="numberRules"
          type="number"
          recommended
          @update:model-value="updateValue(parameter.id, $event)"
        />
        <NjSelect
          v-else-if="parameter.data"
          :label="parameter.label"
          :items="parameter.data.split(';')"
          :model-value="props.modelValue[parameter.id]"
          :rules="choiceRules"
          recommended
          @update:model-value="updateValue(parameter.id, $event)"
        />
      </VCol>
    </template>

    <VCol v-for="cp in computedParameters" :key="cp.id">
      <NjDisplayValue
        :label="cp.label"
        align="end"
        :value="newValues[0]?.[cp.id] + (cp.suffix ? ' ' + cp.suffix : '')"
      />
    </VCol>

    <VCol v-if="results.length > 1">
      <VRow no-gutters class="flex-column">
        <!-- eslint-disable-next-line vue/valid-v-for -->
        <VCol v-for="(v, i) in results">
          <NjDisplayValue :label="'Opération n°' + (i + 1)" :value="formatNumber(v.value ?? 0) + ' kWhc'" />
        </VCol>
      </VRow>
    </VCol>

    <VCol v-if="result != null">
      <NjDisplayValue
        align="end"
        color-value="primary"
        :label="props.resultTitle"
        :value="formatNumber(result ?? 0) + ' KWhc'"
      />
    </VCol>
    <VCol v-else> {{ resultError }} </VCol>
  </VRow>
</template>
<script setup lang="ts">
import type { CalculFormula } from '@/types/calculFormula'
import type { PropType } from 'vue'
import Formula from 'fparser'
import { isEqual } from 'lodash'
import { convertChoiceToIndex, evaluateFormula, parsingFormula } from '@/types/calcul/formula'
import { formatNumber } from '@/types/format'
import { generateMappingTableCombinaisons } from '@/types/calcul/mappingTable'
import { predefinedParameters, type ParameterFormula } from '@/types/calcul/parameterFormula'
import NjDisplayValue from './NjDisplayValue.vue'
import type { BoostBonusSimulation } from '@/types/boostBonus'
import type { PromisableValue } from '@/types/promisableValue'
import type { ValidationRule } from '@/types/rule'

const props = defineProps({
  calculFormula: {
    type: Object as PropType<CalculFormula>,
    required: true,
  },
  modelValue: {
    type: Object as PropType<BoostBonusSimulation['parameterValues']>,
    required: true,
  },
  resultTitle: {
    type: String,
    default: '',
  },
  fieldRequired: {
    type: Boolean,
    default: false,
  },
  predefinedValue: {
    type: Array as PropType<Array<object>>,
    default: () => [{}],
  },
  mode: {
    type: String as PropType<'preview' | 'edit' | 'display'>,
    default: 'edit',
  },
})

const emit = defineEmits<{
  'update:model-value': [v: any]
  'update:cumac': [v: number[]]
}>()

function updateValue(column: string, value: unknown) {
  emit('update:model-value', { ...props.modelValue, [column]: value })
}

const result = ref()

// formula parsing
let formulaObject: any = undefined

const combinaisonsValues = ref<Record<string, string[][]>>({})

const conditionalParameterFormulas: Ref<Record<string, PromisableValue<any>>> = ref({})
const computedParameterFormulas: Ref<Record<string, PromisableValue<any>>> = ref({})
const cacheStandardizedOperationSheetParameters: ParameterFormula[] = []
watch(
  () => props.calculFormula.parameters,
  (parameters) => {
    // console.debug('watch standardizedOperationSheet.parameters', parameters, props.standardizedOperationSheet.id, props.standardizedOperationSheet.operationCode)
    parameters.forEach((p, i) => {
      if (!isEqual(p, cacheStandardizedOperationSheetParameters[i])) {
        if (p.conditionalFormula) {
          // console.debug('set conditionalParameterFormulas', p.id)
          conditionalParameterFormulas.value[p.id] = parsingFormula(p.conditionalFormula)
        } else {
          delete conditionalParameterFormulas.value[p.id]
        }

        if (p.computedFormula) {
          computedParameterFormulas.value[p.id] = parsingFormula(p.computedFormula)
        } else {
          delete computedParameterFormulas.value[p.id]
        }
      }
    })
  },
  {
    immediate: true,
    deep: true,
  }
)

watch(
  () => props.calculFormula,
  (v) => {
    for (const mappingTable of v.mappingTables) {
      combinaisonsValues.value[mappingTable.id] = generateMappingTableCombinaisons(mappingTable, v.parameters)
    }
  },
  {
    immediate: true,
  }
)

const computedParameters = computed((): ParameterFormula[] => {
  // console.debug('compute computedParameters')
  let parameters = props.mode === 'preview' ? predefinedParameters.concat() : []
  parameters = parameters.concat(props.calculFormula.parameters)
  return parameters.filter((p) => p.computedFormula)
})

const resultError = ref()
const results = ref<PromisableValue<number>[]>([])
const newValues = ref<Record<string, any>[]>([])

const newModelValue = ref<Record<string, string | number>>({})

watch(
  () => props.modelValue,
  (v, oldValues) => {
    if (!isEqual(v, oldValues)) {
      const values: Record<string, any> = convertChoiceToIndex(props.calculFormula.parameters, v)
      const enhancedValue = { ...v }

      props.calculFormula.parameters.forEach((p) => {
        if (p.computedFormula && computedParameterFormulas.value[p.id]) {
          const result = evaluateFormula(
            computedParameterFormulas.value[p.id].value,
            values
            // convertedChoicesValues[iOperationLine],
            // convertedChoicesValues
          )
          if (!result.error) {
            if (p.type === 'CHOICE') {
              const newValue = p.data.split(';')[result.value! - 1]
              if (values[p.id] !== newValue && (!isNaN(values[p.id] as number) || !isNaN(newValue))) {
                values[p.id] = newValue
                enhancedValue[p.id] = newValue
              }
            } else {
              if (values[p.id] !== result.value! && (!isNaN(values[p.id] as number) || !isNaN(result.value ?? 0))) {
                values[p.id] = result.value!
                enhancedValue[p.id] = result.value!
              }
            }
          } else {
            logException(result.error)
          }
        }
      })
      //
      // return enhancedValue
      // })
      newModelValue.value = enhancedValue
      emit('update:model-value', enhancedValue)
    }
  },
  {
    immediate: true,
  }
)
watch(
  [() => newModelValue.value, () => props.predefinedValue],
  (v, oldValue) => {
    if (!isEqual(v, oldValue)) {
      results.value = v[1].map((a, index) => {
        const beforeConvert = { ...v[0], ...a }
        const values: Record<string, any> = convertChoiceToIndex(props.calculFormula.parameters, beforeConvert)
        console.log('watch values from CalculFomrulaEvaluator')
        if (props.calculFormula.formula) {
          formulaObject = new Formula(props.calculFormula.formula)
          ;(formulaObject._variables as string[]).forEach((variable) => {
            if (props.calculFormula.mappingTables) {
              const mappingTable = props.calculFormula.mappingTables.find((it) => it.id === variable)
              if (mappingTable) {
                values[variable] =
                  mappingTable.data[
                    combinaisonsValues.value[mappingTable.id].findIndex((it) =>
                      isEqual(
                        it,
                        mappingTable.paramColumns.map((it) => beforeConvert[it])
                      )
                    )
                  ]
              }
            }
          })

          props.calculFormula.parameters.forEach((p) => {
            if (p.conditionalFormula && conditionalParameterFormulas.value[p.id]) {
              const result = evaluateFormula(
                conditionalParameterFormulas.value[p.id].value,
                values
                // convertedChoicesValues[iOperationLine],
                // convertedChoicesValues
              )
              if (!result.error) {
                if (p.type === 'CHOICE') {
                  const newValue = p.data.split(';')[result.value! - 1]
                  if (
                    values['conditional-' + p.id] !== newValue &&
                    (!isNaN(values['conditional-' + p.id]) || !isNaN(newValue))
                  ) {
                    values['conditional-' + p.id] = newValue
                  }
                } else {
                  if (
                    values['conditional-' + p.id] !== result.value! &&
                    (!isNaN(values['conditional-' + p.id]) || !isNaN(result.value ?? 0))
                  ) {
                    values['conditional-' + p.id] = result.value!
                  }
                }
              } else {
                logException(result.error)
              }
            }
          })

          const evaluation = evaluateFormula(formulaObject, values)
          newValues.value[index] = values
          // result.value = evaluation.value
          // resultError.value = evaluation.error
          if (!evaluation.error) {
            evaluation.value = evaluation.value!
          }
          return evaluation
        } else {
          return errorValue<number>('Aucune formule a évaluer')
        }
      })

      if (results.value.every((it) => !it.error)) {
        const cumacs = results.value.map((it) => it.value!)
        emit('update:cumac', cumacs)
        result.value = cumacs.reduce((acc, v) => acc + v, 0)
      } else {
        resultError.value = results.value.find((it) => it?.error)?.error
      }
    }
  },
  {
    immediate: true,
    deep: true,
  }
)

const numberRules = ref<ValidationRule[]>()
const choiceRules = ref<ValidationRule[]>()

watch(
  () => props.fieldRequired,
  (v) => {
    if (v) {
      numberRules.value = [requiredRule, numericRule]
      choiceRules.value = [requiredRule]
    } else {
      numberRules.value = []
      choiceRules.value = []
    }
  },
  {
    immediate: true,
  }
)

import { computed } from 'vue'

const requiredParameters = computed(() => {
  return props.calculFormula.parameters.filter((parameter) => !parameter.optional)
})

const optionalParameters = computed(() => {
  return props.calculFormula.parameters.filter((parameter) => parameter.optional)
})
</script>
