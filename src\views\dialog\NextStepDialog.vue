<template>
  <VDialog v-model="modelValue" :width="width" min-width="576px" :persistent="blocked">
    <VCard class="content-layout">
      <VCardTitle class="d-flex align-center content-layout__header">
        <div class="flex-grow-1">
          <div>Valider l'étape {{ step?.id }}</div>
          <!-- <div class="text-subtitle-1"> {{ step?.name }} </div> -->
          <VCardSubtitle class="pa-0">
            {{ step?.name }}
          </VCardSubtitle>
        </div>
        <NjIconBtn icon="mdi-window-close" variant="flat" rounded="0" @click="modelValue = false" />
      </VCardTitle>
      <NjDivider class="pa-0" />
      <VCardText class="pa-0 content-layout__main" style="flex-basis: auto">
        <slot name="alert"></slot>
        <VRow class="flex-column" no-gutters>
          <VDivider v-if="!hideDivider && $slots.alert" />
          <VCol v-if="stepId < 70" class="pa-4">
            <NjDisplayValue
              label="Offre commerciale"
              :value="commercialStatusTitle.find((status) => status.value === step?.commercialStatus)?.title"
            />
          </VCol>
          <NjDivider v-if="!hideDivider && stepId < 70" />
          <VCol>
            <slot name="informations" />
          </VCol>
          <VDivider v-if="$slots.more" />
          <VCol v-if="$slots.more">
            <slot name="more" />
          </VCol>
        </VRow>
      </VCardText>
      <VDivider />
      <slot name="actions" />
    </VCard>
  </VDialog>
</template>
<script lang="ts" setup>
import NjDisplayValue from '@/components/NjDisplayValue.vue'
import NjDivider from '@/components/NjDivider.vue'
import { useStepsStore } from '@/stores/steps'
import { commercialStatusTitle, type Step } from '@/types/steps'

const modelValue = defineModel<boolean>({
  default: false,
})
const props = defineProps({
  stepId: {
    type: Number,
    required: true,
  },
  width: {
    type: String,
    default: '40%',
  },
  hideDivider: {
    Boolean,
    default: false,
  },
  blocked: Boolean,
})

const stepsStore = useStepsStore()
const step = ref<Step>()

watch(modelValue, (v) => {
  if (v) {
    step.value = stepsStore.steps?.find((step) => step.id === props.stepId)
  }
})
</script>
