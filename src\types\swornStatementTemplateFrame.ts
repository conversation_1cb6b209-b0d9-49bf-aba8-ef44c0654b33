import type { Document } from './document'
import { type OperationTargetRule, makeEmptyOperationTargetRule } from './operationTargetRule'

export const makeEmptySwornStatementTemplateFrame = (): SwornStatementTemplateFrame => {
  return {
    name: '',
    id: 0,
    template: makeEmptyDocument(),
    operationTargetRule: makeEmptyOperationTargetRule(),
    precariousnessCase2FrameD: null,
    repeatOnOperationLine: false,
    copyFooter: false,
  }
}

export const precariousnessCase2FrameDValueLabel: { title: string; value: string }[] = [
  {
    value: 'SOCIAL_LANDLORD',
    title: 'Bailleur sociaux',
  },
  {
    value: 'PRIORITY_URBAN_DISTRICT',
    title: 'Quartier prioritaire de la ville',
  },
]
export type PrecariousnessCase2FrameDValue = (typeof precariousnessCase2FrameDValueLabel)[number]['value']

export interface SwornStatementTemplateFrame {
  id: number
  name: string
  template: Document
  operationTargetRule: OperationTargetRule
  precariousnessCase2FrameD: PrecariousnessCase2FrameDValue | null
  repeatOnOperationLine: boolean
  copyFooter: boolean
}

export interface SwornStatementTemplateFrameRequest extends Omit<SwornStatementTemplateFrame, 'id' | 'document'> {}
