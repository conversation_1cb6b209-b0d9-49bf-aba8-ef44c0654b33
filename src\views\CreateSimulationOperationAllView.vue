<template>
  <NjPage title="Initier une simulation" expend-body>
    <template #header-actions>
      <DoublonDialog />
    </template>
    <template #body>
      <StandardizedOperationSheetAll route="CreateSimulationView" />
    </template>
  </NjPage>
</template>

<script lang="ts" setup>
import NjPage from '@/components/NjPage.vue'
import StandardizedOperationSheetAll from './StandardizedOperationSheetAll.vue'
import DoublonDialog from './DoublonDialog.vue'
</script>
