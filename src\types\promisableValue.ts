import { handleAxiosException } from '@/api'
import type { AxiosPromise, AxiosResponse } from 'axios'
import type { Ref } from 'vue'

export type PromisableValue<T> = {
  loading: boolean
  value: T | undefined
  error: /*Error | */ string | undefined
}

export type ValuePromise<T> = Promise<PromisableValue<T>>

export function succeedValue<T>(value: T): PromisableValue<T> {
  return {
    loading: false,
    value,
    error: undefined,
  }
}

export function loadingValue<T>(previousValue: T | undefined = undefined): PromisableValue<T> {
  return {
    loading: true,
    value: previousValue,
    error: undefined,
  }
}

export function errorValue<T>(
  error: /*Error | */ string | undefined,
  previousValue: T | undefined = undefined
): PromisableValue<T> {
  return {
    loading: false,
    value: previousValue,
    error: error,
  }
}

export function emptyValue<T = boolean>(): PromisableValue<T> {
  return {
    loading: false,
    value: undefined,
    error: undefined,
  }
}

export function succeedValuePromise<T>(value: T): ValuePromise<T> {
  return Promise.resolve({
    loading: false,
    value,
    error: undefined,
  })
}

export function handleAxiosPromise<T>(
  data: Ref<PromisableValue<T>>,
  promise: AxiosPromise<T>,
  callbackSuccess:
    | ((v: AxiosResponse<T>) => void)
    | {
        callbackSuccess?: (v: AxiosResponse<T>) => void
        callbackError?: () => void
        afterSuccess?: (v: AxiosResponse<T>) => void
        afterError?: (e: string) => void
      } = (v) => {
    data.value.value = v.data
  },
  callbackError: (() => void) | undefined = undefined
) {
  data.value.error = ''
  data.value.loading = true
  return promise
    .then((v) => {
      // Essentiel de le mettre dans le then et le catch pour le rendr ecompatible avec la composition unsavedData

      if (typeof callbackSuccess === 'function') {
        data.value.loading = false
        callbackSuccess(v)
      } else {
        if (callbackSuccess.callbackSuccess) {
          callbackSuccess.callbackSuccess(v)
        } else {
          data.value.loading = false
          data.value.value = v.data
        }
        callbackSuccess.afterSuccess?.(v)
      }
      return v
    })
    .catch(async (e) => {
      if (typeof callbackSuccess === 'object') {
        if (callbackSuccess.callbackError) {
          callbackSuccess.callbackError()
        } else {
          data.value.loading = false
          data.value.error = await handleAxiosException(e)
        }
        callbackSuccess.afterError?.(data.value.error ?? '')
      } else {
        data.value.loading = false
        data.value.error = await handleAxiosException(e)
        callbackError?.()
      }
      return Promise.reject(e)
    })
}

export function handlePromise<T>(data: Ref<PromisableValue<T>>, promise: Promise<T>) {
  data.value.error = ''
  data.value.loading = true
  return promise
    .then((v) => {
      // Essentiel de le mettre dans le then et le catch pour le rendr ecompatible avec la composition unsavedData
      data.value.value = v
    })
    .catch((e) => {
      data.value.error = e
    })
    .finally(() => {
      data.value.loading = false
    })
}
