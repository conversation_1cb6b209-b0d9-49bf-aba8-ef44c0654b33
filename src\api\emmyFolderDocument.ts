import type { EmmyFolderDocumentRequest, EnhancedDocument } from '@/types/document'
import type { Page, Pageable } from '@/types/pagination'
import type { AxiosInstance, AxiosPromise } from 'axios'
import axiosInstance from '.'

export interface EmmyFolderDocumentFilter extends Record<string, any> {
  emmyFolderId?: number
}

const emmyFolderDocumentUri = '/emmy_folder_documents'
class EmmyFolderDocumentApi {
  public constructor(private axios: AxiosInstance) {}

  public findAll(filter: EmmyFolderDocumentFilter, pageable: Pageable): AxiosPromise<Page<EnhancedDocument>> {
    return this.axios.get(emmyFolderDocumentUri, {
      params: { ...pageable, ...filter },
    })
  }

  public update(id: number, request: EmmyFolderDocumentRequest): AxiosPromise<EnhancedDocument> {
    return this.axios.put(`${emmyFolderDocumentUri}/${id}`, request)
  }

  public delete(id: number): AxiosPromise {
    return this.axios.delete(`${emmyFolderDocumentUri}/${id}`)
  }

  public download(id: number): AxiosPromise<File> {
    return this.axios.get(`${emmyFolderDocumentUri}/${id}/file`, {
      responseType: 'blob',
    })
  }

  public async uploadFile(
    emmyFolderDocumentRequest: EmmyFolderDocumentRequest,
    file: File,
    documentToReplaceId?: number
  ): AxiosPromise<EnhancedDocument> {
    const formData = new FormData()
    formData.append('file', file, file.name)
    const hash = await hashFile(file)
    formData.append('hash', hash)

    const request = JSON.stringify(engieFormatRequestTransformKey(emmyFolderDocumentRequest))
    const blob = new Blob([request], {
      type: 'application/json',
    })
    formData.append('request', blob)

    return this.axios.post(emmyFolderDocumentUri, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      params: {
        documentToReplaceId,
      },
    })
  }
}

export const emmyFolderDocumentApi = new EmmyFolderDocumentApi(axiosInstance)
