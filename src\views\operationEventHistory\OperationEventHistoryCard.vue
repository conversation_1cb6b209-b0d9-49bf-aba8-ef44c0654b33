<template>
  <div>
    <MessageCard
      v-if="
        operationEvent.event == 'operation.message.created' || operationEvent.event == 'operation.finalVersion.sent'
      "
      :is-final-version-message="operationEvent.event == 'operation.finalVersion.sent'"
      :message="operationEvent.message"
      :operation="operation"
      :documents-available="documentsAvailable"
      @send="emits('reload')"
      @treated="emits('treated', $event)"
    />
    <VCard v-else>
      <VCardTitle class="text-primary font-weight-bold" style="font-size: 1rem !important">
        <VRow>
          <VCol>
            {{ displayFullnameUser(operationEvent.creationUser) }}
          </VCol>
          <VSpacer />
          <VCol class="flex-grow-0">
            {{ getEventLabel(operationEvent.event, operationEvent.changeSet) }}
          </VCol>
        </VRow>
      </VCardTitle>
      <VCardSubtitle class="ps-4">
        {{ formatHumanReadableLocalDateTime(operationEvent.creationDateTime) }}
      </VCardSubtitle>
      <VCardText>
        <div v-for="(change, index) in filterChangeSet(operationEvent)" :key="index">
          <NjDisplayValue
            v-if="isCreationEvent(operationEvent.event)"
            :label="fieldNameLabel(change.fieldName) ?? change.fieldName"
            :value="resolveValue(change, false)"
          />
          <NjDisplayValue
            v-else-if="isDeletionEvent(operationEvent.event)"
            :label="fieldNameLabel(change.fieldName) ?? change.fieldName"
            :value="resolveValue(change, true)"
          />
          <NjDisplayValue
            v-else
            :label="fieldNameLabel(change.fieldName) ?? change.fieldName"
            :value="`${resolveValue(change, true)} → ${resolveValue(change, false)}`"
          />
        </div>
      </VCardText>
    </VCard>
  </div>
</template>
<script setup lang="ts">
import { displayFullnameUser } from '@/types/user'
import { formatHumanReadableLocalDateTime } from '@/types/date'
import { type OperationEvent, changeSetFieldNameLabel, type ObjectChange } from '@/types/operationEvent'
import type { EnhancedDocument } from '@/types/document'
import MessageCard from './MessageCard.vue'
import type { PropType } from 'vue'
import type { Page } from '@/types/pagination'
import { commercialStatusTitle } from '@/types/steps'
import { mapToReadableStatus } from '@/types/operation'
import { type Operation, lostReasonsLabels } from '@/types/operation'
import { controlOrderTypeLabel } from '@/types/calcul/standardizedOperationSheet'
import { useAdminConfigurationStore } from '@/stores/adminConfiguration'

const props = defineProps({
  operation: {
    type: Object as PropType<Operation>,
    required: true,
  },
  operationEvent: {
    type: Object as PropType<OperationEvent>,
    required: true,
  },
  documentsAvailable: Object as PropType<Page<EnhancedDocument>>,
})

const emits = defineEmits<{
  reload: []
  treated: [any]
}>()

const eventLabel = [
  {
    event: 'operation.created',
    label: 'Création',
  },
  {
    event: 'operation.updated',
    label: 'Mise à jour',
  },
  {
    event: 'operation.purge',
    label: 'Purge',
  },
  {
    event: 'operation.validateStep',
    label: "Changement d'étape",
  },
  {
    event: 'operation.deleteOperationsGroup',
    label: 'Suppression du regroupement',
  },
  {
    event: 'operation.document.delete',
    label: 'Supression du document',
  },
  {
    event: 'operation.document.created',
    label: "Ajout d'un document",
  },
  {
    event: 'operation.document.updated',
    label: "Mise à jour d'un document",
  },
  {
    event: 'operation.addedInControlOrderBatch',
    label: 'Ajout dans un lot de contrôle',
  },
  {
    event: 'operation.removedFromControlOrderBatch',
    label: "Sortie d'un lot de contrôle",
  },
  {
    event: 'operation.addedInEmmyFolder',
    label: 'Ajout dans un dossier Emmy',
  },
  {
    event: 'operation.removedFromEmmyFolder',
    label: "Sortie d'un dossier Emmy",
  },
  {
    event: 'operation.exportToCSV',
    label: 'Export vers csv',
  },
  {
    event: 'operation.invalidateimport',
    label: "Annulation de l'import",
  },
  {
    event: 'operation.validateimport',
    label: "Validation de l'import",
  },
  {
    event: 'operation.message.created',
    label: 'Message',
  },
]

const getEventLabel = (event: string, changeSet: ObjectChange[]): string => {
  if (event === 'operation.validateStep') {
    const change = changeSet.find((c) => c.fieldName === 'stepId')
    if (change && (change.newValue as number) > (change.oldValue as number)) {
      return "Validation d'étape"
    } else if (change && change.newValue < change.oldValue) {
      return "Retour à l'étape précédente"
    }
  }
  return eventLabel.find((item) => item.event == event)?.label ?? event
}

const adminConfigurationStore = useAdminConfigurationStore()

const resolveValue = (change: ObjectChange, isOldValue: boolean): string | undefined => {
  const value = isOldValue ? change.oldValue : change.newValue
  if (value === null) {
    return 'Aucun'
  } else if (change.fieldName === 'oshPriorityAutomated') {
    return value ? 'Automatique' : 'Manuel'
  } else if (value === false) {
    return 'Non'
  } else if (value === true) {
    return 'Oui'
  } else if (change.fieldName === 'commercialStatus') {
    return commercialStatusTitle.find((item) => item.value == value)?.title
  } else if (change.fieldName === 'status') {
    return mapToReadableStatus(value)
  } else if (change.fieldName.endsWith('Date')) {
    return formatHumanReadableLocalDate(value)
  } else if (change.fieldName.endsWith('DateTime')) {
    return formatHumanReadableLocalDateTime(value)
  } else if (change.fieldName === 'lostReasons') {
    let res = ''
    for (const s in value) {
      res += lostReasonsLabels[s] + ', '
    }
    return res.slice(0, res.length - 2)
  } else if (change.fieldName === 'instructorId') {
    return displayFullnameUser(value)
  } else if (change.fieldName === 'works1' || change.fieldName === 'works2' || change.fieldName === 'works3') {
    return value.id + ' - ' + value.worksType
  } else if ('controlOrderDetails_controlOrderStatus' == change.fieldName) {
    return mapControlOrderStatus(value)
  } else if ('controlOrderDetails_afterSalesServiceStatus' == change.fieldName) {
    return mapControlOrderAssStatus(value)
  } else if ('controlOrderDetails_controlOrderType' == change.fieldName) {
    return controlOrderTypeLabel.find((i) => i.value == value)?.label ?? 'Aucun'
  } else if ('oshPriority' == change.fieldName) {
    return 'D' + value
  } else if (change.fieldName === 'documentTypeIdsSent') {
    return value
      .map((it: any) => {
        switch (it) {
          case parseInt(adminConfigurationStore.conventionDocumentTypeId?.data ?? '0'):
            return 'Convention'
          case parseInt(adminConfigurationStore.swornStatementDocumentTypeId?.data ?? '0'):
            return "Attestation sur l'honneur"
          case parseInt(adminConfigurationStore.pvDeReceptionDocumentTypeId?.data ?? '0'):
            return 'PV de réception'
          case CONTROL_REPORT_DOCUMENT_TYPE_ID:
            return 'Rapport de contrôle'
          default:
            return '#' + it
        }
      })
      .join(', ')
  } else {
    return value
  }
}

const fieldNameLabel = (fieldName: string) => {
  if (fieldName.startsWith('parameterValues')) {
    const array = fieldName.split('_')
    if (props.operation.standardizedOperationSheet.multipleOperation) {
      return 'Ligne ' + array[2] + ' ' + array[array.length - 1]
    } else {
      return array[array.length - 1]
    }
  } else if (
    fieldName.startsWith('precariousnessBonusParameterValues') ||
    fieldName.startsWith('epcBonusParameterValues_') ||
    fieldName.startsWith('boostBonusParameterValues_')
  ) {
    const array = fieldName.split('_')
    return array[array.length - 1]
  } else {
    return changeSetFieldNameLabel.find((item) => item.fieldName == fieldName)?.value
  }
}
const displayOnlyNewValueEvent = [
  'operation.created',
  'operation.document.created',
  'operation.addedInControlOrderBatch',
  'operation.addedInEmmyFolder',
]
const displayOnlyOldValueEvent = [
  'operation.removedFromControlOrderBatch',
  'operation.removedFromEmmyFolder',
  'operation.document.delete',
]

const isCreationEvent = (event: string) => {
  return !!displayOnlyNewValueEvent.find((item) => event == item)
}

const isDeletionEvent = (event: string) => {
  return !!displayOnlyOldValueEvent.find((item) => event == item)
}

const filterBeforeStep60 = [
  'classicValuationValue',
  'precariousnessValuationValue',
  'classicCumac',
  'precariousnessCumac',
]

const filterChangeSet = (operationEvent: OperationEvent): ObjectChange[] => {
  return operationEvent.changeSet.filter(
    (change) =>
      !(operationEvent.operationHistory.stepId <= 50 && filterBeforeStep60.find((v) => v === change.fieldName))
  )
}
</script>
