<template>
  <NjPage title expend-body v-bind="$attrs">
    <template #subtitle>
      <VRow>
        <VCol>
          <SearchInput v-model="searchBeneficiary" />
        </VCol>
        <VCol v-if="userStore.isAdminPlus" class="flex-grow-0">
          <NjBtn
            @click="
              () => {
                editControlOrganism = makeEmptyControlOrganism()
                createControlOrganismDialog = true
              }
            "
          >
            Créer
          </NjBtn>
          <VDialog v-model="createControlOrganismDialog" height="100%" width="50%">
            <ControlOrganismOneView
              :id="0"
              can-certified
              mode="create"
              :cancel="() => (createControlOrganismDialog = false)"
              :after-success="
                () => {
                  createControlOrganismDialog = false
                  controlOrganismAllViewRef?.reload()
                }
              "
            >
              <template #title> Créer un organisme de contrôle </template>
            </ControlOrganismOneView>
          </VDialog>
        </VCol>
      </VRow>
    </template>
    <template #body>
      <ControlOrganismAllView ref="controlOrganismAllViewRef" v-model="localSelected" fixed class="w-100" />
    </template>
  </NjPage>
  <VNavigationDrawer location="end" :model-value="!!localSelected[0]" width="600" disable-resize-watcher>
    <ControlOrganismDisplayValue v-if="!edit" :model-value="localSelected[0]" with-title with-history>
      <template #title>
        <VRow dense>
          <VCol align-self="center"> Détail </VCol>
          <VCol v-if="userStore.isAdminPlus" class="flex-grow-0">
            <NjIconBtn
              icon="mdi-pencil"
              color="primary"
              rounded="0"
              @click="
                () => {
                  edit = true
                  mode = 'edit'
                  editControlOrganism = localSelected[0]
                }
              "
            />
          </VCol>
          <VCol v-if="userStore.isAdminPlus" class="flex-grow-0">
            <NjIconBtn
              icon="mdi-file-multiple"
              color="primary"
              style="margin-inline: 4px"
              rounded="0"
              @click="duplicate"
            />
          </VCol>

          <VCol class="flex-grow-0">
            <NjIconBtn icon="mdi-close" rounded="0" @click="localSelected = []" />
          </VCol>
        </VRow>
      </template>
    </ControlOrganismDisplayValue>

    <ControlOrganismOneView
      v-else
      :id="localSelected[0]!.id"
      can-certified
      :cancel="() => (edit = false)"
      :after-success="
        () => {
          edit = false
          controlOrganismAllViewRef?.reload()
          //localSelected[0] = {}
        }
      "
    >
      <template #title>
        {{ mode == 'edit' ? 'Modifier un organisme de contrôle' : 'Dupliquer un organisme de contrôle' }}
      </template>
    </ControlOrganismOneView>
  </VNavigationDrawer>
</template>
<script setup lang="ts">
import SearchInput from '@/components/SearchInput.vue'
import { type ControlOrganism, makeEmptyControlOrganism } from '@/types/controlOrganism'
import BeneficiaryAllView from '@/views/BeneficiaryAllView.vue'
import { type BeneficiaryFormAction } from '@/views/BeneficiaryForm.vue'
import ControlOrganismAllView from '@/views/controlorganism/ControlOrganismAllView.vue'
import ControlOrganismOneView from '@/views/controlorganism/ControlOrganismOneView.vue'
import { VCol, VDialog, VNavigationDrawer, VRow } from 'vuetify/components'
import { useUserStore } from '@/stores/user'

const controlOrganismAllViewRef = ref<typeof BeneficiaryAllView | null>(null)
const localSelected = ref<ControlOrganism[]>([])
const searchBeneficiary = ref<string>('')
const edit = ref(false)
const editControlOrganism = ref<ControlOrganism | null>(null)
const mode = ref<BeneficiaryFormAction>()

const duplicate = () => {
  mode.value = 'duplicate'
  editControlOrganism.value = {
    ...localSelected.value[0],
    certified: false,
  }
  edit.value = true
}
const userStore = useUserStore()
const createControlOrganismDialog = ref(false)
</script>
