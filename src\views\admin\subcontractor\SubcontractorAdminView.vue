<template>
  <NjPage title expend-body v-bind="$attrs">
    <template #subtitle>
      <VRow>
        <VCol>
          <SearchInput v-model="searchBeneficiary" />
        </VCol>
        <VCol class="flex-grow-0">
          <NjBtn
            @click="
              () => {
                editSubcontractor = makeEmptySubcontractor()
                createBeneficiaryDialog = true
              }
            "
          >
            Créer
          </NjBtn>
          <VDialog v-model="createBeneficiaryDialog" height="100%" width="50%">
            <SubcontractorForm
              can-certified
              :beneficiary="editSubcontractor!"
              mode="create"
              :cancel="() => (createBeneficiaryDialog = false)"
              :after-success="
                () => {
                  createBeneficiaryDialog = false
                  subcontractorAllViewRef?.reload()
                }
              "
            />
          </VDialog>
        </VCol>
      </VRow>
    </template>
    <template #body>
      <SubcontractorAllView
        ref="subcontractorAllViewRef"
        v-model:selections="localSelected"
        v-model:loading="beneficiaryLoading"
        :search="searchBeneficiary"
        fixed
        class="w-100"
      />
    </template>
  </NjPage>

  <VNavigationDrawer location="end" :model-value="!!localSelected[0]" width="600" disable-resize-watcher>
    <SubcontractorDisplayValue v-if="!edit" :model-value="localSelected[0]" with-title with-history>
      <template #title>
        <VRow dense>
          <VCol align-self="center"> Détail </VCol>
          <VCol class="flex-grow-0">
            <NjIconBtn
              icon="mdi-pencil"
              color="primary"
              rounded="0"
              @click="
                () => {
                  edit = true
                  mode = 'edit'
                  editSubcontractor = localSelected[0]
                }
              "
            />
          </VCol>
          <VCol class="flex-grow-0">
            <NjIconBtn
              icon="mdi-file-multiple"
              color="primary"
              style="margin-inline: 4px"
              rounded="0"
              @click="duplicateBeneficiary"
            />
          </VCol>

          <VCol class="flex-grow-0">
            <NjIconBtn icon="mdi-close" rounded="0" @click="localSelected = []" />
          </VCol>
        </VRow>
      </template>
    </SubcontractorDisplayValue>

    <SubcontractorForm
      v-else
      can-certified
      :subcontractor="editSubcontractor!"
      :mode="mode"
      :cancel="() => (edit = false)"
      :after-success="
        (subcontractor) => {
          edit = false
          subcontractorAllViewRef?.reload()
          localSelected[0] = { ...subcontractor }
        }
      "
    />
  </VNavigationDrawer>
</template>
<script setup lang="ts">
import SearchInput from '@/components/SearchInput.vue'
import { type Subcontractor, makeEmptySubcontractor } from '@/types/subcontractor'
import SubcontractorAllView from '@/views/SubcontractorAllView.vue'
import SubcontractorForm from '@/views/SubcontractorForm.vue'
import { VCol, VDialog, VNavigationDrawer, VRow } from 'vuetify/components'

const subcontractorAllViewRef = ref<typeof SubcontractorAllView | null>(null)
const beneficiaryLoading = ref(false)
const localSelected = ref<Subcontractor[]>([])
const searchBeneficiary = ref<string>('')
const edit = ref(false)
const editSubcontractor = ref<Subcontractor | null>(null)
const mode = ref<string>('')

const duplicateBeneficiary = () => {
  mode.value = 'duplicate'
  editSubcontractor.value = {
    ...localSelected.value[0],
    certified: false,
  }
  edit.value = true
}

const createBeneficiaryDialog = ref(false)
</script>
