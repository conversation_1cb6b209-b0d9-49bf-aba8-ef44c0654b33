<template>
  <VRow class="flex-column h-100">
    <VCol v-if="data.error" class="d-flex align-center">
      <ErrorAlert :message="data.error" />
    </VCol>
    <VCol>
      <NjDataTable
        :headers="headers"
        :selections="selections"
        :pageable="pageable"
        :page="data.value!"
        fixed
        hide-total
        @update:selections="updateSelections"
        @update:pageable="handleUpdatePageable"
      />
    </VCol>
  </VRow>
</template>

<script setup lang="ts">
import { coOwnerShipSyndicateApi } from '@/api/external/gtf/gtfEdgCoOwnerShipSyndicate'
import type { DataTableHeader } from '@/components/okta/NjDataTable.type'
import type { Address } from '@/types/address'
import type { CoOwnerShipSyndicate } from '@/types/coOwnerShipSyndicate'
import { usePagination, type Pageable } from '@/types/pagination'
import type { PropType } from 'vue'

const selections = defineModel<CoOwnerShipSyndicate[]>('selections', { default: () => [] })

const props = defineProps({
  address: {
    type: Object as PropType<Address>,
    required: false,
  },
})

const emit = defineEmits(['update:loading'])

const updateSelections = (value: CoOwnerShipSyndicate[]) => {
  selections.value = value
}

const { data, pageable, updatePageable, debouncedUpdateFilter } = usePagination<CoOwnerShipSyndicate>(
  (filter, pageable) =>
    coOwnerShipSyndicateApi.getAllSyndicates(
      {
        ...filter,
        address: {
          street: props.address?.street || '',
          postalCode: props.address?.postalCode || '',
          city: props.address?.city || '',
          country: props.address?.country ?? null,
        },
      },
      pageable
    ),
  {},
  {
    page: 0,
    size: 20,
    sort: [],
  },
  {
    filterDebounceMilliseconds: 3000,
  }
)

const handleUpdatePageable = (newPageable: Pageable) => {
  const updatedSort = newPageable.sort?.length ? newPageable.sort : (pageable.value?.sort ?? [])
  updatePageable({
    ...pageable.value,
    ...newPageable,
    sort: updatedSort,
  })
}

const headers: DataTableHeader[] = [
  {
    title: "Numéro d'immatriculation",
    value: 'registrationNumber',
    sortable: true,
  },
  {
    title: 'Nom',
    value: 'usageName',
    sortable: true,
  },
  {
    title: 'Numéro et Voie',
    value: 'address.street',
    sortable: true,
  },
  {
    title: 'Code postal',
    value: 'address.postalCode',
    sortable: true,
  },
  {
    title: 'Commune',
    value: 'address.city',
    sortable: true,
  },
]

watch(
  () => data.value.loading,
  (v) => {
    emit('update:loading', v)
  },
  {
    immediate: true,
  }
)

watch(
  () => props.address,
  (v) => {
    debouncedUpdateFilter(Object.assign({}, v ?? {}))
  },
  { immediate: true, deep: true }
)
</script>
