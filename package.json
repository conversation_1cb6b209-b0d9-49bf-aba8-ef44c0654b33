{"name": "portail-cee", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "verify": "run-p lint type-check", "verifyfix": "run-p lintfix type-check", "verify-gitlab": "run-p lint", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build --force", "lint": "eslint  --format gitlab .  --no-fix --max-warnings 0", "lintfix": "eslint . --fix --max-warnings 0", "format": "prettier --write src/"}, "dependencies": {"@mdi/font": "7.4.47", "@okta/okta-auth-js": "^7.9.0", "@okta/okta-vue": "^5.7.0", "@tiptap/core": "^2.10.3", "@tiptap/extension-blockquote": "^2.10.3", "@tiptap/extension-color": "^2.10.3", "@tiptap/extension-font-family": "^2.10.3", "@tiptap/extension-text-style": "^2.10.3", "@tiptap/extension-underline": "^2.10.3", "@tiptap/pm": "^2.10.3", "@tiptap/starter-kit": "^2.10.3", "@tiptap/vue-3": "^2.10.3", "@vueuse/core": "^12.0.0", "@vueuse/integrations": "^12.0.0", "awesome-phonenumber": "^7.2.0", "axios": "^1.7.9", "date-fns": "^4.1.0", "dompurify": "^3.2.3", "echarts": "^5.6.0", "fparser": "^2.1.0", "gsap": "^3.12.7", "i18n-iso-countries": "^7.13.0", "lodash": "^4.17.21", "pinia": "^2.3.0", "qs": "^6.13.1", "roboto-fontface": "^0.10.0", "sortablejs": "^1.15.6", "vue": "^3.5.13", "vue-echarts": "^7.0.3", "vue-router": "^4.5.0", "vuetify": "^3.7.5"}, "devDependencies": {"@tsconfig/node18": "^18.2.4", "@types/lodash": "^4.17.13", "@types/node": "^18.19.67", "@types/qs": "^6.9.17", "@types/sortablejs": "^1.15.8", "@vitejs/plugin-vue": "^5.2.1", "@vue/eslint-config-prettier": "^10.1.0", "@vue/eslint-config-typescript": "^14.1.4", "@vue/tsconfig": "^0.7.0", "eslint": "^9.16.0", "eslint-config-prettier": "^10.0.1", "eslint-formatter-gitlab": "^5.1.0", "eslint-plugin-vue": "^9.32.0", "npm-run-all2": "^7.0.1", "prettier": "^3.4.2", "sass": "1.77.8", "sass-embedded": "1.77.8", "typescript": "~5.4.5", "unplugin-auto-import": "^0.18.6", "unplugin-fonts": "^1.3.1", "unplugin-vue-components": "^0.27.5", "vite": "^5.4.11", "vite-plugin-vue-devtools": "^7.6.7", "vite-plugin-vuetify": "^2.0.4", "vue-tsc": "^2.1.10"}, "engines": {"node": "^18.0.0", "pnpm": ">=7.0.0 <9.0.0"}}