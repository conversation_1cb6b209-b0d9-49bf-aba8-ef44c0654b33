<template>
  <div>
    <VCard elevation="0" border>
      <VCardTitle>RemoteAutoComplete</VCardTitle>
      <RemoteAutoComplete
        v-model="emmyParameterId"
        label="Label"
        :query-for-one="findOneEmmyParamerer"
        :query-for-all="findAllEmmyParamerer"
        item-title="label"
        item-value="id"
      />

      <div>emmyParameterId : {{ emmyParameterId }}</div>
    </VCard>

    <VCard elevation="0" border>
      <VCardTitle>RemoteAutoComplete with value</VCardTitle>
      <RemoteAutoComplete
        v-model="preFilledEmmyParameterId"
        label="Label"
        :query-for-one="findOneEmmyParamerer"
        :query-for-all="findAllEmmyParamerer"
        item-title="label"
        item-value="id"
      />

      <div>emmyParameterId : {{ preFilledEmmyParameterId }}</div>
    </VCard>

    <VCard elevation="0" border>
      <VCardTitle>RemoteAutoComplete with infinite scroll</VCardTitle>
      <RemoteAutoComplete
        v-model="emmyParameterId"
        label="Label"
        :query-for-one="findOneEmmyParamerer"
        :query-for-all="findAllPagedEmmyParamerer"
        item-title="label"
        item-value="id"
        infinite-scroll
      />

      <div>emmyParameterId : {{ emmyParameterId }}</div>
    </VCard>
  </div>
</template>

<script setup lang="ts">
import { emmyParameterApi } from '@/api/emmyParameter'
import RemoteAutoComplete from '@/components/RemoteAutoComplete.vue'
import type { Pageable } from '@/types/pagination'

const emmyParameterId = ref<number>()
const preFilledEmmyParameterId = ref<number>(3)
const findOneEmmyParamerer = (v: unknown) => emmyParameterApi.findOne(v as number)
const findAllEmmyParamerer = (v: string) => emmyParameterApi.findAll({ label: v }, { size: 20 })
const findAllPagedEmmyParamerer = (v: string, pageable: Pageable) => emmyParameterApi.findAll({ label: v }, pageable)
</script>
