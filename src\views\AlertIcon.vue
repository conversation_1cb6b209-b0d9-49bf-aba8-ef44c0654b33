<template>
  <VTooltip>
    <template #activator="{ props }">
      <VIcon v-if="showIcon" v-bind="props" class="ms-2" color="#FF8C47"> mdi-alert </VIcon>
    </template>
    <template v-for="(rule, index) in notRespectedRule" :key="index">
      <p>{{ rule }}</p>
    </template>
  </VTooltip>
</template>
<script setup lang="ts">
import type { PropType } from 'vue'
import { VIcon, VTooltip } from 'vuetify/components'

const props = defineProps({
  rules: Array as PropType<(true | string | undefined)[]>,
})

const notRespectedRule = computed(() => props.rules?.filter((item) => item != true) ?? [])

const showIcon = computed(() => !!(notRespectedRule.value.length ?? 0))
</script>
