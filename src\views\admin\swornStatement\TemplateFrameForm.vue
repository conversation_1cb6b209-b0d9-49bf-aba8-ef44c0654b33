<template>
  <VForm ref="formRef">
    <VRow class="flex-column" dense>
      <VCol>
        <VTextField v-model="mutableSwornStatementTemplateFrame!.name" label="Nom" :rules="[requiredRule]" />
      </VCol>
      <VCol>
        <NjDisplayValue label="Modèle">
          <template #value>
            <template v-if="templateFile">
              {{ templateFile.name }}
            </template>
            <template v-else-if="mutableSwornStatementTemplateFrame?.template.id">
              <VRow>
                <VCol align-self="center">
                  {{ mutableSwornStatementTemplateFrame.template.originalFilename }}
                </VCol>
                <VCol class="flex-grow-0">
                  <NjIconBtn
                    color="primary"
                    rounded="0"
                    icon="mdi-delete"
                    @click="mutableSwornStatementTemplateFrame.template.id = 0"
                  />
                </VCol>
              </VRow>
            </template>
            <VRow v-else class="flex-column" dense>
              <VCol>
                <NjFileInput
                  @new-files="
                    (v) => {
                      templateFile = v?.[0] ?? undefined
                    }
                  "
                />
                <VInput :model-value="templateFile ?? ''" :rules="[requiredRule]"></VInput>
              </VCol>
            </VRow>
          </template>
        </NjDisplayValue>
      </VCol>
      <VDivider />
      <VCol>
        <OperationTargetRuleField
          :id="mutableSwornStatementTemplateFrame!.id"
          v-model="mutableSwornStatementTemplateFrame!.operationTargetRule"
          @update:model-value="
            (v) => {
              if (v?.bonusType !== 'PRECARIOUSNESS') {
                mutableSwornStatementTemplateFrame!.precariousnessCase2FrameD = null
              }
            }
          "
        >
          <template #more>
            <VCol v-if="mutableSwornStatementTemplateFrame?.operationTargetRule.bonusType == 'PRECARIOUSNESS'">
              <VSelect
                v-model="mutableSwornStatementTemplateFrame!.precariousnessCase2FrameD"
                label="Précarité cas 2 bis avec QPV ou bailleur sociaux"
                :items="precariousnessCase2FrameDValueLabel"
                clearable
              />
            </VCol>
          </template>
        </OperationTargetRuleField>
      </VCol>
      <VDivider />
      <VCol>
        <ErrorAlert
          type="warning"
          message="Pour utiliser les paramètres des FOS dans le modèle le paramètre ci-dessous doit être activé"
        />
        <NjSwitch
          v-if="mutableSwornStatementTemplateFrame"
          v-model="mutableSwornStatementTemplateFrame.repeatOnOperationLine"
          label="Générer autant de fois le cadre que de ligne dans l'opération"
        />
      </VCol>
      <VCol>
        <ErrorAlert
          type="warning"
          message="Attention, si plusieurs pied de page sont présent lors de la génération des choses bizarres peuvent se produire"
        />
        <NjSwitch
          v-if="mutableSwornStatementTemplateFrame"
          v-model="mutableSwornStatementTemplateFrame.copyFooter"
          label="Copier le pied de page"
        />
      </VCol>
    </VRow>
  </VForm>
</template>
<script setup lang="ts">
import NjFileInput from '@/components/NjFileInput.vue'
import {
  type SwornStatementTemplateFrame,
  precariousnessCase2FrameDValueLabel,
} from '@/types/swornStatementTemplateFrame'
import { cloneDeep } from 'lodash'
import type { PropType } from 'vue'
import { VCol, VForm, VInput, VRow, VSelect, VTextField } from 'vuetify/components'
import { requiredRule } from '@/types/rule'
import OperationTargetRuleField from '@/views/admin/OperationTargetRuleField.vue'
import NjDisplayValue from '@/components/NjDisplayValue.vue'
import NjSwitch from '@/components/NjSwitch.vue'

const props = defineProps({
  modelValue: Object as PropType<SwornStatementTemplateFrame>,
})

const formRef = ref<typeof VForm | null>(null)

const mutableSwornStatementTemplateFrame = ref<SwornStatementTemplateFrame>()
const templateFile = ref<File | null>(null)
watch(
  () => props.modelValue,
  (v) => {
    if (v) {
      mutableSwornStatementTemplateFrame.value = cloneDeep(v)
    } else {
      mutableSwornStatementTemplateFrame.value = makeEmptySwornStatementTemplateFrame()
    }
  },
  {
    immediate: true,
  }
)

defineExpose<{
  form: Ref<typeof VForm | null>
  mutableTemplateFrame: Ref<SwornStatementTemplateFrame | undefined>
  templateFile: Ref<File | null>
}>({
  form: formRef,
  mutableTemplateFrame: mutableSwornStatementTemplateFrame,
  templateFile: templateFile,
})
</script>
