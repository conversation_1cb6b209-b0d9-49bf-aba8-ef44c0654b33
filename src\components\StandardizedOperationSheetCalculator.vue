<template>
  <VRow dense class="flex-column">
    <VCol>
      <VRow dense class="flex-column">
        <template v-if="mode !== 'display'">
          <VCol v-if="sharedParameters.length > 0">
            <VRow class="flex-column">
              <VCol class="text-section-title d-flex align-center"> Paramè<PERSON> communs </VCol>
              <VCol
                v-for="parameter in requiredSharedParameters"
                :key="parameter.localId"
                cols="12"
                :md="mode === 'preview' ? 6 : undefined"
              >
                <StandardizedOperationSheetCalculatorParameterInput
                  :parameter-formula="parameter"
                  :model-value="values[0][parameter.id]"
                  required
                  @update:model-value="updateSharedValue(parameter.id, $event)"
                />
              </VCol>

              <VCol v-for="parameter in optionalSharedParameters" :key="parameter.localId">
                <StandardizedOperationSheetCalculatorParameterInput
                  recommended
                  :parameter-formula="parameter"
                  :model-value="values[0][parameter.id]"
                  @update:model-value="updateSharedValue(parameter.id, $event)"
                />
              </VCol>
            </VRow>
          </VCol>
          <VCol v-for="(it, iOperationLine) in values" :key="iOperationLine" class="sos-calculator__line">
            <VRow class="flex-column">
              <VCol
                v-if="props.standardizedOperationSheet.multipleOperation"
                class="text-section-title d-flex align-center"
              >
                Opération n°{{ iOperationLine + 1 }}
                <VSpacer />
                <NjIconBtn
                  v-if="values.length > 1"
                  icon="mdi-delete"
                  color="primary"
                  @click="deleteOperationLine(iOperationLine)"
                />
                <NjBtn variant="outlined" @click="addOperationLine">Ajouter une ligne</NjBtn>
              </VCol>
              <VCol v-show="(userStore.isSiege || userStore.hasRole('AGENCE_PLUS')) && mode !== 'preview'">
                <NjTextField
                  label="Commentaire"
                  prepend-inner-icon="mdi-comment-outline"
                  :model-value="it['comment']"
                  :rules="[maxSizeTextRuleGenerator(250)]"
                  counter="250"
                  @update:model-value="updateValue(iOperationLine, 'comment', $event)"
                >
                </NjTextField>
              </VCol>
              <VCol
                v-for="(parameter, index) in requiredPreviewParameters[iOperationLine]"
                :key="index"
                cols="12"
                :md="mode === 'preview' ? 6 : undefined"
              >
                <StandardizedOperationSheetCalculatorParameterInput
                  :parameter-formula="parameter"
                  :model-value="it[parameter.id]"
                  required
                  @update:model-value="updateValue(iOperationLine, parameter.id, $event)"
                />
              </VCol>

              <VCol v-for="parameter in optionalPreviewParameters[iOperationLine]" :key="parameter.localId">
                <StandardizedOperationSheetCalculatorParameterInput
                  recommended
                  :parameter-formula="parameter"
                  :model-value="it[parameter.id]"
                  @update:model-value="updateValue(iOperationLine, parameter.id, $event)"
                />
              </VCol>

              <!-- eslint-disable-next-line vue/valid-v-for-->
              <VCol v-for="cp in computedParameters" v-show="it[cp.id]">
                <NjDisplayValue :label="cp.label" align="end" :value="it[cp.id] + (cp.suffix ? ' ' + cp.suffix : '')" />
              </VCol>
              <VCol v-if="standardizedOperationSheet.multipleOperation" cols="12">
                <NjDisplayValue label="Volume CEE" align="end">
                  <template #value>
                    <span v-show="!volumeCEE[iOperationLine].error" class="text-primary nj-display-value__value">{{
                      formatNumber(volumeCEE[iOperationLine].value!) + ' kWhc'
                    }}</span>
                    <span
                      v-show="
                        volumeCEE[iOperationLine].error &&
                        !volumeCEE[iOperationLine].error?.startsWith('Il manque des champs à remplir')
                      "
                      class="text-error nj-display-value__value"
                    >
                      {{ volumeCEE[iOperationLine].error }}
                    </span>
                  </template>
                </NjDisplayValue>
              </VCol>
            </VRow>
          </VCol>
        </template>
        <template v-else>
          <VCol v-if="sharedParameters.length > 0">
            <VRow class="flex-column" dense>
              <VCol class="d-flex align-center"> Paramètres communs </VCol>
              <VCol v-for="parameter in requiredSharedParameters" :key="parameter.localId" cols="12">
                <NjDisplayValue
                  :label="parameter.label"
                  :value="
                    values[0][parameter.id] == undefined
                      ? 'Non défini'
                      : values[0][parameter.id] + (parameter.suffix ? ' ' + parameter.suffix : '')
                  "
                />
              </VCol>
              <VCol v-if="optionalSharedParameters.length > 0">
                <div class="d-flex gap-4 flex-column optional-section pa-4">
                  <div class="optional-legend">
                    Les champs suivants sont optionnels mais vous permettent de
                    <strong>générer plus rapidement l’AH</strong>.
                  </div>
                  <div
                    v-for="parameter in optionalSharedParameters"
                    :key="parameter.localId"
                    :class="{ 'non-edit-mode': mode === 'display' }"
                  >
                    <NjDisplayValue
                      :label="parameter.label"
                      color-value="discovery"
                      :value="
                        values[0][parameter.id] == undefined
                          ? '-'
                          : values[0][parameter.id] + (parameter.suffix ? ' ' + parameter.suffix : '')
                      "
                    />
                  </div>
                </div>
              </VCol>
            </VRow>
          </VCol>
          <VCol v-for="(operationLine, iOperationLine) in values" :key="iOperationLine">
            <VDivider v-if="iOperationLine > 0" />
            <VRow class="flex-column" dense>
              <VCol v-if="standardizedOperationSheet.multipleOperation"> Opération n°{{ iOperationLine + 1 }} </VCol>
              <VCol
                v-show="
                  (userStore.isSiege || userStore.hasRole('AGENCE_PLUS')) &&
                  operationLine['comment']?.toLocaleString().length > 0
                "
              >
                <NjDisplayValue label="Commentaire" :value="operationLine['comment']"> </NjDisplayValue>
              </VCol>
              <VCol v-for="(parameter, index) in requiredPreviewParameters[iOperationLine]" :key="index">
                <StandardizedOperationSheetCalculatorParameterDisplayValue
                  :parameter-formula="parameter"
                  :value="operationLine[parameter.id]"
                />
              </VCol>
              <VCol v-if="optionalPreviewParameters[iOperationLine].length > 0">
                <div class="d-flex gap-4 flex-column optional-section pa-4">
                  <div class="optional-legend">
                    Les champs suivants sont optionnels mais vous permettent de
                    <strong>générer plus rapidement l’AH</strong>.
                  </div>
                  <div v-for="(parameter, index) in optionalPreviewParameters[iOperationLine]" :key="index">
                    <div :class="{ 'non-edit-mode': mode === 'display' }">
                      <StandardizedOperationSheetCalculatorParameterDisplayValue
                        :parameter-formula="parameter"
                        :value="
                          operationLine[parameter.id] !== undefined && operationLine[parameter.id] !== null
                            ? operationLine[parameter.id]
                            : '-'
                        "
                        color-value="discovery"
                      />
                    </div>
                  </div>
                </div>
              </VCol>
              <VCol v-if="standardizedOperationSheet.multipleOperation">
                <NjDisplayValue label="Volume CEE" align="end">
                  <template #value>
                    <span
                      v-show="!volumeCEE[iOperationLine].error"
                      class="text-primary nj-display-value__value"
                      style="font-size: 1rem"
                      >{{ formatNumber(volumeCEE[iOperationLine].value!) + ' kWhc' }}</span
                    >
                    <span
                      v-show="volumeCEE[iOperationLine].error"
                      class="text-error nj-display-value__value"
                      style="font-size: 1rem"
                      >{{ volumeCEE[iOperationLine].error }}</span
                    >
                  </template>
                </NjDisplayValue>
              </VCol>
            </VRow>
          </VCol>
        </template>
      </VRow>
    </VCol>

    <VCol v-if="mode === 'preview'">
      <VRow dense>
        <VCol>
          <NjDisplayValue align="end" label="Zone climatique" :value="predefinedValues['zone_climatique']" />
        </VCol>
        <VCol cols="12">
          <NjDisplayValue
            :label="standardizedOperationSheet.multipleOperation ? 'Volume CEE' : 'Volume CEE Total'"
            align="end"
          >
            <template #value>
              <span
                v-show="!volumeCEETotal.error"
                class="text-primary nj-display-value__value"
                style="font-size: 1rem"
                >{{ formatNumber(volumeCEETotal.value ?? 0) + ' kWhc' }}</span
              >
              <span v-show="volumeCEETotal.error" class="text-error nj-display-value__value" style="font-size: 1rem">
                {{ volumeCEETotal.error }}
              </span>
            </template>
          </NjDisplayValue>
        </VCol>
      </VRow>
    </VCol>
    <template v-else>
      <VCol>
        <VRow class="flex-column" dense>
          <VCol>
            <NjDisplayValue
              v-if="predefinedValues['zone_climatique']"
              label="Zone climatique"
              :value="predefinedValues['zone_climatique']"
            />
            <NjDisplayValue v-else align="end" label="Zone climatique">
              <template #value>
                <span class="text-error font-weight-bold"> Veuillez saisir le code postal </span>
              </template>
            </NjDisplayValue>
          </VCol>
          <VCol>
            <NjDisplayValue
              :label="standardizedOperationSheet.multipleOperation ? 'Total CEE en kWhc' : 'Total CEE'"
              align="end"
            >
              <template #value>
                <!-- id, value, valueOperationLineCumac pour facilité le scrapping pour extraire issuedFromStandardizedOperationSheetCumac peut être supprimer -->
                <span
                  v-show="!volumeCEETotal.error"
                  id="issuedFromStandardizedOperationSheetCumac"
                  class="text-primary nj-display-value__value"
                  :value="volumeCEETotal.value"
                  :valueOperationLineCumac="volumeCEE.map((i) => i.value)"
                  >{{ formatNumber(volumeCEETotal.value!) + ' kWhc' }}</span
                >
                <span
                  v-show="
                    volumeCEETotal.error &&
                    isEmpty(volumeCEE.filter((el) => el.error?.startsWith('Il manque des champs à remplir')))
                  "
                  class="text-error nj-display-value__value"
                  >{{ volumeCEETotal.error }}</span
                >
              </template>
            </NjDisplayValue>
          </VCol>
        </VRow>
      </VCol>
    </template>
  </VRow>
</template>

<script setup lang="ts">
import { useUserStore } from '@/stores/user'
import { convertChoiceToIndex, evaluateFormula, parsingFormula } from '@/types/calcul/formula'
import { generateMappingTableCombinaisons, type MappingTable } from '@/types/calcul/mappingTable'
import {
  predefinedParameters,
  standardizedOperationSheetPredefinedParameters,
  type ParameterFormula,
} from '@/types/calcul/parameterFormula'
import type { StandardizedOperationSheet } from '@/types/calcul/standardizedOperationSheet'
import { formatNumber } from '@/types/format'
import type { ParameterValues } from '@/types/operation'
import type { PromisableValue } from '@/types/promisableValue'
import { maxSizeTextRuleGenerator } from '@/types/rule'
import { sumCumacOperationLines } from '@/views/operationComposition'
import { cloneDeep, isEmpty, isEqual, isNaN, max, reduce } from 'lodash'
import type { PropType, Ref } from 'vue'
import NjDisplayValue from './NjDisplayValue.vue'
import NjIconBtn from './NjIconBtn.vue'
import StandardizedOperationSheetCalculatorParameterInput from './StandardizedOperationSheetCalculatorParameterInput.vue'
import StandardizedOperationSheetCalculatorParameterDisplayValue from './StandardizedOperationSheetCalculatorParameterDisplayValue.vue'

const props = defineProps({
  standardizedOperationSheet: {
    type: Object as PropType<StandardizedOperationSheet>,
    required: true,
  },
  predefinedValues: {
    type: Object,
    default: () => ({}),
  },
  modelValue: {
    type: Array as PropType<any[]>,
    required: true,
  },
  mode: {
    type: String as PropType<'preview' | 'edit' | 'display'>,
    required: true,
  },
  // displayableAlert: {
  //   type: Boolean,
  // },
  // coupDePouce: {
  //   type: Object as PropType<CoupDePouceBonification>,
  // },
  // coupDePouceParameters: {
  //   type: Object,
  // },
  // displayCoupDePouce: {
  //   type: Boolean,
  //   default: false,
  // },
  // commitmentDate: {
  //   type: String as PropType<LocalDate>,
  // },
})

const emit = defineEmits<{
  (e: 'update:model-value', values: ParameterValues[]): void
  (e: 'update:cumac', value: number[]): void
}>()

const values = ref<ParameterValues[]>([])

watch(
  () => props.modelValue,
  (v) => {
    if (!isEqual(v, values.value)) {
      values.value = cloneDeep(v)
    }
  },
  {
    immediate: true,
    deep: true,
  }
)

// const epcBonusApplicated = ref(!!props.simulation.epcBonusParameterValues)

// const seuilRentabilite = ref(emptyValue<AdminConfiguration>())

function updateValue(index: number, column: string, value: string | number) {
  values.value[index] = { ...values.value[index], [column]: value }

  emit('update:model-value', values.value)
}

function updateSharedValue(column: string, value: string | number) {
  values.value.forEach((it, index) => {
    values.value[index][column] = value
  })
  emit('update:model-value', values.value)
}
// Formula Parsing
const formulaObject: Ref<PromisableValue<any>> = ref(emptyValue())
// const onFormulaUpdate = (v: string) => {
//   evaluationFormula.value.loading = true
//   evaluationFormula.value.error = undefined
//   debounceFormulaUpdate(v)
// }
// const debounceFormulaUpdate = debounce((v: string) => {
//   evaluationFormula.value = parsingFormula(v)
//   if (evaluationFormula.value.value) {
//     formulaObject.value = evaluationFormula.value.value
//   }
// }, 300)
// watch(
//   () => props.ficheOperation,
//   (v) => {
//     // if (!seuilRentabilite.value.value) {
//     //   handleAxiosPromise(seuilRentabilite, adminconfigurationApi.getOne('Seuil de rentabilité'))
//     // }
//   },
//   {
//     immediate: true,
//   }
// )

// MappingTable
const combinaisonsValues = ref<Record<string, string[][]>>({})
const filteredParametersForMappingTable = computed(() => {
  return predefinedParameters.concat(props.standardizedOperationSheet.parameters).filter((it) => it.type === 'CHOICE')
})
watch(
  () => props.standardizedOperationSheet.mappingTables,
  (v, oldV) => {
    console.debug('watch combinaisons')
    const n = max([v.length, oldV?.length]) ?? 0
    for (let i = 0; i < n; i++) {
      // TODO chercher à optimiser
      // if (!isEqual(v[i], oldV?.[i])) {
      updateCacheCombinaisons(v[i])
      // }
    }
  },
  {
    immediate: true,
    deep: true,
  }
)

function updateCacheCombinaisons(mappingTable: MappingTable) {
  try {
    combinaisonsValues.value[mappingTable.id] = generateMappingTableCombinaisons(
      mappingTable,
      filteredParametersForMappingTable.value
    )
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
  } catch (e) {
    // noop
  }
}

// Compute kWh Cumac
// Preview
// const paramPreview = reactive<any>({})
const previewParameters = computed((): ParameterFormula[][] => {
  // console.debug('compute previewParameters')
  let parameters = props.mode === 'preview' ? standardizedOperationSheetPredefinedParameters.concat() : []
  parameters = parameters.concat(props.standardizedOperationSheet.parameters)
  const choicesConvertedValues = values.value.map((temp, iOperationLine) => {
    return convertChoiceToIndex(parameters, props.modelValue[iOperationLine])
  })
  return choicesConvertedValues.map((temp) => {
    return parameters.filter(
      (p) =>
        !p.sharedParameter &&
        !p.computedFormula &&
        (!p.conditionalFormula ||
          (!fetchConditionalParameterFormulas(p).error &&
            evaluateFormula(conditionalParameterFormulas.value[p.id].value, temp, choicesConvertedValues).value))
    )
  })
})

const requiredPreviewParameters = computed(() => {
  return previewParameters.value.map((it) => it.filter((it) => !it.optional))
})

const optionalPreviewParameters = computed((): ParameterFormula[][] => {
  return previewParameters.value.map((it) => it.filter((it) => it.optional))
})
// Fonction créé car le timing entre les computed et les watch ont changé, et il faut que l'on puisse s'adapter a ce changement d'ordre qui ne sera plus jamais garanti
const fetchConditionalParameterFormulas = (p: ParameterFormula) => {
  if (!conditionalParameterFormulas.value[p.id]) {
    conditionalParameterFormulas.value[p.id] = parsingFormula(p.conditionalFormula)
  }

  return conditionalParameterFormulas.value[p.id].value
}
const sharedParameters = computed((): ParameterFormula[] => {
  // console.debug('compute sharedParameters')
  let parameters: ParameterFormula[] = []
  parameters = parameters.concat(props.standardizedOperationSheet.parameters)
  return parameters.filter(
    (p) =>
      p.sharedParameter &&
      (!p.conditionalFormula ||
        evaluateFormula(fetchConditionalParameterFormulas(p), convertChoiceToIndex(parameters, props.modelValue[0]))
          .value)
  )
})

const requiredSharedParameters = computed((): ParameterFormula[] => {
  return sharedParameters.value.filter((it) => !it.optional)
})
const optionalSharedParameters = computed((): ParameterFormula[] => {
  return sharedParameters.value.filter((it) => it.optional)
})
const computedParameters = computed((): ParameterFormula[] => {
  // console.debug('compute computedParameters')
  let parameters = props.mode === 'preview' ? standardizedOperationSheetPredefinedParameters.concat() : []
  parameters = parameters.concat(props.standardizedOperationSheet.parameters)
  return parameters.filter((p) => p.computedFormula)
})
const volumeCEE = ref<PromisableValue<number>[]>([])
const volumeCEETotal = ref(succeedValue(0))
watch(
  () => props.standardizedOperationSheet.formula,
  (f) => {
    console.debug('watch forula parsing')
    if (f) {
      formulaObject.value = parsingFormula(f)
    } else {
      formulaObject.value = emptyValue()
    }
  },
  {
    immediate: true,
  }
)
const conditionalParameterFormulas: Ref<Record<string, PromisableValue<any>>> = ref({})
const computedParameterFormulas: Ref<Record<string, PromisableValue<any>>> = ref({})
const cacheStandardizedOperationSheetParameters: ParameterFormula[] = []
watch(
  () => props.standardizedOperationSheet.parameters,
  (parameters) => {
    // console.debug('watch standardizedOperationSheet.parameters', parameters, props.standardizedOperationSheet.id, props.standardizedOperationSheet.operationCode)
    parameters.forEach((p, i) => {
      if (!isEqual(p, cacheStandardizedOperationSheetParameters[i])) {
        if (p.conditionalFormula) {
          // console.debug('set conditionalParameterFormulas', p.id)
          conditionalParameterFormulas.value[p.id] = parsingFormula(p.conditionalFormula)
        } else {
          delete conditionalParameterFormulas.value[p.id]
        }

        if (p.computedFormula) {
          computedParameterFormulas.value[p.id] = parsingFormula(p.computedFormula)
        } else {
          delete computedParameterFormulas.value[p.id]
        }
      }
    })
  },
  {
    immediate: true,
    deep: true,
  }
)

const deletedLine = ref(false)
watchEffect(() => {
  console.debug('watch compute computedParameters')
  values
  computedParameterFormulas

  let parameters = props.mode === 'preview' ? standardizedOperationSheetPredefinedParameters.concat() : []
  parameters = parameters.concat(props.standardizedOperationSheet.parameters)

  let hasChange = false // Permet de limiter le nombre de mise à jour et surtout de réactivité de VueJS

  if (deletedLine.value) {
    deletedLine.value = false
    hasChange = true
  }

  // Je ne sais pas pourquoi, mais permet d'optimiser en permettant de ne pas lancer d'exception
  const defaultValues: ParameterValues = reduce(
    parameters.filter((it) => it.type === 'NUMBER'),
    (acc, { id }) => ({ ...acc, [id]: 0 }),
    {}
  )
  const convertedChoicesValues = values.value.map((_, iOperationLine) => ({
    ...defaultValues,
    ...convertChoiceToIndex(parameters, props.modelValue[iOperationLine]),
  }))

  const result = values.value.map((v, iOperationLine) => {
    const value = { ...v }
    props.standardizedOperationSheet.parameters.forEach((p) => {
      if (p.computedFormula && computedParameterFormulas.value[p.id]) {
        const result = evaluateFormula(
          computedParameterFormulas.value[p.id].value,
          convertedChoicesValues[iOperationLine],
          convertedChoicesValues
        )
        if (!result.error) {
          if (p.type === 'CHOICE') {
            const newValue = p.data.split(';')[result.value! - 1]
            if (value[p.id] !== newValue && (!isNaN(value[p.id]) || !isNaN(newValue))) {
              value[p.id] = newValue
              hasChange = true
            }
          } else {
            if (value[p.id] !== result.value! && (!isNaN(value[p.id]) || !isNaN(result.value))) {
              value[p.id] = result.value!
              hasChange = true
            }
          }
        } else {
          logException(result.error)
        }
      }
    })
    return value
  })

  if (hasChange) {
    emit('update:model-value', result)
  }
})

watchEffect(() => {
  values // Juste pour activer la réactivité vuejs
  props.predefinedValues // Juste pour activer la réactivité vuejs
  console.debug('watchEffect')
  const mappingTables = props.standardizedOperationSheet.mappingTables
  const combinaisons = combinaisonsValues.value

  if (formulaObject.value.value && (!isEmpty(combinaisons) || mappingTables.length === 0)) {
    // rajout des valeurs par défaut des valeurs conditionelles
    const defaultOptionalValues: Record<string, number> = {}
    props.standardizedOperationSheet.parameters.forEach((p) => {
      if (p.conditionalFormula) {
        defaultOptionalValues[p.id] = 0
      }
    })
    const valuesCopy = values.value.map((it, iOperationLine) => {
      const v: any = { ...props.predefinedValues, ...defaultOptionalValues }

      previewParameters.value[iOperationLine].forEach((p) => {
        v[p.id] = it[p.id]
      })
      computedParameters.value.forEach((p) => {
        v[p.id] = it[p.id]
      })
      sharedParameters.value.forEach((p) => {
        v[p.id] = it[p.id]
      })

      return v
    })

    // remplissage de valeur de mappingTable
    try {
      mappingTables.forEach((mt) => {
        valuesCopy.forEach((parameterValues) => {
          parameterValues[mt.id] =
            mt.data[
              combinaisons[mt.id].findIndex((it) =>
                isEqual(
                  it,
                  mt.paramColumns.map((it) => parameterValues[it])
                )
              )
            ] ?? 0
        })
      })
    } catch (e) {
      logException(e)
    }

    const convertedChoicesByIndexValues = valuesCopy.map((val, iOperationLine) => {
      return convertChoiceToIndex(previewParameters.value[iOperationLine].concat(predefinedParameters), val)
    })
    volumeCEE.value = convertedChoicesByIndexValues.map((val) =>
      evaluateFormula(formulaObject.value.value, val, convertedChoicesByIndexValues)
    )
    if (volumeCEE.value.every((it) => !it.error)) {
      volumeCEETotal.value = succeedValue(sumCumacOperationLines(volumeCEE.value.map((it) => it.value!)))
      emit(
        'update:cumac',
        volumeCEE.value.map((it) => it.value!)
      )
    } else {
      volumeCEETotal.value = props.standardizedOperationSheet.multipleOperation
        ? errorValue('Erreur dans les calculs')
        : volumeCEE.value[0]
    }
  }
})

watchEffect(() => {
  console.debug('watch special values')
  values
  previewParameters.value.forEach((lineParameters, iLine) => {
    Object.keys(values.value[iLine]).forEach((k) => {
      if (
        !lineParameters.find((it) => it.id === k) &&
        !sharedParameters.value.find((it) => it.id === k) &&
        !computedParameters.value.find((it) => it.id === k) &&
        k !== 'comment'
      ) {
        delete values.value[iLine][k]
      }
    })
  })
})

const addOperationLine = () => {
  const newValue: ParameterValues = {}
  sharedParameters.value.forEach((it) => {
    newValue[it.id] = values.value[0][it.id]
  })
  values.value.push(newValue)
  nextTick(() => {
    const el = document.querySelector('.sos-calculator__line:last-of-type')
    el?.scrollIntoView({
      behavior: 'smooth',
      block: 'center',
    })
    el?.classList.add('sos-calculator__line--focus')
  })
}

const deleteOperationLine = (i: number) => {
  deletedLine.value = true
  values.value.splice(i, 1)
}

const userStore = useUserStore()
</script>

<style scoped>
.non-edit-mode {
  background-color: #f2ecf7;
}
@keyframes get-focus {
  from {
    background-color: rgba(0, 122, 205, 0.3);
  }
  to {
    background-color: initial;
  }
}
.sos-calculator__line--focus {
  transition: 1s all linear;
  animation: get-focus 1s ease-out;
}

.optional-section {
  background-color: #f2ecf7;
}
.optional-legend {
  color: #744299;
}
</style>
