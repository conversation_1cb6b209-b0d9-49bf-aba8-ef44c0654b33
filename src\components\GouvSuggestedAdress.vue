<template>
  <VRow class="flex-column" dense>
    <template v-if="suggestedAddresses.value?.features || suggestedAddresses.loading || suggestedAddresses.error">
      <VCol class="nj-display-value__label"> Suggestions d'adresses: </VCol>
      <VCol v-if="suggestedAddresses.error">
        <ErrorAlert :message="suggestedAddresses.error" />
      </VCol>
      <VCol class="nj-display-value__value">
        <VProgressCircular v-show="suggestedAddresses.loading" indeterminate />
        <template v-if="suggestedAddresses.value?.features.length">
          <VListItem
            v-for="f in suggestedAddresses.value?.features"
            :key="f.properties.id"
            @click="applyingSuggestion(f)"
          >
            {{ f.properties.label }}
          </VListItem>
        </template>
        <template v-else-if="!suggestedAddresses.loading"> Aucune adresse trouvée </template>
      </VCol>
    </template>
  </VRow>
</template>
<script lang="ts" setup>
import { frAdresseGouvApi, type Feature, type FeatureCollection } from '@/api/external/gouv/gouvAdresse'
import { debounce } from 'lodash'

const props = defineProps({
  city: String,
  postalCode: String,
  street: String,
})

const emits = defineEmits<{
  'update:city': [string]
  'update:street': [string]
  'update:postal-code': [string]
}>()

const suggestedAddresses = ref(emptyValue<FeatureCollection>())

const debouncedSuggestAddresses = debounce((street, postalCode, city) => {
  const q = (street + ' ' + city).trim()
  if (q.length >= 3 && q.length <= 200) {
    handleAxiosPromise(suggestedAddresses, frAdresseGouvApi.search(q, 10, postalCode))
  } else {
    suggestedAddresses.value = errorValue(
      "Vous devez spécifier une adresse et/ou une ville pour lancer la recherche d'adresse."
    )
  }
}, 1000)

watch([() => props.street, () => props.postalCode, () => props.city], (v) => {
  console.debug('installtion update', props.street, props.postalCode, props.city, v)
  if (
    (resolveStreet(selectedFeature.value!) != v[0] ||
      selectedFeature.value?.properties.postcode != v[1] ||
      selectedFeature.value?.properties.city != v[2]) &&
    (v[0] || v[2])
  ) {
    suggestedAddresses.value.loading = true
    debouncedSuggestAddresses(v[0], v[1], v[2])
  }
})

const selectedFeature = ref<Feature>()

const resolveStreet = (feature: Feature | undefined): string | undefined => {
  if (feature) {
    return feature.properties.housenumber
      ? feature.properties.housenumber + ' ' + feature.properties.street
      : feature.properties.street
  }
  return undefined
}

const applyingSuggestion = (f: Feature) => {
  selectedFeature.value = f
  emits('update:city', f.properties.city)
  emits('update:postal-code', f.properties.postcode)
  emits('update:street', resolveStreet(f)!)

  suggestedAddresses.value = emptyValue<FeatureCollection>()
}

const displaySuggestion = computed(() => {
  return suggestedAddresses.value.value?.features || suggestedAddresses.value.loading || suggestedAddresses.value.error
})
defineExpose({
  displaySuggestion,
})
</script>
