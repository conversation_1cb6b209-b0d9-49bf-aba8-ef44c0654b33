<template>
  <slot v-for="i in items.value?.content" :key="fetchKey(i)" name="item" :history="i"> </slot>
  <div
    v-if="
      !items.error &&
      (items.loading || items.value === undefined || items.value?.content.length !== items.value?.totalElements)
    "
    v-intersect="onIntersect"
  >
    <slot name="loading">
      <VListItem class="text-center">
        <VProgressCircular indeterminate color="primary" />
      </VListItem>
    </slot>
  </div>
  <slot v-if="!items.loading && items.value?.content.length === 0" name="no-data">
    <VListItem> Aucun élément trouvé. </VListItem>
  </slot>
  <VAlert v-if="items.error" type="error">
    Une erreur est survenue lors du chargement des données.
    <NjBtn :color="'error'" variant="outlined" @click="queryNextPage">Réssayer</NjBtn>
  </VAlert>
  <slot v-if="items.value?.content.length && items.value?.content.length === items.value.totalElements" name="end-data">
  </slot>
</template>

<script setup lang="ts" generic="T">
import { useSnackbarStore } from '@/stores/snackbar'
import type { Page } from '@/types/pagination'
import type { PromisableValue } from '@/types/promisableValue'
import type { AxiosPromise } from 'axios'
import { isString } from 'lodash'

const props = withDefaults(
  defineProps<{
    callback: (page: number) => AxiosPromise<Page<T>>
    // size: number,
    itemId?: string | ((item: T) => unknown)
    // itemId
  }>(),
  {
    itemId: 'id',
  }
)

const snackbarStore = useSnackbarStore()

const fetchKey = computed((): ((item: T) => any) => {
  if (isString(props.itemId)) {
    return (item: T) => (item as any)[props.itemId as string]
  } else {
    return props.itemId
  }
})

const items = ref(emptyValue<Page<T>>()) as Ref<PromisableValue<Page<T>>>

// const currentPage = ref(-1)
const isIntersecting = ref(false)
const queryNextPage = () => {
  // console.debug('queryNextPage', items.value.loading, items.value.value?.number)
  if (items.value.value?.number === 10) {
    return
  }
  if (!items.value.loading) {
    items.value.loading = true
    items.value.error = undefined
    props
      .callback((items.value.value?.number ?? -1) + 1)
      .then((it) => {
        if (items.value.value === undefined) {
          items.value.value = it.data
        } else {
          const oldContent = items.value.value.content
          items.value.value = {
            ...it.data,
            content: [...oldContent, ...it.data.content],
          }
        }
      })
      .catch(async (el) => {
        const error = await handleAxiosException(el)
        snackbarStore.setError(error)
        items.value.error = error
      })
      .finally(() => {
        items.value.loading = false

        nextTick(() => {
          if (isIntersecting.value && items.value.value?.content.length !== items.value.value?.totalElements) {
            queryNextPage()
          }
        })
      })
  }
}

const onIntersect = (
  pIsIntersecting: boolean
  // entries: IntersectionObserverEntry[],
  // observer: IntersectionObserver
) => {
  console.debug('isIntersecting', pIsIntersecting)
  isIntersecting.value = pIsIntersecting
  if (pIsIntersecting) {
    queryNextPage()
  }
}

const clear = () => {
  items.value = emptyValue()
}

defineExpose({
  reload: () => {
    if (!items.value.loading && items.value.value !== undefined) {
      clear()
      queryNextPage()
    }
  },
  clear,
})
</script>
