<template>
  <NjDataTable v-bind="$attrs">
    <template #[`item.operationCode`]="{ item }">
      <span
        :class="{
          'sos-datatable__operation-code--usable-for-res': item.usageBonusIntSheetType === 'USABLE_FOR_RES',
          'sos-datatable__operation-code--usable-for-boiler-room':
            item.usageBonusIntSheetType === 'USABLE_FOR_BOILER_ROOM',
        }"
      >
        {{ item.operationCode }}
      </span>
    </template>
    <template #[`item.action`]="{ item }">
      <VLink
        v-if="item.pdfDocument"
        icon="mdi-open-in-new"
        target="_blank"
        class="me-4"
        @click="downloadPdfDocument(item as StandardizedOperationSheet)"
        @click.stop
        >Fiche Officielle</VLink
      >
      <!-- TODO remplacer le lien par celui de la fiche interne Engie -->
      <VLink
        v-if="item.internalPdfDocument"
        icon="mdi-open-in-new"
        target="_blank"
        @click="downloadInternalPdfDocument(item as StandardizedOperationSheet)"
        @click.stop
        >Fiche interne</VLink
      >
    </template>
    <template #[`item.rgeMandatory`]="{ item }">
      <img v-if="item.rgeMandatory" :src="rgeLogo" style="height: 1.75rem" />
      <div v-else />
    </template>
    <template #[`item.heatFund`]="{ item }">
      <NjBooleanIcon :condition="item.heatFund" />
    </template>
    <template #[`item.certified`]="{ item }">
      <VIcon v-show="item.certified" color="#28B750" icon="mdi-shield-check-outline" />
    </template>
  </NjDataTable>
</template>
<script setup lang="ts">
import rgeLogo from '@/assets/rge-logo/RGE.png'
import type { StandardizedOperationSheet } from '@/types/calcul/standardizedOperationSheet'
import { downloadPdfDocument, downloadInternalPdfDocument } from '@/types/operation'
</script>
<style lang="scss">
.sos-datatable__operation-code--usable-for {
  &-res,
  &-boiler-room {
    padding: 4px;
    margin: -4px;
    border-radius: 4px;
  }
  &-res {
    background-color: #fff9c4;
  }
  &-boiler-room {
    background-color: #b2ebf2;
  }
}
</style>
