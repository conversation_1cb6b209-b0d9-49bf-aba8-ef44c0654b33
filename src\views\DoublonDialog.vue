<template>
  <VDialog v-model="activeDialog" width="90%" height="100%">
    <template #activator="{ props }">
      <slot name="activator" v-bind="props">
        <NjBtn v-bind="props" variant="outlined" @click="activeDialog = true">Doublon/Cumul</NjBtn>
      </slot>
    </template>
    <VCard class="content-layout">
      <VCardTitle class="d-flex align-center content-layout__header">
        <VRow>
          <VCol>
            Recherche doublon/cumul
            <VLink target="_blank" :href="adminConfigurationStore.cumulLink?.data">Lien cumul</VLink>
          </VCol>
          <VCol class="flex-grow-0">
            <NjIconBtn icon="mdi-window-close" variant="flat" @click="activeDialog = false" />
          </VCol>
        </VRow>
      </VCardTitle>
      <VDivider />
      <VCardText class="content-layout__main content-layout overflow-hidden">
        <VRow class="flex-column content-layout content-layout__main">
          <VCol v-if="data.error" class="flex-grow-0">
            <ErrorAlert :message="data.error" />
          </VCol>
          <VCol class="content-layout__header">
            <VRow>
              <VCol>
                <VTextField
                  label="Installation"
                  :model-value="pageFilter.propertySearch"
                  prepend-inner-icon="mdi-magnify"
                  @update:model-value="debounceFilterByFieldname('propertySearch', $event)"
                />
              </VCol>
              <VCol>
                <VTextField
                  label="Bénéficiaire"
                  :model-value="pageFilter.beneficiary"
                  prepend-inner-icon="mdi-magnify"
                  @update:model-value="debounceFilterByFieldname('beneficiary', $event)"
                />
              </VCol>
              <VCol>
                <VTextField
                  label="Code opération"
                  :model-value="pageFilter.standardizedOperationSheetCode"
                  prepend-inner-icon="mdi-magnify"
                  @update:model-value="debounceFilterByFieldname('standardizedOperationSheetCode', $event)"
                />
              </VCol>
              <VCol>
                <NjDatePicker
                  label="Date des travaux"
                  :model-value="pageFilter.endWorkDate"
                  @update:model-value="debounceFilterByFieldname('endWorkDate', $event)"
                />
              </VCol>
              <VCol>
                <VTextField
                  label="Rue"
                  :model-value="pageFilter.street"
                  prepend-inner-icon="mdi-magnify"
                  @update:model-value="debounceFilterByFieldname('street', $event)"
                />
              </VCol>
              <VCol>
                <VTextField
                  label="Code postal ou Ville"
                  :model-value="pageFilter.city"
                  prepend-inner-icon="mdi-magnify"
                  @update:model-value="debounceFilterByFieldname('city', $event)"
                />
              </VCol>
            </VRow>
          </VCol>
          <VCol class="content-layout__main">
            <NjDataTable
              :pageable="pageable"
              :page="data.value!"
              :headers="headers"
              :disabled-row="disabledRow"
              fixed
              :loading="data.loading"
              @update:pageable="updatePageable"
            >
              <template #[`item.stepId`]="{ item }">
                <OperationStepChip :operation="item as Operation" />
              </template>
              <template #[`item.beneficiary`]="{ item }">
                {{ `${item.beneficiary?.firstName} ${item.beneficiary?.lastName}` }}
              </template>
              <template #[`item.finalAddress.street`]="{ item }">
                {{
                  `${item.finalAddress?.street ?? ''} - ${item.finalAddress?.postalCode ?? ''} ${
                    item.finalAddress?.city ?? ''
                  }`
                }}
              </template>
              <template #[`item.commercialStatus`]="{ item }">
                <span class="text-no-wrap">
                  {{ commercialStatusTitle.find((status) => status.value === item.commercialStatus)?.title }}
                </span>
              </template>
            </NjDataTable>
          </VCol>
        </VRow>
        <slot class="content-layout__footer flex-grow-0" name="complements" />
      </VCardText>
      <VDivider />
      <slot class="content-layout__footer" name="actions" />
    </VCard>
  </VDialog>
</template>
<script lang="ts" setup>
import NjDatePicker from '@/components/NjDatePicker.vue'
import type { DataTableHeader } from '@/components/okta/NjDataTable.type'
import NjDataTable from '@/components/okta/NjDataTable.vue'
import { commercialStatusTitle } from '@/types/steps'
import { displayFullnameUser } from '@/types/user'
import { debounce } from 'lodash'
import { useAdminConfigurationStore } from '@/stores/adminConfiguration'
import type { OperationFilter } from '@/api/operation'
import type { OperationDuplicateAccumulationDto } from '@/types/operationDoublonCumulDto'
import { formatHumanReadableLocalDate } from '@/types/date'
import type { Operation } from '@/types/operation'
import OperationStepChip from './operation/OperationStepChip.vue'
import { parseLocalDate } from '@/types/date'
import { addYears, format } from 'date-fns'

const props = defineProps({
  modelValue: Boolean,
})

const emit = defineEmits(['update:model-value'])

const activeDialog = ref(false)

watch(
  () => props.modelValue,
  (v) => {
    if (v != activeDialog.value) {
      activeDialog.value = v
    }
  },
  {
    immediate: true,
  }
)
watch(activeDialog, (v) => {
  emit('update:model-value', v)
})

const defaultFilter = {
  beneficiary: '',
  standardizedOperationSheetCode: '',
  endWorkDate: '',
  street: '',
  city: '',
  propertySearch: '',
}

const { data, pageFilter, pageable, updatePageable, updateFilterByFieldname } = usePagination<
  OperationDuplicateAccumulationDto,
  OperationFilter
>(
  (filter, pageable) => {
    return operationApi.findAllReducedDto(filter, pageable)
  },
  {
    ...defaultFilter,
  },
  {
    page: 0,
    size: 20,
    sort: ['stepId,DESC', 'chronoCode,DESC'],
  },
  {
    lazyLoad: true,
  }
)

const debounceFilterByFieldname = debounce(
  (fieldName: keyof OperationFilter, value: any) => updateFilterByFieldname(fieldName, value),
  300
)

const headers: DataTableHeader[] = [
  {
    title: 'Chrono',
    value: 'chronoCode',
  },
  {
    title: 'Code opération',
    value: 'standardizedOperationSheet.operationCode',
  },
  {
    title: 'Etape',
    value: 'stepId',
  },
  {
    title: 'Installation',
    value: 'property.name',
  },
  {
    title: 'Adresse',
    value: 'finalAddress.street',
  },
  {
    title: 'Nom du client',
    value: 'beneficiary.socialReason',
  },
  {
    title: 'Organisation',
    value: 'entity.name',
  },
  {
    title: 'kWhc class.',
    value: 'classicCumac',
    cellClass: 'text-right justify-end',
    formater: (_, value) => formatNumber(value),
  },
  {
    title: 'kWhc Préca.',
    value: 'precariousnessCumac',
    cellClass: 'text-right justify-end',
    formater: (_, value) => formatNumber(value),
  },
  {
    title: 'Fin Travaux réelle',
    value: 'actualEndWorksDate',
    formater: (_, value) => formatHumanReadableLocalDate(value),
  },
  {
    title: 'Valo. Rés.Class €',
    value: 'reservedClassicValuationValue',
    cellClass: 'text-right justify-end',
    formater: (_, value) => formatNumber(value ?? 0) + ' €/MWhC',
  },
  {
    title: 'Valo. Rés.Préca €',
    value: 'reservedPrecariousnessValuationValue',
    cellClass: 'text-right justify-end',
    formater: (_, value) => formatNumber(value ?? 0) + ' €/MWhC',
  },
  {
    title: 'Mnt. Rés. Class €',
    value: 'reservedClassicAmount',
    sortable: false,
    formater: (item) =>
      formatPriceNumber(((item.reservedClassicValuationValue ?? 0) * item.reservedClassicCumac) / 1000),
    cellClass: 'text-right justify-end',
  },
  {
    title: 'Mnt. Rés. Préca €',
    value: 'reservedPrecariousnessAmount',
    sortable: false,
    formater: (item) =>
      formatPriceNumber(((item.reservedPrecariousnessValuationValue ?? 0) * item.reservedPrecariousnessCumac) / 1000),
    cellClass: 'text-right justify-end',
  },
  {
    title: 'Type Valorisation',
    value: 'valuationType.name',
  },
  {
    title: 'Offre commerciale',
    value: 'commercialStatus',
  },
  {
    title: 'Demandeur',
    value: 'applicantUser',
    formater: (item) => displayFullnameUser(item.applicantUser),
  },
  {
    title: 'Durée de vie conventionnelle',
    value: 'conventionalLifespan',
    sortable: false,
    formater: (item) =>
      item.actualEndWorksDate && item.standardizedOperationSheet.conventionalLifespanInYears
        ? format(
            addYears(
              parseLocalDate(item.actualEndWorksDate),
              item.standardizedOperationSheet.conventionalLifespanInYears
            ),
            'dd/MM/yyyy'
          ) + ''
        : '',
  },
]

const disabledRow = (ope: OperationDuplicateAccumulationDto) =>
  ope.status === 'CANCELLED' || ope.status === 'IMPROPER' || ope.status === 'LOST'

const adminConfigurationStore = useAdminConfigurationStore()
</script>
