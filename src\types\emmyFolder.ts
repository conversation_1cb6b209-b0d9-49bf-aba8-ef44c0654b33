import type { LocalDate, LocalDateTime } from './date'
import type { User } from './user'

export interface EmmyFolder {
  id: number
  name: string
  stepId: number
  emmyCode: string
  pnceeSubmissionDate: LocalDate | null
  pnceeIssuedDate: LocalDate | null
  pnceeClassicIssuedNumber: string | null
  pnceePrecariousnessIssuedNumber: string | null
  creationUser: User
  creationDateTime: LocalDateTime
  updateUser: User
  updateDateTime: LocalDateTime
}

export const makeEmptyEmmyFolder = (): EmmyFolder => ({
  id: 0,
  name: '',
  emmyCode: '',
  creationUser: makeEmptyUser(),
  creationDateTime: '',
  updateUser: makeEmptyUser(),
  updateDateTime: '',
  stepId: 80,
  pnceeSubmissionDate: null,
  pnceeIssuedDate: null,
  pnceeClassicIssuedNumber: null,
  pnceePrecariousnessIssuedNumber: null,
})

export interface EmmyFolderBatchSummary {
  batchId: number
  numberOfOperation: number
  classicCumacSum: number
  precariousnessCumacSum: number
}

export interface EmmyFolderWithSummary {
  emmyFolder: EmmyFolder
  emmyFolderBatchSummaries: EmmyFolderBatchSummary[]
  minActualEndWorks: LocalDate
  maxActualEndWorks: LocalDate
}
