<template>
  <VCard v-bind="$attrs" class="content-layout" :loading="emmyParameter.loading">
    <VCardTitle class="content-layout__header d-flex align-center">
      {{ id === 0 ? 'Nouveau paramètre EMMY' : "Gestion d'un paramètre EMMY" }}
      <VSpacer />
      <template v-if="id">
        <NjIconBtn :active="edit" icon="mdi-pencil" class="rounded-0 me-2" color="primary" @click="edit = !edit" />
        <NjIconBtn icon="mdi-close" class="rounded-0" color="primary" @click="close" />
      </template>
    </VCardTitle>
    <VDivider />
    <VCardText v-if="edit" class="content-layout__main">
      <VForm ref="formRef">
        <VRow class="flex-column">
          <VCol>
            <VTextField v-model="emmyParameter.value!.label" label="Label" :rules="[requiredRule]" />
          </VCol>
          <VCol>
            <VTextField
              v-model="emmyParameter.value!.position"
              type="number"
              label="Position"
              :rules="[requiredRule]"
            />
          </VCol>
          <VCol>
            <VAutocomplete
              v-model="emmyParameter.value!.staticValue"
              label="Valeur statique"
              :items="operationStaticValue"
              :item-value="(e: OperationStaticValue | null) => e"
              :item-title="(e: OperationStaticValue | null) => (e ? operationStaticValueLabel[e].label : '')"
              clearable
            />
          </VCol>
          <VCol>
            <VTextField v-model="emmyParameter.value!.defaultValue" label="Valeur par défaut" />
          </VCol>
          <VCol>
            <VTextField v-model="emmyParameter.value!.referenceText" label="Référence" />
          </VCol>
        </VRow>
        <VRow>
          <VCol>
            <VCheckbox v-model="emmyParameter.value!.required" label="Obligatoire" />
          </VCol>
        </VRow>
        <VRow>
          <VCol>
            <VTextarea v-model="emmyParameter.value!.comment" label="Commentaire" />
          </VCol>
        </VRow>
      </VForm>
    </VCardText>
    <VCardText v-else class="content-layout__main">
      <VRow class="flex-column">
        <VCol>
          <NjDisplayValue :value="emmyParameter.value!.label" label="Label" :rules="[requiredRule]" />
        </VCol>
        <VCol>
          <NjDisplayValue :value="emmyParameter.value!.position" label="Position" />
        </VCol>
        <VCol>
          <NjDisplayValue
            :value="
              emmyParameter.value?.staticValue ? operationStaticValueLabel[emmyParameter.value.staticValue].label : ''
            "
            label="Valeur statique"
          />
        </VCol>
        <VCol>
          <NjDisplayValue :value="emmyParameter.value!.defaultValue" label="Valeur par défaut" />
        </VCol>
        <VCol>
          <NjDisplayValue :value="emmyParameter.value!.referenceText" label="Référence" />
        </VCol>
        <VCol>
          <NjDisplayValue checkbox :value="emmyParameter.value!.required" label="Obligatoire" />
        </VCol>
        <VCol>
          <NjDisplayValue :value="emmyParameter.value!.comment" label="Commentaire" />
        </VCol>
      </VRow>
    </VCardText>
    <VDivider v-if="id && edit" />
    <VCardActions v-if="id && edit" class="content-layout__footer">
      <VSpacer />
      <NjBtn variant="outlined" color="error" :disabled="saving.loading" @click="cancel"> Annuler </NjBtn>
      <NjBtn :loading="saving.loading" @click="save"> Enregistrer </NjBtn>
    </VCardActions>
  </VCard>
  <ConfirmUnsavedDataDialog v-model:unsaved-data-dialog="unsavedDataDialog" @save="save" />
</template>
<script lang="ts" setup>
import { useSnackbarStore } from '@/stores/snackbar'
import {
  makeEmptyEmmyParamter,
  operationStaticValue,
  operationStaticValueLabel,
  type EmmyParameter,
  type OperationStaticValue,
} from '@/types/emmyParameter'
import { requiredRule } from '@/types/rule'
import type { VForm } from 'vuetify/components/VForm'

const props = defineProps({
  id: {
    type: Number,
    default: undefined,
  },
})

const emit = defineEmits<{
  'update:model-value': [boolean]
}>()

const edit = ref(false)

const snackbarStore = useSnackbarStore()

// Refs
const formRef = ref<typeof VForm | null>(null)

const emmyParameter = ref(emptyValue<EmmyParameter>())
const { unsavedDataDialog, failedSave, succeedSave, check } = useUnsavedData(emmyParameter)

const cancel = () => {
  check(() => {
    edit.value = false
    load()
  })
}

const close = () => {
  check(() => {
    emit('update:model-value', false)
  })
}

// Enregistrement
const saving = ref(emptyValue<EmmyParameter>())
const save = async () => {
  if ((await formRef.value!.validate()).valid) {
    if (props.id) {
      handleAxiosPromise(saving, emmyParameterApi.update(props.id, emmyParameter.value.value!), {
        afterSuccess() {
          snackbarStore.setSuccess('Paramètre mis à jour avec succès')
          edit.value = false
          succeedSave()
        },
        afterError: () => {
          failedSave()
          snackbarStore.setError(saving.value.error ?? 'Une erreur est survenue lors de la mise à jour du paramètre')
        },
      })
    } else {
      handleAxiosPromise(
        saving,
        emmyParameterApi.create(emmyParameter.value.value!),
        () => {
          snackbarStore.setSuccess('Paramètre créé avec succès')
          edit.value = false
          succeedSave()
        },
        () => {
          snackbarStore.setError(saving.value.error ?? 'Une erreur est survenue lors de la création du paramètre')
          failedSave()
        }
      )
    }
  }
}

const load = () => {
  emmyParameter.value.value = makeEmptyEmmyParamter()
  formRef.value?.resetValidation()
  if (props.id) {
    emmyParameter.value.loading = true
    handleAxiosPromise(emmyParameter, emmyParameterApi.findOne(props.id), (response) => {
      emmyParameter.value.value = response.data
      succeedSave()
    })
  } else {
    succeedSave()
  }
  edit.value = props.id === 0
}

// chargement
watch(
  () => props.id,
  () => {
    load()
  },
  {
    immediate: true,
  }
)

defineExpose({
  save,
})
</script>
