import type { LocalDateTime } from '@/types/date'
import type { AxiosInstance, AxiosPromise } from 'axios'
import { gtfAxiosInstance } from '.'
import { mapPageableToGtfPageable, type GtfPage as GtfPage, type OrdsQueryParams } from './type'
import type { Pageable } from '@/types/pagination'

const prefixApiUrl = '/orgs/api/v2/'

export interface GtfCompanyDto {
  id: number
  name: string
  short_name: string
  siret: string
  type: string
  address_route_number: string
  address_route: string
  address_postal_code: string
  address_city: string
  address_country: string
  fr_regime_communitaire: string
  capital: number
  enabled: 'O' | 'N'
}
export interface GtfEntityDto {
  id: string
  name: string
  short_name: string
  parent_id: string
  company_id: number
  nav_level: string
  nav_position: string
  nav_full_id: string
  enabled: number
  territory_description?: string
  territory_id?: number
  insee_siret?: string
  address_building?: string
  address_route_number?: string
  address_route?: string
  address_complement?: string
  address_postal_code?: string
  address_city?: string
  address_country?: string
  phone_number?: string
  nature_code?: string
  nature_description?: string

  '@last_update_datetime': LocalDateTime
}

export interface GtfEntityFilter {
  search?: string
  company_id?: number
}

class GtfOrgsApi {
  public constructor(private axios: AxiosInstance) {}

  public findAllEntities(params: OrdsQueryParams<GtfEntityDto>): AxiosPromise<GtfPage<GtfEntityDto>> {
    return this.axios.get(prefixApiUrl + 'entities', {
      params: {
        q: JSON.stringify(params.q),
        limit: params.limit,
        offset: params.offset,
      },
    })
  }

  public getAllEntities(pageable: { offset: number }, filter: GtfEntityFilter): AxiosPromise<GtfPage<GtfEntityDto>> {
    const ordsFilter: OrdsQueryParams<GtfEntityDto> = {
      q: {},
    }
    if (filter.search) {
      ordsFilter.q.name = {
        $instr: filter.search.trim(),
      }
    }
    if (filter.company_id) {
      ordsFilter.q.company_id = filter.company_id
    }
    ordsFilter.q.$or = [
      {
        nav_level: {
          $lte: 4,
        },
      },
      {
        $and: [
          {
            nav_level: {
              $and: [
                {
                  $lte: 6,
                },
                {
                  $gte: 5,
                },
              ],
            },
          },
          {
            enabled: { $eq: 1 },
          },
        ],
      },
    ]
    return this.axios.get(prefixApiUrl + 'entities', {
      params: { q: JSON.stringify(ordsFilter.q), offset: pageable.offset, $orderby: { full_nav_id: 'ASC' } },
    })
  }

  public getAllCompanies(filter: GtfEntityFilter, pageable: Pageable): AxiosPromise<GtfPage<GtfCompanyDto>> {
    const ordsFilter: OrdsQueryParams<GtfCompanyDto> = {
      q: {},
    }
    if (filter.search) {
      ordsFilter.q.$or = [
        {
          name: {
            $instr: filter.search.trim(),
          },
        },
      ]
    }
    return this.axios.get(prefixApiUrl + 'companies', {
      params: { q: JSON.stringify(ordsFilter.q), ...mapPageableToGtfPageable(pageable) },
    })
  }

  public getOneEntity(id: string): AxiosPromise<GtfEntityDto> {
    return this.axios.get(prefixApiUrl + 'entities/' + id)
  }

  public getOneCompany(id: number): AxiosPromise<GtfCompanyDto> {
    return this.axios.get(prefixApiUrl + 'companies/' + id)
  }

  public getCompanyLogo(id: number): AxiosPromise<Blob> {
    return this.axios.get(prefixApiUrl + 'companies/' + id + '/logo', {
      responseType: 'blob',
    })
  }
}

export const gtfOrgsApi = new GtfOrgsApi(gtfAxiosInstance)
