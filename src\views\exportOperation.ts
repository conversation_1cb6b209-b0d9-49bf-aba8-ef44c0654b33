import type { OperationFilter } from '@/api/operation'
import { trace } from '@/stores/analytics'
import { useDialogStore } from '@/stores/dialog'
import { useSnackbarStore } from '@/stores/snackbar'
import { useUserStore } from '@/stores/user'
import type { Operation } from '@/types/operation'
import type { OperationExportResultDto } from '@/types/operationExportResultDto'
import type { Page } from '@/types/pagination'
import type { PromisableValue } from '@/types/promisableValue'
import { formatDate } from '@vueuse/core'
import { isEmpty, omitBy } from 'lodash'

export const useExportOperation = (
  filter: Ref<OperationFilter>,
  selections: Ref<Operation[] | OperationExportResultDto[]>,
  data: Ref<PromisableValue<Page<Operation | OperationExportResultDto>>>,
  simulationsOnly: boolean
) => {
  const dialogStore = useDialogStore()
  const userStore = useUserStore()
  const snackbarStore = useSnackbarStore()
  const route = useRoute()

  const exportOperationsLoading = ref(false)
  const getNumberOfLineToExport = async () => {
    if (selections.value.length) {
      return selections.value.length
    } else if (simulationsOnly) {
      return (await operationApi.findAll({ ...filter.value, withSimulations: true, stepIds: [0] }, { size: 1 })).data
        .totalElements
    } else {
      return data.value.value!.totalElements
    }
  }

  const exportOperations = async () => {
    let exportFilter: OperationFilter = {}

    if (selections.value.length == 0) {
      if (
        await dialogStore.addAlert({
          title: 'Export',
          message:
            "Vous n'avez pas fait de sélection. Vous allez donc exporter toutes les simulation associées à votre filtre",
          positiveButton: 'Confirmer',
          negativeButton: 'Annuler',
          closeable: true,
          maxWidth: '400px',
        })
      ) {
        exportFilter = { ...filter.value }
      } else {
        return
      }
    } else {
      exportFilter = { operationIds: selections.value.map((item) => item.id) }
    }

    if (simulationsOnly) {
      exportFilter = { ...exportFilter, withSimulations: true, stepIds: [0] }
    }

    if (
      !(await dialogStore.addAlert({
        title: 'Téléchargement',
        message: `Attention vous allez télécharger des données sensibles et confidentielles, merci de suivre les règles suivantes :
                    - Ces données ne doivent pas être partagées à l’extérieur de l’entreprise
                    - Après utilisation pensez à supprimer le fichier Excel
                    Merci de noter que tous les téléchargements réalisés sont historisés.

                    Nombre de ligne à télécharger: ${(await getNumberOfLineToExport()).toString()}
                    Le ${formatDate(new Date(), 'DD/MM/YYYY à HH:mm')}
                    Par ${displayFullnameUser(userStore.currentUser)}
                  `,
        positiveButton: 'Télécharger',
        negativeButton: 'Annuler',
        maxWidth: '600px',
      }))
    ) {
      return
    }

    exportOperationsLoading.value = true
    return operationApi
      .export(exportFilter)
      .then((response) => {
        downloadFile('export capte.xlsx', response.data)
        trace('exportOperations', {
          route: {
            name: route.name,
            params: route.params,
            fullpath: route.fullPath,
          },
          filter: omitBy(toRaw(filter.value), (it) => isEmpty(it) || it == null || it === false),
        })
      })
      .catch(async (err) => {
        snackbarStore.setError(await handleAxiosException(err))
        throw err
      })
      .finally(() => {
        exportOperationsLoading.value = false
      })
  }

  return {
    exportOperationsLoading,
    exportOperations,
  }
}
