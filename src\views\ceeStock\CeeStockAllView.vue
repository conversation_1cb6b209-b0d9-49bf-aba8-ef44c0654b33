<template>
  <NjPage title="Suivi DAF">
    <template #header-actions>
      <NjBtn v-if="userStore.hasRole('DAF_SIEGE')" :loading="loadingImport" @click="openCeeStockDialog">Importer</NjBtn>
      <CardDialog v-model="importCeeStockDialog" width="600px" title="Importer les pièces comptables">
        <NjFileInput
          v-if="!accountingEntriesFile"
          @new-files="
            (event) => {
              accountingEntriesFile = event[0]
            }
          "
        />
        <template v-else
          >{{ accountingEntriesFile.name }}
          <NjIconBtn
            :disabled="loadingImport"
            color="primary"
            icon="mdi-delete"
            @click="accountingEntriesFile = null"
          />
        </template>
        <template #actions>
          <NjBtn variant="outlined" @click="cancelImportCeeStockDialog">Annuler</NjBtn>
          <NjBtn :loading="loadingImport" :disabled="!accountingEntriesFile" @click="importAccountingEntries">
            Valider
          </NjBtn>
        </template>
      </CardDialog>
      <NjBtn :loading="loadingExport" @click="exportCeeStock">Exporter</NjBtn>
    </template>
    <template #sub-header>
      <VRow>
        <VCol :cols="selectedCeeStock[0] ? 6 : 12">
          <VRow>
            <VCol cols="6">
              <SearchInput v-model="filter.search" :loading="stockCEEs.loading || internalLoading" />
            </VCol>
            <VCol>
              <NjDatePicker v-model="filter.startDate" label="Début" />
            </VCol>
            <VCol>
              <NjDatePicker v-model="filter.endDate" label="Fin" />
            </VCol>
          </VRow>
        </VCol>
        <template v-if="selectedCeeStock[0]">
          <VCol cols="4">
            <h2>Détails des entrées en stock pour la période</h2>
          </VCol>
          <VCol cols="2">
            <SearchInput v-model="searchInCeeStockOperation" />
          </VCol>
        </template>
      </VRow>
    </template>
    <template #body>
      <VRow class="h-100" dense>
        <VCol :cols="selectedCeeStock.length ? 6 : 12">
          <VRow class="flex-column content-layout h-100" dense>
            <VCol v-if="selectedCeeStock.length" class="flex-grow-0">
              <VRow dense>
                <VCol v-for="(ceeStock, index) in selectedCeeStock" :key="index" class="flex-grow-0">
                  <VChip>{{ ceeStock.entity.name }} </VChip>
                </VCol>
              </VRow>
            </VCol>
            <VCol>
              <NjDataTable
                v-model:selections="selectedCeeStock"
                :pageable="pageableStockCEE"
                :page="stockCEEs.value"
                :headers="ceeStockByEntityHeader"
                checkboxes
                multi-selection
                fixed
                @update:pageable="updatePageableStockCEE"
              >
                <template #[`item.name`]="{ item }">
                  <span style="white-space: nowrap" :style="{ 'margin-inline-start': 24 * item.entity.level + 'px' }">
                    {{ item.entity.name }}
                  </span>
                </template>
              </NjDataTable>
            </VCol>
            <VCol v-if="selectedCeeStock.length">
              <CeeStockMonthly :filter="ceeStockMonthlyFilter" />
            </VCol>
          </VRow>
        </VCol>
        <VCol v-if="selectedCeeStock[0]" cols="6">
          <VRow class="h-100 flex-column" dense>
            <VCol>
              <CeeStockOperation
                ref="ceeStockOperationRef"
                v-model:with-corrections="withCorrections"
                v-model:with-emmy-folder="withEmmyFolder"
                v-model:selections="selectedCeeStockOperation"
                class="h-100"
                :entity-ids="entityIds"
                :start-date="filter.startDate"
                :end-date="filter.endDate"
                :search="searchInCeeStockOperation"
              />
            </VCol>
            <VCol v-if="selectedCeeStockOperation[0]">
              <CeeStockEntriesAllView :operation-id="selectedCeeStockOperation[0].operationId" class="h-100" />
            </VCol>
          </VRow>
        </VCol>
      </VRow>
    </template>
  </NjPage>
</template>
<script setup lang="ts">
import { stockCEEApi, type CeeStockFilter } from '@/api/ceeStock'
import { usePagination } from '@/types/pagination'
import type { CeeStock } from '@/types/ceeStock'
import NjDataTable from '@/components/okta/NjDataTable.vue'
import CeeStockOperation from './CeeStockOperation.vue'
import { VChip, VRow } from 'vuetify/components'
import CeeStockEntriesAllView from './CeeStockEntriesAllView.vue'
import CeeStockMonthly from './CeeStockMonthly.vue'
import { debounce } from 'lodash'
import { type CeeStockMonthlyFilter } from '@/api/ceeStockMonthly'
import type { CeeStockEntryDto } from '@/types/ceeStockEntry'
import { useSnackbarStore } from '@/stores/snackbar'
import useClearSideBar from '../clearSidebar'
import { ceeStockByEntityHeader } from './ceeStockByEntityHeader'
import { useUserStore } from '@/stores/user'
import { awaitResponse } from '@/types/responseHandler'

const {
  data: stockCEEs,
  pageable: pageableStockCEE,
  updatePageable: updatePageableStockCEE,
  updateFilter,
} = usePagination<CeeStock, CeeStockFilter>(
  (filter, pageable) => stockCEEApi.findAll(pageable, filter),
  {},
  {
    sort: ['originEntityNavFullId'],
  }
)
const userStore = useUserStore()

const selectedCeeStock = ref<CeeStock[]>([])
const entityIds = computed(() => selectedCeeStock.value.map((s) => s.entity.id))

const searchInCeeStockOperation = ref<string>()

const selectedCeeStockOperation = ref<CeeStockEntryDto[]>([])

const filter = ref<CeeStockFilter>({})
const ceeStockMonthlyFilter = ref<CeeStockMonthlyFilter>({})
const internalLoading = ref(false)

const debounceFilter = debounce((filter: CeeStockFilter) => {
  updateFilter(filter)
  ceeStockMonthlyFilter.value = { startDate: filter.startDate, endDate: filter.endDate, entityIds: entityIds.value }
  internalLoading.value = false
}, 300)

watch(entityIds, (v) => {
  ceeStockMonthlyFilter.value = { ...ceeStockMonthlyFilter.value, entityIds: v }
})

watch(
  filter,
  (v) => {
    internalLoading.value = true
    debounceFilter(v)
  },
  {
    deep: true,
  }
)

const snackbarStore = useSnackbarStore()

const withCorrections = ref<boolean>()
const withEmmyFolder = ref<boolean>()
const loadingExport = ref(false)
const ceeStockOperationRef = ref<typeof CeeStockOperation | null>(null)
const exportCeeStock = () => {
  loadingExport.value = true
  const promise = userStore.hasRole('DAF_SIEGE')
    ? ceeStockOperationApi.exportWithAccountingEntrySummary(ceeStockOperationRef.value?.ceeStockOperationFilter ?? {})
    : ceeStockOperationApi.export(ceeStockOperationRef.value?.ceeStockOperationFilter ?? {})

  promise
    .then((res) => {
      downloadFile('Export Stock capte.xlsx', res.data)
    })
    .catch(async (err) => {
      console.log(JSON.parse(await err.response.data.text()))
      snackbarStore.setError(
        (await handleAxiosException(err, JSON.parse(await err.response.data.text()).message)) ??
          "Erreur lors de l'export des Stock capte"
      )
    })
    .finally(() => (loadingExport.value = false))
}

useClearSideBar()

const loadingImport = ref(false)
const importCeeStockDialog = ref(false)
const accountingEntriesFile = ref<File | null>(null)
const openCeeStockDialog = () => {
  importCeeStockDialog.value = true
}

const cancelImportCeeStockDialog = () => {
  importCeeStockDialog.value = false
  accountingEntriesFile.value = null
}

const importAccountingEntries = () => {
  loadingImport.value = true
  accountingEntryApi.save(accountingEntriesFile.value!).then(async (res) => {
    awaitResponse(res.data)
      .then(() => {
        snackbarStore.setSuccess('Les pièces comptables ont bien été mis à jour')
        cancelImportCeeStockDialog()
      })
      .catch((response) => {
        snackbarStore.setError(response.errorMessage ?? "Les pièces comptables n'ont pas pu être mises à jours")
        cancelImportCeeStockDialog()
      })
    loadingImport.value = false
  })
}
</script>
