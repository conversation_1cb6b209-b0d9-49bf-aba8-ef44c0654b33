import type { DialogStoreRequest } from '@/stores/dialog'
import type { Operation } from '@/types/operation'

export const deleteControlOrderBatchDialogRequest: DialogStoreRequest = {
  maxWidth: '640px',
  title: 'Suppression du lot de contrôle',
  message:
    'En retirant la dernière opération du lot de contrôle vous le supprimerez par la même occasion, voulez-vous continuer?',
}

export const removeOperationFromControlOrderBatchRequest = (operation: Operation): DialogStoreRequest => ({
  title: "Sortir l'opération du lot de contrôle",
  message: `Vous allez sortir l'opération "${operation.operationName}" du lot de contrôle "${operation.controlOrderBatch?.batchCode}".\nVoulez-vous continuer ?`,
  maxWidth: '640px',
})
