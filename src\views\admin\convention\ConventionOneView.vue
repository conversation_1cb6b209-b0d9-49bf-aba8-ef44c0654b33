<template>
  <VCard :class="{ 'content-layout': expend }">
    <VCardTitle class="content-layout__header">
      <slot name="title"> Nouvelle convention </slot>
    </VCardTitle>
    <VDivider />
    <VCardText class="content-layout__main">
      <VForm ref="formRef">
        <VRow class="flex-column">
          <VCol>
            <VRow v-if="template.value" class="flex-column">
              <VCol>
                <VRow class="flex-column">
                  <VCol>
                    <VTextField v-model="template.value.name" label="Nom" :rules="[requiredRule]" />
                  </VCol>
                  <VCol>
                    <VSelect
                      v-model="template.value.type"
                      label="Type"
                      item-title="label"
                      item-value="type"
                      :items="conventionTypeValuesLabels"
                      clearable
                    />
                  </VCol>
                </VRow>
              </VCol>
              <VCol>
                <VRow align="stretch">
                  <VCol>
                    <NjDisplayValue label="Modèle">
                      <template #value>
                        <template v-if="templateFile || (template.value.document && template.value.document.id !== 0)">
                          <VRow class="align-center">
                            <VCol>
                              <component
                                :is="template.value.document && template.value.document.id !== 0 ? VLink : 'div'"
                                @click="
                                  template.value.document && template.value.document.id !== 0
                                    ? downloadTemplate()
                                    : undefined
                                "
                              >
                                {{ templateFile ? templateFile.name : template.value.document?.originalFilename }}
                              </component>
                            </VCol>
                            <VCol class="flex-grow-0">
                              <NjIconBtn
                                icon="mdi-delete-outline"
                                @click="
                                  () => {
                                    template.value!.document = null
                                    templateFile = undefined
                                  }
                                "
                              />
                            </VCol>
                            <VCol class="flex-grow-0">
                              <NjBtn v-if="template.value.document" :loading="generatingPreview" @click="preview">
                                Générer preview
                              </NjBtn>
                            </VCol>
                          </VRow>
                        </template>
                        <template v-else>
                          <div class="d-flex flex-column">
                            <NjFileInput
                              @new-files="
                                (event) => {
                                  templateFile = event[0]
                                }
                              "
                            />
                            <VInput :model-value="templateFile ?? ''" :rules="[requiredRule]" />
                          </div>
                        </template>
                      </template>
                    </NjDisplayValue>
                  </VCol>
                </VRow>
              </VCol>
            </VRow>
          </VCol>
        </VRow>
      </VForm>
    </VCardText>
    <VDivider />
    <VCardActions class="content-layout__footer">
      <VSpacer />
      <NjBtn variant="outlined" @click="cancel">Annuler</NjBtn>
      <NjBtn @click="save">{{ id !== 0 ? 'Enregistrer' : 'Valider' }}</NjBtn>
    </VCardActions>
  </VCard>
</template>
<script lang="ts" setup>
import { conventionTemplateApi } from '@/api/conventionTemplate'
import NjBtn from '@/components/NjBtn.vue'
import VLink from '@/components/VLink.vue'
import { useSnackbarStore } from '@/stores/snackbar'
import {
  type ConventionTemplate,
  mapToConventionTemplateRequest,
  makeEmptyConventionTemplate,
  conventionTypeValuesLabels,
} from '@/types/convention/conventionTemplate'
import { downloadFile } from '@/types/file'
import { requiredRule } from '@/types/rule'
import type { VForm } from 'vuetify/components/VForm'
const props = defineProps({
  id: {
    type: Number,
  },
  expend: Boolean,
})

const emit = defineEmits<{
  'update:model-value': [void]
  cancel: []
}>()

const snackbarStore = useSnackbarStore()

const template = ref(emptyValue<ConventionTemplate>())

const formRef = ref<typeof VForm | null>(null)

const cancel = () => {
  emit('cancel')
  formRef.value?.resetValidation()
}
const save = async () => {
  if (!template.value.value) {
    return
  }
  const validation = await formRef.value!.validate()

  if (!validation.valid) {
    return
  }
  if (template.value.value.id == 0) {
    await handleAxiosPromise(
      template,
      conventionTemplateApi.create(mapToConventionTemplateRequest(template.value.value), templateFile.value),
      {
        afterError: () => snackbarStore.setError('Erreur lors de la création de la convention'),
        afterSuccess: () => {
          emit('update:model-value')
          snackbarStore.setSuccess('La convention a bien été créée')
        },
      }
    )
  } else {
    await handleAxiosPromise(
      template,
      conventionTemplateApi.update(
        template.value.value.id,
        mapToConventionTemplateRequest(template.value.value),
        templateFile.value
      ),
      {
        afterError: () => snackbarStore.setError("Erreur lors de l'enregistrement de la convention"),
        afterSuccess: () => {
          snackbarStore.setSuccess('La convention a bien été enregistrée')
          emit('update:model-value')
        },
      }
    )
  }
}

const generatingPreview = ref(false)
const preview = () => {
  if (!template.value.value?.id) {
    return
  }
  generatingPreview.value = true
  conventionTemplateApi
    .preview(template.value.value.id)
    .then((response) => {
      downloadFile(template.value.value?.name + '-preview' + '.docx', response.data)
      snackbarStore.setSuccess('La génération du fichier de preview a réussi')
    })
    .catch(async (error) => {
      const responseObj = JSON.parse(await error.response.data.text())
      snackbarStore.setError(responseObj.message ?? 'La génération du fichier de preview a échoué')
    })
    .finally(() => (generatingPreview.value = false))
}

const downloadingFile = ref(false)
const downloadTemplate = () => {
  if (!template.value.value?.id) {
    return
  }
  downloadingFile.value = true
  conventionTemplateApi
    .download(template.value.value.id)
    .then((response) => {
      downloadFile(template.value.value?.name + '-template' + '.docx', response.data)
      snackbarStore.setSuccess('Le téléchargement a réussi')
    })
    .catch(() =>
      snackbarStore.setError(
        'Le téléchargement du fichier a échoué. Aviez-vous envoyé ce fichier sur boCEE ? Si oui, veuillez nous contacter.'
      )
    )
    .finally(() => (downloadingFile.value = false))
}

const templateFile = ref<File>()

watch(
  () => props.id,
  (v) => {
    if (v) {
      handleAxiosPromise(template, conventionTemplateApi.findOne(v))
    } else {
      template.value.value = makeEmptyConventionTemplate()
    }
    templateFile.value = undefined
  },
  {
    immediate: true,
  }
)

defineExpose({
  save,
})
</script>
