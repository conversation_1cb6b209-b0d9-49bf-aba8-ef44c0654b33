import { useAdminConfigurationStore } from '@/stores/adminConfiguration'
import type { Operation } from '@/types/operation'
import type { Valuation } from '@/types/valuation'

export const useValidateOperation = (
  operation: Ref<Operation | null | undefined>,
  valuations: Ref<Valuation[] | undefined>
) => {
  const adminConfigurationStore = useAdminConfigurationStore()
  const expirationPeriod = adminConfigurationStore.expirationPeriod
  const commitmentPeriod = adminConfigurationStore.commitmentPeriod
  const workPeriod = adminConfigurationStore.workPeriod
  const rgeEndofValidityAlertDayNumber = adminConfigurationStore.rgeEndofValidityAlertDayNumber

  const validateStepDurationRule = computed(() =>
    isOperationDurationStepValid(operation.value)
      ? true
      : `La durée de validation de l'étape ${operation.value?.stepId} a expirée`
  )

  const validateBeneficiaryRule = computed(() =>
    (operation.value?.stepId ?? 0) >= 80 || isBeneficiaryValid(operation.value?.beneficiary)
      ? true
      : 'Certains champs du bénéficiaire ne sont pas valides'
  )

  const validateStandardizedOperationSheetRule = computed(() =>
    isStandardizedOperationSheetValid(operation.value, expirationPeriod?.data) ? true : 'La fiche opération est expirée'
  )

  const validateStandardizedOperationSheetAndCommitmentValidityRule = computed(() =>
    isStandardizedOperationSheetAndCommitmentCompatible(operation.value)
      ? true
      : `La date d'engagement ${
          (operation.value?.stepId ?? 0) < 50 ? 'prévisionnelle' : ''
        } n'est pas compatible avec les dates de validité de la FOS`
  )

  const validateEstimatedCommitmentDateRule = computed(() =>
    isEstimatedCommitmentDateValid(operation.value, commitmentPeriod?.data)
      ? true
      : `La date d'engagement prévisionnelle ${
          new Date() < new Date(operation.value?.estimatedCommitmentDate!) ? 'approche' : 'est dépassée'
        }`
  )

  const validateEstimatedEndWorkRule = computed(() =>
    isEstimatedEndWorkValid(operation.value, workPeriod?.data)
      ? true
      : `La date de fin de travaux prévisionnelle ${
          new Date() < new Date(operation.value?.estimatedEndOperationDate!) ? 'approche' : 'est dépassée'
        }`
  )

  const validateOperationDurationRule = computed(() =>
    isOperationDurationValid(operation.value) ? true : "L'opération a été créée il y a plus de 12 mois"
  )

  const validateSubcontractorRule = computed(() =>
    isOperationSubcontractorValid(operation.value) ? true : 'Le sous traitant est requis'
  )

  const validateRgeRule = computed(() =>
    isRGEOperationValid(operation.value, (rgeEndofValidityAlertDayNumber?.valueAsInt as number | undefined) ?? 0)
      ? true
      : `Le RGE ${new Date() < new Date(operation.value?.rgeEndOfValidityDate!) ? 'va bientot expirer' : 'a expiré'}`
  )

  const validateOperation = computed(() =>
    isOperationValid(
      operation.value,
      rgeEndofValidityAlertDayNumber?.valueAsInt ?? 0,
      expirationPeriod?.data,
      commitmentPeriod?.data,
      workPeriod?.data,
      valuations.value
    )
  )

  const validateValuationRule = computed(() =>
    !isValuationDifferent(operation.value, valuations.value) ? true : 'La valorisation doit être modifiée'
  )

  const validateAlrt50EndworkFirstAlertRule = computed(
    () =>
      isEndWorkValidAtStep50(operation.value, adminConfigurationStore.validateStep50FirstAlert?.valueAsInt) ||
      'La date de dépôt de l’opération arrive à échéance dans moins de 11 mois'
  )

  const validateAlrt50EndworkSecondAlertRule = computed(
    () =>
      isEndWorkValidAtStep50(operation.value, adminConfigurationStore.validateStep50SecondAlert?.valueAsInt) ||
      'La date de dépôt de l’opération arrive à échéance dans moins de 1 mois'
  )

  const operationRuleWarnings = computed(() =>
    [
      validateBeneficiaryRule.value,
      validateSubcontractorRule.value,
      validateRgeRule.value,
      validateOperationDurationRule.value,
      validateEstimatedEndWorkRule.value,
      validateAlrt50EndworkFirstAlertRule.value,
      validateEstimatedCommitmentDateRule.value,
      validateStandardizedOperationSheetRule.value,
      validateStandardizedOperationSheetAndCommitmentValidityRule.value,
      validateStepDurationRule.value,
      validateValuationRule.value,
    ].filter((it) => it !== true && it !== '')
  )

  const operationRuleErrors = computed(() =>
    [validateStepDurationRule.value, validateAlrt50EndworkSecondAlertRule.value].filter(
      (it) => it !== true && it !== ''
    )
  )

  return {
    validateOperation,
    validateEstimatedCommitmentDateRule,
    validateEstimatedEndWorkRule,
    validateOperationDurationRule,
    validateSubcontractorRule,
    validateRgeRule,
    validateStandardizedOperationSheetRule,
    validateStandardizedOperationSheetAndCommitmentValidityRule,
    validateStepDurationRule,
    validateValuationRule,
    validateBeneficiaryRule,
    operationRuleErrors,
    operationRuleWarnings,
  }
}
