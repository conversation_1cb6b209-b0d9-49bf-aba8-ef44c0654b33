<template>
  <NjPage
    can-go-back
    :title="'Liste des opérations du regroupement: ' + operationsGroup.value?.name"
    expend-body
    v-bind="$attrs"
  >
    <template #header-actions>
      <VRow class="flex-nowrap" dense>
        <VCol>
          <NjBtn
            v-show="!operationsAfterStep50.loading && operationsAfterStep50.value!.totalElements == 0"
            variant="outlined"
            color="error"
            @click="deleteOperationsGroup"
            >Supprimer</NjBtn
          >
          <AlertDialog v-bind="deleteDialog.props" title="Attention" max-width="640px">
            Tous les documents ajoutés au niveau du regroupement seront supprimés et les opérations seront remises à
            l'étape 30. <br />
            Si vous souhaiter conserver les documents, il faut les télécharger avant de supprimer le regroupement.
          </AlertDialog>
        </VCol>
        <VCol class="d-flex gap-2 flex-nowrap">
          <NjBtn
            v-if="displayValidateStep40Button"
            variant="outlined"
            :loading="previousStepLoading"
            @click="previousStep(40)"
          >
            Etape précédente
          </NjBtn>
          <NjBtn v-if="displayValidateStep30Button" :loading="operations.loading" @click="step30Dialog = true">
            Valider l'étape 30
          </NjBtn>
          <NjBtn v-if="displayValidateStep40Button" :loading="operations.loading" @click="step40Dialog = true">
            Valider l'étape 40
          </NjBtn>
        </VCol>
        <NextStepDialog v-model="step30Dialog" :step-id="30" :blocked="promisableValidateStep30.loading">
          <template v-if="operationsGroup.value" #informations>
            <NjExpansionPanel title="Bénéficiaire">
              <BeneficiaryDisplayValue v-model="operationsGroup.value.beneficiary" />
            </NjExpansionPanel>
            <NjExpansionPanel title="Sous traitant">
              <SubcontractorDisplayValue v-model="operationsGroup.value.subcontractor" />
            </NjExpansionPanel>
            <NjDivider />
            <VForm ref="formStep30" :disabled="promisableValidateStep30.loading">
              <WorksField
                v-model:final-works-type="validateStep30Request.finalWorksType"
                :operation="validateStep30Request as any"
                edit
                @delete="(validateStep30Request as any)['works' + $event] = undefined"
                @selected="selectWorks"
                @update:self-works="onUpdateSelfWorks"
              />
              <div class="pa-2">
                <ErrorAlert type="info" :message="selfWorksMessages" />
              </div>

              <NjDivider v-if="!validateStep30Request.selfWorks" />
              <NjExpansionPanel v-if="!validateStep30Request.selfWorks" title="Offre client">
                <VRow class="flex-column">
                  <VCol>
                    <VTextField
                      v-model="validateStep30Request.customerOfferNumber"
                      label="Numéro offre client"
                      :rules="validateStep30Request.selfWorks ? [] : [requiredRule]"
                    />
                  </VCol>
                  <VCol>
                    <NjDatePicker
                      v-model="validateStep30Request.offersDispatchDate"
                      label="Date d'envoi de l'offre"
                      :rules="validateStep30Request.selfWorks ? [] : [requiredRule]"
                    />
                  </VCol>
                </VRow>
              </NjExpansionPanel>
            </VForm>
            <NjExpansionPanel v-if="!missingDocumentTypes.value?.empty" title="Document requis">
              <GenericDocumentSubmit
                ref="genericDocumentSubmit"
                :operations-group-id="props.id"
                :missing-documents="missingDocumentTypes.value?.content"
                mode="nextStep"
              />
            </NjExpansionPanel>
            <template v-if="promisableValidateStep30.error">
              <ErrorAlert
                v-for="error in parseOperationsError(promisableValidateStep30.error)"
                :key="error"
                class="ma-2"
                :message="error"
              />
            </template>
          </template>
          <template #actions>
            <VCardActions>
              <VSpacer />
              <NjBtn variant="outlined" :disabled="promisableValidateStep30.loading" @click="step30Dialog = false">
                Annuler
              </NjBtn>
              <NjBtn :loading="promisableValidateStep30.loading" :disabled="disabledNext" @click="validateStep30">
                Valider
              </NjBtn>
            </VCardActions>
          </template>
        </NextStepDialog>
        <NextStepDialog v-model="step40Dialog" :step-id="40" :blocked="promisableValidateStep40.loading">
          <template v-if="operationsGroup.value" #informations>
            <VForm ref="formStep40" :disabled="promisableValidateStep40.loading">
              <NjExpansionPanel title="Opération">
                <NjDatePicker
                  v-model="validateStep40Request.signedDate"
                  label="Date engagement réelle"
                  :rules="[requiredRule, isEmptyOrBeforeToday]"
                />
              </NjExpansionPanel>
              <NjExpansionPanel title="Doublon/Cumul">
                <VSwitch v-model="validateStep40Request.customerDuplicate" label="Recherche dans la base du client" />
              </NjExpansionPanel>
              <NjExpansionPanel v-if="!missingDocumentTypes.value?.empty" title="Document requis">
                <GenericDocumentSubmit
                  ref="genericDocumentSubmit"
                  :operations-group-id="props.id"
                  :missing-documents="missingDocumentTypes.value?.content"
                  mode="nextStep"
                />
              </NjExpansionPanel>
            </VForm>
            <VCol v-if="promisableValidateStep40.error" dense>
              <ErrorAlert
                v-for="(error, i) in parseOperationsError(promisableValidateStep40.error)"
                :key="i"
                :message="error"
                class="ma-2"
              />
            </VCol>
          </template>
          <template #actions>
            <VCardActions>
              <VSpacer />
              <NjBtn variant="outlined" :disabled="promisableValidateStep40.loading" @click="step40Dialog = false">
                Annuler
              </NjBtn>
              <NjBtn :disabled="disabledNext" :loading="promisableValidateStep40.loading" @click="validateStep40">
                Valider
              </NjBtn>
            </VCardActions>
          </template>
        </NextStepDialog>
        <CardDialog v-model="setOperationsDialog" title="Gérer le regroupement" height="100%">
          <SetOperationOperationsGroup
            v-if="operationsGroup.value"
            :operations-group="operationsGroup.value"
            class="h-100"
          />
          <template #actions>
            <NjBtn @click="setOperationsDialog = false"> Valider </NjBtn>
          </template>
        </CardDialog>
      </VRow>
    </template>
    <template #body>
      <VRow class="w-100">
        <VCol :cols="openDrawer ? 12 : 7">
          <VRow class="flex-column content-layout">
            <VCol class="flex-grow-0">
              <VRow>
                <VCol>
                  <VRow>
                    <VCol>
                      <SearchInput v-model="search" :loading="operations.loading" />
                    </VCol>
                    <VCol v-if="valuationToUpdate" class="flex-grow-0">
                      <NjBtn :disabled="updatingAvailable.length === 0" @click="updatingValuation = true">
                        Modifier valorisation
                      </NjBtn>
                      <CardDialog
                        v-model="updatingValuation"
                        :title="`Mettre à jour ${
                          updatingAvailable.length === 1 ? 'la valorisation' : 'les valorisations'
                        }`"
                        width="40%"
                      >
                        Voulez-vous mettre à jour
                        {{ updatingAvailable.length === 1 ? 'la valorisation' : 'les valorisations' }} avec les
                        informations suivantes: <br />
                        <template v-for="operation in updatingAvailable" :key="operation.id">
                          {{ operation.operationName }} :
                          <NjDisplayValue
                            label="Valorisation classique"
                            :value="`${operation.classicValuationValue} →
                                ${
                                  valuations[
                                    operations.value?.content.findIndex((ope) => ope.id === operation.id) ?? -1
                                  ].find((valuation: Valuation) => !valuation.precariousness)?.value
                                }`"
                          />
                          <NjDisplayValue
                            label="Valorisation précartité"
                            :value="`${operation.precariousnessValuationValue} →
                                ${
                                  valuations[
                                    operations.value?.content.findIndex((ope) => ope.id === operation.id) ?? -1
                                  ].find((valuation: Valuation) => valuation.precariousness)?.value
                                }`"
                          />
                        </template>
                        <template #actions>
                          <NjBtn variant="outlined" @click="updatingValuation = false">Non</NjBtn>
                          <NjBtn @click="updateValuation"> Oui </NjBtn>
                        </template>
                      </CardDialog>
                      <CardDialog v-model="updatingError" title="Rapport d'erreur" width="40%">
                        Une erreur est survenue lors de la mise à jour
                        {{ errorReports.length === 1 ? " de l'opération suivante" : ' des opérations suivantes' }} :
                        <div v-for="report in errorReports" :key="report">
                          <span class="nj-display-value__value">{{ report.operationName }}</span>
                          , avec pour message :<br />
                          <span class="nj-display-value__value">{{ report.message }}</span>
                        </div>
                        <template #actions>
                          <NjBtn @click="updatingError = false"> Ok </NjBtn>
                        </template>
                      </CardDialog>
                    </VCol>
                    <VCol class="flex-grow-0">
                      <NjBtn @click="setOperationsDialog = true">Gérer le regroupement</NjBtn>
                    </VCol>
                  </VRow>
                </VCol>
              </VRow>
            </VCol>
            <VCol>
              <NjDataTable
                v-model:selections="selection"
                :pageable="pageableOperation"
                :page="operations.value!"
                :headers="headers"
                :disabled-row="
                  (ope: Operation) => ope.status === 'CANCELLED' || ope.status === 'IMPROPER' || ope.status === 'LOST'
                "
                :on-click-row="clickRow"
                :clicked-row="clickedRow"
                :checkboxes="valuationToUpdate"
                :multi-selection="valuationToUpdate"
                fixed
                @update:pageable="updatePageableOperations"
              >
                <template #[`item.documents`]="{ item }">
                  <VMenu>
                    <template #activator="{ props }">
                      <NjIconBtn rounded="0" icon="mdi-dots-horizontal" v-bind="props" />
                    </template>
                    <VCard>
                      <VCardText>
                        <DocumentLocationAllView :operation-group-id="props.id" :operation-id="item.id" />
                      </VCardText>
                    </VCard>
                  </VMenu>
                </template>
                <template #[`item.stepId`]="{ item }">
                  <OperationStepChip :operation="item" />
                </template>
              </NjDataTable>
            </VCol>
          </VRow>
        </VCol>
        <VCol v-show="!openDrawer" cols="5">
          <VCard class="content-layout h-100">
            <VCardTitle class="content-layout__header pa-0">
              <VTabs v-model="tabs">
                <VTab>Regroupement</VTab>
                <VTab>Document associés</VTab>
                <VTab>Traçabilité</VTab>
              </VTabs>
            </VCardTitle>
            <VDivider />
            <VCardText class="content-layout__main pa-0">
              <VWindow v-model="tabs" class="h-100">
                <VWindowItem :class="tabs === 0 ? 'content-layout' : 'h-100'">
                  <VRow v-if="operationsGroup.value" class="flex-column content-layout content-layout__main" no-gutters>
                    <VCol>
                      <VRow no-gutters>
                        <VCol class="content-layout__main">
                          <NjExpansionPanel title="Regroupement">
                            <VRow class="flex-column" dense>
                              <VCol>
                                <NjDisplayValue
                                  label="Nom"
                                  :value="operationsGroup.value.name"
                                  editable
                                  @click="editName"
                                />
                                <AlertDialog
                                  v-bind="editNameDialog.props"
                                  max-width="480px"
                                  title="Modifier le nom du regroupement"
                                >
                                  <VTextField
                                    v-model="editNameDialog.value.value"
                                    label="Nom du regroupement"
                                    :rules="[requiredRule]"
                                    clearable
                                  />
                                </AlertDialog>
                              </VCol>
                              <VCol>
                                <NjDisplayValue
                                  label="Créateur"
                                  :value="displayFullnameUser(operationsGroup.value.creationUser)"
                                />
                              </VCol>
                              <VCol>
                                <NjDisplayValue
                                  label="Date de création"
                                  :value="formatHumanReadableLocalDate(operationsGroup.value.creationDateTime)"
                                />
                              </VCol>
                              <VCol>
                                <NjDisplayValue
                                  label="Regroupement CPE"
                                  :value="operationsGroup.value.onlyEpcOperations ? 'OUI' : 'NON'"
                                />
                              </VCol>
                              <VCol>
                                <NjDisplayValue
                                  label="Organisation"
                                  :value="`(${operationsGroup.value.entity.id}) ${operationsGroup.value.entity.name}`"
                                  class="align-center"
                                  editable
                                  @click="editOrganisation"
                                >
                                </NjDisplayValue>
                                <AlertDialog
                                  v-bind="editOrganisationDialog.props"
                                  max-width="480px"
                                  title="Edition Organisation du regroupement"
                                >
                                  <RemoteAutoComplete
                                    v-model="editOrganisationDialog.value.value"
                                    label="Organisation"
                                    :query-for-all="
                                      (search, pageable) =>
                                        entityApi.getAll({ search: search, myEntities: true, level: 4 }, pageable)
                                    "
                                    :query-for-one="(org: Entity) => entityApi.getOne(org?.id ?? 0)"
                                    item-title="name"
                                    :rules="[requiredRule]"
                                    return-object
                                    clearable
                                    infinite-scroll
                                  />
                                </AlertDialog>
                              </VCol>
                            </VRow>
                          </NjExpansionPanel>
                        </VCol>
                        <VDivider />
                        <VCol>
                          <NjExpansionPanel title="Installations">
                            <NjDataTable
                              v-if="properties.value"
                              :pageable="pageableProperties"
                              :page="properties.value!"
                              :headers="propertyHeaders"
                              @update:pageable="updatePageableProperties"
                            >
                              <template #[`item.address`]="{ item }">
                                {{ item.streetName + ', ' + item.postalCode + ' ' + item.city }}
                              </template>
                            </NjDataTable>
                          </NjExpansionPanel>
                        </VCol>
                        <VDivider />
                        <VCol>
                          <NjExpansionPanel>
                            <template #title>
                              <div class="d-flex align-center fill-width">
                                <TitleInput> Bénéficiaire </TitleInput>
                                <VSpacer />

                                <VLink
                                  size="small"
                                  icon="mdi-format-list-bulleted"
                                  style="font-weight: initial; font-size: initial"
                                  @click.stop="openBeneficiaryDialog"
                                  >Bénéficiaires</VLink
                                >
                              </div>
                            </template>
                            <BeneficiaryDisplayValue v-model="operationsGroup.value.beneficiary" />
                          </NjExpansionPanel>
                        </VCol>
                        <VDivider />
                        <VCol>
                          <SubcontractorInputPanel
                            :model-value="true"
                            :subcontractor="operationsGroup.value.subcontractor"
                            :can-certified="canCertified"
                            @update:subcontractor="handleUpdateSelectedSubcontractor"
                          />
                        </VCol>
                        <VDivider />
                        <VCol>
                          <NjExpansionPanel title="Valorisations">
                            <VRow class="flex-column" dense>
                              <VCol>
                                <NjDisplayValue
                                  label="Valo. atypique classique"
                                  :value="formatNumber(operationsGroup.value.atypicalClassicValuationValue)"
                                  value-class="text-no-wrap"
                                  :editable="userStore.isAdminPlus"
                                  @click="userStore.isAdminPlus && editValuationAtypical()"
                                />
                                <NjDisplayValue
                                  label="Valo. atypique précarité"
                                  :value="formatNumber(operationsGroup.value.atypicalPrecariousnessValuationValue)"
                                  value-class="text-no-wrap"
                                  :editable="userStore.isAdminPlus"
                                  @click="userStore.isAdminPlus && editValuationAtypical()"
                                />
                                <AlertDialog
                                  v-bind="editValuationAtypicalDialog.props"
                                  max-width="480px"
                                  title="Edition Valorisation atypique"
                                >
                                  <VTextField
                                    v-model.number="
                                      editValuationAtypicalDialog.value.value!.atypicalClassicValuationValue
                                    "
                                    class="mb-4"
                                    label="Valo. atypique classique"
                                    type="number"
                                    suffix="€ / kWhc"
                                    clearable
                                  />
                                  <VTextField
                                    v-model.number="
                                      editValuationAtypicalDialog.value.value!.atypicalPrecariousnessValuationValue
                                    "
                                    label="Valo. atypique précarité"
                                    type="number"
                                    suffix="€ / kWhc"
                                    clearable
                                  />
                                </AlertDialog>
                              </VCol>
                            </VRow>
                          </NjExpansionPanel>
                        </VCol>
                        <VDivider />
                        <VCol>
                          <NjExpansionPanel title="Incitation financière">
                            <VRow v-if="operationsGroupSummaryDto.value" class="flex-column" dense>
                              <VCol v-if="!isSelfWorksOnOperationsGroup">
                                <NjDisplayValue
                                  label="Offre commerciale financière à répartir"
                                  :value="
                                    formatPriceNumber(operationsGroup.value.commercialOfferWithoutFinancialIncentive)
                                  "
                                  editable
                                  value-class="text-no-wrap"
                                  @click="editFinancial"
                                />
                                <NjDisplayValue
                                  label="Incitation financière à répartir"
                                  :value="formatPriceNumber(operationsGroup.value.customerFinancialIncentive)"
                                  editable
                                  value-class="text-no-wrap"
                                  @click="editFinancial"
                                />
                                <AlertDialog
                                  v-bind="editFinancialIncentiveDialog.props"
                                  max-width="480px"
                                  title="Edition Informations financières"
                                >
                                  <VTextField
                                    v-model.number="
                                      editFinancialIncentiveDialog.value.value!.commercialOfferWithoutFinancialIncentive
                                    "
                                    class="mb-4"
                                    label="Offre commerciale financière à répartir"
                                    type="number"
                                    suffix="€ TTC"
                                    clearable
                                  />
                                  <VTextField
                                    v-model.number="
                                      editFinancialIncentiveDialog.value.value!.customerFinancialIncentive
                                    "
                                    label="Incitation financière à répartir"
                                    type="number"
                                    suffix="€ TTC"
                                    clearable
                                  />
                                </AlertDialog>
                              </VCol>
                              <VCol
                                v-if="
                                  !isSelfWorksOnOperationsGroup &&
                                  operationsGroup.value.commercialOfferWithoutFinancialIncentive
                                "
                              >
                                <NjDisplayValue
                                  label="Offre commerciale avant incitation financière CEE/€ TTC"
                                  value-class="text-no-wrap"
                                  :value="
                                    formatNumber(
                                      operationsGroupSummaryDto.value.commercialOfferWithoutFinancialIncentive
                                    )
                                  "
                                />
                              </VCol>
                              <VCol
                                v-if="
                                  !isSelfWorksOnOperationsGroup && !operationsGroup.value.customerFinancialIncentive
                                "
                              >
                                <NjDisplayValue
                                  label="Incitation financière CEE client/€ TTC"
                                  :value="formatNumber(operationsGroupSummaryDto.value.customerFinancialIncentive)"
                                />
                              </VCol>

                              <VCol>
                                <NjDisplayValue
                                  label="Montant de l'offre commerciale après déduction de l'incitation financière CEE/€ TTC"
                                  value-class="text-no-wrap"
                                  :value="
                                    formatNumber(
                                      operationsGroupSummaryDto.value.commercialOfferWithoutFinancialIncentive -
                                        operationsGroupSummaryDto.value.customerFinancialIncentive
                                    )
                                  "
                                />
                              </VCol>

                              <VCol>
                                <NjDisplayValue
                                  label="Charge Pôle CEE"
                                  value-class="text-no-wrap"
                                  :value="formatNumber(operationsGroupSummaryDto.value.totalFee)"
                                />
                              </VCol>

                              <VCol>
                                <NjDisplayValue
                                  label="Marge CEE Nette TTC"
                                  value-class="text-no-wrap"
                                  :value="
                                    formatNumber(
                                      operationsGroupSummaryDto.value.classicValuationAmountSum +
                                        operationsGroupSummaryDto.value.precariousnessValuationAmountSum -
                                        operationsGroupSummaryDto.value.customerFinancialIncentive -
                                        operationsGroupSummaryDto.value.totalFee
                                    )
                                  "
                                />
                              </VCol>
                            </VRow>
                          </NjExpansionPanel>
                        </VCol>
                        <VDivider />

                        <VCol>
                          <NjExpansionPanel title="Chantier">
                            <div class="d-flex align-center">
                              <AlertDialog
                                style="max-width: 500px"
                                v-bind="updateSelfWorksDialog.props"
                                title="Changer travaux en propre"
                              >
                                <NjSwitch v-model="selfWorksValue" label="Travaux en propre" />
                              </AlertDialog>
                              <NjDisplayValue
                                label="Chantier en propre"
                                :value="numberOfOperationsWithSelfWorks.value?.totalElements ? 'OUI' : 'NON'"
                                editable
                                @click="handleUpdateSelfWorksDialog"
                              />
                              <div v-if="selfWorksMessages" class="pa-2">
                                <ErrorAlert type="info" :message="selfWorksMessages" />
                              </div>
                            </div>
                          </NjExpansionPanel>
                        </VCol>
                      </VRow>
                    </VCol>
                  </VRow>
                </VWindowItem>
                <VWindowItem class="h-100">
                  <OperationsGroupDocumentAllView
                    :id="props.id"
                    ref="operationsGroupDocumentAllViewRef"
                    :operations-group="operationsGroup.value"
                    :actions="true"
                    :show-disabled-documents="showDisabledDocuments"
                    :number-of-properties="properties.value?.totalElements"
                    :number-of-standardized-operation-sheet="standardizedOperationSheets.value?.totalElements"
                  />
                </VWindowItem>
                <VWindowItem :class="tabs === 2 ? 'content-layout' : 'h-100'">
                  <VRow class="flex-column content-layout content-layout__main">
                    <OperationsGroupEventHistoryView
                      :id="props.id"
                      ref="operationsGroupEventHistoryViewRef"
                      class="pa-6"
                      @treated="
                        () => {
                          reloadOperationsGroup()
                          operationsGroupEventHistoryViewRef!.reload()
                        }
                      "
                    />
                    <VDivider />
                    <VCol class="content-layout__footer pb-5 pe-5">
                      <VRow>
                        <VSpacer />
                        <VCol class="flex-grow-0">
                          <NjBtn @click="commentaireDialog = true">Commentaire</NjBtn>
                        </VCol>
                      </VRow>
                      <CommentaireDialog
                        v-model="commentaireDialog"
                        :operation-group="operationsGroup.value"
                        @send="
                          () => {
                            commentaireDialog = false
                            operationsGroupEventHistoryViewRef!.reload()
                          }
                        "
                        @close="commentaireDialog = false"
                      />
                    </VCol>
                  </VRow>
                </VWindowItem>
              </VWindow>
            </VCardText>
          </VCard>
        </VCol>
      </VRow>
    </template>
    <template #drawer>
      <OperationDrawer
        ref="operationDrawerRef"
        v-model="openDrawer"
        :operation="selectedOperation.value"
        in-operation-group
        @update:operation="reloadFinancialIncentive"
      />
    </template>
  </NjPage>

  <BeneficiaryDialog
    v-model="beneficiaryDialog"
    :can-certified="userStore.canCertified"
    :selected="operationsGroup.value?.beneficiary ? [operationsGroup.value!.beneficiary] : ([] as Beneficiary[])"
    @update:selected="handleUpdateSelectedBeneficiary"
  />
</template>
<script setup lang="ts">
import SubcontractorInputPanel from '@/views/operation/SubcontractorInputPanel.vue'
import { formatNumber, formatPriceNumber } from '@/types/format'
import AlertDialog from '@/components/AlertDialog.vue'
import NjBtn from '@/components/NjBtn.vue'
import NjDataTable from '@/components/okta/NjDataTable.vue'
import { type Operation } from '@/types/operation'
import SetOperationOperationsGroup from './SetOperationOperationsGroup.vue'
import type {
  EnhancedOperationsGroup,
  OperationsGroup,
  OperationsGroupSummaryDto,
  ValidateStep30OperationsGroupRequest,
} from '@/types/operationsGroup'
import NjIconBtn from '@/components/NjIconBtn.vue'
import NjDisplayValue from '@/components/NjDisplayValue.vue'
import NjExpansionPanel from '@/components/NjExpansionPanel.vue'
import type { Property } from '@/types/property'
import { formatHumanReadableLocalDate } from '@/types/date'
import { debounce } from 'lodash'
import NjPage from '@/components/NjPage.vue'
import { operationsGroupApi } from '@/api/operationsGroup'
import { propertyApi } from '@/api/property'
import type { StandardizedOperationSheet } from '@/types/calcul/standardizedOperationSheet'
import type { Page } from '@/types/pagination'
import { useSnackbarStore } from '@/stores/snackbar'
import OperationDrawer from '../operation/OperationDrawer.vue'
import OperationsGroupDocumentAllView from '../operationsGroupDocument/OperationsGroupDocumentAllView.vue'
import DocumentLocationAllView from './DocumentLocationAllView.vue'
import BeneficiaryDisplayValue from '@/components/BeneficiaryDisplayValue.vue'
import NjDatePicker from '@/components/NjDatePicker.vue'
import { requiredRule, isEmptyOrBeforeToday } from '@/types/rule'
import {
  makeEmptyValidateStep30OperationsGroupRequest,
  makeEmptyValidateStep40OperationsGroupRequest,
} from '@/types/operationsGroup'
import ErrorAlert from '@/components/ErrorAlert.vue'
import type { PromisableValue } from '@/types/promisableValue'
import NextStepDialog from '../dialog/NextStepDialog.vue'
import CardDialog from '@/components/CardDialog.vue'
import type { VForm } from 'vuetify/components/VForm'
import CommentaireDialog from '../operation/dialog/CommentaireDialog.vue'
import OperationStepChip from '../operation/OperationStepChip.vue'
import type { DocumentType } from '@/types/documentType'
import type { Valuation } from '@/types/valuation'
import { type Works } from '@/types/works'
import { VCol, VDivider, VRow } from 'vuetify/components'
import OperationsGroupEventHistoryView from './OperationsGroupEventHistoryView.vue'
import WorksField from '../operation/WorksField.vue'
import GenericDocumentSubmit from '../document/GenericDocumentSubmit.vue'
import { type Entity } from '@/types/entity'
import { displayFullnameUser } from '@/types/user'
import { useUserStore } from '@/stores/user'
import NjSwitch from '@/components/NjSwitch.vue'
import type { Beneficiary } from '@/types/beneficiary'
import { useDialogStore } from '@/stores/dialog'
import BeneficiaryDialog from '../dialog/BeneficiaryDialog.vue'
import { entityApi } from '@/api/entity'
import type { DataTableHeader } from '@/components/okta/NjDataTable.type'
import NjDivider from '@/components/NjDivider.vue'
import type { Subcontractor } from '@/types/subcontractor'
import { missingDocumentTypeIdsKey } from '../dashboard/keys'

const props = defineProps({
  id: {
    type: Number,
    required: true,
  },
})
const router = useRouter()
const snackbarStore = useSnackbarStore()
const userStore = useUserStore()
const dialogStore = useDialogStore()

const selection = ref<Operation[]>([])
const tabs = ref()

const commentaireDialog = ref(false)
const operationsGroupEventHistoryViewRef = ref<typeof OperationsGroupEventHistoryView | null>(null)

const valuations = ref<Valuation[][]>([[]])
const updatingValuation = ref(false)
const updatingError = ref(false)
const errorReports = ref<any[]>([])

const headers: DataTableHeader<Operation>[] = [
  {
    title: 'Nom',
    value: 'operationName',
  },
  {
    title: 'Chrono',
    value: 'chronoCode',
  },
  {
    title: 'Code',
    value: 'standardizedOperationSheet.operationCode',
  },
  {
    title: 'Étape',
    value: 'stepId',
  },
  {
    title: 'Documents associés',
    value: 'documents',
    sortable: false,
  },
]

const operationsGroup = ref(emptyValue<EnhancedOperationsGroup>())

const showDisabledDocuments = ref(false)

const reloadOperationsGroup = () => {
  if (props.id) {
    handleAxiosPromise(operationsGroup, operationsGroupApi.findById(props.id), {
      afterSuccess: () => {
        if (!operationsGroup.value.value?.operationsNumber) {
          setOperationsDialog.value = true
        }
      },
    })
  }
}

onMounted(() => {
  reloadOperationsGroup()
})

const operationsGroupSummaryDto = ref(emptyValue<OperationsGroupSummaryDto>())

const reloadFinancialIncentive = () =>
  handleAxiosPromise(operationsGroupSummaryDto, operationsGroupApi.findFinancialIncentiveById(props.id))

onMounted(() => {
  reloadFinancialIncentive()
})

const differentValuation = (ope: Operation, valuation: Valuation[]) =>
  !(ope.atypicalClassicValuationValue || ope.atypicalPrecariousnessValuationValue) &&
  valuation &&
  (ope.classicValuationValue !== valuation?.find((valuation) => !valuation.precariousness)?.value ||
    ope.precariousnessValuationValue !== valuation?.find((valuation) => valuation.precariousness)?.value)

const updateValuation = () => {
  Promise.allSettled(
    updatingAvailable.value.map((ope) => {
      return operationApi.updateValuation(ope.id, {
        classicValuationValue:
          valuations.value[operations.value.value?.content.findIndex((o) => ope.id === o.id) ?? -1].find(
            (valuation: Valuation) => !valuation.precariousness
          )?.value ?? 0,
        precariousnessValuationValue:
          valuations.value[operations.value.value?.content.findIndex((o) => ope.id === o.id) ?? -1].find(
            (valuation: Valuation) => valuation.precariousness
          )?.value ?? 0,
      })
    })
  ).then((response) => {
    if (response.filter((res) => res.status === 'rejected').length > 0) {
      errorReports.value = []
      response.forEach((error, index) => {
        if (error.status === 'rejected') {
          errorReports.value.push({
            operationName: updatingAvailable.value[index].operationName,
            message: handleAxiosException((error as any).reason, undefined, {
              defaultMessage: 'Une erreur est survenue lors de la mise à jour',
            }),
          })
        }
      })
      updatingValuation.value = false
      updatingError.value = true
    } else {
      snackbarStore.setSuccess(
        `${
          updatingAvailable.value.length === 1
            ? 'La valorisation a été mise à jour avec succès'
            : 'Les valorisations ont été mises à jour avec succès'
        }`
      )
      updatingValuation.value = false
    }
    reloadOperations()
    selection.value = []
  })
}

const openDrawer = ref(false)
const operationDrawerRef = ref<typeof OperationDrawer | null>(null)
const selectedOperation = ref(emptyValue<Operation>())
const clickedRow = ref<Operation>()
const clickRow = (item: Operation) => {
  operationDrawerRef.value!.check(async () => {
    clickedRow.value = item
    if (item.id !== selectedOperation.value.value?.id) {
      await handleAxiosPromise(selectedOperation, simulationApi.findById(item.id), {
        afterError: () =>
          snackbarStore.setError(
            selectedOperation.value.error ?? "Une erreur est survenue lors de la récpération de l'opération"
          ),
      })
      openDrawer.value = true
      return
    }
    openDrawer.value = !openDrawer.value
    if (!openDrawer.value) {
      clickedRow.value = undefined
    }
  })
}

//Operation
const {
  data: operations,
  pageable: pageableOperation,
  pageFilter: pageFilterOperations,
  updatePageable: updatePageableOperations,
  reload: reloadOperations,
} = usePagination<Operation>(
  (filter, pageable) =>
    operationApi.findAll(filter, pageable).then((response) =>
      Promise.all(
        response.data.content.map((ope) => {
          return valuationApi.getAppropriate(ope.id, ope.valuationType.id, false).then((res) => {
            return res.data
          })
        })
      )
        .then((v) => {
          valuations.value = v
          return response
        })
        .catch(() => response)
    ),
  {
    operationsGroupId: props.id,
  },
  {}
)

const search = ref('')

const debounceSearch = debounce((v: string) => {
  pageFilterOperations.value.search = v
  reloadOperations()
}, 300)

watch(search, (v) => {
  operations.value.loading = true
  debounceSearch(v)
})

const setOperationsDialog = ref()

watch(setOperationsDialog, (v) => {
  if (!v) {
    reloadOperations().then(() => {
      if (operationsGroup.value.value && operations.value.value) {
        operationsGroup.value.value.operationsNumber = operations.value.value.totalElements
      }
    })
    reloadProperty()
    operationsGroupEventHistoryViewRef.value?.reload()
  }
})

//Propertys
const propertyHeaders = [
  {
    title: 'Total',
    value: 'count',
    formater: (_: any, value: number) => formatNumber(value),
    cellClass: 'text-right justify-end',
  },
  {
    title: 'Adresse',
    value: 'street',
  },
  {
    title: 'Code Postal',
    value: 'postalCode',
  },
  {
    title: 'Ville',
    value: 'city',
  },
]

const {
  data: properties,
  pageable: pageableProperties,
  updatePageable: updatePageableProperties,
  reload: reloadProperty,
} = usePagination<Property>(
  (filter, pageable) => propertyApi.findAll(filter, pageable),
  {
    operationsGroupId: props.id,
  },
  {}
)

const deleteDialog = useConfirmAlertDialog()

const deleteOperationsGroup = async () => {
  if (operationsGroup.value.value && (await deleteDialog.confirm())) {
    operationsGroupApi
      .delete([operationsGroup.value.value?.id])
      .then(() => {
        snackbarStore.setSuccess('Le regroupement a été supprimé', 5000)
        router.push({
          name: 'OperationsGroupAllView',
        })
      })
      .catch(async (err) => {
        snackbarStore.setError(err.error ?? 'Une erreur est survenue lors de la suppression du regroupement')
      })
  }
}

//convention
const standardizedOperationSheets: Ref<PromisableValue<Page<StandardizedOperationSheet>>> =
  ref(emptyValue<Page<StandardizedOperationSheet>>())
const operationsBeforeStep30 = ref(emptyValue<Page<Operation>>())

const oneElementPageable = {
  page: 0,
  size: 1,
}

//Operation
const { data: operationsAtStep30, reload: reloadOperationsAtStep30 } = usePagination<Operation>(
  (filter, pageable) => operationApi.findAll(filter, pageable),
  {
    operationsGroupId: props.id,
    stepIds: [30],
  },
  { ...oneElementPageable }
)

const { data: operationsAtStep40, reload: reloadOperationsAtStep40 } = usePagination<Operation>(
  (filter, pageable) => operationApi.findAll(filter, pageable),
  {
    operationsGroupId: props.id,
    stepIds: [40],
  },
  { ...oneElementPageable }
)

watch(
  setOperationsDialog,
  (v) => {
    if (!v) {
      handleAxiosPromise(
        operationsBeforeStep30,
        operationApi.findAll(
          {
            operationsGroupId: props.id,
            stepIds: [10, 20],
          },
          oneElementPageable
        ),
        {
          afterSuccess: () => {
            if (operationsBeforeStep30.value.value && operationsBeforeStep30.value.value?.totalElements == 0) {
              handleAxiosPromise(
                standardizedOperationSheets,
                standardizedOperationSheetApi.findAll(oneElementPageable, { operationGroupId: props.id })
              )
            }
          },
        }
      )
      reloadOperationsAtStep30()
      reloadOperationsAtStep40()
      reloadFinancialIncentive()
    }
  },
  {
    immediate: true,
  }
)

//Document manquant validation étape
const missingDocumentTypes = ref(emptyValue<Page<DocumentType>>())
provide(missingDocumentTypeIdsKey, {
  missingDocumentTypes: computed(() => missingDocumentTypes.value.value?.content ?? []),
  reload: () => {
    throw new Error('reload not implemented')
  },
})

const getMissingDocumentType = (stepId: number) => {
  if (stepId == 30) {
    handleAxiosPromise(
      missingDocumentTypes,
      documentTypeApi.getAll(
        {},
        {
          missingToValidateOperationsGroupForStep30: props.id,
          ignoreForSelfWorks: validateStep30Request.value.selfWorks ? false : undefined,
        }
      )
    )
  } else if (stepId == 40) {
    console.debug('content', operations.value.value?.content)
    handleAxiosPromise(
      missingDocumentTypes,
      documentTypeApi.getAll(
        {},
        {
          missingToValidateOperationsGroupForStep40: props.id,
          ignoreForSelfWorks: operations.value.value?.content[0].selfWorks ? false : undefined,
        }
      )
    )
  }
}

//Valider étape 30
const numberOfOperationsWithSelfWorks = ref(emptyValue<Page<Operation>>())

const getNumberOfOperationsWithSelfWorks = async () => {
  return handleAxiosPromise(
    numberOfOperationsWithSelfWorks,
    operationApi.findAll({ selfWorks: true, operationsGroupId: props.id }, { size: 1 })
  )
}

onMounted(getNumberOfOperationsWithSelfWorks)

const step30Dialog = ref(false)
const operationsGroupDocumentAllViewRef = ref<typeof OperationsGroupDocumentAllView | null>(null)
watch(step30Dialog, async (v) => {
  if (v) {
    await getNumberOfOperationsWithSelfWorks()
    validateStep30Request.value.selfWorks =
      numberOfOperationsWithSelfWorks.value.value?.totalElements == operations.value.value?.totalElements

    getMissingDocumentType(30)
    promisableValidateStep30.value.error = undefined
  } else {
    operationsGroupDocumentAllViewRef.value?.reload()
    operationsGroupEventHistoryViewRef.value?.reload()
    operationsGroupDocumentAllViewRef.value?.reload()
  }
})

const displayValidationButton = (
  group: PromisableValue<OperationsGroup>,
  simulationAtStepX: PromisableValue<Page<Operation>>
) =>
  group.value &&
  simulationAtStepX.value &&
  group.value.operationsNumber == simulationAtStepX.value.totalElements &&
  group.value.operationsNumber > 0

const displayValidateStep30Button = computed(() =>
  displayValidationButton(operationsGroup.value, operationsAtStep30.value)
)

const disabledNext = computed(
  () =>
    step40Dialog.value && (!validateStep40Request.value.customerDuplicate || !validateStep40Request.value.signedDate)
)

const validateStep30Request = ref<ValidateStep30OperationsGroupRequest>(makeEmptyValidateStep30OperationsGroupRequest())
const promisableValidateStep30 = ref(emptyValue())
const formStep30 = ref<typeof VForm | null>(null)
const validateStep30 = async () => {
  if (!props.id) {
    return
  }
  const validation = await formStep30.value!.validate()
  if (!validation.valid) {
    return
  }

  promisableValidateStep30.value = loadingValue()
  const sendDocumentsResponse = (await genericDocumentSubmitRef.value?.uploadFiles()) ?? []
  if (sendDocumentsResponse !== false && sendDocumentsResponse.some((it) => it.type === 'success')) {
    getMissingDocumentType(30)
  }
  if (sendDocumentsResponse === false || sendDocumentsResponse.some((it) => it.type === 'error')) {
    promisableValidateStep30.value.loading = false
    return
  }

  if (validateStep30Request.value.selfWorks) {
    validateStep30Request.value = {
      ...validateStep30Request.value,
      customerOfferNumber: null,
      offersDispatchDate: null,
    }
  }

  handleAxiosPromise(
    promisableValidateStep30,
    operationsGroupApi.validateStep30(props.id, validateStep30Request.value),
    {
      afterSuccess: () => {
        snackbarStore.setSuccess("Succès lors du passage à l'étape 40")
        step30Dialog.value = false
        reloadOperationsAtStep40()
        reloadOperations()
        operationsAtStep30.value = emptyValue<Page<Operation>>()
      },
      afterError: async (e) => {
        snackbarStore.setError(await handleAxiosException(e))
      },
    }
  )
}

//Valider étape 40
const step40Dialog = ref(false)

watch(step40Dialog, (v) => {
  if (v) {
    getMissingDocumentType(40)
    promisableValidateStep40.value.error = undefined
  } else {
    operationsGroupDocumentAllViewRef.value?.reload()
    operationsGroupEventHistoryViewRef.value?.reload()

    operationsGroupDocumentAllViewRef.value?.reload()
  }
})
const promisableValidateStep40 = ref(emptyValue<OperationsGroup>())
const formStep40 = ref<typeof VForm | null>(null)
const validateStep40Request = ref(makeEmptyValidateStep40OperationsGroupRequest())

const displayValidateStep40Button = computed(() =>
  displayValidationButton(operationsGroup.value, operationsAtStep40.value)
)

const validateStep40 = async () => {
  if (!props.id) {
    return
  }
  const validation = await formStep40.value!.validate()
  if (!validation.valid) {
    return
  }
  promisableValidateStep40.value = loadingValue()
  const sendDocumentsResponse = (await genericDocumentSubmitRef.value?.uploadFiles()) ?? []
  if (sendDocumentsResponse !== false && sendDocumentsResponse.some((it) => it.type === 'success')) {
    getMissingDocumentType(40)
  }
  if (sendDocumentsResponse === false || sendDocumentsResponse.some((it) => it.type === 'error')) {
    promisableValidateStep40.value.loading = false
    return
  }

  handleAxiosPromise(
    promisableValidateStep40,
    operationsGroupApi.validateStep40(props.id, validateStep40Request.value),
    {
      afterSuccess: () => {
        snackbarStore.setSuccess("Succès lors du passage à l'étape 50")
        step40Dialog.value = false
        reloadOperations()
        operationsAtStep40.value = emptyValue<Page<Operation>>()
      },
    }
  )
}

const parseOperationsError = (response: string): string[] => {
  return JSON.parse(response)
}

const { data: operationsAfterStep50 } = usePagination<Operation>(
  (filter, pageable) => operationApi.findAll(filter, pageable),
  {
    operationsGroupId: props.id,
    stepIds: [60, 70, 80, 90, 100, 110, 120],
  },
  { ...oneElementPageable }
)

const valuationToUpdate = computed(
  () =>
    (operations.value.value?.content.filter((ope, index) => differentValuation(ope, valuations.value[index]))?.length ??
      0) > 0
)

const updatingAvailable = computed(() =>
  selection.value.filter((ope, index) => differentValuation(ope, valuations.value[index]))
)

// Chantier
const selectWorks = (works: Works[]) => {
  validateStep30Request.value.works1 = works[0]
  validateStep30Request.value.works2 = works[1]
  validateStep30Request.value.works3 = works[2]
}

const selfWorksMessages = computed(
  () =>
    `Attention il y a ${
      numberOfOperationsWithSelfWorks.value.value?.totalElements
    } opération(s) en travaux en propre et ${
      (operations.value.value?.totalElements ?? 0) - (numberOfOperationsWithSelfWorks.value.value?.totalElements ?? 0)
    } opération(s) qui ne le sont pas. Toutes les opérations d’un regroupement doivent avoir le même statut de travaux en propre`
)

const updateSelfWorksDialog = useConfirmAlertDialog()
const selfWorksValue = ref<boolean>(false)

const handleUpdateSelfWorksDialog = async () => {
  selfWorksValue.value =
    (operations.value.value?.totalElements ?? 0) == (numberOfOperationsWithSelfWorks.value.value?.totalElements ?? 0)
  if (await updateSelfWorksDialog.confirm()) {
    operationsGroupApi
      .updateSelfWorks(props.id, selfWorksValue.value)
      .then(() => {
        reloadFinancialIncentive()
        reloadOperations()
        getNumberOfOperationsWithSelfWorks()
        snackbarStore.setSuccess('Le statut travaux en propre a bien été modifié')
        if (selfWorksValue.value == true) {
          operationsGroup.value.value!.commercialOfferWithoutFinancialIncentive = 0
          operationsGroup.value.value!.customerFinancialIncentive = 0
        } else {
          operationsGroup.value.value!.commercialOfferWithoutFinancialIncentive = null
          operationsGroup.value.value!.customerFinancialIncentive = null
        }
      })
      .catch(async (err) => {
        snackbarStore.setError(await handleAxiosException(err))
      })
  }
}

//
const editFinancialIncentiveDialog = useEditDialog(
  computed(() => ({
    commercialOfferWithoutFinancialIncentive: operationsGroup.value.value?.commercialOfferWithoutFinancialIncentive,
    customerFinancialIncentive: operationsGroup.value.value?.customerFinancialIncentive,
  }))
)
const editFinancial = async () => {
  if (await editFinancialIncentiveDialog.confirm()) {
    try {
      await operationsGroupApi.edit(
        operationsGroup.value.value!.id,
        mapToOperationsGroupRequest(operationsGroup.value.value!, {
          customerFinancialIncentive: editFinancialIncentiveDialog.value.value?.customerFinancialIncentive,
          commercialOfferWithoutFinancialIncentive:
            editFinancialIncentiveDialog.value.value?.commercialOfferWithoutFinancialIncentive,
        })
      )
      operationsGroup.value.value!.customerFinancialIncentive =
        editFinancialIncentiveDialog.value.value?.customerFinancialIncentive
      operationsGroup.value.value!.commercialOfferWithoutFinancialIncentive =
        editFinancialIncentiveDialog.value.value?.commercialOfferWithoutFinancialIncentive
      reloadFinancialIncentive()
      snackbarStore.setSuccess('Incitation financière mise à jour avec succès')
      operationsGroupEventHistoryViewRef.value?.reload()
    } catch (e) {
      snackbarStore.setError(await handleAxiosException(e))
    }
  }
}

// Edit Valorisation
const editValuationAtypicalDialog = useEditDialog(
  computed(() => ({
    atypicalClassicValuationValue: operationsGroup.value.value?.atypicalClassicValuationValue,
    atypicalPrecariousnessValuationValue: operationsGroup.value.value?.atypicalPrecariousnessValuationValue,
  }))
)
const editValuationAtypical = async () => {
  if (await editValuationAtypicalDialog.confirm()) {
    try {
      await operationsGroupApi.edit(
        operationsGroup.value.value!.id,
        mapToOperationsGroupRequest(operationsGroup.value.value!, {
          atypicalClassicValuationValue: editValuationAtypicalDialog.value.value?.atypicalClassicValuationValue ?? null,
          atypicalPrecariousnessValuationValue:
            editValuationAtypicalDialog.value.value?.atypicalPrecariousnessValuationValue ?? null,
        })
      )
      operationsGroup.value.value!.atypicalClassicValuationValue =
        editValuationAtypicalDialog.value.value?.atypicalClassicValuationValue ?? null
      operationsGroup.value.value!.atypicalPrecariousnessValuationValue =
        editValuationAtypicalDialog.value.value?.atypicalPrecariousnessValuationValue ?? null
      snackbarStore.setSuccess('Valorisation atypique mise à jour avec succès')
      operationsGroupEventHistoryViewRef.value?.reload()
      reloadOperationsGroup()
    } catch (e) {
      snackbarStore.setError(await handleAxiosException(e))
    }
  }
}

// Bénéficiaires
const beneficiaryDialog = ref(false)

const openBeneficiaryDialog = () => {
  beneficiaryDialog.value = true
}

const handleUpdateSelectedBeneficiary = async (selected: Beneficiary[]) => {
  if (selected.length) {
    if (operationsGroup.value.value?.beneficiary?.id != selected[0].id) {
      if (
        !(await dialogStore.addAlert({
          title: 'Confirmation de changement de bénéficiaire',
          message:
            "Vous allez changer le bénéficiaire d'un regroupement.\nCela va modifier les bénéficiaires de toutes les opérations du regroupement.\nVoulez-vous continuer ?",
          maxWidth: '640px',
        }))
      ) {
        return
      }
      operationsGroupApi
        .edit(
          operationsGroup.value.value!.id,
          mapToOperationsGroupRequest(operationsGroup.value.value!, {
            beneficiaryId: selected[0].id,
          })
        )
        .then(() => {
          operationsGroup.value.value!.beneficiary = selected[0]
          snackbarStore.setSuccess('Le bénéficiaire a été mis à avec succès')
        })
        .catch(async (e) => {
          snackbarStore.setError(await handleAxiosException(e))
        })
      return
    }
  } else {
    dialogStore.addAlert2({
      message: 'Impossible de supprimer un bénéficiaire dans un regroupement',
      title: "Edition de bénéficiaire d'un regroupement",
      width: '600px',
    })
  }
}

// Sous traitants
const handleUpdateSelectedSubcontractor = async (selected: Subcontractor | null) => {
  if (operationsGroup.value.value?.subcontractor?.id != selected?.id) {
    if (
      !(await dialogStore.addAlert({
        title: 'Confirmation de changement de sous traitant',
        message:
          "Vous allez changer le sous traitant d'un regroupement.\nCela va modifier les sous traitants de toutes les opérations du regroupement qui n'ont pas spécifié de sous traitant.\nVoulez-vous continuer ?",
        maxWidth: '640px',
      }))
    ) {
      return
    }
    operationsGroupApi
      .edit(
        operationsGroup.value.value!.id,
        mapToOperationsGroupRequest(operationsGroup.value.value!, {
          subcontractorId: selected?.id,
        })
      )
      .then(() => {
        operationsGroup.value.value!.subcontractor = selected
        snackbarStore.setSuccess('Le sous traitant a été mis à avec succès')
      })
      .catch(async (e) => {
        snackbarStore.setError(await handleAxiosException(e))
      })
    return
  }
}

// Organisation
const editOrganisationDialog = useEditDialog(
  computed(() => operationsGroup.value.value?.entity),
  {
    dialogStoreRequest: {
      title: 'Confirmation Modification Organisation',
      message:
        "Vous allez modifier une organisation d'un regroupement, et par conséquent, les organisations de toutes les opérations concernées par le regroupement.\nVoulez-vous continuer ?",
      maxWidth: '640px',
    },
  }
)
const editOrganisation = async () => {
  if (await editOrganisationDialog.confirm()) {
    try {
      await operationsGroupApi.edit(
        operationsGroup.value.value!.id,
        mapToOperationsGroupRequest(operationsGroup.value.value!, {
          entityId: editOrganisationDialog.value.value!.id,
        })
      )
      operationsGroup.value.value!.entity = editOrganisationDialog.value.value!
      reloadFinancialIncentive()
      snackbarStore.setSuccess('Incitation financière mise à jour avec succès')
    } catch (e) {
      snackbarStore.setError(await handleAxiosException(e))
    }
  }
}

// Name
const editNameDialog = useEditDialog(computed(() => operationsGroup.value.value?.name))
const editName = async () => {
  if (await editNameDialog.confirm()) {
    try {
      await operationsGroupApi.edit(
        operationsGroup.value.value!.id,
        mapToOperationsGroupRequest(operationsGroup.value.value!, {
          name: editNameDialog.value.value!,
        })
      )
      operationsGroup.value.value!.name = editNameDialog.value.value!
      reloadFinancialIncentive()
      snackbarStore.setSuccess('Nom du regroupement mise à jour avec succès')
    } catch (e) {
      snackbarStore.setError(await handleAxiosException(e))
    }
  }
}

const isSelfWorksOnOperationsGroup = computed(() => {
  return (
    numberOfOperationsWithSelfWorks.value.value?.totalElements &&
    numberOfOperationsWithSelfWorks.value.value?.totalElements == operations.value.value?.totalElements
  )
})

const onUpdateSelfWorks = (value: boolean) => {
  validateStep30Request.value.selfWorks = value
  getMissingDocumentType(30)
}

const canCertified = computed(() => userHasRole(userStore.currentUser, 'SIEGE', 'INSTRUCTEUR', 'ADMIN', 'ADMIN_PLUS'))

const genericDocumentSubmitRef = useTemplateRef('genericDocumentSubmit')

const previousStepLoading = ref(false)
const previousStep = (currentStepId: number) => {
  previousStepLoading.value = true
  operationsGroupApi
    .previousStep(props.id, currentStepId)
    .then(() => {
      snackbarStore.setSuccess("Le regroupement a été mis à l'étape précédente")
      return Promise.allSettled([reloadOperationsAtStep30(), reloadOperationsAtStep40(), reloadOperations()])
    })
    .catch(async (err) => {
      snackbarStore.setError(await handleAxiosException(err))
    })
    .finally(() => {
      previousStepLoading.value = false
    })
}
</script>
