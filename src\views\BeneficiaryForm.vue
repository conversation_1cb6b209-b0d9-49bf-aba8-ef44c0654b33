<template>
  <VCard class="content-layout">
    <VCardTitle class="content-layout__header">
      <div v-if="mode == 'create'">Créer un bénéficiaire</div>
      <div v-else-if="mode == 'edit'">Modifier un bénéficiaire</div>
      <div v-else>Dupliquer un bénéficiaire</div>
    </VCardTitle>
    <VDivider />
    <VCardText v-if="localBeneficiary" class="content-layout__main">
      <VForm ref="formRef">
        <VRow class="flex-column">
          <VCol>
            <VTextField v-model="localBeneficiary.socialReason" label="Raison sociale" />
          </VCol>
          <VCol>
            <NjSiretInput v-model="localBeneficiary.siren" label="Siren" type="siren" />
          </VCol>
          <VCol>
            <VTextField v-model="localBeneficiary.address" label="Adresse" />
          </VCol>
          <VCol>
            <VTextField v-model="localBeneficiary.postalCode" label="Code Postal" :rules="[emptyOrPostalCodeRule]" />
          </VCol>
          <VCol>
            <VTextField v-model="localBeneficiary.city" label="Ville" />
          </VCol>
          <VCol v-show="gouvSuggestedAdressRef?.displaySuggestion">
            <GouvSuggestedAdress
              ref="gouvSuggestedAdressRef"
              v-model:city="localBeneficiary.city"
              v-model:postal-code="localBeneficiary.postalCode"
              v-model:street="localBeneficiary.address"
            />
          </VCol>
          <VCol>
            <VTextField v-model="localBeneficiary.email" label="Adresse mail du bénéficiaire" :rules="[emptyOrEmail]" />
          </VCol>
          <VCol>
            <NjPhoneInput
              v-model="localBeneficiary.phoneNumber"
              label="Numéro de téléphone"
              :included-region-codes="['FR']"
            />
          </VCol>
          <VCol>
            <VTextField v-model="localBeneficiary.lastName" label="Nom" :rules="[requiredRule]" />
          </VCol>
          <VCol>
            <VTextField v-model="localBeneficiary.firstName" label="Prénom" />
          </VCol>
          <VCol>
            <VTextField v-model="localBeneficiary.capacity" label="En qualité de" />
          </VCol>
          <VCol>
            <VTextField v-model="localBeneficiary.legalStatus" label="Forme juridique" />
          </VCol>
          <VCol>
            <VDivider />
            <VSwitch v-if="canCertified" v-model="localBeneficiary.certified" label="Certifié" />
          </VCol>
        </VRow>
      </VForm>
    </VCardText>
    <VCardActions class="content-layout__footer">
      <VSpacer />
      <NjBtn variant="outlined" @click="cancel">Annuler</NjBtn>
      <NjBtn v-if="mode == 'create'" :disabled="promisableBeneficiary.loading" @click="createBeneficiary"
        >Ajouter</NjBtn
      >
      <NjBtn v-else-if="mode == 'edit'" :disabled="promisableBeneficiary.loading" @click="editBeneficiary"
        >Valider</NjBtn
      >
      <NjBtn v-else :disabled="promisableBeneficiary.loading" @click="createBeneficiary">Valider</NjBtn>
    </VCardActions>
  </VCard>
</template>
<script setup lang="ts">
import { beneficiaryApi } from '@/api/beneficiary'
import GouvSuggestedAdress from '@/components/GouvSuggestedAdress.vue'
import NjBtn from '@/components/NjBtn.vue'
import { useDialogStore } from '@/stores/dialog'
import { useSnackbarStore } from '@/stores/snackbar'
import { mapToBeneficiaryRequest, type Beneficiary } from '@/types/beneficiary'
import { emptyOrEmail, emptyOrPostalCodeRule, requiredRule } from '@/types/rule'
import type { PropType } from 'vue'
import { VForm } from 'vuetify/components/VForm'

export type BeneficiaryFormAction = 'create' | 'edit' | 'duplicate'
const props = defineProps({
  mode: {
    type: String as PropType<BeneficiaryFormAction>,
    default: 'create',
  },
  beneficiary: {
    type: Object as PropType<Beneficiary>,
  },
  cancel: {
    type: Function as PropType<(item: any) => void>,
    default: () => {},
  },
  afterSuccess: {
    type: Function as PropType<(beneficiary: Beneficiary) => void>,
    default: () => {},
  },
  canCertified: {
    type: Boolean,
  },
  updatedBeneficiary: {
    type: Object as PropType<Beneficiary | null>,
  },
})

const emit = defineEmits(['update:updatedBeneficiary'])

const localBeneficiary = ref<Beneficiary>()

const snackbarStore = useSnackbarStore()
const dialogStore = useDialogStore()

onMounted(() => {
  if (props.beneficiary) {
    localBeneficiary.value = { ...props.beneficiary, certified: false }
  } else {
    localBeneficiary.value = makeEmptyBeneficiary()
  }
})
const formRef = ref<VForm | null>(null)
const promisableBeneficiary = ref(emptyValue<Beneficiary>())

const createBeneficiary = async () => {
  const validation = await formRef.value!.validate()
  if (validation.valid) {
    handleAxiosPromise(promisableBeneficiary, beneficiaryApi.create(mapToBeneficiaryRequest(localBeneficiary.value!)), {
      afterSuccess: () => {
        snackbarStore.setSuccess('Le bénéficiaire a bien été créé')
        props.afterSuccess(promisableBeneficiary.value.value!)
      },
      afterError: (e) => {
        snackbarStore.setError(e ?? "Le bénéficiaire n'a pas pu être créé")
      },
    })
  }
}

const editBeneficiary = async () => {
  const validation = await formRef.value!.validate()
  if (validation.valid) {
    if (
      !localBeneficiary.value?.siren &&
      !(await dialogStore.addAlert({
        title: 'Champs SIREN vide',
        message:
          "Le champ SIREN est vide, or ce cas s'applique uniquement au Particulier ou au Syndic Bénévole. Si ce n'est pas votre cas, merci de renseigner le SIREN.",
        maxWidth: '640px',
      }))
    ) {
      return
    }

    handleAxiosPromise(
      promisableBeneficiary,
      beneficiaryApi.update(localBeneficiary.value!.id, mapToBeneficiaryRequest(localBeneficiary.value!)),
      {
        afterSuccess: () => {
          emit('update:updatedBeneficiary', promisableBeneficiary.value.value)
          localBeneficiary.value = promisableBeneficiary.value.value
          snackbarStore.setSuccess('Le bénéficiaire a bien été modifié')
          props.afterSuccess(promisableBeneficiary.value.value!)
        },
        afterError: (e) => {
          snackbarStore.setError(e ?? "Le bénéficiaire n'a pas pu être édité")
        },
      }
    )
  }
}

const gouvSuggestedAdressRef = ref<typeof GouvSuggestedAdress | null>(null)
</script>
