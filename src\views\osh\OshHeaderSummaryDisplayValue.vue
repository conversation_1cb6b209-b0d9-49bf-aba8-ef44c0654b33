<template>
  <VRow>
    <VCol v-if="oshHeaderSummaryItems.error">
      <ErrorAlert :message="oshHeaderSummaryItems.error" />
    </VCol>
    <VCol v-if="oshHeaderSummaryItems.loading">
      <VProgressLinear indeterminate color="primary" />
    </VCol>
    <template v-if="oshHeaderSummaryItems.value">
      <VCol v-for="item in oshHeaderSummaryItems.value" :key="item.title + '-' + item.value">
        <span class="text-no-wrap">
          <b>{{ item.title }} : </b>
          {{ item.value }}
          <span class="text-primary"
            >&nbsp;{{ item.selectedValue != undefined ? `(${item.selectedValue})` : '' }}
          </span>
        </span>
      </VCol>
    </template>
  </VRow>
</template>
<script setup lang="ts">
import type { OperationExportResultDto } from '@/types/operationExportResultDto'
import type { OperationSummary } from '@/types/operationSummary'
import { getSelectedSummary, type OshFolderSummary } from '@/types/osh/oshFolder'
import type { PromisableValue } from '@/types/promisableValue'
import { add, format } from 'date-fns'

const props = defineProps<{
  summary: PromisableValue<OshFolderSummary | OperationSummary>
  filteredSummary: PromisableValue<OshFolderSummary | OperationSummary>
  selections: OperationExportResultDto[]
}>()

const { summary: oshFolderSummary, selections, filteredSummary } = toRefs(props)

const emmyFolderNames = computed(() => {
  if (oshFolderSummary.value.value && 'emmyFolderIdWithName' in oshFolderSummary.value.value) {
    const names = oshFolderSummary.value.value?.emmyFolderIdWithName
      ?.filter((i) => i.id)
      .map((i) => i.name)
      ?.join(', ')
    return names ? names : 'N/A'
  } else {
    return undefined
  }
})

const selectionSummary = computed(() => getSelectedSummary(selections.value))

const oshHeaderSummaryItems = computed(
  (): PromisableValue<
    {
      title: string
      value?: string
      selectedValue?: string
    }[]
  > => ({
    loading: oshFolderSummary.value.loading,
    error: oshFolderSummary.value.error,
    value: oshFolderSummary.value.value
      ? [
          ...(oshFolderSummary.value.value && 'minEndWorksDate' in oshFolderSummary.value.value
            ? [
                {
                  title: 'Dossier Emmy',
                  value: emmyFolderNames.value,
                },
                {
                  title: 'Limite Dépot PNCEE min.',
                  value: oshFolderSummary.value.value?.minEndWorksDate
                    ? format(
                        minusBusinessDay(add(oshFolderSummary.value.value.minEndWorksDate, { years: 1 }), 5),
                        dateHumanFormat
                      )
                    : 'N/A',
                },
              ]
            : []),
          {
            title: 'Total cumac',
            value: formatNumber(
              (oshFolderSummary.value.value?.classicCumacSum ?? 0) +
                (oshFolderSummary.value.value?.precariousnessCumacSum ?? 0)
            ),
            selectedValue: selectionSummary.value.operationsNumber
              ? formatNumber(selectionSummary.value.totalCumac)
              : props.filteredSummary.value
                ? formatNumber(
                    (filteredSummary.value.value?.classicCumacSum ?? 0) +
                      (filteredSummary.value.value?.precariousnessCumacSum ?? 0)
                  )
                : undefined,
          },
          {
            title: 'Total cumac classique',
            value: formatNumber(oshFolderSummary.value.value?.classicCumacSum),
            selectedValue: selectionSummary.value.operationsNumber
              ? formatNumber(selectionSummary.value.classicCumac)
              : props.filteredSummary.value
                ? formatNumber(filteredSummary.value.value?.classicCumacSum)
                : undefined,
          },
          {
            title: 'Total cumac précarité',
            value: formatNumber(oshFolderSummary.value.value?.precariousnessCumacSum),
            selectedValue: selectionSummary.value.operationsNumber
              ? formatNumber(selectionSummary.value.precariousnessCumac)
              : props.filteredSummary.value
                ? formatNumber(filteredSummary.value.value?.precariousnessCumacSum)
                : undefined,
          },
          {
            title: "Nombre d'opérations",
            value: formatNumber(oshFolderSummary.value.value?.operationsNumber),
            selectedValue: selectionSummary.value.operationsNumber
              ? formatNumber(selectionSummary.value.operationsNumber)
              : props.filteredSummary.value
                ? formatNumber(filteredSummary.value.value?.operationsNumber)
                : undefined,
          },
        ]
      : undefined,
  })
)
</script>
