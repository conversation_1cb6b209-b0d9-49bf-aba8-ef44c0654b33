<template>
  <NjExpansionPanel title="Arrêté contrôle">
    <VRow class="flex-column" dense>
      <VCol>
        <NjDisplayValue
          label="Type de contrôle"
          :value="controlOrderTypeLabel.find((i) => i.value == operation.controlOrderDetails?.controlOrderType)?.label"
        >
          <template v-if="edit && operation.stepId === 70" #value>
            <div class="w-50">
              <VSelect
                :items="controlOrderTypeItems"
                item-title="label"
                item-value="value"
                :model-value="operation.controlOrderDetails?.controlOrderType"
                clearable
                @update:model-value="
                  (event) => {
                    emit('update:controlOrderDetails', {
                      ...operation.controlOrderDetails,
                      controlOrderType: event,
                      controlOrderStatus: undefined,
                      afterSalesServiceStatus: undefined,
                    })
                  }
                "
              />
            </div>
          </template>
        </NjDisplayValue>
      </VCol>
      <VCol>
        <NjDisplayValue
          label="Statut du contrôle"
          :value="mapControlOrderStatus(operation.controlOrderDetails?.controlOrderStatus ?? null)"
        >
          <template v-if="edit && operation.stepId === 70" #value>
            <div class="w-50">
              <VSelect
                :disabled="!operation.controlOrderDetails?.controlOrderType"
                :items="controlOrderStatusItems"
                :model-value="operation.controlOrderDetails?.controlOrderStatus"
                item-title="title"
                item-value="value"
                clearable
                @update:model-value="
                  (event) => {
                    emit('update:controlOrderDetails', {
                      ...operation.controlOrderDetails,
                      controlOrderStatus: event,
                      afterSalesServiceStatus: undefined,
                    })
                  }
                "
              />
            </div>
          </template>
        </NjDisplayValue>
      </VCol>
      <VCol>
        <NjDisplayValue label="Commentaire du contrôle" :value="operation.controlOrderDetails?.commmentary">
          <template v-if="edit && operation.stepId === 70" #value>
            <div class="w-50">
              <VTextarea
                :model-value="operation.controlOrderDetails?.commmentary"
                clearable
                @update:model-value="
                  emit('update:controlOrderDetails', {
                    ...operation.controlOrderDetails,
                    commmentary: $event,
                  })
                "
              />
            </div>
          </template>
        </NjDisplayValue>
      </VCol>
      <VCol>
        <NjDisplayValue
          label="Statut du SAV"
          :value="mapControlOrderAssStatus(operation.controlOrderDetails?.afterSalesServiceStatus ?? null)"
        >
          <template
            v-if="
              edit && operation.stepId === 70 && operation.controlOrderDetails?.controlOrderStatus == 'NOT_SATISFYING'
            "
            #value
          >
            <div class="w-50">
              <VSelect
                :disabled="operation.controlOrderDetails?.controlOrderStatus != 'NOT_SATISFYING'"
                :items="controlOrderAfterSalesServiceStatusItems"
                :model-value="operation.controlOrderDetails?.afterSalesServiceStatus"
                item-title="title"
                item-value="value"
                clearable
                :rules="[availableAfterSalesServiceStatusRule]"
                @update:model-value="
                  emit('update:controlOrderDetails', {
                    ...operation.controlOrderDetails,
                    afterSalesServiceStatus: $event,
                    worksConformityAfterSalesService: undefined,
                  })
                "
              />
            </div>
          </template>
        </NjDisplayValue>
      </VCol>
      <VCol
        v-if="
          operation.controlOrderDetails?.afterSalesServiceStatus ==
          'ADMINISTRATIVE_CONTROL_OF_AFTER_SALES_SERVICE_SHEETS_AND_SUPPORT_DOCUMENTS'
        "
      >
        <NjDisplayValue
          label="Conformité après travaux"
          :value="
            operation.controlOrderDetails.worksConformityAfterSalesService == undefined
              ? ''
              : operation.controlOrderDetails.worksConformityAfterSalesService
                ? 'OUI'
                : 'NON'
          "
        >
          <template v-if="edit && operation.stepId === 70" #value>
            <div class="w-50">
              <VSelect
                :items="[
                  { title: 'Oui', value: true },
                  { title: 'Non', value: false },
                ]"
                clearable
                :model-value="operation.controlOrderDetails.worksConformityAfterSalesService"
                @update:model-value="
                  emit('update:controlOrderDetails', {
                    ...operation.controlOrderDetails,
                    worksConformityAfterSalesService: $event,
                  })
                "
              />
            </div>
          </template>
        </NjDisplayValue>
      </VCol>
    </VRow>
  </NjExpansionPanel>
</template>
<script lang="ts" setup>
import { controlOrderTypeLabel } from '@/types/calcul/standardizedOperationSheet'
import {
  controlOrderAfterSalesServiceStatusItems,
  mapControlOrderStatus,
  mapControlOrderAssStatus,
  type ControlOrderAfterSalesServiceStatus,
} from '@/types/controlOrder'
import type { ControlOrderDetails, Operation } from '@/types/operation'
import { VSelect, VTextarea } from 'vuetify/components'

const props = defineProps({
  edit: Boolean,
  operation: {
    type: Object as PropType<
      Pick<Operation, 'stepId' | 'standardizedOperationSheet'> &
        Partial<Pick<Operation, 'controlOrderBatch' | 'controlOrderDetails'>>
    >,
    default: makeEmptyOperation,
  },
})

const emit = defineEmits<{
  'update:controlOrderDetails': [ControlOrderDetails]
}>()

const defaultAfterSalesServiceStatus = ref()

onMounted(() => {
  defaultAfterSalesServiceStatus.value = props.operation.controlOrderDetails?.afterSalesServiceStatus ?? ''
})

const availableAfterSalesServiceStatusRule = computed(() => {
  return (value: ControlOrderAfterSalesServiceStatus | null) => {
    const error = 'Vous devez passer les étapes une par une'
    if (!value) {
      return true
    }
    switch (defaultAfterSalesServiceStatus.value) {
      case '':
        return [null, 'TO_BE_SENT_BY_CONTROL_OFFICE'].includes(value) ? true : error
      case 'TO_BE_SENT_BY_CONTROL_OFFICE':
        return ['TO_BE_SENT_BY_CONTROL_OFFICE', 'SENT_BY_CONTROL_OFFICE'].includes(value) ? true : error
      case 'SENT_BY_CONTROL_OFFICE':
        return ['TO_BE_SENT_BY_CONTROL_OFFICE', 'SENT_BY_CONTROL_OFFICE', 'SENT_AFTER_SALES_SERVICE_SHEETS'].includes(
          value
        )
          ? true
          : error
      case 'SENT_AFTER_SALES_SERVICE_SHEETS':
        return [
          'SENT_BY_CONTROL_OFFICE',
          'SENT_AFTER_SALES_SERVICE_SHEETS',
          'ADMINISTRATIVE_CONTROL_OF_AFTER_SALES_SERVICE_SHEETS_AND_SUPPORT_DOCUMENTS',
        ].includes(value)
          ? true
          : error
      case 'ADMINISTRATIVE_CONTROL_OF_AFTER_SALES_SERVICE_SHEETS_AND_SUPPORT_DOCUMENTS':
        return [
          'SENT_AFTER_SALES_SERVICE_SHEETS',
          'ADMINISTRATIVE_CONTROL_OF_AFTER_SALES_SERVICE_SHEETS_AND_SUPPORT_DOCUMENTS',
        ].includes(value)
          ? true
          : error
    }
    return error
  }
})

const controlOrderTypeItems = computed(() => {
  if (props.operation.standardizedOperationSheet.controlOrderType == 'SITE_AND_CONTACT') {
    return controlOrderTypeLabel.filter((i) => i.value != 'SITE_AND_CONTACT')
  } else {
    return [controlOrderTypeLabel.find((i) => i.value == props.operation.standardizedOperationSheet.controlOrderType)]
  }
})

const controlOrderStatusItems = computed(() => {
  if (props.operation.controlOrderDetails?.controlOrderType == 'CONTACT') {
    return controlOrderContactStatusItems
  } else {
    return controlOrderSiteStatusItems
  }
})
</script>
