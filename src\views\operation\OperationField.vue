<template>
  <div>
    <NjExpansionPanel :model-value="expandedDetail">
      <template #title>
        <div class="d-flex align-center fill-width">
          Opération
          <VSpacer />
          <OperationStepChip v-if="operation.id > 0 && !createOperation" :operation="operation" />
          <AlertIcon :rules="[validateStepDurationRule, validateSubcontractorRule, validateRgeRule]" />
        </div>
      </template>
      <VRow class="flex-column" dense>
        <VCol>
          <NjDisplayValue label="Chrono" :value="operation.chronoCode" />
        </VCol>
        <VCol v-if="operation.stepId >= 10">
          <NjTextField
            v-if="edit"
            :model-value="operation.operationName"
            label="Nom de l'opération"
            :rules="[requiredRule]"
            @update:model-value="emit('update:operation', { ...operation, operationName: $event })"
          />
          <NjDisplayValue v-else label="Nom" :value="operation.operationName" />
        </VCol>
        <VCol>
          <NjDisplayValue label="Organisation" :value="displayEntity(operation.entity)" class="align-center">
            <template v-if="edit && selectEntity" #value>
              <div class="w-50">
                <RemoteAutoComplete
                  label="Organisation d'appartenance"
                  :model-value="selectedEntity"
                  :query-for-all="
                    (search, pageable) =>
                      entityApi.getAll({ search: search, myEntities: true, level: 4, visible: true }, pageable)
                  "
                  :query-for-one="(org: Entity) => entityApi.getOne(org?.id ?? 0)"
                  item-title="name"
                  :rules="[requiredRule]"
                  return-object
                  clearable
                  infinite-scroll
                  @update:model-value="emit('update:selected-entity', $event)"
                />
              </div>
            </template>
          </NjDisplayValue>
        </VCol>
        <VCol v-if="operation.stepId > 0">
          <NjDisplayValue label="Période" :value="(operation.period ?? realPeriod)?.name">
            <template v-if="!createOperation && operation.period?.id !== realPeriod?.id" #value>
              <div class="d-flex align-center text-warning font-weight-bold" style="gap: 8px">
                {{ operation.period?.name }} <VIcon icon="mdi-arrow-right" /> {{ realPeriod?.name }}
              </div>
            </template>
          </NjDisplayValue>
        </VCol>
        <VCol v-if="operation.stepId > 30">
          <NjDisplayValue label="Numéro Offre Client" :value="operation.customerOfferNumber">
            <template v-if="edit" #value>
              <div class="w-50">
                <NjTextField
                  :model-value="operation.customerOfferNumber"
                  :rules="!operation.selfWorks && operation.stepId >= 40 && operation.stepId < 60 ? [requiredRule] : []"
                  @update:model-value="emit('update:operation', { ...operation, customerOfferNumber: $event })"
                />
              </div>
            </template>
          </NjDisplayValue>
        </VCol>
        <template v-if="!edit">
          <VCol v-if="operation.lostDate">
            <NjDisplayValue label="Date de la perte de l'offre" :value="operation.lostDate" />
          </VCol>
          <VCol v-if="operation.lostReasons">
            <NjDisplayValue label="Raisons de la perte de l'offre">
              <template #value>
                <VRow class="flex-column" no-gutter>
                  <VCol
                    v-for="(reasons, index) in Object.keys(operation.lostReasons as Object)"
                    :key="index"
                    align="end"
                  >
                    <b>{{ lostReasonsLabels[reasons] }}:</b> {{ operation.lostReasons[reasons] }}
                  </VCol>
                </VRow>
              </template>
            </NjDisplayValue>
          </VCol>
        </template>
        <VCol v-if="operation.stepId > 0">
          <NjDisplayValue
            label="Demandeur"
            :value="displayFullnameUser(createOperation ? userStore.currentUser : operation.applicantUser)"
          />
        </VCol>
        <VCol>
          <NjDisplayValue label="Second interlocuteur" :value="operation.secondInterlocutor">
            <template v-if="edit" #value>
              <div class="w-50">
                <NjTextField
                  :model-value="operation.secondInterlocutor"
                  @update:model-value="emit('update:operation', { ...operation, secondInterlocutor: $event })"
                />
              </div>
            </template>
          </NjDisplayValue>
        </VCol>
        <VCol>
          <VInput :model-value="operation.instructor" :rules="[requiredRule]" hide-details="auto">
            <NjDisplayValue label="Instructeur" :value="displayFullnameUser(operation.instructor ?? makeEmptyUser())">
              <template v-if="edit && userStore.isSiege" #value>
                <div class="nj-display-value__value">
                  {{ displayFullnameUser(operation.instructor ?? makeEmptyUser()) }}
                </div>
                <NjIconBtn
                  v-if="operation.id !== 0 && (operation.stepId > 50 || userStore.isAdmin)"
                  icon="mdi-pencil"
                  color="primary"
                  @click="instructorDialog = true"
                />
                <UserAllViewDialog
                  v-model="instructorDialog"
                  title="Sélectionner un instructeur"
                  :filter="{ roles: ['INSTRUCTEUR'] as ProfileType[], active: true }"
                  :selections="[operation.instructor]"
                  @update:selections="emit('update:operation', { ...operation, instructor: $event[0] })"
                />
              </template>
            </NjDisplayValue>
          </VInput>
        </VCol>
        <VCol v-if="operation.standardizedOperationSheet.heatFund">
          <NjSwitch
            v-if="edit"
            class="nj-display-value__label"
            label="Fonds chaleur prévu"
            :model-value="operation.heatFundActive"
            @update:model-value="emit('update:operation', { ...operation, heatFundActive: $event, ademeCode: null })"
          />
          <NjDisplayValue v-else label="Fonds chaleur prévu" :value="operation.heatFundActive ? 'OUI' : 'NON'" />
        </VCol>
        <template v-if="operation.heatFundActive">
          <VCol>
            <VTextField
              v-if="edit"
              label="Numéro Ademe"
              :rules="operation.stepId > 50 ? [requiredRule] : []"
              :model-value="operation.ademeCode"
              @update:model-value="emit('update:operation', { ...operation, ademeCode: $event })"
            />
            <NjDisplayValue v-else label="Numéro Ademe" :value="operation.ademeCode ?? ''" />
          </VCol>
          <VCol>
            <NjDatePickerDisplayValue
              v-if="edit"
              label="Date de signature de la convention Ademe"
              :rules="operation.stepId > 50 ? [requiredRule] : []"
              :model-value="operation.ademeConventionSignedDate"
              @update:model-value="emit('update:operation', { ...operation, ademeConventionSignedDate: $event })"
            />
            <NjDisplayValue
              v-else
              label="Date de signature de la convention Ademe"
              :value="
                operation.ademeConventionSignedDate
                  ? formatHumanReadableLocalDate(operation.ademeConventionSignedDate)
                  : ''
              "
            />
          </VCol>
        </template>

        <VCol v-if="!edit && operation.backToStep50Counter">
          <NjDisplayValue
            label="Nombre de fois que l'opération est retourné à l'étape 50"
            :value="operation.backToStep50Counter"
          />
        </VCol>
      </VRow>
    </NjExpansionPanel>
    <VDivider />
    <NjExpansionPanel :model-value="expandedDetail">
      <template #title>
        <div class="d-flex align-center fill-width">
          Date
          <VSpacer />
          <AlertIcon :rules="[validateEstimatedCommitmentDateRule, validateEstimatedEndWorkRule]" />
        </div>
      </template>
      <VRow class="flex-column" dense>
        <VCol>
          <NjDisplayValue
            v-if="operation.stepId <= 40"
            label="Date d'engagement prévisionnelle"
            :color-title="
              edit && operation.estimatedCommitmentDate && !(validateEstimatedCommitmentDateRule == true)
                ? 'warning'
                : ''
            "
            :color-value="
              isEstimatedCommitmentAfterToday
                ? !(validateEstimatedCommitmentDateRule == true)
                  ? 'warning'
                  : 'black'
                : 'error'
            "
            :value="formatHumanReadableLocalDate(operation.estimatedCommitmentDate ?? operation?.signedDate)"
          >
            <template v-if="edit && operation.stepId <= 40" #value>
              <div class="w-50">
                <NjDatePicker
                  :model-value="operation.estimatedCommitmentDate"
                  :rules="operation.stepId >= 10 ? [requiredRule] : undefined"
                  @update:model-value="
                    (event) => {
                      emit('update:operation', { ...operation, estimatedCommitmentDate: event ?? '' })
                      formRef?.validate()
                    }
                  "
                />
              </div>
            </template>
          </NjDisplayValue>
          <NjDisplayValue v-else label="Engagement" :value="formatHumanReadableLocalDate(operation.signedDate)">
            <template v-if="edit" #value>
              <div class="w-50">
                <NjDatePicker
                  :model-value="operation.signedDate"
                  :rules="[requiredRule]"
                  @update:model-value="
                    (event) => {
                      emit('update:operation', { ...operation, signedDate: event ?? '' })
                      formRef?.validate()
                    }
                  "
                />
              </div>
            </template>
          </NjDisplayValue>
        </VCol>
        <VCol v-if="operation.stepId > 30">
          <NjDisplayValue
            label="Date d'envoi de l'offre"
            :value="formatHumanReadableLocalDate(operation.offersDispatchDate ?? '')"
          >
            <template v-if="edit" #value>
              <div class="w-50">
                <NjDatePicker
                  :model-value="operation.offersDispatchDate"
                  :rules="!operation.selfWorks && operation.stepId >= 40 && operation.stepId < 60 ? [requiredRule] : []"
                  @update:model-value="emit('update:operation', { ...operation, offersDispatchDate: $event ?? '' })"
                />
              </div>
            </template>
          </NjDisplayValue>
        </VCol>
        <VCol v-if="operation.stepId <= 50">
          <NjDisplayValue
            label="Date de fin de travaux prévisionnelle"
            :value="formatHumanReadableLocalDate(operation?.estimatedEndOperationDate)"
            :color-title="
              edit && operation.estimatedEndOperationDate && !(validateEstimatedEndWorkRule == true) ? 'warning' : ''
            "
            :color-value="
              isEstimatedEndOperationAfterToday
                ? !(validateEstimatedEndWorkRule == true)
                  ? 'warning'
                  : 'black'
                : 'error'
            "
          >
            <template v-if="edit && operation.stepId <= 50" #value>
              <div class="w-50">
                <NjDatePicker
                  :model-value="operation.estimatedEndOperationDate"
                  :rules="estimatedEndOperationRules"
                  @update:model-value="
                    emit('update:operation', { ...operation, estimatedEndOperationDate: $event ?? '' })
                  "
                />
              </div>
            </template>
          </NjDisplayValue>
        </VCol>
        <VCol v-if="operation.stepId >= 50">
          <NjDisplayValue
            label="Date de fin de travaux"
            :value="formatHumanReadableLocalDate(operation.actualEndWorksDate ?? '')"
          >
            <template v-if="edit" #value>
              <div class="w-50">
                <NjDatePicker
                  :model-value="operation.actualEndWorksDate"
                  :rules="[
                    (value: string) => (isEmpty(value) ? true : rgeRule(value)),
                    actualEndOperationRule,
                    isEmptyOrBeforeToday,
                  ]"
                  @update:model-value="emit('update:operation', { ...operation, actualEndWorksDate: $event ?? '' })"
                />
              </div>
            </template>
          </NjDisplayValue>
        </VCol>
        <VCol v-if="operation.endContractDate">
          <NjDisplayValue
            label="Date de fin de contrat"
            :value="formatHumanReadableLocalDate(operation.endContractDate)"
          ></NjDisplayValue>
        </VCol>
        <VCol
          v-if="
            (operation.stepId === 50 && needControlReportInfos(operation.standardizedOperationSheet)) ||
            (operation.stepId >= 60 && operation.standardizedOperationSheet.controlOrderType === 'SITE') ||
            (operation.stepId >= 70 &&
              operation.standardizedOperationSheet.controlOrderNature === 'SAMPLE' &&
              (operation.standardizedOperationSheet.controlOrderType === 'SITE' ||
                operation.standardizedOperationSheet.controlOrderType === 'SITE_AND_CONTACT'))
          "
        >
          <NjDisplayValue
            label="Date d'émission du rapport de contrôle"
            :value="formatHumanReadableLocalDate(operation.controlReportIssueDate ?? '')"
          >
            <template v-if="edit" #value>
              <div class="w-50">
                <NjDatePicker
                  :model-value="operation.controlReportIssueDate"
                  :rules="[isEmptyOrBeforeToday]"
                  @update:model-value="emit('update:operation', { ...operation, controlReportIssueDate: $event ?? '' })"
                />
              </div>
            </template>
          </NjDisplayValue>
        </VCol>
      </VRow>
    </NjExpansionPanel>
  </div>
</template>
<script lang="ts" setup>
import { entityApi } from '@/api/entity'
import { useUserStore } from '@/stores/user'
import { formatHumanReadableLocalDate } from '@/types/date'
import type { Entity } from '@/types/entity'
import { displayEntity } from '@/types/entity'
import { lostReasonsLabels, type Operation } from '@/types/operation'
import { isEmptyOrBeforeToday, requiredRule, type ValidationRule } from '@/types/rule'
import { displayFullnameUser, makeEmptyUser, type ProfileType } from '@/types/user'
import { isEmpty } from 'lodash'
import { type VForm } from 'vuetify/components'
import AlertIcon from '../AlertIcon.vue'
import { useRealPeriod } from '../operationComposition'
import UserAllViewDialog from '../UserAllViewDialog.vue'
import OperationStepChip from './OperationStepChip.vue'
import { needControlReportInfos } from '@/types/calcul/standardizedOperationSheet'

const props = defineProps({
  expandedDetail: Boolean,
  operation: {
    type: Object as PropType<Operation>,
    default: makeEmptyOperation,
  },
  selectedEntity: Object as PropType<Entity>,
  formRef: Object as PropType<VForm | null>,
  estimatedEndOperationRules: Array as PropType<ValidationRule[]>,
  validateEstimatedCommitmentDateRule: [String, Boolean] as PropType<true | string>,
  validateEstimatedEndWorkRule: [String, Boolean] as PropType<true | string>,
  validateOperation: Boolean as PropType<boolean | null>,
  validateStepDurationRule: [String, Boolean] as PropType<true | string>,
  validateSubcontractorRule: [String, Boolean] as PropType<true | string>,
  validateRgeRule: [String, Boolean] as PropType<true | string>,
  createOperation: Boolean,
  edit: Boolean,
})

const emit = defineEmits<{
  'update:operation': [value: Operation]
  'update:selected-entity': [value: Entity]
}>()

const userStore = useUserStore()

const instructorDialog = ref(false)

const getAgencies = computed(() => userStore.currentUser.entities.filter((orga) => orga.level === 4))

const selectEntity = computed(() => {
  return (
    userStore.currentUser.entities.length &&
    !props.operation.property &&
    !(getAgencies.value.length == userStore.currentUser.entities.length && getAgencies.value.length == 1)
  )
})

const isEstimatedCommitmentAfterToday = computed(
  () => props.operation && new Date() < new Date(props.operation.estimatedCommitmentDate)
)

const isEstimatedEndOperationAfterToday = computed(
  () => props.operation && new Date() < new Date(props.operation.estimatedEndOperationDate)
)

const { realPeriod } = useRealPeriod(computed(() => props.operation))

const { rgeRule, actualEndOperationRule } = useRgeRule(computed<Operation | undefined>(() => props.operation))
</script>
