<template>
  <VRow class="flex-column my-n2">
    <VCol>
      <VRow>
        <VCol cols="4">
          <VTextField v-model="filter.search" label="Recherche" prepend-inner-icon="mdi-magnify" />
        </VCol>
        <VCol cols="4">
          <RemoteAutoComplete
            label="Territoire"
            :model-value="pageFilter.territoryIds?.[0]"
            :query-for-one="(v: number) => territoryApi.findOne(v)"
            :query-for-all="
              (v: string, pageable: Pageable) =>
                territoryApi.findAll({ search: v }, { ...pageable, sort: ['description,ASC'] })
            "
            :item-title="formatTerritory"
            item-value="id"
            infinite-scroll
            clearable
            @update:model-value="updateFilterByFieldname('territoryIds', $event ? [parseInt($event)] : undefined)"
          />
        </VCol>
      </VRow>
    </VCol>
    <VCol>
      <NjDataTable
        v-model:selections="localSelected"
        :page="data.value"
        :pageable="pageable"
        :headers="headers"
        checkboxes
        multi-selection
        @update:pageable="updatePageable"
      >
        <template #[`item.name`]="{ item }">
          <span style="white-space: nowrap" :style="{ 'margin-inline-start': 24 * item.level + 'px' }">
            {{ item.name }}
          </span>
        </template>
        <template #[`item.level`]="{ item }">
          {{ entityLevelLabels[item.level] }}
        </template>
      </NjDataTable>
    </VCol>
    <VCardActions>
      <VSpacer />
      <NjBtn variant="outlined" @click="emit('previous')"> Précédent </NjBtn>
      <NjBtn @click="emit('update:selected', localSelected)"> Valider </NjBtn>
    </VCardActions>
  </VRow>
</template>
<script lang="ts" setup>
import type { DataTableHeader } from '@/components/okta/NjDataTable.type'
import type { EntityFilter } from '@/api/entity'
import { territoryApi } from '@/api/territory'
import type { Entity } from '@/types/entity'
import { entityLevelLabels } from '@/types/entity'
import { debounce } from 'lodash'
import { useUserStore } from '@/stores/user'
import type { Pageable } from '@/types/pagination'
import { formatTerritory } from '@/types/territory'

const props = defineProps({
  selected: {
    type: Array as PropType<Entity[]>,
    default: () => [],
  },
})

const emit = defineEmits<{
  previous: [void]
  'update:selected': [value: Entity[]]
}>()

const userStore = useUserStore()

const localSelected = ref<Entity[]>([])
watch(
  () => props.selected,
  (v) => {
    localSelected.value = v
  },
  {
    immediate: true,
  }
)

const filter = ref({
  search: '',
  territoryIds: undefined as number[] | undefined,
  enabled: true,
  visible: true,
  myEntities: userHasRole(userStore.currentUser, 'ADMIN', 'ADMIN_PLUS') ? undefined : true,
})

const { data, pageable, pageFilter, updatePageable, updateFilter, updateFilterByFieldname } = usePagination<
  Entity,
  EntityFilter
>(
  (filter, pageable) => entityApi.getAll(filter, pageable),
  {
    ...filter.value,
  },
  {
    page: 0,
    size: 10,
    sort: ['id'],
  }
)

const headers: DataTableHeader[] = [
  {
    title: 'Nom',
    value: 'name',
  },
  {
    title: 'Niveau',
    value: 'level',
  },
  {
    title: 'Code',
    value: 'id',
  },
  {
    title: 'Hiérarchie',
    value: 'navFullId',
  },
]

const debounceSearch = debounce((v: string | undefined) => {
  updateFilter({ ...pageFilter.value, search: v! })
}, 300)

watch(
  () => filter.value.search,
  (v) => {
    if (v || v === '') {
      data.value.loading = true
      debounceSearch(v)
    }
  },
  {
    immediate: true,
  }
)
</script>
