<template>
  <NjPage :error-message="data.error">
    <template #sub-header>
      <VRow>
        <VCol cols="3">
          <SearchInput
            v-model:loading="data.loading"
            :model-value="pageFilter.search"
            @update:model-value="updateSearch"
          />
        </VCol>
        <VSpacer />
        <VCol class="flex-grow-0">
          <NjBtn :to="{ name: 'PrecariousnessBonusSheetOneNewView' }">Nouvelle bonification précarité</NjBtn>
        </VCol>
      </VRow>
    </template>
    <template #body>
      <NjDataTable
        :pageable="pageable"
        :page="data.value!"
        :headers="headers"
        :on-click-row="
          (value) =>
            router.push({
              name: 'PrecariousnessBonusSheetOneView',
              params: { id: value.id },
            })
        "
        fixed
        @update:pageable="updatePageable"
      >
        <template #[`item.certified`]="{ item }">
          <VIcon v-show="item.certified" color="#28B750" icon="mdi-shield-check-outline" />
        </template>
      </NjDataTable>
    </template>
  </NjPage>
</template>

<script lang="ts" setup>
import { precariousnessBonusSheetApi } from '@/api/precariousnessBonus'
import NjBtn from '@/components/NjBtn.vue'
import NjPage from '@/components/NjPage.vue'
import type { DataTableHeader } from '@/components/okta/NjDataTable.type'
import NjDataTable from '@/components/okta/NjDataTable.vue'
import { usePaginationInQuery } from '@/types/pagination'
import { VIcon } from 'vuetify/components'

const router = useRouter()

const { data, pageable, pageFilter, updateFilter, updatePageable } = usePaginationInQuery(
  (filter, pageable) => precariousnessBonusSheetApi.findAll(pageable, filter),
  {
    defaultPageFilter: {
      search: '',
    },
    saveFiltersName: 'PrecariousnessBonusSheetAllView',
  }
)

const updateSearch = (value: string) => {
  updateFilter({ ...pageFilter.value, search: value })
}

const headers: DataTableHeader[] = [
  {
    title: 'Nom',
    value: 'name',
  },
  {
    title: 'Certifié',
    value: 'certified',
  },
  {
    title: 'MAJ par',
    value: 'updateUser',
    formater(_, value) {
      return displayFullnameUser(value)
    },
  },
  {
    title: 'Date MAJ',
    value: 'updateDateTime',
    formater(_, value) {
      return formatHumanReadableLocalDateTime(value)
    },
  },
]
</script>
