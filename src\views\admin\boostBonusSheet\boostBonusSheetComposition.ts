import type { BoostBonusSheet } from '@/types/boostBonus'
import type { Page } from '@/types/pagination'
import type { PromisableValue } from '@/types/promisableValue'
import type { Operation } from '@/types/operation'
import type { Ref } from 'vue'

export const DEFAULT_DATE_MAX_BONIFICATION = '2060-01-01'
export const DEFAULT_DATE_MIN_BONIFICATION = '1970-01-01'

export const useFindCorrectBoostBonusSheet = (simulation: Ref<Operation | undefined>) => {
  const boostBonusSheet = computed((): PromisableValue<BoostBonusSheet> => {
    const commitmentDate = simulation.value?.signedDate || simulation.value?.estimatedCommitmentDate
    const endOperation = simulation.value?.actualEndWorksDate || simulation.value?.estimatedEndOperationDate
    if (boostBonusSheets.value.loading) {
      return loadingValue()
    } else if (boostBonusSheets.value.error) {
      return errorValue(boostBonusSheets.value.error)
    } else if (!simulation.value) {
      return loadingValue()
    } else if (boostBonusSheets.value.value?.totalElements === 0) {
      return errorValue('Indispo. pour cette fiche')
    } else if (!commitmentDate) {
      return errorValue('Date eng. ou sign. requise')
    } else if (!endOperation) {
      return errorValue('Date fin travaux prév. ou réel. requise')
    } else if (!boostBonusSheets.value.value?.totalElements) {
      return errorValue('Aucun CDP pour ces dates')
    } else {
      const v = boostBonusSheets.value.value.content
        .sort((op1, op2) =>
          (op1.commitmentMaxDate ?? DEFAULT_DATE_MAX_BONIFICATION).localeCompare(
            op2.commitmentMaxDate ?? DEFAULT_DATE_MAX_BONIFICATION
          )
        )
        .filter(
          (it) =>
            (commitmentDate ?? DEFAULT_DATE_MAX_BONIFICATION) <=
              (it.commitmentMaxDate ?? DEFAULT_DATE_MAX_BONIFICATION) &&
            (commitmentDate ?? DEFAULT_DATE_MIN_BONIFICATION) >= it.commitmentMinDate &&
            (endOperation ?? DEFAULT_DATE_MAX_BONIFICATION) <= (it.endOperationMaxDate ?? DEFAULT_DATE_MAX_BONIFICATION)
        )
      if (v.length === 0) {
        return errorValue('Aucun CDP pour ces dates')
      } else if (v.length !== 1) {
        return errorValue("Erreur imprévue survenue. Contactez l'administrateur")
      } else {
        return succeedValue(v[0])
      }
    }
  })
  const boostBonusSheets = ref(emptyValue<Page<BoostBonusSheet>>())
  const loadingTimeoutId = ref<number>()
  onUnmounted(() => clearTimeout(loadingTimeoutId.value))
  const load = (operationCode: string) => {
    return handleAxiosPromise(
      boostBonusSheets,
      boostBonusSheetApi.findAll({ size: 100 }, { operationCode, visible: true })
    ).catch(() => {
      loadingTimeoutId.value = setTimeout(() => load(operationCode), 5000)
    })
  }
  watch(
    () => simulation.value?.standardizedOperationSheet.operationCode,
    (v) => {
      clearTimeout(loadingTimeoutId.value)
      if (v) {
        load(v)
      } else {
        boostBonusSheets.value = emptyValue()
      }
    },
    {
      immediate: true,
    }
  )

  return { boostBonusSheet: boostBonusSheet, boostBonusSheets: boostBonusSheets }
}
