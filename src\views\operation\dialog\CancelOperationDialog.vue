<template>
  <VDialog :model-value="modelValue" :width="cancelReason === 'LOST' ? '70%' : '40%'">
    <VCard>
      <VCardTitle class="d-flex align-center">
        <span class="d-flex w-100"> Annulation de l'opération </span>
        <NjIconBtn icon="mdi-window-close" variant="flat" @click="emit('update:model-value', false)" />
      </VCardTitle>
      <VDivider />
      <VCardText v-if="cancelReason === 'CANCELLED'"> Êtes-vous sûr de vouloir abandonner l'opération? </VCardText>
      <VCardText v-else-if="cancelReason === 'IMPROPER'">
        Êtes-vous sûr de vouloir passer l'opération en Non Conforme?
      </VCardText>
      <VCardText v-else-if="cancelReason === 'KO_PNCEE'">
        Êtes-vous sûr de vouloir passer l'opération en refusé par le PNCEE?
      </VCardText>
      <VCardText v-else>
        <VRow>
          <VCol><h1>Sélectionnez les éléments qui ont entrainé la perte de l'offre CEE</h1> </VCol>
        </VRow>
        <VRow>
          <VCol>
            <VCheckbox v-model="superiorValuation" label="Valorisation supérieure des MWh cumac" />
          </VCol>
          <VCol v-if="superiorValuation">
            <VTextField v-model="superiorValuationComment" />
          </VCol>
        </VRow>
        <VRow>
          <VCol>
            <VCheckbox v-model="lessExpensive" label="Montant des travaux inférieur" />
          </VCol>
          <VCol v-if="lessExpensive">
            <VTextField v-model="lessExpensiveComment" />
          </VCol>
        </VRow>
        <VRow>
          <VCol>
            <VCheckbox v-model="otherTechnology" label="Variante travaux - autre technologie" />
          </VCol>
          <VCol v-if="otherTechnology">
            <VTextField v-model="otherTechnologyComment" />
          </VCol>
        </VRow>
        <VRow>
          <VCol>
            <VCheckbox v-model="betterCompetitor" label="Engagement contractuel concurrent plus performant" />
          </VCol>
          <VCol v-if="betterCompetitor">
            <VTextField v-model="betterCompetitorComment" />
          </VCol>
        </VRow>
        <VRow>
          <VCol>
            <VCheckbox v-model="abandon" label="Abandon par le client" />
          </VCol>
          <VCol v-if="abandon">
            <VTextField v-model="abandonComent" />
          </VCol>
        </VRow>
        <VRow>
          <VCol> Nom du concurrent qui a remporté l'offre : </VCol>
          <VCol>
            <VTextField v-model="competitorName" />
          </VCol>
        </VRow>
      </VCardText>
      <VDivider />
      <VCardActions>
        <VSpacer />
        <NjBtn variant="outlined" @click="emit('update:model-value', false)"> Non </NjBtn>
        <NjBtn :disabled="disableCancel" @click="cancelOperation"> Oui </NjBtn>
      </VCardActions>
    </VCard>
  </VDialog>
</template>
<script lang="ts" setup>
import router from '@/router'
import { useSnackbarStore } from '@/stores/snackbar'
import type { OperationStatus, Operation } from '@/types/operation'

const props = defineProps({
  modelValue: Boolean,
  cancelReason: {
    type: String as PropType<OperationStatus | null>,
  },
  operation: {
    type: Object as PropType<Pick<Operation, 'id' | 'stepId' | 'backToStep50Counter'>>,
    default: makeEmptyOperation,
  },
  cancellingMail: {
    type: Function as PropType<() => Promise<boolean>>,
    required: true,
  },
  routing: Boolean,
})

const emit = defineEmits<{
  'update:model-value': [value: boolean]
  cancelled: [void]
}>()

const snackbar = useSnackbarStore()

const superiorValuation = ref(false)
const lessExpensive = ref(false)
const otherTechnology = ref(false)
const betterCompetitor = ref(false)
const abandon = ref(false)
const superiorValuationComment = ref('')
const lessExpensiveComment = ref('')
const otherTechnologyComment = ref('')
const betterCompetitorComment = ref('')
const abandonComent = ref('')
const competitorName = ref('')

const addReasons = () => {
  const reasons: Record<string, string> = {}
  if (superiorValuation.value) {
    reasons.superiorValuation = superiorValuationComment.value
  }
  if (lessExpensive.value) {
    reasons.lessExpensive = lessExpensiveComment.value
  }
  if (otherTechnology.value) {
    reasons.otherTechnology = otherTechnologyComment.value
  }
  if (betterCompetitor.value) {
    reasons.betterCompetitor = betterCompetitorComment.value
  }
  if (abandon.value) {
    reasons.abandon = abandonComent.value
  }
  if (competitorName.value) {
    reasons.competitorName = competitorName.value
  }
  return reasons
}

const disableCancel = computed(() => {
  return (
    props.operation.stepId === 40 &&
    props.cancelReason === 'LOST' &&
    !superiorValuation.value &&
    !lessExpensive.value &&
    !otherTechnology.value &&
    !betterCompetitor.value &&
    !abandon.value
  )
})

const cancelOperation = async () => {
  let reasons = null
  if (props.operation.stepId === 40) {
    reasons = addReasons()
  }

  if (props.operation.stepId > 50 || props.operation.backToStep50Counter > 0) {
    if (!(await props.cancellingMail())) {
      return
    }
  }

  operationApi
    .updateStatus(props.operation.id, props.cancelReason!, reasons ?? undefined)
    .then(() => {
      snackbar.setSuccess("Le statut de l'opération à été mis à jour")
      if (props.routing) {
        router.push({
          name: 'OperationAllView',
        })
      }
      emit('cancelled')
    })
    .catch(async (err) =>
      snackbar.setError(
        await handleAxiosException(err, undefined, {
          defaultMessage: "Une erreur est survenue lors de la mise à jour du statut de l'opération",
        })
      )
    )
}
</script>
