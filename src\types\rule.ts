import { isArray, isEmpty } from 'lodash'
import type { Operation } from './operation'
import { isBefore, parseISO } from 'date-fns'
import type { LocalDate } from './date'
import { computed } from 'vue'
import type { StandardizedOperationSheet } from './calcul/standardizedOperationSheet'

export const codePostalRule = (v: string) => v && (v.match(/^[0-9]{5}$/) != null ? true : 'Code postale non valide')

export const requiredRuleGenerator = (errorMessage: string) => (v: string | number | any[] | any) =>
  v !== undefined &&
  v !== '' &&
  v !== null &&
  (!isArray(v) || v.length !== 0) &&
  !(typeof v?.id === 'number' && v.id === 0)
    ? true
    : errorMessage

export const isBetweenDatesRuleGenerator =
  (operationSheet: StandardizedOperationSheet) =>
  (value: string): true | string => {
    const date = parseHumanReadableLocalDate(value)
    const startDate = new Date(operationSheet.startDate)
    const expirationDate = operationSheet.expirationDate ? new Date(operationSheet.expirationDate) : null
    if (date >= startDate && (!expirationDate || date <= expirationDate)) {
      return true
    } else {
      return 'Date en dehors des dates limites de la FOS'
    }
  }
export const requiredRule = requiredRuleGenerator('Le champ est requis')

export const variableNameRuleHint = "Caractères autorisés: alpha numériques et '_'"
export const variableNameRule = (v: string) =>
  v.match(/^[A-Za-z][A-Za-z_0-9]*$/) != null ? true : variableNameRuleHint

export const numericRule = (v: string) => (!isNaN(Number(v)) ? true : 'Le paramètre doit être un nombre')

export const conditionRuleGenerator = (condition: boolean, errorMessage: string): ValidationRule => {
  return () => {
    return condition ? true : errorMessage
  }
}

export const emptyOrNumericRule = (v: string) =>
  isEmpty(v) || numericRule(v) == true ? true : 'Le paramètre doit être un nombre'

export const positiveNumericRuleGenerator =
  (message = 'Le paramètre doit être un nombre supérieur à 0') =>
  (v: string) =>
    !isNaN(parseFloat(v)) && parseFloat(v) > 0 ? true : message

export const positiveOrNullNumericRuleGenerator =
  (message = 'Le paramètre doit être un nombre supérieur ou égal à 0') =>
  (v: string) =>
    !isNaN(Number(v)) && Number(v) >= 0 ? true : message

export const inferiorToRule = (max: number) => (v: string) =>
  !isNaN(Number(v)) && Number(v) <= max ? true : 'Vous ne pouvez pas saisir une valeur supérieure au maximum'

export const emptyOrPostalCodeRule = (v: string) =>
  isEmpty(v) || v.match(/^[0-9]{5}$/) != null ? true : 'Code postale non valide'

export const emptyOrNumbers = (v: string) =>
  isEmpty(v) || v.match(/^[0-9]*$/) ? true : 'Le paramètre ne doit contenir que des chiffres'

export const emptyOrPositiveOrNullNumber =
  (message = 'Le paramètre doit être un nombre supérieur ou égal à 0') =>
  (v: string) =>
    isEmpty(v) || (!isNaN(Number(v)) && Number(v) >= 0) ? true : message

export const emptyOrSirenRule = (v: string) => (isEmpty(v) || v.match(/^[0-9]{9}$/) != null ? true : 'Siren non valide')
export const emptyOrSiretRule = (v: string) =>
  isEmpty(v) || v.match(/^[0-9]{14}$/) != null ? true : 'Siret non valide'

export const isEmptyOrAfterToday = (v: string) => {
  return isEmpty(v) || new Date() < parseHumanReadableLocalDate(v) ? true : "La date doit se trouver après aujourd'hui"
}

export const isEmptyOrAfter = (date: LocalDate) => (v: string) => {
  return isEmpty(v) || new Date(date) < parseHumanReadableLocalDate(v)
    ? true
    : 'La date doit se trouver après ' + formatHumanReadableLocalDate(date)
}

export const isEmptyOrBeforeToday = (v: string) => {
  return isEmpty(v) || parseHumanReadableLocalDate(v) <= new Date() ? true : "La date doit se trouver avant aujourd'hui"
}

export const emailRegExp = /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9-]+(?:\.[a-zA-Z0-9-]+)*$/

export const emptyOrEmail = (v: string) => {
  return isEmpty(v) || v.match(emailRegExp) ? true : "Le champ n'est pas un email"
}

const urlPattern = /(?:https?):\/\/(\w+:?\w*)?(\S+)(:\d+)?(\/|\/([\w#!:.?+=&%!\-/]))?/
export const urlRule = (v: string) => {
  return isEmpty(v) || v.match(urlPattern) ? true : 'Format URL invalide'
}

export const maxSizeTextRuleGenerator = (max: number) => {
  return (v: string) => !v || v.length <= max || 'Max. ' + max + ' caractères'
}

export const rgeEndOfValidityDateRule = (operation: Operation) => {
  return (value: LocalDate | null) => {
    if (!operation?.rgeGrantedDate) {
      return true
    } else {
      return isEmpty(value) || parseISO(operation?.rgeGrantedDate) < parseHumanReadableLocalDate(value!)
        ? true
        : "La date doit se trouver après la date d'attribution du RGE"
    }
  }
}

export const useRgeRule = (operation: Ref<Operation | undefined>) => {
  const rgeRule = computed(() => {
    return (value: string) => {
      if (!operation.value?.standardizedOperationSheet.rgeMandatory) {
        return true
      }
      return !operation.value.rgeGrantedDate ||
        !operation.value.rgeEndOfValidityDate ||
        (parseISO(operation.value.rgeGrantedDate) <= parseHumanReadableLocalDate(value) &&
          parseHumanReadableLocalDate(value) <= parseISO(operation.value.rgeEndOfValidityDate))
        ? true
        : 'La date doit être comprise dans la période du RGE'
    }
  })

  const actualEndOperationRule = computed(
    () => (value: string) =>
      !value || !isBefore(new Date(parseHumanReadableLocalDate(value)), parseISO(operation.value!.signedDate!))
        ? true
        : "La date de fin de travaux doit être après la date d'engagement"
  )

  return { rgeRule, actualEndOperationRule }
}

export type ValidationRule = (v: any) => true | string
