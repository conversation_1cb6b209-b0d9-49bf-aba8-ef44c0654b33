export interface OperationSummary {
  classicCumacSum: number
  precariousnessCumacSum: number
  availableClassicCumacSum: number
  availablePrecariousnessCumacSum: number
  operationsNumber: number
}

export interface OperationSummaryByStepDto extends OperationSummary {
  stepId: number
  isDelayed: boolean
  netMarginSum: number
}

export interface OperationSummaryByDelayDto extends OperationSummary {
  delayMonths: number
}
