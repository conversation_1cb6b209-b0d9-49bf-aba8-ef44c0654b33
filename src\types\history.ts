import type { Address } from './address'
import type { ControlTypes } from './controlOrganism'
import type { LocalDateTime } from './date'
import type { ObjectChange } from './operationEvent'
import type { User } from './user'

export interface History {
  uuid: string
  creationDateTime: LocalDateTime
  creationUser: User
  event: string
  changeSet: ObjectChange[]
}

export interface BeneficiaryHistory extends History {
  beneficiaryId: number
  beneficiarySocialReason: string
  beneficiarySiren: string
  beneficiaryAddress: string
  beneficiaryPostalCode: string
  beneficiaryCity: string
  beneficiaryPhoneNumber: string
  beneficiaryEmail: string
  beneficiaryLastName: string
  beneficiaryFirstName: string
  beneficiaryCapacity: string
  beneficiaryLegalStatus: string
  beneficiaryCertified: string
}

export interface ControlOrganismHistory extends History {
  controlOrganismId: number
  controlOrganismSocialReason: string
  controlOrganismSiren: string
  controlOrganismAddress: string
  controlOrganismPostalCode: string
  controlOrganismCity: string
  controlOrganismCertified: boolean
  controlOrganismControlType: ControlTypes
  controlOrganismContactName: string
  controlOrganismContactPhoneNumber: string
  controlOrganismContactEmail: string
}

export interface SubcontractorHistory extends History {
  subcontractorId: number
  subcontractorSocialReason: string
  subcontractorSiret: string
  subcontractorAddress: string
  subcontractorCertified: boolean
}

export interface EntityDetailsHistory extends History {
  // subcontractorId: number
  // subcontractorSocialReason: string
  // subcontractorSiret: string
  // subcontractorAddress: string
  // subcontractorCertified: boolean
}

export interface TerritoryDetailsHistory extends History {}

export const beneficiaryHistoryDisplayProperties: {
  fieldname: string
  label: string
  resolver?: (value: any) => any
}[] = [
  {
    fieldname: 'id',
    label: 'Id',
    resolver: (value: number) => formatNumber(value),
  },
  {
    fieldname: 'socialReason',
    label: 'Raison social',
  },
  {
    fieldname: 'siren',
    label: 'SIREN',
  },
  {
    fieldname: 'address',
    label: 'Rue',
  },
  {
    fieldname: 'postalCode',
    label: 'Code postal',
  },
  {
    fieldname: 'city',
    label: 'Ville',
  },
  {
    fieldname: 'phoneNumber',
    label: 'Numéro de téléphone',
  },
  {
    fieldname: 'email',
    label: 'Email',
  },
  {
    fieldname: 'lastName',
    label: 'Nom',
  },
  {
    fieldname: 'firstName',
    label: 'Prénom',
  },
  {
    fieldname: 'capacity',
    label: 'Qualité',
  },
  {
    fieldname: 'legalStatus',
    label: 'Forme juridique',
  },
  {
    fieldname: 'certified',
    label: 'Certifié',
    resolver: (value: boolean) => (value ? 'OUI' : 'NON'),
  },
]

export const subcontractorHistoryDisplayProperties: {
  fieldname: string
  label: string
  resolver?: (value: any) => any
}[] = [
  {
    fieldname: 'id',
    label: 'Id',
    resolver: (value: number) => formatNumber(value),
  },
  {
    fieldname: 'socialReason',
    label: 'Raison social',
  },
  {
    fieldname: 'siret',
    label: 'SIRET',
  },
  {
    fieldname: 'address',
    label: 'Adresse',
    resolver: (value: Address) => formatAddressable(value),
  },
  {
    fieldname: 'certified',
    label: 'Certifié',
    resolver: (value: boolean) => (value ? 'OUI' : 'NON'),
  },
]

export const controlOrganismHistoryDisplayProperties: {
  fieldname: string
  label: string
  resolver?: (value: any) => any
}[] = [
  {
    fieldname: 'id',
    label: 'Id',
    resolver: (value: number) => formatNumber(value),
  },
  {
    fieldname: 'socialReason',
    label: 'Raison social',
  },
  {
    fieldname: 'siren',
    label: 'SIREN',
  },
  {
    fieldname: 'address',
    label: 'Rue',
  },
  {
    fieldname: 'postalCode',
    label: 'Code postal',
  },
  {
    fieldname: 'city',
    label: 'Ville',
  },
  {
    fieldname: 'contactPhoneNumber',
    label: 'Numéro de téléphone',
  },
  {
    fieldname: 'contactName',
    label: 'Nom',
  },
  {
    fieldname: 'contactEmail',
    label: 'Email',
  },
  {
    fieldname: 'controlType',
    label: 'Type de contrôle',
    resolver: (value: ControlTypes) => controlTypeLabel.find((i) => i.value == value)?.label,
  },
  {
    fieldname: 'certified',
    label: 'Certifié',
    resolver: (value: boolean) => (value ? 'OUI' : 'NON'),
  },
]

export const entityDetailsHistoryDisplayProperties: {
  fieldname: string
  label: string
  resolver?: (value: any) => any
}[] = [
  {
    fieldname: 'id',
    label: 'Id',
    resolver: (value: number) => formatNumber(value),
  },
  {
    fieldname: 'firstNameAgent',
    label: 'Prénom du représentant',
  },
  {
    fieldname: 'lastNameAgent',
    label: 'Nom du représentant',
  },
  {
    fieldname: 'officeAgent',
    label: 'Fonction du représentant',
  },
  {
    fieldname: 'civilityAgent',
    label: 'Civilite du représentant',
  },
  {
    fieldname: 'possessivePronounAgent',
    label: 'Pronom possessif du représentant',
  },

  {
    fieldname: 'instructor',
    label: 'Instructeur',
    resolver: displayFullnameUser,
  },
  {
    fieldname: 'territoryReferent',
    label: 'Référent territoire',
    resolver: displayFullnameUser,
  },
  {
    fieldname: 'regionalDirector',
    label: 'Directeur régional',
    resolver: displayFullnameUser,
  },
  {
    fieldname: 'directorOfOperationalActivities',
    label: 'Directeur des activités Opérationnelles',
    resolver: displayFullnameUser,
  },
  {
    fieldname: 'industryOperationsAndPerformanceManager',
    label: 'Responsable Opérations et Performance (industrie)',
    resolver: displayFullnameUser,
  },

  {
    fieldname: 'chiefExecutive',
    label: 'Directeur(e) général(e)',
    resolver: displayFullnameUser,
  },
  {
    fieldname: 'chairman',
    label: "Président(e) du conseil d'administration",
    resolver: displayFullnameUser,
  },

  {
    fieldname: 'visible',
    label: 'Visible',
    resolver: (it: boolean) => (it ? 'Oui' : 'Non'),
  },
  {
    fieldname: 'canSellCumacs',
    label: 'Vente de CEE possible',
    resolver: (it: boolean) => (it ? 'Oui' : 'Non'),
  },
]

export const territoryDetailsHistoryDisplayProperties: {
  fieldname: string
  label: string
  resolver?: (value: any) => any
}[] = [
  {
    fieldname: 'id',
    label: 'Id',
    resolver: (value: number) => formatNumber(value),
  },
  {
    fieldname: 'territoryReferent',
    label: 'Référent territoire',
    resolver: displayFullnameUser,
  },
  {
    fieldname: 'missionManagers',
    label: 'Chargés de mission',
    resolver: (el: User[]) => el?.map((it) => displayFullnameUser(it)).join(', '),
  },
  {
    fieldname: 'operationsAndPerformanceDirector',
    label: 'Directeur des opérations et performance',
    resolver: displayFullnameUser,
  },
  {
    fieldname: 'operationTerritoryCfo',
    label: 'CFO Opérationnel du Territoire',
    resolver: displayFullnameUser,
  },
  {
    fieldname: 'networkDevelopmentDirector',
    label: 'Directeur Développement Réseau',
    resolver: displayFullnameUser,
  },

  {
    fieldname: 'industryDevelopmentDirector',
    label: 'Directeur Développement Industrie',
    resolver: displayFullnameUser,
  },
  {
    fieldname: 'buildingDevelopmentDirector',
    label: 'Directeur Développement Bâtiments',
    resolver: displayFullnameUser,
  },
  {
    fieldname: 'developmentDirector',
    label: 'Directeur Développement',
    resolver: displayFullnameUser,
  },
  {
    fieldname: 'territoryDirector',
    label: 'Directeur Territoire',
    resolver: displayFullnameUser,
  },
]
