import type { Page, Pageable } from '@/types/pagination'
import type { AxiosInstance, AxiosPromise } from 'axios'
import axiosInstance from '.'
import type {
  CeeSale,
  VolumeCeeSaleRequest,
  OperationCeeSaleRequest,
  CeeSaleDateDto,
  OperationCeeSaleRequestDto,
} from '@/types/ceeSale'
import type { LocalDate } from '@/types/date'
import type { OperationFilter } from './operation'

export type CeeSaleFilter = Partial<{
  operationId: number
  saleDate: LocalDate
  active: boolean
}>

const ceeSalesUri = 'cee_sales'
class CeeSaleApi {
  public constructor(private axios: AxiosInstance) {}

  public findAll(pageable: Pageable, filter: CeeSaleFilter): AxiosPromise<Page<CeeSale>> {
    return this.axios.get(ceeSalesUri, {
      params: { ...pageable, ...filter },
    })
  }

  public findAllByDate(pageable: Pageable, filter: CeeSaleFilter): AxiosPromise<Page<CeeSaleDateDto>> {
    return this.axios.get(`${ceeSalesUri}/date`, { params: { ...pageable, ...filter } })
  }

  public getPreview(request: VolumeCeeSaleRequest): AxiosPromise<OperationCeeSaleRequestDto[]> {
    return this.axios.get(`${ceeSalesUri}/preview`, { params: request })
  }

  public createFiltered(requests: OperationCeeSaleRequest[]): AxiosPromise<void> {
    return this.axios.post(`${ceeSalesUri}/filtered`, requests)
  }

  public deleteById(saleId: number): AxiosPromise<void> {
    return this.axios.delete(`${ceeSalesUri}/${saleId}`)
  }

  public deleteBySaleDate(saleDate: LocalDate): AxiosPromise<void> {
    return this.axios.delete(ceeSalesUri, { params: { saleDate } })
  }

  public export(filter: OperationFilter): AxiosPromise {
    return this.axios.get(`${ceeSalesUri}/export`, {
      params: { ...filter },
      responseType: 'blob',
    })
  }
}

export const ceeSaleApi = new CeeSaleApi(axiosInstance)
