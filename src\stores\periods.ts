import type { Period } from '@/types/period'
import { isEqual } from 'lodash'
import { defineStore } from 'pinia'

export const usePeriodsStore = defineStore('periods', () => {
  const periods = ref<Period[]>()

  const timeoutId = ref<number>()

  const load = async () => {
    clearTimeouts()

    return await periodApi
      .getAll({ size: 1000 }, {})
      .then((v) => {
        v.data.content.sort((arg1, arg2) => arg1.id - arg2.id)
        if (!isEqual(v, periods.value)) {
          periods.value = v.data.content
        }

        timeoutId.value = setTimeout(load, 30 * 60 * 1000)
      })
      .catch((e) => {
        logException(e)
        timeoutId.value = setTimeout(load, ((periods.value?.length ?? 0 > 0) ? 30 * 60 : 5) * 1000)
      })
  }

  const clearTimeouts = () => {
    if (timeoutId.value !== undefined) {
      clearTimeout(timeoutId.value)
    }
  }

  return { periods, load, clearTimeouts }
})
