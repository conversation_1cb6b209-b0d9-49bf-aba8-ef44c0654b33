import type { Subcontractor, SubcontractorFilter, SubcontractorRequest } from '@/types/subcontractor'
import axiosInstance from '.'
import type { AxiosInstance, AxiosPromise } from 'axios'
import type { Page, Pageable } from '@/types/pagination'

const subcontractors_url = '/subcontractors'
class SubcontractorApi {
  public constructor(private axios: AxiosInstance) {}

  public create(request: SubcontractorRequest): AxiosPromise<Subcontractor> {
    return this.axios.post(subcontractors_url, request)
  }

  public update(id: number, request: SubcontractorRequest): AxiosPromise<Subcontractor> {
    return this.axios.put(subcontractors_url + '/' + id, request)
  }

  public findAll(pageable: Pageable, filter: SubcontractorFilter): AxiosPromise<Page<Subcontractor>> {
    return this.axios.get(subcontractors_url, {
      params: { ...pageable, ...filter },
    })
  }
}

export const subcontractorApi = new Subcontractor<PERSON>pi(axiosInstance)
