import type { AxiosInstance, AxiosPromise, AxiosRequestConfig } from 'axios'
import axiosInstance from '.'
import type { User, UserWithEntities } from '@/types/user'

class AuthenticationApi {
  public constructor(private axios: AxiosInstance) {}

  public login(
    loginRequest: { firstName: string; lastName: string; email: string },
    config?: AxiosRequestConfig<unknown>
  ): AxiosPromise<User> {
    return this.axios.put('/users/me', loginRequest, config)
  }

  public loginOne(): AxiosPromise<UserWithEntities> {
    return this.axios.get('/users/me')
  }
}

export const authenticationApi = new AuthenticationApi(axiosInstance)
