import type { EpcBonusSheet } from '@/types/epcBonus'
import type { Page, Pageable } from '@/types/pagination'
import type { AxiosInstance, AxiosPromise } from 'axios'
import axiosInstance from '.'
import type { LocalDate } from '@/types/date'

export type EpcBonusSheetFilter = Partial<{
  search: string
  date: LocalDate
  prefix: string
  certified: boolean
}>

class EpcBonusSheetApi {
  public constructor(private axios: AxiosInstance) {}

  public create(epcBonusSheet: EpcBonusSheet): AxiosPromise<EpcBonusSheet> {
    return this.axios.post('/epc_bonus_sheets', epcBonusSheet)
  }

  public update(id: number, epcBonusSheet: EpcBonusSheet): AxiosPromise<EpcBonusSheet> {
    return this.axios.put(`/epc_bonus_sheets/${id}`, epcBonusSheet)
  }

  public getAll(pageable: Pageable, filter: EpcBonusSheetFilter): AxiosPromise<Page<EpcBonusSheet>> {
    return this.axios.get('/epc_bonus_sheets', {
      params: {
        ...pageable,
        ...filter,
      },
    })
  }

  public getOne(id: number): AxiosPromise<EpcBonusSheet> {
    return this.axios.get(`/epc_bonus_sheets/${id}`)
  }
}

export const epcBonusSheetApi = new EpcBonusSheetApi(axiosInstance)
