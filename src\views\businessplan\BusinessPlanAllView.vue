<template>
  <NjPage title="Liste des Business Plans" expend-body>
    <template #header-actions>
      <NjBtn @click="newBusinessPlanDialog = true">Nouveau Business Plan</NjBtn>
    </template>

    <template #sub-header>
      <VRow>
        <VCol cols="3">
          <SearchInput v-model="filter.search" :loading="data.loading" />
        </VCol>
        <VCol class="subheader-actions align-center">
          <VCheckbox
            v-if="userStore.isAdmin"
            v-model="filter.myBusinessPlans"
            label="Mes Business Plans"
            class="flex-grow-0"
          />
          <!-- <NjBtn @click="showFilters = !showFilters"> Filtres </NjBtn> -->
          <VLink v-show="showResetFilter" icon="mdi-sync" @click="filter = defaultFilter">
            Réinitialiser les filtres
          </VLink>
        </VCol>
        <VCol v-if="selections.length" class="d-flex justify-end">
          <NjBtn color="error" variant="outlined" @click="deleteAll">Supprimer</NjBtn>
        </VCol>
      </VRow>
    </template>
    <template #body>
      <VRow class="w-100">
        <VCol>
          <NjDataTable
            v-model:selections="selections"
            :headers="headers"
            :pageable="pageable"
            :page="data.value!"
            :on-click-row="clickRow"
            :to-row="(item: BusinessPlan) => ({ name: 'BusinessPlanOneView', params: { id: item.id } })"
            multi-selection
            checkboxes
            fixed
            @update:pageable="updatePageable"
          >
            <template #[`item.creationDateTime`]="{ item }">
              {{ formatHumanReadableLocalDate(item.creationDateTime) }}
            </template>
            <template #[`item.toProcess`]="{ item }">
              <NjBooleanIcon :condition="item.toProcess" />
            </template>
          </NjDataTable>
        </VCol>
      </VRow>
      <CardDialog v-model="newBusinessPlanDialog" :width="'40%'" :title="'Nouveau Business Plan'">
        <VRow>
          <VCol>
            <VForm ref="formRef">
              <VRow class="flex-column">
                <VCol>
                  <VTextField v-model="name" label="Nom" :rules="[requiredRule]" />
                </VCol>
              </VRow>
            </VForm>
          </VCol>
        </VRow>
        <template #actions>
          <NjBtn variant="outlined" @click="newBusinessPlanDialog = false"> Annuler </NjBtn>
          <NjBtn color="primary" @click="createBusinessPlan"> Valider </NjBtn>
        </template>
      </CardDialog>
    </template>
  </NjPage>
</template>
<script setup lang="ts">
import type { BusinessPlanFilter } from '@/api/businessPlan'
import CardDialog from '@/components/CardDialog.vue'
import NjBtn from '@/components/NjBtn.vue'
import type { DataTableHeader } from '@/components/okta/NjDataTable.type'
import NjDataTable from '@/components/okta/NjDataTable.vue'
import { useDialogStore } from '@/stores/dialog'
import { useSnackbarStore } from '@/stores/snackbar'
import { useUserStore } from '@/stores/user'
import type { BusinessPlan, BusinessPlanRequest } from '@/types/businessPlan'
import { formatHumanReadableLocalDate } from '@/types/date'
import { requiredRule } from '@/types/rule'
import { cloneDeep, debounce, isArray, isEqual } from 'lodash'
import type { LocationQuery } from 'vue-router'
import type { VForm } from 'vuetify/components/VForm'

const headers: DataTableHeader[] = [
  {
    title: 'Nom',
    value: 'name',
  },
  {
    title: "Nombre d'opération",
    value: 'operationsNumber',
    formater: (_: any, value: number) => formatNumber(value),
    sortable: false,
    cellClass: 'text-right justify-end',
  },
  {
    title: 'Créateur',
    value: 'creationUser',
    formater: (_, value) => displayFullnameUser(value),
  },
  {
    title: 'A traiter',
    value: 'toProcess',
  },
  {
    title: 'Date de création',
    value: 'creationDateTime',
    formater: (_, value) => formatHumanReadableLocalDate(value),
  },
]

const userStore = useUserStore()

const makeEmptyFilter = (): BusinessPlanFilter => ({
  search: '',
  myBusinessPlans: false,
})

const defaultFilter = {
  ...makeEmptyFilter(),
} as BusinessPlanFilter

const showResetFilter = computed(() => {
  return !isEqual(filter.value, defaultFilter)
})

const filter = ref<BusinessPlanFilter>(cloneDeep(defaultFilter))

const queryMapper = (query: LocationQuery): Record<string, unknown> => ({
  ...query,
  periodIds: mapQueryToTable(query.periodIds, true),
  entityNavFullIds: isArray(query.entityNavFullIds)
    ? query.entityNavFullIds
    : query.entityNavFullIds
      ? [query.entityNavFullIds]
      : [],
})

const selections = ref<BusinessPlan[]>([])
const { data, pageFilter, pageable, updatePageable, updateFilter, reload } = usePaginationInQuery<BusinessPlan>(
  (filter, pageable) => businessPlanApi.findAll(filter, pageable),
  {
    defaultPageFilter: { ...filter.value },
    queryToFilterMapper: queryMapper,
    saveFiltersName: 'BusinessPlanAllView',
  }
)

const router = useRouter()

const clickRow = (businessPlan: BusinessPlan) => {
  router.push({
    name: 'BusinessPlanOneView',
    params: { id: businessPlan.id },
  })
}

const debounceFilter = debounce((v: any) => updateFilter(v), 300)

watch(
  filter,
  (v) => {
    debounceFilter(v)
  },
  {
    deep: true,
  }
)

watch(
  () => pageFilter.value,
  (v) => {
    const tempFilter = cloneDeep(v) as any
    Object.keys(tempFilter).forEach((key) => {
      if (isArray(tempFilter[key]) && key !== 'entityNavFullIds' && (tempFilter[key] as any[]).length !== 0) {
        ;(tempFilter[key] as any[]).forEach((val: string, index: number) => {
          if (!Number.isNaN(parseInt(val))) {
            ;(tempFilter[key] as any[])[index] = parseInt(val)
          }
        })
      }
    })
    filter.value = tempFilter as any
  },
  {
    immediate: true,
    deep: true,
  }
)

//Create BusinessPlan
const newBusinessPlanDialog = ref(false)

const snackbarStore = useSnackbarStore()

const name = ref('')
const formRef = ref<VForm | null>(null)
const businessPlan = ref(emptyValue<BusinessPlan>())
const createBusinessPlan = async () => {
  const validate = await formRef.value!.validate()
  if (validate.valid) {
    const request: BusinessPlanRequest = {
      name: name.value,
      toProcess: false,
    }
    handleAxiosPromise(businessPlan, businessPlanApi.create(request), {
      afterError: () => snackbarStore.setError('Erreur lors de la création du Business Plan'),
      afterSuccess: () =>
        router.push({
          name: 'BusinessPlanOneView',
          params: { id: businessPlan.value.value?.id },
        }),
    })
  }
}

const dialogStore = useDialogStore()
const deleteAll = async () => {
  // if (selections.value.some((it) => it.toProcess)) {
  //   snackbarStore.setError('Vous ne pouvez pas supprimer des Business Plan à traiter')
  //   return
  // }
  if (
    await dialogStore.addAlert({
      title: 'Suppression en masse de Business Plan',
      message:
        'Vous allez supprimer ' + selections.value.length + ' Business plans.\nÊtes-vous sûr de vouloir continuer ?',
      maxWidth: '640px',
    })
  ) {
    businessPlanApi
      .deleteByIds(selections.value.map((it) => it.id))
      .then(() => {
        reload()
        selections.value = []
      })
      .catch(async (e) => {
        snackbarStore.setError(await handleAxiosException(e))
      })
  }
}
</script>
