import type { Pageable } from '@/types/pagination'
import type { AxiosInstance, AxiosPromise } from 'axios'
import { gtfAxiosInstance } from '.'
import { mapPageableToOrderByGtfPageable, type GtfPage, type OrdsQueryParams, mapPageableToGtfPageable } from './type'

const prefixApiUrl = '/gde/api/v1/'

export interface GtfGdePropertyDto {
  id: number
  address_type: string
  building?: string
  road_number: string
  road_name: string
  additional_address: string
  postal_code?: string
  city: string
  country: string
  latitude?: number
  longitude?: number
  code: string
  name: string
  short_name: string
  enabled: 0 | 1
  parent_entity_id: string
  parent_entity_nav_full_id: string
}

export interface GtfGdePropertyFilter {
  search?: string
  enabled?: boolean
  noEmptyAddress?: boolean
  parentEntityIds?: string[]
}

class GtfGdeApi {
  public constructor(private axios: AxiosInstance) {}

  public getAllProperties(filter: GtfGdePropertyFilter, pageable: Pageable): AxiosPromise<GtfPage<GtfGdePropertyDto>> {
    const ordsFilter: OrdsQueryParams<GtfGdePropertyDto> = {
      q: {},
    }
    if (filter.search) {
      ordsFilter.q.$or = [
        {
          building: {
            $instr: filter.search.trim(),
          },
        },
        {
          name: {
            $instr: filter.search.trim(),
          },
        },
        {
          road_name: {
            $instr: filter.search.trim(),
          },
        },
        {
          code: {
            $instr: filter.search.trim(),
          },
        },
        {
          postal_code: {
            $instr: filter.search.trim(),
          },
        },
        {
          city: {
            $instr: filter.search.trim(),
          },
        },
      ]
    }
    if (filter.parentEntityIds && filter.parentEntityIds.length > 0) {
      ordsFilter.q['parent_entity_nav_full_id'] = {
        $or: filter.parentEntityIds.map((it) => ({ $instr: it.trim() })),
      }
    }
    if (filter.noEmptyAddress) {
      ordsFilter.q['road_name'] = {
        $notnull: null,
      }
      ordsFilter.q['postal_code'] = {
        $notnull: null,
      }
      ordsFilter.q['city'] = {
        $notnull: null,
      }
    }
    // if (filter.enabled !== undefined) {
    //   ordsFilter.q.enabled = filter.enabled ? 1 : 0
    // }
    ordsFilter.q.$orderby = mapPageableToOrderByGtfPageable(pageable.sort)
    return this.axios.get(prefixApiUrl + 'properties', {
      params: { q: JSON.stringify(ordsFilter.q), ...mapPageableToGtfPageable(pageable) },
    })
  }

  public getOneProperty(id: number): AxiosPromise<GtfGdePropertyDto> {
    return this.axios.get(prefixApiUrl + 'properties/' + id)
  }
}

export const gtfGdeApi = new GtfGdeApi(gtfAxiosInstance)
