<template>
  <NjPage>
    <template #body>
      <NjDataTable
        :items="data.value!"
        :headers="headers"
        :on-click-row="
          (value) =>
            router.push({
              name: 'StepOneView',
              params: { id: value.id },
            })
        "
      >
        <template #[`item.id`]="{ item }">
          <span class="text-primary" style="font-weight: 700; font-size: 1.5rem">
            {{ item.id }}
          </span>
        </template>
        <template #[`item.commercialStatus`]="{ item }">
          {{ commercialStatusTitle.filter((status) => status.value === item.commercialStatus)[0].title }}
        </template>
      </NjDataTable>
    </template>
  </NjPage>
</template>
<script lang="ts" setup>
import { stepsApi } from '@/api/steps'
import NjPage from '@/components/NjPage.vue'
import type { DataTableHeader } from '@/components/okta/NjDataTable.type'
import NjDataTable from '@/components/okta/NjDataTable.vue'
import { useSnackbarStore } from '@/stores/snackbar'
import { type Step, commercialStatusTitle } from '@/types/steps'

const router = useRouter()
const snackbarStore = useSnackbarStore()

const data = ref(emptyValue<Step[]>())

const load = () => {
  handleAxiosPromise(
    data,
    stepsApi.getAll(),
    (v) => {
      data.value.value = v.data
      data.value.value.sort((a, b) => (a.id < b.id ? -1 : b.id < a.id ? 1 : 0))
    },
    () => snackbarStore.setError('Une erreur est survenue lors de la récupération des étapes')
  )
}

const headers: DataTableHeader[] = [
  {
    title: 'Etapes',
    value: 'id',
    sortable: false,
  },
  {
    title: "Nom de l'étape",
    value: 'name',
    sortable: false,
  },
  {
    title: "Statut de l'offre",
    value: 'commercialStatus',
    sortable: false,
  },
  {
    title: 'Qui',
    value: 'treatedBy',
    sortable: false,
  },
  {
    title: 'Documents',
    value: 'nbDocuments',
    sortable: false,
  },
  {
    title: 'Validité',
    value: 'validMonthDuration',
    sortable: false,
  },
]

onMounted(load)
</script>
