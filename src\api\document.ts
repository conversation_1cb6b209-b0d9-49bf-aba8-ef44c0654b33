import type { Document } from '@/types/document'
import type { AxiosInstance, AxiosPromise } from 'axios'
import axiosInstance from '.'

class DocumentApi {
  public constructor(private axios: AxiosInstance) {}

  public async create(file: File): AxiosPromise<Document> {
    const formData = new FormData()
    formData.append('file', file, file.name)
    formData.append('hash', await hashFile(file))

    return this.axios.post(`documents`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    })
  }
}

export const documentApi = new DocumentApi(axiosInstance)
