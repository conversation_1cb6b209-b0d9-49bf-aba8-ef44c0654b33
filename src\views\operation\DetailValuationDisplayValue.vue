<template>
  <slot
    :classic-valuation="{
      value: classicValuation,
      total: classicValuationCalculus,
    }"
    :precariousness-valuation="{
      value: precariousnessValuation,
      total: precariousnessValuationCalculus,
    }"
  >
    <VRow dense class="flex-column" style="font-size: 1rem">
      <VCol>
        <div class="d-flex justify-end">
          <div style="font-weight: 700">Total/€ :</div>
          <div class="text-primary font-weight-bold ms-2">
            {{ formatPriceNumber(totalValuation) }}
          </div>
        </div>
      </VCol>
      <VCol>
        <div class="d-flex justify-end">
          <div style="font-weight: 700">Total/kWhc :</div>
          <div class="text-primary font-weight-bold ms-2">
            {{ formatNumber(operation.classicCumac + operation.precariousnessCumac) + ' kWhc' }}
          </div>
        </div>
      </VCol>
      <VCol class="d-flex justify-end flex-wrap" style="gap: 4px">
        <div class="d-flex text-no-wrap" style="gap: 4px">
          <span class="nj-display-value__label">
            Total classique {{ operation.stepId > 50 ? 'demandé' : '' }} kWhc :
          </span>
          <span class="font-weight-bold">
            {{ formatNumber(operation.classicCumac) }}
          </span>
        </div>
        <div class="d-flex text-no-wrap" style="gap: 4px">
          <span class="nj-display-value__label"> avec valorisation : </span>
          <span class="font-weight-bold">
            {{
              formatPriceNumber(
                operation.atypicalClassicValuationValue ?? operation.classicValuationValue ?? classicValuation ?? 0
              ) + '/MWhc'
            }}
          </span>
        </div>
      </VCol>
      <VCol v-if="operation.stepId > 50" class="d-flex justify-end flex-wrap" style="gap: 4px">
        <div class="d-flex text-no-wrap" style="gap: 4px">
          <span class="nj-display-value__label"> Total classique réservé kWhc : </span>
          <span class="font-weight-bold">
            {{ formatNumber(operation.reservedClassicCumac ?? 0) }}
          </span>
        </div>
        <div class="d-flex text-no-wrap" style="gap: 4px">
          <span class="nj-display-value__label"> avec valorisation : </span>
          <span class="font-weight-bold">
            {{ formatPriceNumber(operation.reservedClassicValuationValue ?? 0) + '/MWhc' }}
          </span>
        </div>
      </VCol>
      <VCol class="d-flex justify-end flex-wrap" style="gap: 4px">
        <div class="d-flex text-no-wrap" style="gap: 4px">
          <span class="nj-display-value__label">
            Total précarité {{ operation.stepId > 50 ? 'demandé' : '' }} kWhc :
          </span>
          <span class="font-weight-bold">
            {{ formatNumber(operation.precariousnessCumac) }}
          </span>
        </div>

        <div class="d-flex text-no-wrap" style="gap: 4px">
          <span class="nj-display-value__label"> avec valorisation : </span>
          <span class="font-weight-bold">
            {{
              formatPriceNumber(
                operation.atypicalPrecariousnessValuationValue ??
                  operation.precariousnessValuationValue ??
                  formatNumber(precariousnessValuation) ??
                  'N/A'
              ) + '/MWhc'
            }}
          </span>
        </div>
      </VCol>
      <VCol v-if="operation.stepId > 50" class="d-flex justify-end flex-wrap" style="gap: 4px">
        <div class="d-flex text-no-wrap" style="gap: 4px">
          <span class="nj-display-value__label"> Total précarité réservé kWhc : </span>
          <span class="font-weight-bold">
            {{ formatNumber(operation.reservedPrecariousnessCumac ?? 0) }}
          </span>
        </div>
        <div class="d-flex text-no-wrap" style="gap: 4px">
          <span class="nj-display-value__label"> avec valorisation : </span>
          <span class="font-weight-bold">
            {{ formatPriceNumber(operation.reservedPrecariousnessValuationValue ?? 0) + '/MWhc' }}
          </span>
        </div>
      </VCol>
    </VRow>
  </slot>
</template>

<script setup lang="ts">
import { useSnackbarStore } from '@/stores/snackbar'
import type { Operation } from '@/types/operation'
import type { Valuation } from '@/types/valuation'
import type { PropType } from 'vue'
import { formatPriceNumber, formatNumber } from '@/types/format'
import { isEqual } from 'lodash'

const props = defineProps({
  operation: {
    type: Object as PropType<Operation>,
    required: true,
  },
  volumeCEE: {
    type: Number,
    default: 0,
  },
  classicCEE: {
    type: Number,
    default: 0,
  },
  precariousnessCEE: {
    type: Number,
    default: 0,
  },
  simulateStep60: Boolean,
})

const snackbarStore = useSnackbarStore()

// Calcul
const valuations = ref<Valuation[] | undefined>([])

const classicValuation = computed(
  () =>
    props.operation.atypicalClassicValuationValue ??
    props.operation.classicValuationValue ??
    valuations.value?.find((valuation) => !valuation.precariousness)?.value
)
const precariousnessValuation = computed(
  () =>
    props.operation.atypicalPrecariousnessValuationValue ??
    props.operation.precariousnessValuationValue ??
    valuations.value?.find((valuation) => valuation.precariousness)?.value
)

const classicValuationCalculus = computed(() => {
  if (props.operation.classicCumac !== 0 || props.volumeCEE > 0) {
    return (
      Math.round(
        ((props.operation.classicCumac !== 0
          ? props.operation.classicCumac
          : props.operation.precariousnessBonusParameterValues != null
            ? props.classicCEE
            : props.volumeCEE) /
          1000) *
          (props.operation.atypicalClassicValuationValue ?? classicValuation.value ?? 0) *
          100
      ) / 100
    )
  }
  return 0
})

const precariousnessValuationCalculus = computed(() =>
  props.operation.precariousnessBonusParameterValues != null || props.operation.precariousnessCumac !== 0
    ? Math.round(
        ((props.operation.precariousnessCumac !== 0 ? props.operation.precariousnessCumac : props.precariousnessCEE) /
          1000) *
          (props.operation.atypicalPrecariousnessValuationValue ?? precariousnessValuation.value ?? 0) *
          100
      ) / 100
    : 0
)

const totalValuation = computed(() => (classicValuationCalculus.value ?? 0) + precariousnessValuationCalculus.value)

watch(
  () => props.operation.valuationType,
  (v, oldV) => {
    if (v && v.id !== 0 && !props.operation.atypicalClassicValuationValue && !isEqual(oldV, v)) {
      valuations.value = []
      valuationApi
        .getAppropriate(props.operation.id, props.operation.valuationType.id)
        .then((response) => {
          valuations.value = response.data
        })
        .catch(async (err) => {
          valuations.value = undefined
          if (err.response.status !== 404) {
            snackbarStore.setError(
              await handleAxiosException(err, undefined, {
                defaultMessage: 'Une erreur est survenue lors de la récupération de la valorisation',
              })
            )
          }
        })
    }
  },
  {
    immediate: true,
  }
)

defineExpose({
  valuations: valuations,
  totalValuation: totalValuation,
})
</script>
