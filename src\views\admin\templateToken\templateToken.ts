export const tokenOperation = [
  {
    name: "Nom de l'opération",
    value: '{{NOM_OPERATION}}',
  },
  {
    name: "Date d'engagement réelle",
    value: '{{DATE_ENGAGEMENT_REELLE}}',
  },
  {
    name: 'Date de fin de travaux réelle',
    value: '{{DATE_FIN_DE_TRAVAUX_REELLE}}',
  },
  {
    name: 'Numéro de chantier',
    value: '{{NUMERO_DE_CHANTIER}}',
  },
  {
    name: 'Liste des numéros de chantiers',
    value: '{{LISTE_NUMERO_DE_CHANTIER}}',
  },
  {
    name: 'Numéro offre client',
    value: '{{NUMERO_OFFRE_CLIENT}}',
  },
  {
    name: "Paramètre de calcul (utiliser l'identifiant saisi dans la fiche de calcul)",
    value: '{{PARAMETRE_*}}',
  },
  {
    name: "Paramètre de calcul CPE (utiliser l'identifiant saisi dans la fiche CPE)",
    value: '{{PARAMETRE_CPE_*}}',
  },
  {
    name: "Paramètre de calcul précarité (utiliser l'identifiant saisi dans la fiche précarité)",
    value: '{{PARAMETRE_PRECARITE_*}}',
  },
  {
    name: "Paramètre de calcul coup de pouce (utiliser l'identifiant saisi dans la fiche coup de pouce)",
    value: '{{PARAMETRE_COUP_DE_POUCE_*}}',
  },
]
export const tokenBeneficiary = [
  {
    name: 'Raison sociale',
    value: '{{RAISON_SOCIALE_BENEFICIAIRE}}',
  },

  {
    name: 'Siren',
    value: '{{SIREN_BENEFICIAIRE}}',
  },
  {
    name: 'Adresse',
    value: '{{ADRESSE_BENEFICIAIRE}}',
  },
  {
    name: 'Code postal',
    value: '{{CODE_POSTAL_BENEFICIAIRE}}',
  },

  {
    name: 'Ville',
    value: '{{VILLE_BENEFICIAIRE}}',
  },
  {
    name: 'Téléphone',
    value: '{{TELEPHONE_BENEFICIAIRE}}',
  },
  {
    name: 'Email',
    value: '{{EMAIL_BENEFICIAIRE}}',
  },
  {
    name: 'Nom',
    value: '{{NOM_BENEFICIAIRE}}',
  },
  {
    name: 'Prénom',
    value: '{{PRENOM_BENEFICIAIRE}}',
  },
  {
    name: 'Qualité',
    value: '{{QUALITE_DU_BENEFICIAIRE}}',
  },
  {
    name: 'Statut légal',
    value: '{{STATUT_LEGAL_BENEFICIAIRE}}',
  },
]

export const tokenProperty = [
  {
    name: 'Nom',
    value: '{{NOM_INSTALLATION}}',
  },
  {
    name: 'Numéro',
    value: '{{NUMERO_INSTALLATION}}',
  },
  {
    name: 'Ville',
    value: '{{VILLE_INSTALLATION}}',
  },
  {
    name: 'Adresse',
    value: '{{ADRESSE_INSTALLATION}}',
  },
  {
    name: 'Complément adress',
    value: '{{COMPLEMENT_ADRESSE_INSTALLATION}}',
  },
  {
    name: 'Code postal',
    value: '{{CODE_POSTAL_INSTALLATION}}',
  },
  {
    name: 'Nom et adresse',
    value: '{{NOM_ET_ADRESSE_INSTALLATION}}',
  },
]

export const tokenStandardizedOperationSheet = [
  {
    name: 'Code opération',
    value: '{{OPERATION_CODE_FICHE_OPERATION}}',
  },
  {
    name: 'Description',
    value: '{{DESCRIPTION_FICHE_OPERATION}}',
  },
]

export const tokenValuation = [
  {
    name: 'Incitation financière',
    value: '{{INCITATION_FINANCIERE}}',
  },
  {
    name: 'Offre commercial sans incitation financière',
    value: '{{OFFRE_COMMERCIALE_SANS_INCITATION_FINANCIERE}}',
  },
  {
    name: 'Offre commercial avec incitation financière',
    value: '{{OFFRE_COMMERCIALE_AVEC_INCITATION_FINANCIERE}}',
  },
]

export const tokenEntity = [
  {
    name: 'Siren',
    value: '{{SIREN_AGENCE}}',
  },
  {
    name: 'Siret',
    value: '{{SIRET_AGENCE}}',
  },
  {
    name: 'Nom',
    value: '{{NOM_AGENCE}}',
  },
  {
    name: 'Raison social officiel',
    value: '{{RAISON_SOCIAL_OFFICIEL_AGENCE}}',
  },
  {
    name: 'Adresse',
    value: '{{ADRESSE_AGENCE}}',
  },
  {
    name: 'Code postal',
    value: '{{CODE_POSTAL_AGENCE}}',
  },
  {
    name: 'Ville',
    value: '{{VILLE_AGENCE}}',
  },
  {
    name: 'Téléphone',
    value: '{{TELEPHONE_AGENCE}}',
  },
  {
    name: 'Prénom du représentant',
    value: '{{PRENOM_REPRESENTANT}}',
  },
  {
    name: 'Nom du représentant',
    value: '{{NOM_REPRESENTANT}}',
  },
  {
    name: 'Fonction du représentant',
    value: '{{FONCTION_REPRESENTANT}}',
  },
  {
    name: 'Pronom posséssif du représentant',
    value: '{{PRONOM_POSSESSIF_REPRESENTANT}}',
  },
  {
    name: 'Civilité du représentant',
    value: '{{CIVILITE_REPRESENTANT}}',
  },
]

export const tokenCompany = [
  {
    name: 'Registre communautaire des sociétés',
    value: '{{FR_REGIME_COMMUNAUTAIRE_SOCIETE}}',
  },
  {
    name: 'Capital',
    value: '{{CAPITAL_SOCIETE}}',
  },
  {
    name: 'Logo (doit être placé dans un tableau pour pouvoir contrôler la taille)',
    value: '{{LOGO_SOCIETE}}',
  },
]

export const titleTokens = [
  {
    title: 'Opération',
    tokens: tokenOperation,
  },
  {
    title: 'Bénéficiaire',
    tokens: tokenBeneficiary,
  },
  {
    title: 'Installation',
    tokens: tokenProperty,
  },
  {
    title: 'Fiche opération standardisé',
    tokens: tokenStandardizedOperationSheet,
  },
  {
    title: 'Valorisation',
    tokens: tokenValuation,
  },
  {
    title: 'Organisation',
    tokens: tokenEntity,
  },
  {
    title: 'Société',
    tokens: tokenCompany,
  },
]
