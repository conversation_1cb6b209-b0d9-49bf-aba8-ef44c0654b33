import type { Page, Pageable } from '@/types/pagination'
import type { AxiosInstance, AxiosPromise } from 'axios'
import axiosInstance from '.'
import type { Property } from '@/types/property'

export interface PropertyFilter {
  search?: string
}

class PropertyApi {
  public constructor(private axios: AxiosInstance) {}

  public findAll(filter: PropertyFilter, pageable: Pageable): AxiosPromise<Page<Property>> {
    return this.axios.get('/properties', {
      params: { ...filter, ...pageable },
    })
  }
}

export const propertyApi = new PropertyApi(axiosInstance)
