import type { Page, Pageable } from '@/types/pagination'
import type { SimpleTerritoryDto, Territory } from '@/types/territory'
import type { AxiosInstance, AxiosPromise } from 'axios'
import axiosInstance from '.'

export type TerritoryFilter = Partial<{ search: string; ids: number[] }>

class TerritoryApi {
  public constructor(private axios: AxiosInstance) {}

  public findOne(id: number): AxiosPromise<Territory> {
    return this.axios.get('/territories/' + id)
  }

  public findAll(filter: TerritoryFilter, pageable: Pageable): AxiosPromise<Page<Territory>> {
    return this.axios.get('/territories', {
      params: { ...filter, ...pageable },
    })
  }

  public findPublicOne(id: number): AxiosPromise<SimpleTerritoryDto> {
    return this.axios.get('/public_territories/' + id)
  }

  public findPublicAll(filter: TerritoryFilter, pageable: Pageable): AxiosPromise<Page<SimpleTerritoryDto>> {
    return this.axios.get('/public_territories', {
      params: { ...filter, ...pageable },
    })
  }
}

export const territoryApi = new TerritoryApi(axiosInstance)
