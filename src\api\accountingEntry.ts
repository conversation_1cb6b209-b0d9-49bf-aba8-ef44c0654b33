import type { AxiosInstance, AxiosPromise } from 'axios'
import axiosInstance from '.'
import type { ResponseHandler } from '@/types/responseHandler'
import type { AccountingEntryOperationDto, AccountingEntryType } from '@/types/accountingEntry'
import { type Page, type Pageable } from '@/types/pagination'

export type AccountingEntryFilter = Partial<{
  operationId: number
  accountingEntryType: AccountingEntryType
}>

class AccountingEntryApi {
  public constructor(private axios: AxiosInstance) {}

  public async save(file: File): AxiosPromise<ResponseHandler> {
    const formData = new FormData()
    if (file) {
      formData.append('file', file, file.name)
      const hash = await hashFile(file)
      formData.append('hash', hash)
    }

    return this.axios.post('/accounting_entries', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    })
  }

  public async getAll(
    pageable: Pageable,
    filter: AccountingEntryFilter
  ): AxiosPromise<Page<AccountingEntryOperationDto>> {
    return this.axios.get('/accounting_entries', { params: { ...pageable, ...filter } })
  }
}

export const accountingEntryApi = new AccountingEntryApi(axiosInstance)
