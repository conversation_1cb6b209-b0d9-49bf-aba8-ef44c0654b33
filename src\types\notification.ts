import type { LocalDate, LocalDateTime } from './date'
import type { Message } from './message'
import type { Operation } from './operation'
import type { User } from './user'
import type { UserProfileRequest } from './userProfileRequest'

export interface Notification {
  id: number
  creationDateTime: LocalDate
  recipient: User
  readDateTime?: LocalDateTime
  deleteDateTime?: LocalDateTime
  metaData?: NotificationMetaData
}

export type NotificationMetaData =
  | FinalVersionToSendNotificationMetadata
  | RgeEndOfValidityNotificationMetadata
  | OperationToProcessNotificationMetadata
  | MessageNotificationMetadata
  | ResponseToAtypicalValuationNotificationMetadata
  | ProfileRequestToProcessNotificationMetadata
  | AlertValidateStep50NotificationMetadata

export interface UpdateNotificationRequest {
  read: boolean
}

export interface FinalVersionToSendNotificationMetadata {
  '@type': 'FinalVersionToSendNotificationMetadata'
  operation: Operation
}
export interface RgeEndOfValidityNotificationMetadata {
  '@type': 'RgeEndOfValidityNotificationMetadata'
  operation: Operation
}
export interface OperationToProcessNotificationMetadata {
  '@type': 'OperationToProcessNotificationMetadata'
  operation: Operation
}
export interface AlertValidateStep50NotificationMetadata {
  '@type': 'AlertValidateStep50NotificationMetadata'
  operation: Operation
}

export interface MessageNotificationMetadata {
  '@type': 'MessageNotificationMetadata'
  message: Message
}

export interface ResponseToAtypicalValuationNotificationMetadata {
  operation: Operation
  accepted: boolean
  '@type': 'ResponseToAtypicalValuationNotificationMetadata'
}

export interface ProfileRequestToProcessNotificationMetadata {
  userProfileRequest: UserProfileRequest
  '@type': 'ProfileRequestToProcessNotificationMetadata'
}
