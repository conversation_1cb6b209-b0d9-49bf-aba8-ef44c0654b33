<template>
  <VChip>
    {{ standardizedOperationSheet.operationCode }}
    {{ duration }}
  </VChip>
</template>

<script setup lang="ts">
import type { StandardizedOperationSheetReduced } from '@/types/calcul/standardizedOperationSheet'
import type { PropType } from 'vue'

const props = defineProps({
  standardizedOperationSheet: {
    type: Object as PropType<StandardizedOperationSheetReduced>,
    required: true,
  },
})

const duration = computed(() => {
  if (!props.standardizedOperationSheet.startDate && !props.standardizedOperationSheet.expirationDate) {
    return ''
  }
  if (props.standardizedOperationSheet.startDate && props.standardizedOperationSheet.expirationDate) {
    return `(entre ${formatHumanReadableLocalDate(
      props.standardizedOperationSheet.startDate
    )} et ${formatHumanReadableLocalDate(props.standardizedOperationSheet.expirationDate)})`
  }
  if (props.standardizedOperationSheet.startDate) {
    return `(à partir du ${formatHumanReadableLocalDate(props.standardizedOperationSheet.startDate)})`
  }
  return `(jusqu'au ${formatHumanReadableLocalDate(props.standardizedOperationSheet.expirationDate)})`
})
</script>
