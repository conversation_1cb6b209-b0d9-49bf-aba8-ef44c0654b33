<template>
  <VTooltip v-if="item?.status == 'DOING'">
    <template #activator="{ props }">
      <div v-bind="props" class="d-flex align-center">
        <VIcon v-if="instructionDelay < 0 && item!.stepId <= 50" icon="mdi-clock-outline" color="error" class="mr-1" />
      </div>
    </template>
    <span
      v-if="
        instructionDelay <
        -NUMBER_OF_DAY_BEFORE_PNCEE_EXPIRATION + NUMBER_OF_DAY_AFTER_EFFECTIVE_END_WORKS_FOR_INSTRUCTION_DELAY
      "
    >
      <PERSON><PERSON><PERSON> PNCEE dépassé
    </span>
    <span
      v-else-if="instructionDelay < -TEN_MONTHS_IN_DAYS + NUMBER_OF_DAY_AFTER_EFFECTIVE_END_WORKS_FOR_INSTRUCTION_DELAY"
    >
      10 mois de délais après fin de travaux dépassé, validez l'étape 50 sous 5 jours
    </span>

    <span
      v-else-if="
        instructionDelay < -HEIGHT_MONTHS_IN_DAYS + NUMBER_OF_DAY_AFTER_EFFECTIVE_END_WORKS_FOR_INSTRUCTION_DELAY
      "
    >
      8 mois de délais après fin de travaux dépassé, validez l'étape 50</span
    >
    <span v-else> 3 mois de délais après fin de travaux dépassé, validez l'étape 50</span>
  </VTooltip>
</template>
<script setup lang="ts">
import { type Operation } from '@/types/operation'
import {
  getInstructionDelay,
  NUMBER_OF_DAY_BEFORE_PNCEE_EXPIRATION,
  NUMBER_OF_DAY_AFTER_EFFECTIVE_END_WORKS_FOR_INSTRUCTION_DELAY,
} from '@/types/osh/instructionDelay'
const props = defineProps({
  item: {
    type: Object as PropType<Operation>,
    nullable: false,
  },
})

const HEIGHT_MONTHS_IN_DAYS = 242
const TEN_MONTHS_IN_DAYS = 303

const instructionDelay = computed(() => {
  const date = props.item?.actualEndWorksDate ?? props.item?.estimatedEndOperationDate
  return date ? getInstructionDelay(date) : 0
})
</script>
