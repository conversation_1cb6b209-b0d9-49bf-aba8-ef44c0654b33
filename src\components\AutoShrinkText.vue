<template>
  <VTooltip v-if="text.length > maxLength" location="bottom">
    <template #activator="{ props }">
      <div v-bind="props">
        {{
          truncate(text, {
            length: maxLength,
            separator: ' ',
          })
        }}
      </div>
    </template>
    <span style="font-size: 1.125rem">
      {{ text }}
    </span>
  </VTooltip>
  <span v-else>
    {{ text }}
  </span>
</template>

<script setup lang="ts">
import { truncate } from 'lodash'

defineProps({
  text: {
    type: String,
    default: '',
  },
  maxLength: {
    type: Number,
    default: 50,
  },
})
</script>
