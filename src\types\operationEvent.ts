import type { User } from './user'
import type { LocalDate, LocalDateTime } from './date'
import type { Property } from './property'
import type { Address } from './address'
import type { CommercialStatus } from './steps'
import type { WorksType } from './works'
import type { ControlOrderDetails, OperationStatus, ParameterValues } from './operation'
import type { Message } from './message'

export interface OperationEvent {
  uuid: string
  creationDateTime: LocalDate
  creationUser: User
  event: string
  operationHistory: OperationHistory
  changeSet: ObjectChange[]
  message: Message
}

export interface OperationHistory {
  id: number
  chronoCode: string
  simulationName: string
  operationName: string
  entityId: number
  stepId: number
  classicCumac: number
  precariousnessCumac: number
  reservedClassicCumac: number
  reservedPrecariousnessCumac: number
  classicValuationValue: number
  precariousnessValuationValue: number
  reservedClassicValuationValue: number
  reservedPrecariousnessValuationValue: number
  property: Property | null
  finalAddress: Address
  standardizedOperationSheetId: number
  eesDuplicate: boolean
  customerDuplicate: boolean
  cumul: boolean
  periodId: number | null
  estimatedCommitmentDate: LocalDate
  estimatedEndOperationDate: LocalDate
  actualEndWorksDate: LocalDate
  signedDate: LocalDate
  status: OperationStatus
  lostDate: LocalDate
  lostReasons: Record<string, string> | null
  beneficiaryId: number | null
  controlOrganismId: number | null
  subcontractorId: number | null
  parameterValues: ParameterValues[]
  precariousnessBonusParameterValues?: Map<string, string | number>
  epcBonusParameterValues: Record<string, string | number> | null
  valuationTypeId: number
  creationDateTime: LocalDateTime
  creationUserId: number
  updateDateTime: LocalDateTime
  updateUserId: number
  boostBonusSheetId: number
  boostBonusParameterValues: Record<string, string | number> | null
  commentary: string
  commercialStatus: CommercialStatus | null
  customerOfferNumber: string
  offersDispatchDate: LocalDate | null
  operationsGroupId: number | null
  customerFinancialIncentive: number
  commercialOfferWithoutFinancialIncentive: number
  finalWorksType: WorksType | null
  backToStep50Counter: number
  accountedMessagesNumber: number
  emmyFolderId: number | null
  emmyLotId: number
  exportToCsvDateTime: LocalDateTime | null
  validateImportInEmmyUserId: number
  validateImportInEmmyDateTime: LocalDateTime | null
  specialFee: number
  rgeGrantedDate: LocalDate
  rgeEndOfValidityDate: LocalDate
  controlOrderBatchId: number
  controlOrderDetails: ControlOrderDetails
  documentTypeIdsSent?: number[]
}

export const changeSetFieldNameLabel: {
  fieldName: string
  value: string
}[] = [
  {
    fieldName: 'id',
    value: 'id',
  },
  {
    fieldName: 'chronoCode',
    value: 'Code chrono',
  },
  {
    fieldName: 'simulationName',
    value: 'Nom de la simulation',
  },
  {
    fieldName: 'operationName',
    value: "Nom de l'opération",
  },
  {
    fieldName: 'name',
    value: 'Nom',
  },
  {
    fieldName: 'entityId',
    value: 'Organisation',
  },
  {
    fieldName: 'entity',
    value: 'Organisation',
  },
  {
    fieldName: 'stepId',
    value: 'Etape',
  },
  {
    fieldName: 'classicCumac',
    value: 'Nombre de kWhC classique',
  },
  {
    fieldName: 'precariousnessCumac',
    value: 'Nombre de kWhC précarité',
  },
  {
    fieldName: 'reservedClassicCumac',
    value: 'Nombre de kWhc réservé classique',
  },
  {
    fieldName: 'reservedPrecariousnessCumac',
    value: 'Nombre de kWhc réservé précarité',
  },
  {
    fieldName: 'classicValuationValue',
    value: 'Montant de la valorisation classique demandée',
  },
  {
    fieldName: 'precariousnessValuationValue',
    value: 'Montant de la valorisation précarité demandée',
  },
  {
    fieldName: 'reservedClassicValuationValue',
    value: 'Montant de la valorisation classique réservé',
  },
  {
    fieldName: 'reservedPrecariousnessValuationValue',
    value: 'Montant de la valorisation précarité réservé',
  },
  {
    fieldName: 'property',
    value: 'Installation',
  },
  {
    fieldName: 'finalAddress',
    value: 'Adresse',
  },
  {
    fieldName: 'standardizedOperationSheetId',
    value: 'Fiche opération',
  },
  {
    fieldName: 'eesDuplicate',
    value: 'EES',
  },
  {
    fieldName: 'customerDuplicate',
    value: 'Client',
  },
  {
    fieldName: 'cumul',
    value: 'Cumul',
  },
  {
    fieldName: 'periodId',
    value: 'Periode',
  },
  {
    fieldName: 'estimatedCommitmentDate',
    value: "Date d'engagement prévisionnelle",
  },
  {
    fieldName: 'estimatedEndOperationDate',
    value: 'Date de fin de travaux prévisionnelle',
  },
  {
    fieldName: 'actualEndWorksDate',
    value: 'Date de fin de travaux',
  },
  {
    fieldName: 'signedDate',
    value: 'Date de signature',
  },
  {
    fieldName: 'status',
    value: 'Statut',
  },
  {
    fieldName: 'lostDate',
    value: "Date de la perte de l'offre",
  },
  {
    fieldName: 'lostReasons',
    value: "Raisons de la perte de l'offre",
  },
  {
    fieldName: 'businessPlanId',
    value: 'Business Plan',
  },
  {
    fieldName: 'beneficiaryId',
    value: 'Bénéficiaire',
  },
  {
    fieldName: 'beneficiary',
    value: 'Bénéficiaire',
  },
  {
    fieldName: 'controlOrganismId',
    value: 'Organisme de contrôle',
  },
  {
    fieldName: 'subcontractor',
    value: 'Sous traitant',
  },
  {
    fieldName: 'parameterValues',
    value: 'Paramètre de calcul',
  },
  {
    fieldName: 'precariousnessBonusParameterValues',
    value: 'Paramètre de calcul précarité',
  },
  {
    fieldName: 'epcBonusParameterValues',
    value: 'Paramètre de calcul CPE',
  },
  {
    fieldName: 'valuationTypeId',
    value: 'Type de valorisation',
  },
  {
    fieldName: 'creationDateTime',
    value: 'Date de création',
  },
  {
    fieldName: 'creationUserId',
    value: 'Créateur',
  },
  {
    fieldName: 'updateDateTime',
    value: 'Date de mise à jour',
  },
  {
    fieldName: 'updateUserId',
    value: 'Utilisateur',
  },
  {
    fieldName: 'boostBonusSheetId',
    value: 'Coup de pouce',
  },
  {
    fieldName: 'boostBonusParameterValues',
    value: 'Paramètre du coup de pouce',
  },
  {
    fieldName: 'commentary',
    value: 'Commentaire',
  },
  {
    fieldName: 'commercialStatus',
    value: 'Statut commercial',
  },
  {
    fieldName: 'customerOfferNumber',
    value: "Numéro de l'offre client",
  },
  {
    fieldName: 'offersDispatchDate',
    value: "Date de l'offre",
  },
  {
    fieldName: 'operationsGroupId',
    value: 'Regroupement',
  },
  {
    fieldName: 'customerFinancialIncentive',
    value: 'Incitation financière client',
  },
  {
    fieldName: 'commercialOfferWithoutFinancialIncentive',
    value: "Incitation financière client sans l'offre commercial",
  },
  {
    fieldName: 'finalWorksType',
    value: 'Type de chantier',
  },
  {
    fieldName: 'backToStep50Counter',
    value: "Nombre de retour à l'étape 50",
  },
  {
    fieldName: 'accountedMessagesNumber',
    value: 'Nombre de message comptabilisé',
  },
  {
    fieldName: 'emmyFolderId',
    value: 'Dossier Emmy',
  },
  {
    fieldName: 'emmyLotId',
    value: 'Lot emmy',
  },
  {
    fieldName: 'exportToCsvDateTime',
    value: "Date d'export vers le CSV",
  },
  {
    fieldName: 'validateImportInEmmyDateTime',
    value: "Date de validation de l'import",
  },
  {
    fieldName: 'isFromBocee',
    value: 'Vient de Bocee',
  },
  {
    fieldName: 'documentTypeId',
    value: 'Type de document',
  },
  {
    fieldName: 'description',
    value: 'Description',
  },
  {
    fieldName: 'originalFilename',
    value: 'Nom du fichier',
  },
  {
    fieldName: 'active',
    value: 'Actif',
  },
  {
    fieldName: 'finalAddress_street',
    value: 'Rue',
  },
  {
    fieldName: 'finalAddress_postalCode',
    value: 'Code postal',
  },
  {
    fieldName: 'finalAddress_city',
    value: 'Ville',
  },
  {
    fieldName: 'finalAddress_additionalPostalAddress',
    value: 'Adresse informations complémentaire ',
  },
  {
    fieldName: 'finalPropertyName',
    value: "Nom d'installation",
  },
  {
    fieldName: 'caseDate',
    value: "Date de l'arrivé au pôle CAP",
  },
  {
    fieldName: 'reservedDate',
    value: 'Date de réservation',
  },
  {
    fieldName: 'atypicalClassicValuationValue',
    value: 'Valorisation atypique classique',
  },
  {
    fieldName: 'atypicalPrecariousnessValuationValue',
    value: 'Valorisation atypique précarité',
  },
  {
    fieldName: 'instructorId',
    value: 'Instructeur',
  },
  {
    fieldName: 'secondInterlocutor',
    value: 'Second interlocuteur',
  },
  {
    fieldName: 'specialFee',
    value: 'Charge pôle CEE spécifique',
  },
  {
    fieldName: 'finalFee',
    value: 'Charge pôle CEE',
  },
  {
    fieldName: 'feeIssuedEntityId',
    value: "Charge pôle CEE émis par l'organisation",
  },
  {
    fieldName: 'works1',
    value: 'Chantier 1',
  },
  {
    fieldName: 'works2',
    value: 'Chantier 2',
  },
  {
    fieldName: 'works3',
    value: 'Chantier 3',
  },
  {
    fieldName: 'rgeGrantedDate',
    value: "Date d'attribution du RGE pour l'entreprise réalisant les travaux",
  },
  {
    fieldName: 'rgeEndOfValidityDate',
    value: "Date de fin de validité du RGE de l'entreprise réalisant les travaux",
  },
  {
    fieldName: 'controlOrderBatchId',
    value: 'Lot de contrôle',
  },
  {
    fieldName: 'controlOrderSiteStatus',
    value: "Statut de l'arrêté contrôle site",
  },
  {
    fieldName: 'controlOrderContactStatus',
    value: "Statut de l'arrêté contrôle contact",
  },
  {
    fieldName: 'controlOrderAssStatus',
    value: "Statut du SAV de l'arrêté contrôle",
  },
  {
    fieldName: 'heatFundActive',
    value: 'Fonds chaleur actif',
  },
  {
    fieldName: 'ademeCode',
    value: 'Code Ademe',
  },
  {
    fieldName: 'headOperation',
    value: 'Opération chapeau',
  },
  {
    fieldName: 'applicantUserId',
    value: 'Demandeur',
  },
  {
    fieldName: 'toProcess',
    value: 'À traiter',
  },
  {
    fieldName: 'controlOrderDetails_controlOrderType',
    value: 'Type de contrôle',
  },
  {
    fieldName: 'controlOrderDetails_controlOrderStatus',
    value: 'Status du contrôle',
  },
  {
    fieldName: 'controlOrderDetails_afterSalesServiceStatus',
    value: 'Status du SAV du contrôle',
  },
  {
    fieldName: 'controlOrderDetails_commmentary',
    value: 'Commentaire du contrôle',
  },
  {
    fieldName: 'selfWorks',
    value: 'Travaux en propre',
  },
  {
    fieldName: 'duplicateChronoCode',
    value: 'Référence Duplication',
  },
  {
    fieldName: 'conventionSent',
    value: 'Convention Envoyé',
  },
  {
    fieldName: 'documentTypeIdsSent',
    value: 'Documents envoyés',
  },
  {
    fieldName: 'subcontractorId',
    value: 'Sous traitant',
  },
  {
    fieldName: 'oshPriority',
    value: 'Dossier OSH',
  },
  {
    fieldName: 'oshPriorityAutomated',
    value: 'Affectation OSH',
  },
  {
    fieldName: 'sendFinalVersionToBeneficiaryDateTime',
    value: "Date d'envoi de la VF",
  },
  {
    fieldName: 'coOwnerShipSyndicateName',
    value: 'Syndicat de copropriété',
  },
  {
    fieldName: 'coOwnerShipSyndicateImmatriculationNumber',
    value: "Numéro d'immatriculation du syndicat de copropriété",
  },
  {
    fieldName: 'orderNumber',
    value: 'Numéro de commande',
  },
]

export interface ObjectChange {
  fieldName: string
  oldValue: any
  newValue: any
}
