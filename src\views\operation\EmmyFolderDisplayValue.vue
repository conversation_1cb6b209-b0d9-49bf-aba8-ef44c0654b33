<template>
  <VRow class="flex-column" dense>
    <VCol>
      <NjDisplayValue label="Nom" :value="operation.emmyFolder?.name" />
    </VCol>
    <VCol>
      <NjDisplayValue label="Numéro" :value="operation.emmyFolder?.emmyCode" />
    </VCol>
    <VCol>
      <NjDisplayValue label="Envoi EMMY" :value="operation.validateImportInEmmyDateTime ? 'OUI' : 'NON'" />
    </VCol>
    <VCol v-if="operation.emmyFolder!.stepId > 90">
      <NjDisplayValue
        label="Date d'envoi au PNCEE"
        :value="formatHumanReadableLocalDate(operation.emmyFolder?.pnceeSubmissionDate!)"
      />
    </VCol>
    <VCol v-if="operation.emmyFolder!.stepId > 100">
      <NjDisplayValue
        label="Date de décision délivrance PNCEE"
        :value="formatHumanReadableLocalDate(operation.emmyFolder?.pnceeIssuedDate!)"
      />
    </VCol>
    <VCol v-if="operation.emmyFolder!.stepId > 100">
      <NjDisplayValue label="Numéro de délivrance PNCEE" :value="operation.emmyFolder?.pnceeClassicIssuedNumber" />
    </VCol>
  </VRow>
</template>
<script lang="ts" setup>
import type { Operation } from '@/types/operation'
import type { PropType } from 'vue'
import NjDisplayValue from '@/components/NjDisplayValue.vue'
import { formatHumanReadableLocalDate } from '@/types/date'

// eslint-disable-next-line @typescript-eslint/no-unused-vars
const props = defineProps({
  operation: {
    type: Object as PropType<Operation>,
    default: makeEmptyOperation,
  },
})
</script>
