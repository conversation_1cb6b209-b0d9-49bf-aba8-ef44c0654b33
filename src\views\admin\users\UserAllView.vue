<template>
  <NjPage title="Gestion des Utilisateurs" :error-message="data.error" :loading="data.loading" expend-body>
    <template #header-actions>
      <NjBtn
        @click="
          () => {
            createUser = makeEmptyUserWithEntities()
            createDialog = true
          }
        "
        >Créer un nouvel utilisateur</NjBtn
      >
    </template>
    <template #sub-header>
      <VRow>
        <VCol cols="2">
          <SearchInput v-model="search" v-model:loading="data.loading" />
        </VCol>
        <VCol cols="2">
          <RemoteAutoComplete
            label="Territoires"
            :model-value="pageFilter.territoryIds"
            :query-for-one="(v) => territoryApi.findOne(v)"
            :query-for-all="(v, p) => territoryApi.findAll({ search: v }, p)"
            :query-for-ones="(v) => territoryApi.findAll({ ids: v }, { size: 1000 })"
            :item-title="formatTerritory"
            item-value="id"
            infinite-scroll
            clearable
            multiple
            :page-size="50"
            @update:model-value="(event) => updateFilterByFieldname('territoryIds', event)"
          />
        </VCol>
        <VCol cols="2">
          <RemoteAutoComplete
            v-model="entities"
            label="Organisations"
            :query-for-one="(v) => entityApi.getOne(v)"
            :query-for-all="(v, p) => entityApi.getAll({ search: v }, p)"
            :item-title="(it) => it.id + '-' + it.name"
            infinite-scroll
            clearable
            :page-size="50"
            multiple
            chips
            closable-chips
            return-object
            @update:model-value="
              (event: Entity[]) => {
                updateFilterByFieldname(
                  'entityIds',
                  event.map((i) => i.id)
                )
              }
            "
          />
        </VCol>
        <VCol cols="2">
          <VSelect
            :model-value="pageFilter.roles"
            label="Rôles"
            class="bg-white"
            multiple
            clearable
            :items="displayProfiles"
            @update:model-value="updateFilterByFieldname('roles', $event)"
          />
        </VCol>
        <VCol>
          <VSelect
            :model-value="pageFilter.active"
            label="Etat"
            class="bg-white"
            :items="activeFilterItems"
            clearable
            @update:model-value="(value) => updateFilterByFieldname('active', value)"
          />
        </VCol>
        <VSpacer />
        <VCol cols="1">
          <NjBtn class="w-100" @click="copyProfileDialogActive = true">Copier Profil</NjBtn>
        </VCol>
      </VRow>
    </template>
    <template #body>
      <VRow class="w-100">
        <VCol>
          <NjDataTable
            v-model:selections="selection"
            :headers="headers"
            :pageable="pageable"
            :page="data.value!"
            :on-click-row="() => {}"
            :download="downloadExport"
            fixed
            @update:pageable="updatePageable"
          >
            <template #[`item.intervenant`]="{ item }"> {{ item.firstName }} {{ item.lastName }} </template>
            <template #[`item.active`]="{ item }">
              <NjBooleanIcon :condition="item.active" />
            </template>
            <template #[`item.entities`]="{ item }">
              {{ item.entities[0]?.name ?? '' }}
              {{ item.entities.length > 1 ? `, +${item.entities.length - 1}` : '' }}
            </template>
            <template #[`item.codes`]="{ item }">
              <div>
                {{ item.entities[0]?.id ?? '' }}
                {{ item.entities.length > 1 ? `, +${item.entities.length - 1}` : '' }}
                <VTooltip v-if="item.entities.length > 1" location="top" activator="parent">
                  {{ item.entities.map((orga) => orga.id).join(', ') }}
                </VTooltip>
              </div>
            </template>
            <template #[`item.support_agence_plus`]="{ item }">
              <NjBooleanIcon :condition="item.roles.includes('SUPPORT_AGENCE_PLUS')" :null="!item.active" />
            </template>
            <template #[`item.agence`]="{ item }">
              <NjBooleanIcon :condition="item.roles.includes('AGENCE')" :null="!item.active" />
            </template>
            <template #[`item.agence_plus`]="{ item }">
              <NjBooleanIcon :condition="item.roles.includes('AGENCE_PLUS')" :null="!item.active" />
            </template>
            <template #[`item.territoire`]="{ item }">
              <NjBooleanIcon :condition="item.roles.includes('TERRITOIRE')" :null="!item.active" />
            </template>
            <template #[`item.siege`]="{ item }">
              <NjBooleanIcon :condition="item.roles.includes('SIEGE')" :null="!item.active" />
            </template>
            <template #[`item.instructeur`]="{ item }">
              <NjBooleanIcon :condition="item.roles.includes('INSTRUCTEUR')" :null="!item.active" />
            </template>
            <template #[`item.daf`]="{ item }">
              <NjBooleanIcon :condition="item.roles.includes('DAF')" :null="!item.active" />
            </template>
            <template #[`item.dafSiege`]="{ item }">
              <NjBooleanIcon :condition="item.roles.includes('DAF_SIEGE')" :null="!item.active" />
            </template>
            <template #[`item.vente_cee`]="{ item }">
              <NjBooleanIcon :condition="item.roles.includes('VENTE_CEE')" :null="!item.active" />
            </template>
            <template #[`item.admin`]="{ item }">
              <NjBooleanIcon :condition="item.roles.includes('ADMIN')" :null="!item.active" />
            </template>
            <template #[`item.admin_plus`]="{ item }">
              <NjBooleanIcon :condition="item.roles.includes('ADMIN_PLUS')" :null="!item.active" />
            </template>
            <template #[`item.arrete_controle_siege`]="{ item }">
              <NjBooleanIcon :condition="item.roles.includes('ARRETE_CONTROLE_SIEGE')" :null="!item.active" />
            </template>
          </NjDataTable>
        </VCol>
        <VNavigationDrawer v-if="selection.length > 0" location="right" width="400" disable-resize-watcher permanent>
          <VCard class="h-100 content-layout">
            <VCardTitle class="content-layout__header py-0 pe-0">
              <VRow class="align-center">
                <VCol>Détails</VCol>
                <VCol class="flex-grow-0" justify="end">
                  <NjIconBtn
                    v-if="userHasRole(selection[0], 'ADMIN_PLUS') ? userStore.isAdminPlus : true"
                    icon="mdi-pencil"
                    color="primary"
                    class="rounded-0"
                    @click="edit = !edit"
                  />
                  <NjIconBtn
                    icon="mdi-window-close"
                    variant="flat"
                    class="rounded-0"
                    color="primary"
                    @click="selection = []"
                  />
                </VCol>
              </VRow>
            </VCardTitle>
            <VDivider />
            <VCardText class="content-layout__main content-layout">
              <UserForm
                v-if="edit"
                :user="selection[0]"
                @save="
                  // eslint-disable-next-line prettier/prettier
                  edit = false;
                  reload()
                  reloadUser()
                "
                @cancel="
                  () => {
                    selection = []
                    reload()
                  }
                "
              />
              <UserDisplay v-else :user="selection[0]" />
            </VCardText>
          </VCard>
        </VNavigationDrawer>
      </VRow>
      <CardDialog v-model="createDialog" title="Nouvel Utilisateur" :width="displayForm ? '40%' : '70%'" no-actions>
        <UserForm
          v-model:display-form="displayForm"
          :user="createUser"
          inline
          @save="
            () => {
              createDialog = false
              reload()
            }
          "
          @cancel="
            () => {
              createDialog = false
            }
          "
        />
      </CardDialog>

      <CopyProfileDialog v-model="copyProfileDialogActive" />
    </template>
  </NjPage>
</template>
<script lang="ts" setup>
import { userApi, type UserFilter } from '@/api/user'
import NjBooleanIcon from '@/components/NjBooleanIcon.vue'
import NjPage from '@/components/NjPage.vue'
import type { DataTableHeader } from '@/components/okta/NjDataTable.type'
import NjDataTable from '@/components/okta/NjDataTable.vue'
import { useSnackbarStore } from '@/stores/snackbar'
import { useUserStore } from '@/stores/user'
import type { UserWithEntities } from '@/types/user'
import { userHasRole } from '@/types/user'
import UserDisplay from './UserDisplay.vue'
import UserForm from './UserForm.vue'
import { makeEmptyUserWithEntities } from '@/types/user'
import CopyProfileDialog from './CopyProfileDialog.vue'
import { formatTerritory } from '@/types/territory'
import { territoryApi } from '@/api/territory'
import { entityApi } from '@/api/entity'
import type { Entity } from '@/types/entity'
import { VSelect } from 'vuetify/components'
import type { LocalDateTime } from '@/types/date'
import { displayProfiles } from '@/types/user'

const createDialog = ref(false)
const selection = ref<UserWithEntities[]>([])

const search = ref<string>()

const entities = ref<Entity[]>([])

const { data, pageable, pageFilter, updatePageable, updateFilter, reload, updateFilterByFieldname } =
  usePaginationInQuery<UserWithEntities, UserFilter>(
    (filter, pageable) => {
      return userApi.getAll(pageable, filter).then((v) => {
        v.data.content.forEach((it) => {
          it.entities.sort((a, b) => a.id.localeCompare(b.id))
        })
        return v
      })
    },
    {
      defaultPageFilter: {
        search: search.value,
        territoryIds: [],
      },
      saveFiltersName: 'UserAllView',
      queryToFilterMapper(query) {
        return {
          ...query,
          territoryIds: mapQueryToTable(query.territoryIds, true),
          roles: mapQueryToTable(query.roles, false),
        }
      },
    }
  )

const headers: DataTableHeader[] = [
  {
    title: 'Intervenant',
    value: 'intervenant',
    sortable: false,
  },
  {
    title: 'Actif',
    value: 'active',
  },
  {
    title: 'Organisation',
    value: 'entities',
    sortable: false,
  },
  {
    title: 'Code',
    value: 'codes',
    sortable: false,
  },
  {
    title: 'Agence',
    value: 'agence',
    sortable: false,
  },
  {
    title: 'Support Agence +',
    value: 'support_agence_plus',
    sortable: false,
  },
  {
    title: 'Agence +',
    value: 'agence_plus',
    sortable: false,
  },
  {
    title: 'Territoire',
    value: 'territoire',
    sortable: false,
  },
  {
    title: 'Siège',
    value: 'siege',
    sortable: false,
  },
  {
    title: 'Instructeur',
    value: 'instructeur',
    sortable: false,
  },
  {
    title: 'Arrêté contrôle siège',
    value: 'arrete_controle_siege',
    sortable: false,
  },
  {
    title: 'DAF',
    value: 'daf',
    sortable: false,
  },
  {
    title: 'DAF Siège',
    value: 'dafSiege',
    sortable: false,
  },
  {
    title: 'Vente CEE',
    value: 'vente_cee',
    sortable: false,
  },
  {
    title: 'Admin',
    value: 'admin',
    sortable: false,
  },
  {
    title: 'Admin +',
    value: 'admin_plus',
    sortable: false,
  },
  {
    title: 'GID',
    value: 'gid',
  },
  {
    title: 'Dernière connexion',
    value: 'lastConnectionDateTime',
    formater: (_, v: LocalDateTime) => formatHumanReadableLocalDateTime(v),
  },
]

watch(search, (v) => {
  const filter = {
    ...unref(pageFilter),
    search: v,
  }
  updateFilter(filter)
})

watch(
  pageFilter,
  (v) => {
    if (v) {
      search.value = v.search
      if (v.entityIds?.length) {
        entityApi.getAll({ ids: v.entityIds }, { size: v.entityIds?.length }).then((res) => {
          entities.value = res.data.content
        })
      }
    }
  },
  {
    immediate: true,
  }
)

const createUser = ref(makeEmptyUserWithEntities())
const displayForm = ref(true)
const edit = ref(false)

watch(selection, () => (edit.value = false))
const snackbarStore = useSnackbarStore()

const userStore = useUserStore()
const downloadExport = computed(() =>
  userStore.isAdminPlus
    ? () => {
        userApi
          .export(pageFilter.value)
          .then((response) => {
            downloadFile('export utilisateurs.xlsx', response.data)
            snackbarStore.setSuccess('Export des utilisateurs a réussi')
          })
          .catch(async (err) =>
            snackbarStore.setError(await handleAxiosException(err, JSON.parse(await err.response.data.text()).message))
          )
      }
    : undefined
)

const reloadUser = () => {
  if (selection.value.length > 0) {
    userApi.getOne(selection.value[0].id).then((v) => {
      selection.value[0] = v.data
    })
  }
}

const copyProfileDialogActive = ref(false)

const activeFilterItems: { title: string; value: boolean | null }[] = [
  {
    title: 'Actif',
    value: true,
  },
  {
    title: 'Inactif',
    value: false,
  },
]
</script>
