<template>
  <NjPage>
    <template #body>
      <VRow class="flex-column">
        <VCol>
          <VRow no-gutters>
            <VCol>
              <VBtn
                prepend-icon="mdi-keyboard-backspace"
                elevation="0"
                :style="{ backgroundColor: 'transparent', paddingLeft: '4px', paddingRight: '4px' }"
                @click="router.push({ name: 'StepAllView' })"
              >
                Retour
              </VBtn>
            </VCol>
          </VRow>
          <VRow v-if="step.value" class="flex-row">
            <VCol cols="3" :style="{ display: 'flex', gap: '10px' }">
              <VBtn rounded="0" icon="mdi-chevron-up" :disabled="step.value.id === 10" @click="previous"></VBtn>
              <VBtn rounded="0" icon="mdi-chevron-down" :disabled="step.value.id === 120" @click="next"></VBtn>
              <h1 :style="{ marginLeft: '10px' }">Étape {{ step.value.id }}</h1>
            </VCol>
          </VRow>
          <VRow>
            <VCol cols="4">
              <VTextField v-model="step.value!.name" label="Nom"> </VTextField>
            </VCol>
            <VCol cols="4">
              <VSelect
                v-model="step.value!.commercialStatus"
                label="Offre commerciale"
                :items="commercialStatusTitle"
              />
            </VCol>
            <VCol cols="2">
              <VSelect v-model="step.value!.treatedBy" label="Qui" :items="displayProfiles" />
            </VCol>
            <VCol cols="1">
              <VTextField v-model="step.value!.validMonthDuration" label="Validité" suffix="mois"> </VTextField>
            </VCol>
            <VCol cols="1" style="align-items: center; display: flex">
              <VTooltip
                text="Enregistrer les modifications relatives à l'étape (hors documents)"
                :disabled="isEqual(step.value, originalStep)"
              >
                <template #activator="{ props }">
                  <NjBtn v-bind="props" :disabled="isEqual(step.value, originalStep)" @click="updateStep()">
                    Enregistrer
                  </NjBtn>
                </template>
              </VTooltip>
            </VCol>
          </VRow>
        </VCol>
        <VCol>
          <VRow>
            <VCol cols="8">
              <VCard>
                <VContainer style="display: flex">
                  <div class="divide">
                    <div>
                      <h3 class="pb-2">Documents disponibles</h3>
                      <VTextField v-model="search" prepend-inner-icon="mdi-magnify" placeholder="Recherche" />
                    </div>
                    <VList>
                      <!-- eslint-disable-next-line vue/valid-v-for -->
                      <VListItem
                        v-for="document in searchDocument"
                        class="mb-2 py-0"
                        border
                        @dblclick="selectDocument(document)"
                      >
                        <template #default>
                          {{ document.name }}
                        </template>
                        <template #append>
                          <NjIconBtn
                            :disabled="selectOneDocumentLoading"
                            icon="mdi-plus"
                            @click="() => selectDocument(document)"
                          />
                        </template>
                      </VListItem>
                    </VList>
                  </div>
                  <div style="display: flex; flex-direction: column; justify-content: center">
                    <NjIconBtn icon="mdi-chevron-double-right" @click="selectAllDocument" />
                    <NjIconBtn icon="mdi-chevron-double-left" @click="unselectAllDocument()" />
                  </div>
                  <div class="divide">
                    <div class="pb-2">
                      <h3>Documents sélectionnés</h3>
                    </div>
                    <VList class="py-0">
                      <!-- eslint-disable-next-line vue/valid-v-for -->
                      <VListItem
                        v-for="assignedDoc in assignedDocuments.value"
                        :key="assignedDoc.documentType.id"
                        border
                        style="margin-bottom: 8px"
                        class="py-0"
                        @dblclick="unselectDocument(assignedDoc)"
                      >
                        <template #default>
                          <RouterLink
                            :to="AdminValueListViewVue"
                            :event="''"
                            style="width: fit-content"
                            @click="setInfoCompl(assignedDoc)"
                          >
                            {{ assignedDoc.documentType.name }}
                          </RouterLink>
                        </template>
                        <template #append>
                          <NjIconBtn
                            :disabled="unselectOneDocumentLoading"
                            icon="mdi-minus"
                            @click="() => unselectDocument(assignedDoc)"
                          />
                        </template>
                      </VListItem>
                    </VList>
                  </div>
                </VContainer>
              </VCard>
            </VCol>
            <VCol cols="4">
              <VCard>
                <VCardTitle>Fiche explicative</VCardTitle>
                <VDivider />
                <VCardText>
                  <VRow>
                    <VCol>
                      <RichTextInput v-model="step.value!.explanation" />
                    </VCol>
                  </VRow>
                </VCardText>
              </VCard>
            </VCol>
          </VRow>
        </VCol>
      </VRow>
      <StepDocumentLinkDialog v-if="infoCompl" v-model="infoCompl" v-model:link="link" @update:link="refreshList" />
    </template>
  </NjPage>
</template>

<script lang="ts" setup>
import { documentTypeApi } from '@/api/documentType'
import { stepsApi } from '@/api/steps'
import NjBtn from '@/components/NjBtn.vue'
import NjIconBtn from '@/components/NjIconBtn.vue'
import NjPage from '@/components/NjPage.vue'
import { useSnackbarStore } from '@/stores/snackbar'
import { makeEmptyDocumentType, type DocumentType } from '@/types/documentType'
import type { StepDocumentLink, StepDocumentLinkRequest } from '@/types/stepDocumentLink'
import { makeEmptyOperationTargetRule } from '@/types/operationTargetRule'
import { commercialStatusTitle, type Step } from '@/types/steps'
import type { Page } from '@/types/pagination'
import StepDocumentLinkDialog from '@/views/admin/StepDocumentLinkDialog.vue'
import { cloneDeep, isEqual } from 'lodash'
import AdminValueListViewVue from './AdminValueListView.vue'

const props = defineProps({
  id: {
    type: Number,
    default: undefined,
  },
})
const snackbarStore = useSnackbarStore()
const router = useRouter()

const step = ref(emptyValue<Step>())
step.value.value = {
  id: 0,
  name: '',
  commercialStatus: 'STUDIED',
  treatedBy: 'AGENCE',
  nbDocuments: 0,
  explanation: '',
}
const originalStep = ref<Step>()
const search = ref('')
const documentsAvailable = ref(emptyValue<Page<DocumentType>>())
const assignedDocuments = ref(emptyValue<StepDocumentLink[]>())
const link = ref<StepDocumentLink>({
  stepId: step.value.value?.id!,
  documentType: makeEmptyDocumentType(),
  description: '',
  operationTargetRule: makeEmptyOperationTargetRule(),
})
const infoCompl = ref(false)

const documents = ref(emptyValue<Page<StepDocumentLink>>())
const load = () => {
  handleAxiosPromise(
    documentsAvailable,
    documentTypeApi.getAll({ sort: ['name'], size: 2000 }, {}),
    (value) => {
      documentsAvailable.value.value = value.data
      handleAxiosPromise(
        documents,
        documentTypeApi.getAssignedDocuments({ size: 2000 }, { stepId: props.id }),
        (value) => {
          assignedDocuments.value.value = value.data.content
          assignedDocuments.value.value.forEach((link) =>
            documentsAvailable.value.value?.content?.splice(
              documentsAvailable.value.value.content.findIndex((doc) => doc.id === link.documentType.id),
              1
            )
          )
          assignedDocuments.value.value.sort((a, b) =>
            a.documentType > b.documentType ? 1 : b.documentType > a.documentType ? -1 : 0
          )
        },
        () => snackbarStore.setError('Une erreur est survenue lors de la récupération des documents')
      )
    },
    () => snackbarStore.setError('Une erreur est survenue lors de la récupération des documents')
  )
}

const setInfoCompl = (newLink: StepDocumentLink) => {
  link.value = cloneDeep(newLink)
  infoCompl.value = true
}

const refreshList = () => {
  if (assignedDocuments.value.value) {
    assignedDocuments.value.value[
      assignedDocuments.value.value.findIndex((doc) => doc.documentType.id === link.value.documentType.id)
    ] = link.value
  }
}

const searchDocument = computed(() => {
  return documentsAvailable.value.value?.content?.filter((doc) =>
    doc.name.toLowerCase().includes(search.value.toLowerCase())
  )
})

const selectOneDocumentLoading = ref(false)
const selectOneDocument = (doc: DocumentType, index: number) => {
  if (index !== -1) {
    selectOneDocumentLoading.value = true
    const request: StepDocumentLinkRequest = {
      stepId: step.value.value!.id,
      documentTypeId: doc.id,
      description: '',
      operationTargetRule: makeEmptyOperationTargetRule(),
    }
    documentTypeApi
      .updateLink(request)
      .then((response) => {
        documentsAvailable.value.value!.content.splice(index, 1)[0]!
        assignedDocuments.value.value?.push(response.data)
        assignedDocuments.value.value?.sort((a, b) =>
          a.documentType.name > b.documentType.name ? 1 : b.documentType.name > a.documentType.name ? -1 : 0
        )
      })
      .catch(() => snackbarStore.setError("Une erreur est survenue lors de l'affectation du document"))
      .finally(() => {
        selectOneDocumentLoading.value = false
      })
  }
}

const selectDocument = (doc: DocumentType) => {
  if (documentsAvailable.value.value) {
    const selectedIndex = documentsAvailable.value.value.content.findIndex((document) => document === doc)
    selectOneDocument(doc, selectedIndex)
  }
}

const selectAllDocument = () => {
  if (documentsAvailable.value.value) {
    documentsAvailable.value.value.content.forEach((doc) => selectOneDocument(doc, 0))
  }
}

const unselectOneDocumentLoading = ref(false)
const unselectOneDocument = (link: StepDocumentLink, index: number) => {
  if (index !== -1) {
    unselectOneDocumentLoading.value = true
    documentTypeApi
      .removeFromStep(link)
      .then(() => {
        documentsAvailable.value.value?.content?.push(assignedDocuments.value.value?.splice(index, 1)[0].documentType!)
        documentsAvailable.value.value?.content?.sort((a, b) => (a.name > b.name ? 1 : b.name > a.name ? -1 : 0))
      })
      .catch(() => snackbarStore.setError('Une erreur est survenue lors de la désaffectation du document'))
      .finally(() => {
        unselectOneDocumentLoading.value = false
      })
  }
}

const unselectDocument = (link: StepDocumentLink) => {
  if (assignedDocuments.value.value) {
    const selectedIndex = assignedDocuments.value.value.findIndex((document) => document === link)
    unselectOneDocument(link, selectedIndex)
  }
}

const unselectAllDocument = () => {
  if (assignedDocuments.value.value) {
    assignedDocuments.value.value.forEach((doc) => unselectOneDocument(doc, 0))
  }
}

const updateStep = () => {
  if (step.value.value) {
    stepsApi
      .update(step.value.value.id, step.value.value)
      .then(() => {
        snackbarStore.setSuccess("L'étape a bien été modifiée")
        originalStep.value = { ...step.value.value! }
      })
      .catch(() => snackbarStore.setError("Une erreur est survenue lors de la modification de l'étape"))
  }
}

// Navigation entre étapes
const previous = () => router.push({ name: 'StepOneView', params: { id: step.value.value!.id - 10 } })
const next = () => router.push({ name: 'StepOneView', params: { id: step.value.value!.id + 10 } })

//refresh le composant quand changement d'étape
watch(
  () => props.id,
  (v) => {
    if (v) {
      handleAxiosPromise(
        step,
        stepsApi.getById(v),
        (value) => {
          step.value.value = value.data
          originalStep.value = { ...step.value.value }
          load()
        },
        () => snackbarStore.setError("Une erreur est survenue lors de la récupération de l'étape")
      )
    }
  },
  {
    immediate: true,
  }
)

const displayProfiles = ref<any[]>([])
onMounted(() =>
  profiles.forEach((profile) =>
    displayProfiles.value.push({
      title: profile.substring(0, 1) + profile.substring(1, profile.length).toLowerCase().replace('_', ' '),
      value: profile,
    })
  )
)
</script>

<style scoped>
.divide {
  margin: 8px;
  width: 100%;
}

.document {
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.router-link-active {
  color: #007acd;
}
</style>
