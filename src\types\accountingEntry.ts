import type { LocalDate } from './date'

export const accountingEntryTypes: readonly { label: string; value: string }[] = [
  {
    label: 'Entrée',
    value: 'ENTRY',
  },
  {
    label: 'Cout CEE imputé sur chantier',
    value: 'WORKS_CEE_COST',
  },
  {
    label: 'Transfert organisation',
    value: 'ENTITY_TRANSFER',
  },
  {
    label: 'Sortie',
    value: 'EXIT',
  },
  {
    label: 'Vente',
    value: 'SELL',
  },
]
export type AccountingEntryType = (typeof accountingEntryTypes)[number]['value']

export interface AccountingEntry {
  sapCode: string
  creationDate: LocalDate
  accountingEntryType: AccountingEntryType
}

export interface AccountingEntryOperationDto {
  accountingEntry: AccountingEntry
  classicCumac: number
  precariousnessCumac: number
}
