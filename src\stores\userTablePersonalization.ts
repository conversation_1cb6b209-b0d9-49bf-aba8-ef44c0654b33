import type { UserTablePersonalization, UserTablePersonalizationRequest } from '@/types/userTablePersonalization'
import { userTablePersonalizationApi } from '@/api/userTablePersonalization'
import { defineStore } from 'pinia'
import type { DataTableHeader } from '@/components/okta/NjDataTable.type'
import { useSnackbarStore } from './snackbar'

export const useUserTablePersonalizationsStore = defineStore('userTablePersonalization', () => {
  const userTablePersonalizations = ref<UserTablePersonalization[]>([])

  const snackBarStore = useSnackbarStore()
  const getOne = async (tableName: string, originalHeaders: DataTableHeader[]): Promise<UserTablePersonalization> => {
    const oldLocation = 'column-manager__' + tableName
    const oldPersonalization = localStorage.getItem(oldLocation)
    //Cette condition sert à porter les personnalisations qui sont stocké actuellement dans le navigateur vers le backend
    if (oldPersonalization) {
      const parsedPersonalization: { value: string; visible: boolean }[] = JSON.parse(oldPersonalization)
      const result = (await userTablePersonalizationApi.save(mapOldPersonalization(tableName, parsedPersonalization)))
        .data
      localStorage.removeItem(oldLocation)
      userTablePersonalizations.value.push(result)
      return result
    } else {
      let result = userTablePersonalizations.value.find((i) => i.id.tableName == tableName)
      if (result) {
        return Promise.resolve(result)
      }

      try {
        result = (await userTablePersonalizationApi.getOne(tableName)).data
        userTablePersonalizations.value.push(result)
        return result
      } catch (err: any) {
        if (err.response.status == 404) {
          result = (
            await userTablePersonalizationApi.save(mapOriginalHeaderToPersonalization(tableName, originalHeaders))
          ).data
          userTablePersonalizations.value.push(result)
          return result
        } else {
          throw err
        }
      }
    }
  }

  const mapOldPersonalization = (
    name: string,
    old: { value: string; visible: boolean }[]
  ): UserTablePersonalizationRequest => {
    return {
      tableName: name,
      columnPersonalizations: old.map((i) => {
        return { columnName: i.value, visible: i.visible == undefined ? true : !!i.visible }
      }),
    }
  }

  const mapOriginalHeaderToPersonalization = (
    tableName: string,
    header: DataTableHeader[]
  ): UserTablePersonalizationRequest => {
    return {
      tableName: tableName,
      columnPersonalizations: header.map((i) => {
        return { columnName: i.key ?? i.value, visible: true }
      }),
    }
  }

  const update = (request: UserTablePersonalizationRequest) => {
    userTablePersonalizationApi
      .save(request)
      .then((res) => {
        userTablePersonalizations.value[
          userTablePersonalizations.value.findIndex((i) => (i.id.tableName = request.tableName))
        ] = res.data
      })
      .catch(async (err) => snackBarStore.setError(await handleAxiosException(err)))
  }

  return { getOne, update }
})
