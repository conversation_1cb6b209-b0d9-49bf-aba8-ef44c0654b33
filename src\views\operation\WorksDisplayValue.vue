<template>
  <VInput :model-value="modelValue" :rules="rules" hide-details="auto">
    <VCard v-if="modelValue || finalAddress" class="w-100">
      <VCardText class="pa-2">
        <NjDisplayValue label="Code Chantier" :value="modelValue.id"></NjDisplayValue>
        <NjDisplayValue label="Type Installation" :value="modelValue.worksType"></NjDisplayValue>
        <div v-if="deletable" style="text-align: end">
          <VLink icon="mdi-delete-outline" @click="$emit('delete')">Supprimer</VLink>
        </div>
      </VCardText>
    </VCard>
  </VInput>
</template>

<script setup lang="ts">
import { type Address } from '@/types/address'
import type { ValidationRule } from '@/types/rule'
import type { Works } from '@/types/works'
import type { PropType } from 'vue'

defineProps({
  modelValue: {
    type: Object as PropType<Works>,
    required: true,
  },
  rules: {
    type: Array as PropType<Array<ValidationRule>>,
  },
  finalAddress: {
    type: Object as PropType<Address>,
  },
  deletable: Boolean,
})

defineEmits<{
  delete: []
}>()
</script>
@/types/works
