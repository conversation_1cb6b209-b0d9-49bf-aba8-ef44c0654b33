import type { TerritoryDetails, TerritoryDetailsRequest } from '@/types/territory'
import type { AxiosInstance, AxiosPromise } from 'axios'
import axiosInstance from '.'

class TerritoryDetailsApi {
  public constructor(private axios: AxiosInstance) {}

  public save(id: number, request: TerritoryDetailsRequest): AxiosPromise<TerritoryDetails> {
    return this.axios.put('/territory_details/' + id, request)
  }
}

export const territoryDetailsApi = new TerritoryDetailsApi(axiosInstance)
