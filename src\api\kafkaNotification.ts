import type { Page, Pageable } from '@/types/pagination'
import type { AxiosInstance, AxiosPromise } from 'axios'
import axiosInstance from '.'
import type { RemainingMail } from '@/types/remainingMail'
import type { LocalDateTime } from '@/types/date'

export type KafkaNotificationFilter = Partial<{
  creationDateTimeLower: LocalDateTime
}>

class KafkaNotificationApi {
  public constructor(private axios: AxiosInstance) {}

  public getAll(pageable: Pageable, filter: KafkaNotificationFilter): AxiosPromise<Page<RemainingMail>> {
    return this.axios.get('kafka_notifications', {
      params: { ...pageable, ...filter },
    })
  }
}

export const kafkaNotificationApi = new KafkaNotificationApi(axiosInstance)
