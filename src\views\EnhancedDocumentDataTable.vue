<template>
  <NjDataTable
    :headers="headers"
    :pageable="pageable"
    :page="page"
    :disabled-row="(operationsGroupDocument: EnhancedDocument) => !operationsGroupDocument.active"
    column-fixed
    fixed
    @update:pageable="emit('update:pageable', $event)"
  >
    <template #[`item.document.extension`]="{ item }">
      {{ getFileExtension(item.document.originalFilename) }}
    </template>
    <template #[`item.documentType.name`]="{ item }">
      <VProgressCircular v-show="downloading[item.id]" class="ms-2" indeterminate size="small" />
      <EmediaDocumentIcon :emedia-id="item.document.emediaId" :emedia-errors="item.document.emediaErrors" />
      <VLink v-if="!downloading[item.id]" @click="downloadEnhancedDocument(item)"> {{ item.documentType.name }} </VLink>
    </template>
    <template #[`item.document.creationDateTime`]="{ item }">
      {{ formatHumanReadableLocalDate(item.document.creationDateTime) }}
    </template>
    <template #[`item.actions`]="{ item }">
      <DocumentMenu
        :item="item"
        :on-deactivate="onDeactivate"
        :on-delete="onDelete"
        :on-replace="onReplace"
        :on-edit="onEdit"
      />
    </template>
    <template #[`item.description`]="{ item }">
      <VRow class="flex-nowrap">
        <VCol align-self="center">
          <AutoShrinkText :max-length="50" :text="item.description" />
        </VCol>
      </VRow>
    </template>
  </NjDataTable>
</template>
<script setup lang="ts">
import type { EnhancedDocument } from '@/types/document'
import DocumentMenu from './DocumentMenu.vue'
import { formatHumanReadableLocalDate } from '@/types/date'
import type { PropType } from 'vue'
import type { Page, Pageable } from '@/types/pagination'
import type { DataTableHeader } from '@/components/okta/NjDataTable.type'
import { gtfEmediaApi } from '@/api/external/gtf/gtfEmedia'
import { useSnackbarStore } from '@/stores/snackbar'
import { downloadFile as downloadFileBlob, getFileExtension } from '@/types/file'
import type { AxiosPromise } from 'axios'
import EmediaDocumentIcon from './emediaDocument/EmediaDocumentIcon.vue'

const props = defineProps({
  actions: Boolean,
  onDeactivate: { type: Function as PropType<(item: EnhancedDocument) => any>, required: true },
  onDelete: { type: Function as PropType<(item: EnhancedDocument) => any>, required: true },
  onReplace: { type: Function as PropType<(item: EnhancedDocument) => any>, required: true },
  onEdit: { type: Function as PropType<(item: EnhancedDocument) => any>, required: true },
  pageable: Object as PropType<Pageable>,
  updatePageable: Function as PropType<(options: Pageable) => any>,
  page: Object as PropType<Page<EnhancedDocument>>,
  type: {
    type: String as PropType<'operation' | 'operationsGroup' | 'emmyFolder' | 'controlOrderBatch'>,
    required: true,
  },
})

const emit = defineEmits<{
  'update:pageable': [Pageable]
}>()

const headers = computed((): DataTableHeader[] => {
  const result = [
    {
      title: 'Type',
      value: 'documentType.name',
      width: '200px',
    },
    {
      title: "Date d'import",
      value: 'document.creationDateTime',
      width: '160px',
    },
    {
      title: 'Doc.',
      value: 'document.extension',
      width: '64px',
      sortable: false,
    },
    {
      title: 'Description',
      value: 'description',
    },
  ]
  if (props.actions) {
    result.push({
      title: '',
      value: 'actions',
      sortable: false,
      width: '64px',
    })
  }
  return result
})

const downloading: Ref<Record<number, boolean>> = ref({})
const snackbarStore = useSnackbarStore()
const downloadEnhancedDocument = (document: EnhancedDocument) => {
  if (!downloading.value[document.id]) {
    downloading.value[document.id] = true
    const downloadDocumentName =
      document.documentType.name + '-' + document.id + '.' + getFileExtension(document.document.originalFilename)
    if (document.document.emediaId) {
      return gtfEmediaApi
        .downloadDocument(document.document.id)
        .then((response) => {
          downloadFileBlob(downloadDocumentName, response.data)
          snackbarStore.setSuccess('Le téléchargement a réussi')
        })
        .catch(() => snackbarStore.setError('Le téléchargement du fichier a échoué.'))
        .finally(() => {
          delete downloading.value[document.id]
        })
    } else {
      let promise: AxiosPromise<Blob>
      if (props.type === 'operation') {
        promise = operationDocumentApi.download(document.id)
      } else if (props.type === 'operationsGroup') {
        promise = operationsGroupDocumentApi.download(document.id)
      } else if (props.type === 'emmyFolder') {
        promise = emmyFolderDocumentApi.download(document.id)
      } else {
        promise = controlOrderBatchDocumentApi.download(document.id)
      }

      promise
        .then((response) => {
          downloadFile(downloadDocumentName ?? document.document.originalFilename, response.data)
          snackbarStore.setSuccess('Le téléchargement a réussi')

          delete downloading.value[document.id]
        })
        .catch(() =>
          snackbarStore.setError(
            'Le téléchargement du fichier a échoué. Aviez-vous envoyé ce fichier sur boCEE ? Si oui, veuillez nous contacter.'
          )
        )
    }
  }
}
</script>
