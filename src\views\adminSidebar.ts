import { useSidebarStore } from '@/stores/sidebar'

export default function useAdminSidebar() {
  useSidebarStore().setItems([
    {
      icon: 'mdi-format-list-bulleted',
      title: 'Liste des valeurs',
      routeName: 'ValueManagerView',
    },
    {
      icon: 'mdi-calculator-variant-outline',
      title: 'Outil de calcul',
      routeName: 'OutilCalculView',
    },
    {
      icon: 'mdi-file-eye-outline',
      title: 'Paramètres EMMY',
      routeName: 'EmmyParameterAllView',
    },
    {
      icon: 'mdi-account-box-multiple-outline',
      title: 'Gérer les Utilisateurs',
      routeName: 'UserAllView',
    },
    {
      icon: 'mdi-ticket-account',
      title: 'Gérer les inscriptiions',
      routeName: 'UserProfileRequestAllView',
    },
    {
      icon: 'mdi-file-document-edit-outline',
      title: 'Modèle de document',
      routeName: 'ConventionAllView',
    },
    {
      icon: 'mdi-file-excel',
      title: 'Arr<PERSON><PERSON> Contrôle',
      routeName: 'ControlOrderAdministrationView',
    },
    {
      icon: 'mdi-mailbox',
      title: 'Mails à envoyer',
      routeName: 'RemainingMailAllView',
    },
    {
      icon: 'mdi-history',
      title: 'Historique des exports',
      routeName: 'DownloadHistoryAllView',
    },
    {
      icon: 'mdi-console',
      title: 'Logs Applicatif',
      routeName: 'LoggingEventsAllView',
    },
  ])
}
