<template>
  <NjPage :error-message="items.error" :loading="items.loading" title="Explorateur de fichiers">
    <template #body>
      <VRow class="flex-column content-layout">
        <VCol class="content-layout__header">
          <VRow>
            <VCol>
              <NjDatePicker
                :model-value="pageFilter.from"
                @update:model-value="updateFilterByFieldname('from', $event)"
              />
            </VCol>
            <VCol>
              <NjDatePicker :model-value="pageFilter.to" @update:model-value="updateFilterByFieldname('to', $event)" />
            </VCol>
          </VRow>
        </VCol>
        <VCol class="content-layout__main">
          <NjDataTable
            :headers="headers"
            :pageable="pageable"
            :page="data.value!"
            :disabled-row="(it) => it.length > 0"
            fixed
            @update:pageable="updatePageable"
          ></NjDataTable>
        </VCol>
      </VRow>
    </template>
  </NjPage>
</template>

<script setup lang="ts">
import axiosInstance from '@/api'
import type { DataTableHeader } from '@/components/okta/NjDataTable.type'
import type { LocalDate } from '@/types/date'

defineProps<{
  id: string
}>()

const items =
  ref(
    emptyValue<
      {
        filename: string
        type: 'FILE' | 'FOLDER'
      }[]
    >()
  )
const headers: DataTableHeader[] = [
  {
    value: 'document.id',
    title: 'ID',
  },
  {
    value: 'document.originalFilename',
    title: 'Nom de Fichier',
  },
  {
    value: 'length',
    title: 'Taille',
  },
  {
    value: 'error',
    title: 'Erreur',
  },
]
const { data, pageable, updatePageable, pageFilter, updateFilterByFieldname } = usePaginationInQuery<
  {
    document: Document
    length: number
    error?: string
  },
  { from: LocalDate; to: LocalDate }
>(
  (filter, pageable) =>
    axiosInstance.get('explorer/check-empty-documents', {
      params: {
        ...pageable,
        ...filter,
      },
    }),
  {
    saveFiltersName: 'BoostBonusSheetAllView',
  }
)
</script>
