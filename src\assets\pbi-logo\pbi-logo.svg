<svg width="96" height="96" viewBox="0 0 96 96" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0)">
<mask id="mask0" mask-type="alpha" maskUnits="userSpaceOnUse" x="12" y="0" width="72" height="96">
<path d="M52 4C52 1.79086 53.7909 0 56 0H80C82.2091 0 84 1.79086 84 4V92C84 94.2091 82.2091 96 80 96H16C13.7909 96 12 94.2091 12 92V52C12 49.7909 13.7909 48 16 48H32.0001V28C32.0001 25.7909 33.791 24 36.0001 24H52V4Z" fill="white"/>
</mask>
<g mask="url(#mask0)">
<path d="M83.9998 0L83.9998 96H51.9998L51.9998 0H83.9998Z" fill="url(#paint0_linear)"/>
<g filter="url(#filter0_f)">
<path d="M64 28.4V96.4H32V24.4H60C62.2091 24.4 64 26.1909 64 28.4Z" fill="black" fill-opacity="0.2"/>
</g>
<g filter="url(#filter1_f)">
<path d="M64 30V98H32V26H60C62.2091 26 64 27.7909 64 30Z" fill="black" fill-opacity="0.18"/>
</g>
<path d="M64 28V96H32V24H60C62.2091 24 64 25.7909 64 28Z" fill="url(#paint1_linear)"/>
<path d="M11.9998 48V96H43.9998V52C43.9998 49.7909 42.2089 48 39.9998 48H11.9998Z" fill="url(#paint2_linear)"/>
</g>
</g>
<defs>
<filter id="filter0_f" x="31.2" y="23.6" width="33.6" height="73.6" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="0.4" result="effect1_foregroundBlur"/>
</filter>
<filter id="filter1_f" x="24" y="18" width="48" height="88" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="4" result="effect1_foregroundBlur"/>
</filter>
<linearGradient id="paint0_linear" x1="46.6664" y1="-8.97795e-07" x2="88.865" y2="89.4466" gradientUnits="userSpaceOnUse">
<stop stop-color="#E6AD10"/>
<stop offset="1" stop-color="#C87E0E"/>
</linearGradient>
<linearGradient id="paint1_linear" x1="31.9974" y1="24" x2="67.4859" y2="92.8262" gradientUnits="userSpaceOnUse">
<stop stop-color="#F6D751"/>
<stop offset="1" stop-color="#E6AD10"/>
</linearGradient>
<linearGradient id="paint2_linear" x1="11.9972" y1="48" x2="31.608" y2="94.4775" gradientUnits="userSpaceOnUse">
<stop stop-color="#F9E589"/>
<stop offset="1" stop-color="#F6D751"/>
</linearGradient>
<clipPath id="clip0">
<rect width="96" height="96" fill="white"/>
</clipPath>
</defs>
</svg>
    