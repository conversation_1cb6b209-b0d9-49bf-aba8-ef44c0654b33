import type { LocalDateTime } from '@/types/date'
import type { DocumentType } from '@/types/documentType'

export interface DocumentDataTableItem {
  id?: number
  emediaId?: string
  emediaErrors?: string
  documentType: DocumentType
  requiredStepId?: number
  creationDateTime?: LocalDateTime
  format?: string
  description?: string
  fillableTemplateId?: number
  templateId?: number
  active?: boolean
}
