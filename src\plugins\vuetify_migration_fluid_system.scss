.v-field__outline__start,
.v-field__outline__end {
  border-radius: 0px !important;
}

body {
  font-family: 'Lato';
}

.v-input__prepend,
.v-input__append {
  display: flex;
  padding-top: 0 !important;
}

.v-tabs .v-btn {
  font-weight: initial;
  text-transform: initial;

  &--size-default {
    font-size: initial;
  }
}

.v-btn {
  letter-spacing: normal !important;
  text-transform: initial !important;
}

.v-main {
  background-color: #f5f5f5;
}

.v-card-title {
  font-size: 1.5rem !important;
}
.v-card-actions .v-btn ~ .v-btn {
  margin-inline-start: 0 !important;
}
.v-card-actions {
  column-gap: 16px;
  row-gap: 16px;
  padding: 16px;
}

.v-switch > .v-input__control > .v-selection-control {
  flex-direction: row-reverse;
  .v-label {
    flex-grow: inherit;
  }
}

.v-switch .v-label,
.v-radio-group .v-label,
.v-checkbox .v-label {
  opacity: 1;
}
.v-switch .v-label {
  padding-inline-start: 0 !important;
}

// TODO MVI: j'aime pas cette façon de faire, je vais voir déjà où est le soucis, et je le corrigerais
// .mdi-checkbox-blank-outline {
//   color: #768C9B;
// }

// .v-text-field {
//   color: #768C9B;
//   background: white;
//   border: 1px solid #778C9B;
// }

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

main {
  height: 100vh;
  overflow: hidden;
}

:root {
  --nj-color-border-brand-bold: #007acd;
  --nj-color-background-primary-selected: #f1f5fd;
}

.v-radiogroup--align-end.v-radio-group .v-selection-control-group {
  justify-content: end;
}

.v-alert.text-error.v-alert--variant-outlined {
  background-color: #ffeae5;
  border: solid 1px #ff8979;

  .v-alert__content {
    color: black;
  }
}
.v-alert.text-warning.v-alert--variant-outlined {
  background-color: #ffe9da;

  .v-alert__content {
    color: black;
  }
}
.v-alert.text-info.v-alert--variant-outlined {
  background-color: #e7eefc;

  .v-alert__content {
    color: black;
  }
}

.v-field {
  &--one-line-chip {
    .v-field__input {
      display: flex;
      flex-wrap: nowrap;
    }
    input {
      flex-grow: 0;
    }
  }
}