import type { AxiosInstance } from 'axios'
import axiosInstance from '.'
import type { PrecariousnessCase2FrameDValue } from '@/types/swornStatementTemplateFrame'

class SwornStatementApi {
  public constructor(private axios: AxiosInstance) {}

  public create(operationId: number, precariousnessCase2FrameD: PrecariousnessCase2FrameDValue | null) {
    return this.axios.post(
      '/sworn_statements',
      {
        operationId: operationId,
        precariousnessCase2FrameD,
      },
      {
        responseType: 'blob',
      }
    )
  }
}

export const swornStatementApi = new SwornStatementApi(axiosInstance)
