import type { Operation } from './operation'

export type OperationDuplicateAccumulationDto = Pick<
  Operation,
  | 'id'
  | 'chronoCode'
  | 'stepId'
  | 'status'
  | 'property'
  | 'finalAddress'
  | 'beneficiary'
  | 'entity'
  | 'classicCumac'
  | 'precariousnessCumac'
  | 'actualEndWorksDate'
  | 'valuationType'
  | 'commercialStatus'
  | 'applicantUser'
  | 'reservedClassicCumac'
  | 'reservedPrecariousnessCumac'
  | 'reservedClassicValuationValue'
  | 'reservedPrecariousnessValuationValue'
  | 'standardizedOperationSheet'
>
