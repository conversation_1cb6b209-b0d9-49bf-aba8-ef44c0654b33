<template>
  <NjPage title="Gestion des inscriptions utilisateurs" expend-body v-bind="$attrs">
    <template #sub-header>
      <VRow>
        <VCol>
          <NjSwitch
            label="Voir uniquement ceux en attente de réponse"
            :model-value="pageFilter.hasResponse"
            inline
            :true-value="false"
            :false-value="null"
            @update:model-value="updateFilterByFieldname('hasResponse', $event)"
          />
        </VCol>
        <VCol>
          <RemoteAutoComplete
            label="Territoires"
            :model-value="pageFilter.territoryIds"
            :query-for-one="(v) => territoryApi.findOne(v)"
            :query-for-ones="(v) => territoryApi.findAll({ ids: v }, { size: 1000 })"
            :query-for-all="(v, p) => territoryApi.findAll({ search: v }, p)"
            :item-title="formatTerritory"
            item-value="id"
            infinite-scroll
            clearable
            :page-size="50"
            multiple
            chips
            closable-chips
            @update:model-value="(event) => updateFilterByFieldname('territoryIds', event)"
          />
        </VCol>
      </VRow>
    </template>

    <template #body>
      <NjDataTable
        :headers="headers"
        :pageable="pageable"
        :page="data.value"
        fixed
        class="w-100"
        @update:pageable="updatePageable"
      >
        <template #[`item.actions`]="{ item }">
          <NjIconBtn
            v-if="item.response == null"
            icon="mdi-keyboard-return"
            size="small"
            color="primary"
            @click.stop="respondRequest(item)"
          />
        </template>
        <template #[`item.response`]="{ item }">
          <div>
            <VIcon v-if="item.response == null" icon="mdi-help-circle" />
            <VIcon v-else-if="item.response === false" icon="mdi-close-circle" color="error" />
            <VIcon v-else-if="item.response === true" icon="mdi-check-circle" color="success" />
          </div>
        </template>
      </NjDataTable>

      <VDialog v-model="responseDialog.active" max-width="640px">
        <VCard>
          <VCardTitle
            >Réponse à la demande de {{ responseDialog.userProfileRequest.user.firstName }}
            {{ responseDialog.userProfileRequest.user.lastName }}</VCardTitle
          >
          <VCardText>
            <VForm ref="formRef">
              <VRow class="flex-column">
                <VCol>
                  L'utilisateur a fait la demande suivante :
                  <VSheet border class="pa-2">
                    <NjDisplayValue
                      label="Profil"
                      :value="
                        responseDialog.userProfileRequest.userRole === 'AGENCE' ? 'Simulation' : 'Correspondant CEE'
                      "
                    />
                    <NjDisplayValue label="Fonction" :value="responseDialog.userProfileRequest.function" />
                    <NjDisplayValue label="Organisations" :value="responseDialog.userProfileRequest.entity.id" />
                    <NjDisplayValue label="Commentaire" :value="responseDialog.userProfileRequest.requestComment" />
                  </VSheet>
                </VCol>
                <VCol>
                  <VInput
                    label="Votre réponse"
                    :rules="[requiredRuleGenerator('Vous devez sélectionner votre réponse')]"
                    :model-value="responseRequest.response"
                    hide-details="auto"
                  >
                    <VBtnToggle v-model="responseRequest.response" class="rounded-0">
                      <NjBtn color="success" :value="true"> <VIcon start>mdi-check</VIcon> Accepter </NjBtn>

                      <NjBtn color="error" :value="false"> <VIcon start>mdi-close</VIcon> Refuser </NjBtn>
                    </VBtnToggle>
                  </VInput>
                </VCol>
                <VCol>
                  <VTextarea
                    v-model="responseRequest.comment"
                    label="Commentaire de votre réponse"
                    counter="500"
                    rows="4"
                  />
                </VCol>
              </VRow>
            </VForm>
          </VCardText>
          <VCardActions>
            <VSpacer />
            <NjBtn @click="respond">Répondre</NjBtn>
          </VCardActions>
        </VCard>
      </VDialog>
    </template>
  </NjPage>
</template>
<script lang="ts" setup>
import { territoryApi } from '@/api/territory'
import type { ResponseUserProfileRequestRequest, UserPorfileRequestFilter } from '@/api/userProfileRequest'
import NjIconBtn from '@/components/NjIconBtn.vue'
import NjPage from '@/components/NjPage.vue'
import type { DataTableHeader } from '@/components/okta/NjDataTable.type'
import NjDataTable from '@/components/okta/NjDataTable.vue'
import { useSnackbarStore } from '@/stores/snackbar'
import { requiredRuleGenerator } from '@/types/rule'
import { formatTerritory } from '@/types/territory'
import type { UserProfileRequest } from '@/types/userProfileRequest'
import { VForm } from 'vuetify/components/VForm'

const responseDialog = ref({
  active: false,
  userProfileRequest: {} as UserProfileRequest,
})

const snackbarStore = useSnackbarStore()
const { data, pageable, updatePageable, reload, pageFilter, updateFilterByFieldname } = usePaginationInQuery<
  UserProfileRequest,
  UserPorfileRequestFilter
>((filter, pageable) => userProfileRequestApi.getAll(pageable, filter), {
  defaultPageablePartial: {
    page: 0,
    size: 20,
    sort: ['creationDateTime'],
  },
  saveFiltersName: 'UserProfileRequestAllView',
  queryToFilterMapper(query) {
    return {
      ...query,
      territoryIds: mapQueryToTable(query.territoryIds, true),
    }
  },
})

const headers: DataTableHeader<UserProfileRequest>[] = [
  {
    title: 'Utilisateur',
    value: 'user',
    formater(item) {
      return `${item.user.firstName} ${item.user.lastName} (${item.user.gid})`
    },
  },
  {
    title: 'Fonction',
    value: 'function',
  },
  {
    title: 'Organisation',
    value: 'entity.id',
  },
  {
    title: 'Status',
    value: 'response',
  },
  {
    title: 'Commentaire Réponse',
    value: 'responseComment',
  },
  {
    title: 'Demandé le',
    value: 'creationDateTime',
    formater(item) {
      return formatHumanReadableLocalDateTime(item.creationDateTime)
    },
  },
  {
    title: 'Actions',
    value: 'actions',
    sortable: false,
  },
]

const responseRequest = ref<Partial<ResponseUserProfileRequestRequest>>({})

const respondRequest = (email: UserProfileRequest) => {
  responseRequest.value = {}
  responseDialog.value = {
    active: true,
    userProfileRequest: email,
  }
  // const newWindow = window.open(
  //   'about:blank',
  //   'Aperçu Mail',
  //   'resizable=no, toolbar=no, scrollbars=no, menubar=no, status=no, directories=no,location=no'
  // )
  // newWindow?.document.write(DOMPurify.sanitize(email.body))
}

const formRef = ref<typeof VForm | null>(null)
const respondingProfileRequest = ref(emptyValue<UserProfileRequest>())

const respond = async () => {
  if ((await formRef.value!.validate()).valid) {
    handleAxiosPromise(
      respondingProfileRequest,
      userProfileRequestApi.respond(responseDialog.value.userProfileRequest.id, {
        response: responseRequest.value.response!,
        comment: responseRequest.value.comment ?? '',
      }),
      {
        afterSuccess() {
          responseDialog.value.active = false
          snackbarStore.setSuccess('Votre réponse a bien été prise en compte')
          reload()
        },
        async afterError(e) {
          snackbarStore.setError(await handleAxiosException(e))
        },
      }
    )
  }
}
</script>
