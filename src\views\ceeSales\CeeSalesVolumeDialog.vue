<template>
  <div>
    <CardDialog
      :model-value="modelValue"
      title="Vente d'un volume"
      height="100%"
      width="80%"
      @update:model-value="emit('update:model-value', false)"
    >
      <VRow class="flex-column h-100">
        <VCol class="flex-grow-0">
          <VCard
            class="pa-2"
            variant="outlined"
            rounded="1"
            style="border: 1px solid #007acd; background-color: #e7eefc"
          >
            <VCardText class="py-2" style="font-size: inherit; letter-spacing: inherit">
              <VRow align="center">
                <VCol class="flex-grow-0">
                  <VIcon icon="mdi-information" size="x-large" color="primary" />
                </VCol>
                <VCol>
                  <span v-if="selection.length === 0">
                    Vous n'avez pas sélectionné d'opérations. Cela signifie que la vente de kWhc choisira
                    automatiquement les opérations à partir desquelles effectuer une vente, en commençant par la plus
                    ancienne disponible.
                  </span>
                  <span v-else>
                    La vente de kWhc s'effectuera en commençant par l'opération la plus ancienne que vous ayez
                    sélectionnée jusqu'à atteindre le nombre de kWhc souhaité. Veillez à vérifier que votre sélection
                    comporte assez de kWhc disponibles.
                  </span>
                </VCol>
              </VRow>
            </VCardText>
          </VCard>
        </VCol>
        <VCol>
          <NjDataTable
            v-model:selections="selection"
            :pageable="localPageable"
            :page="data.value!"
            :headers="headers"
            :loading="data.loading"
            fixed
            checkboxes
            multi-selection
            @update:pageable="updatePageable"
          />
        </VCol>
        <VCol class="flex-grow-0">
          <VForm ref="formRef">
            <VRow class="align-center">
              <VCol>
                <VTextField
                  v-model="classicCumacToSell"
                  label="kWhc Class. à Vendre"
                  :rules="[
                    precariousnessCumacToSell ? positiveOrNullNumericRuleGenerator() : positiveNumericRuleGenerator(),
                  ]"
                />
              </VCol>
              <VCol>
                <VTextField
                  v-model="precariousnessCumacToSell"
                  label="kWhc Préca. à Vendre"
                  :rules="[classicCumacToSell ? positiveOrNullNumericRuleGenerator() : positiveNumericRuleGenerator()]"
                />
              </VCol>
            </VRow>
          </VForm>
        </VCol>
      </VRow>
      <template #actions>
        <NjBtn variant="outlined" @click="emit('update:model-value', false)">Annuler</NjBtn>
        <NjBtn :loading="loading" @click="getSalePreview">Valider</NjBtn>
      </template>
    </CardDialog>
    <CardDialog
      v-model="displayPreview"
      title="Apercu des ventes"
      :width="classicCumacToSell && precariousnessCumacToSell ? '70%' : '50%'"
    >
      <VCard
        class="pa-2 mb-2"
        variant="outlined"
        rounded="1"
        style="border: 1px solid #007acd; background-color: #e7eefc"
      >
        <VCardText class="py-2" style="font-size: inherit; letter-spacing: inherit">
          <VRow align="center">
            <VCol class="flex-grow-0">
              <VIcon icon="mdi-information" size="x-large" color="primary" />
            </VCol>
            <VCol> Les ventes suivantes vont être créées, voulez-vous continuer? </VCol>
          </VRow>
        </VCardText>
      </VCard>
      <VRow class="flex-column">
        <VCol>
          <VRow class="align-center">
            <VCol class="value__label text-center">Nom Opération</VCol>
            <VCol v-if="classicCumacToSell" class="value__label text-center"> kWhc Class. Dispo. </VCol>
            <VCol v-if="precariousnessCumacToSell" class="value__label text-center">kWhc Préca. Dispo.</VCol>
            <VCol v-if="classicCumacToSell" class="value__label text-center"> kWhc Class. Vendu </VCol>
            <VCol v-if="precariousnessCumacToSell" class="value__label text-center"> kWhc Préca. Vendu </VCol>
          </VRow>
        </VCol>
        <VCol v-for="sale in saleRequests" :key="sale.operation.id">
          <VRow class="align-center">
            <VCol class="text-center">{{ sale.operation.operationName }}</VCol>
            <VCol v-if="classicCumacToSell" class="value__value text-center">
              {{ formatNumber(sale.operation.availableClassicCumac) }}
            </VCol>
            <VCol v-if="precariousnessCumacToSell" class="value__value text-center">
              {{ formatNumber(sale.operation.availablePrecariousnessCumac) }}
            </VCol>
            <VCol v-if="classicCumacToSell" class="value__value text-center">
              {{ formatNumber(sale.soldClassicCumac) }}
            </VCol>
            <VCol v-if="precariousnessCumacToSell" class="value__value text-center">
              {{ formatNumber(sale.soldPrecariousnessCumac) }}
            </VCol>
          </VRow>
        </VCol>
      </VRow>
      <template #actions>
        <NjBtn
          variant="outlined"
          @click="
            () => {
              displayPreview = false
              emit('update:model-value', true)
            }
          "
        >
          Annuler
        </NjBtn>
        <NjBtn @click="sellCee">Valider</NjBtn>
      </template>
    </CardDialog>
  </div>
</template>
<script lang="ts" setup>
import type { DataTableHeader } from '@/components/okta/NjDataTable.type'
import { useSnackbarStore } from '@/stores/snackbar'
import {
  mapToOperationCeeSaleRequestFromDto,
  type OperationCeeSaleRequest,
  type OperationCeeSaleRequestDto,
} from '@/types/ceeSale'
import type { LocalDate } from '@/types/date'
import { positiveOrNullNumericRuleGenerator, positiveNumericRuleGenerator } from '@/types/rule'
import { formatNumber } from '@/types/format'
import { VForm } from 'vuetify/components/VForm'
import type { OperationFilter } from '@/api/operation'
import type { Operation } from '@/types/operation'
import type { PropType } from 'vue'
import type { Pageable } from '@/types/pagination'

const props = defineProps({
  modelValue: Boolean,
  form: Object as PropType<{
    saleDate: LocalDate
    buyer: string | undefined
    comment: string
    unitSellingPrice: number | undefined
  }>,
  selection: Array as PropType<Operation[]>,
  filter: {
    type: Object as PropType<OperationFilter>,
    required: true,
  },
  pageable: {
    type: Object as PropType<Pageable>,
    required: true,
  },
})

const emit = defineEmits<{
  'update:model-value': [value: boolean]
  'update:pageable': [pageable: Pageable]
  'update:selection': [selection: Operation[]]
  sold: [void]
}>()

const snackbarStore = useSnackbarStore()

// const filter = ref<OperationFilter>({
//   operationStatuses: ['DONE'],
//   available: true,
//   eligibleForSale: true,
// })

const selection = computed({
  get: () => props.selection ?? [],
  set: (it) => emit('update:selection', it),
})

const {
  data,
  updatePageable,
  reload,
  pageable: localPageable,
} = usePagination<Operation, OperationFilter>(
  (filter, pageable) => {
    emit('update:pageable', pageable)
    return operationApi.findAll(props.filter!, { ...props.pageable, sort: ['lastValidatedStepDateTime,ASC'] })
  },
  { ...props.filter },
  { ...props.pageable, sort: ['lastValidatedStepDateTime,ASC'] }
)

const headers: DataTableHeader<Operation>[] = [
  {
    title: 'Date 100',
    value: 'lastValidatedStepDateTime',
    formater: (_, value) => formatHumanReadableLocalDate(value),
    sortable: false,
  },
  {
    title: 'Chrono',
    value: 'chronoCode',
    sortable: false,
  },
  {
    title: 'Nom de dossier',
    value: 'operationName',
    sortable: false,
  },
  {
    title: 'kWhc dem. Class',
    value: 'classicCumac',
    formater: (_, value) => formatNumber(value),
    cellClass: 'text-right',
    sortable: false,
  },
  {
    title: 'kWhc dem. Préc',
    value: 'precariousnessCumac',
    formater: (_, value) => formatNumber(value),
    cellClass: 'text-right',
    sortable: false,
  },
  {
    title: 'kWhc dispo. Class',
    value: 'availableClassicCumac',
    formater: (_, value) => formatNumber(value),
    cellClass: 'text-right',
    sortable: false,
  },
  {
    title: 'kWhc dispo. Préc',
    value: 'availablePrecariousnessCumac',
    formater: (_, value) => formatNumber(value),
    cellClass: 'text-right',
    sortable: false,
  },
  {
    title: 'kWhc dispo. Total',
    value: 'cumacAvailable',
    formater: (item) => formatNumber(item.availableClassicCumac + item.availablePrecariousnessCumac),
    cellClass: 'text-right',
    sortable: false,
  },
  {
    title: 'Étape',
    value: 'pas convaincu',
    sortable: false,
  },
  {
    title: 'Processus',
    value: 'pas très utile',
    sortable: false,
  },
  {
    title: 'Période',
    value: 'period.name',
    sortable: false,
  },
  {
    title: 'Code EMMY',
    value: 'emmyFolder.emmyCode',
    sortable: false,
  },
  {
    title: 'Désignation EMMY',
    value: 'na',
    sortable: false,
  },
  {
    title: 'kWhc vendus Class.',
    value: 'soldClassicCumac',
    formater: (_, value) => formatNumber(value),
    cellClass: 'text-right',
    sortable: false,
  },
  {
    title: 'Mnt vendus Class.',
    value: 'na',
    sortable: false,
  },
  {
    title: 'kWhc vendus Préc.',
    value: 'soldPrecariousnessCumac',
    formater: (_, value) => formatNumber(value),
    cellClass: 'text-right',
    sortable: false,
  },
  {
    title: 'Mnt vendus Préc.',
    value: 'na',
    sortable: false,
  },
  {
    title: 'Nombre ventes',
    value: 'na',
    sortable: false,
  },
  {
    title: '1ère vente',
    value: 'na',
    sortable: false,
  },
  {
    title: 'Dernière vente',
    value: 'na',
    sortable: false,
  },
  {
    title: 'Envoi PNCEE',
    value: 'emmyFolder.pnceeSubmissionDate',
    formater: (_, value) => formatHumanReadableLocalDate(value),
    sortable: false,
  },
  {
    title: 'Délivrance PNCEE',
    value: 'emmyFolder.pnceeIssuedDate',
    formater: (_, value) => formatHumanReadableLocalDate(value),
    sortable: false,
  },
  {
    title: 'Nom agence',
    value: 'entity.name',
    formater: (item) => `${item.entity.name} (${item.entity.id})`,
    sortable: false,
  },
  {
    title: 'Valo Class.',
    value: 'classicValuationValue',
    formater: (_, value) => formatPriceNumber(value) + '/MWhc',
    cellClass: 'text-right',
    sortable: false,
  },
  {
    title: 'Valo Préc.',
    value: 'precariousnessValuationValue',
    formater: (_, value) => formatPriceNumber(value) + '/MWhc',
    cellClass: 'text-right',
    sortable: false,
  },
  {
    title: 'PU Vente',
    value: 'na',
    sortable: false,
  },
  {
    title: 'Dif. Class.',
    value: 'na',
    sortable: false,
  },
  {
    title: 'Dif. Préc.',
    value: 'na',
    sortable: false,
  },
]

const classicCumacToSell = ref(0)
const precariousnessCumacToSell = ref(0)
const formRef = ref<typeof VForm | null>()
const saleRequests = ref<OperationCeeSaleRequestDto[]>([])
const displayPreview = ref(false)
const loading = ref(false)

const getSalePreview = async () => {
  const validation = await formRef.value!.validate()
  if (validation.valid) {
    loading.value = true
    ceeSaleApi
      .getPreview({
        operationIds: selection.value.map((stock) => stock.id),
        soldClassicCumac: classicCumacToSell.value,
        soldPrecariousnessCumac: precariousnessCumacToSell.value,
        saleDate: props.form!.saleDate,
        buyer: props.form!.buyer!,
        comment: props.form!.comment,
        unitSellingPrice: props.form!.unitSellingPrice!,
      })
      .then((response) => {
        saleRequests.value = response.data
        displayPreview.value = true
        emit('update:model-value', false)
      })
      .catch(async (error) =>
        snackbarStore.setError(
          await handleAxiosException(error, undefined, {
            defaultMessage: 'Une erreur est survenue lors de la création de la vente',
          })
        )
      )
      .finally(() => (loading.value = false))
  }
}

const sellCee = () => {
  const requests: OperationCeeSaleRequest[] = []
  saleRequests.value.forEach((request) => requests.push(mapToOperationCeeSaleRequestFromDto(request)))
  ceeSaleApi
    .createFiltered(requests)
    .then(() => {
      snackbarStore.setSuccess(
        `${requests.length === 1 ? 'La vente a été créée' : 'Les ventes ont été créées'} avec succès`
      )
      displayPreview.value = false
      emit('sold')
    })
    .catch(async (err) =>
      snackbarStore.setError(
        await handleAxiosException(err, undefined, {
          defaultMessage: "Une erreur est survenue lors de la création d'une vente",
        })
      )
    )
}

watch(() => props.filter, reload, { deep: true })
</script>
