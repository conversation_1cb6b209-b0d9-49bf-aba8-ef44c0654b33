<template>
  <NjPage title expend-body>
    <template #subtitle>
      <VRow>
        <VCol cols="4">
          <SearchInput @update:model-value="updateSearch" />
        </VCol>
      </VRow>
    </template>
    <template #header-actions>
      <NjBtn @click="createPeriod">Ajouter une période</NjBtn>
    </template>
    <template #body>
      <VRow>
        <VCol>
          <NjDataTable
            :headers="headers"
            :pageable="pageable"
            :page="data.value"
            :disabled-row="disabledRow"
            fixed
            @update:pageable="updatePageable"
          >
            <template #[`item.changeStatus`]="{ item }">
              <NjIconBtn icon="mdi-pencil" color="primary" @click="editPeriod(item)" />
            </template>
          </NjDataTable>
        </VCol>
      </VRow>

      <CardDialog
        v-model="editPeriodDialog"
        :title="newPeriod.id ? 'Modification d\'une période' : 'Ajout d\'une période'"
        width="60%"
      >
        <VForm ref="formRef">
          <VRow class="my-n2">
            <VCol cols="3">
              <VTextField v-model="newPeriod.name" label="Nom" :rules="[requiredRule]" />
            </VCol>
            <VCol cols="3">
              <NjDatePicker v-model="newPeriod.startDate" label="Date de début" :rules="[requiredRule]" />
            </VCol>
            <VCol cols="3">
              <NjDatePicker v-model="newPeriod.endDate" label="Date de fin" :rules="[requiredRule]" />
            </VCol>
            <VCol cols="3">
              <VCheckbox v-model="newPeriod.active" label="Actif" />
            </VCol>
          </VRow>
        </VForm>
        <template #actions>
          <NjBtn :loading="saving.loading" @click="save">Enregistrer</NjBtn>
        </template>
      </CardDialog>
    </template>
  </NjPage>
</template>
<script lang="ts" setup>
import { periodApi } from '@/api/period'
import type { DataTableHeader } from '@/components/okta/NjDataTable.type'
import { useSnackbarStore } from '@/stores/snackbar'
import { makeEmptyPeriod, type Period } from '@/types/period'
import { requiredRule } from '@/types/rule'
import { VForm, VRow } from 'vuetify/components'

const snackbarStore = useSnackbarStore()

const { data, pageable, updatePageable, updateFilter, reload } = usePaginationInQuery(
  (filter, pageable) => periodApi.getAll(pageable, filter),
  {
    defaultPageablePartial: {
      sort: ['startDate'],
    },
    saveFiltersName: 'PeriodAllView',
  }
)

const newPeriod = ref(makeEmptyPeriod())
const editPeriodDialog = ref(false)
const formRef = ref<VForm | null>(null)

const headers: DataTableHeader[] = [
  {
    title: 'Nom',
    value: 'name',
  },
  {
    title: 'Date de début',
    value: 'startDate',
    formater: (_, value) => formatHumanReadableLocalDate(value),
  },
  {
    title: 'Date de fin',
    value: 'endDate',
    formater: (_, value) => formatHumanReadableLocalDate(value),
  },
  {
    title: ' ',
    value: 'changeStatus',
    sortable: false,
  },
]

const saving = ref(emptyValue<Period>())
const save = async () => {
  if ((await formRef.value!.validate()).valid) {
    handleAxiosPromise(
      saving,
      newPeriod.value.id ? periodApi.update(newPeriod.value) : periodApi.create(newPeriod.value),
      {
        afterSuccess: () => {
          snackbarStore.setSuccess(
            newPeriod.value.id ? 'La période a bien été modifiée' : 'La période a bien été créée'
          )
          editPeriodDialog.value = false
          reload()
        },
        afterError: () => {
          snackbarStore.setError(saving.value.error ?? 'Une erreur est survenue lors de la création de la période')
        },
      }
    )
  }
}

const disabledRow = (period: Period) => !period.active

const createPeriod = () => {
  newPeriod.value = makeEmptyPeriod()
  editPeriodDialog.value = true
}

const updateSearch = (value: string) => {
  updateFilter({
    search: value,
  })
}

const editPeriod = (period: Period) => {
  newPeriod.value = { ...period }
  editPeriodDialog.value = true
}
</script>
