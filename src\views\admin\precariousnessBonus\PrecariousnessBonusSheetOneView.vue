<template>
  <NjPage
    :title="
      id
        ? 'Edition de ' + (precariousnessBonusSheet.value ? precariousnessBonusSheet.value.name : '---')
        : 'Création de Bonification Précarité'
    "
    :error-message="precariousnessBonusSheet.error || saving.error"
    :loading="precariousnessBonusSheet.loading || saving.loading"
    :can-go-back="{ name: 'PrecariousnessBonusSheetAllView' }"
    v-bind="$attrs"
  >
    <template #after-title>
      <VIcon v-show="certified" color="#28B750" icon="mdi-shield-check-outline" />
    </template>
    <template #header-actions>
      <VRow class="flex-nowrap" dense>
        <VCol>
          <NjBtn variant="outlined" color="error" :disabled="saving.loading"> Annuler </NjBtn>
        </VCol>
        <VCol>
          <NjBtn :loading="saving.loading" @click="save"> Enregistrer </NjBtn>
        </VCol>
      </VRow>
    </template>

    <template #body>
      <VCard>
        <VForm ref="formRef">
          <NjExpansionPanel title="Détail Précarité">
            <VRow>
              <VCol cols="12" md="3">
                <VTextField
                  v-model="precariousnessBonusSheet.value!.name"
                  label="Nom"
                  :rules="[requiredRule]"
                ></VTextField>
              </VCol>
              <VCol cols="12" md="3">
                <NjDatePicker
                  v-model="precariousnessBonusSheet.value!.commitmentDate"
                  label="Date maximale d'engagement"
                  :rules="[requiredRule]"
                />
              </VCol>
              <VCol cols="12" md="3">
                <NjDatePicker
                  v-model="precariousnessBonusSheet.value!.endOperationDate"
                  label="Date maximale de fin d'opération"
                  :rules="[requiredRule]"
                />
              </VCol>
              <VCol cols="12" md="3">
                <RemoteAutoComplete
                  label="Colonne EMMY"
                  :model-value="precariousnessBonusSheet.value!.casEmmyParameterId"
                  :query-for-one="findOneEmmyParameter"
                  :query-for-all="findAllEmmyParameter"
                  item-title="label"
                  item-value="id"
                  infinite-scroll
                  @update:model-value="updateEmmyParameterId"
                />
              </VCol>
              <VCol :cols="6">
                <VSwitch
                  v-model="precariousnessBonusSheet.value!.certified"
                  ripple
                  label="Certifié"
                  :disabled="certified"
                />
              </VCol>
            </VRow>
          </NjExpansionPanel>

          <VDivider />

          <NjExpansionPanel>
            <template #title>
              <div class="d-flex align-center fill-width">
                Paramètres
                <VSpacer />
                <NjIconBtn v-if="editable" icon="mdi-plus" size="small" color="primary" @click.stop="addParameters" />
              </div>
            </template>
            <template #default>
              <VRow>
                <VCol cols="12">
                  <!-- <VAlert type="error" v-show="!allParamIdValid || !allIdValid"
          >Tous vos identifiants de paramètres et de tables de correspondances doivent être uniques</VAlert
        > -->
                  <!-- </VCol> -->

                  <!-- eslint-disable-next-line vue/valid-v-for -->
                  <VRow v-for="(arg, i) in precariousnessBonusSheet.value!.parameters">
                    <VCol>
                      <VCard>
                        <VCardText>
                          <ParameterCalculEditor
                            v-model="precariousnessBonusSheet.value!.parameters[i]"
                            :reserved-words-rule="precariousnessReservedWordsRule"
                            :disabled="!editable"
                            @delete="deleteParameters(i)"
                          />
                        </VCardText>
                      </VCard>
                    </VCol>
                  </VRow>
                </VCol>
              </VRow>
            </template>
          </NjExpansionPanel>

          <NjExpansionPanel>
            <template #title>
              <div class="d-flex align-center fill-width">
                Tables de correspondances
                <VSpacer />
                <NjIconBtn icon="mdi-plus" size="small" color="primary" @click.stop="addMappingTable"></NjIconBtn>
              </div>
            </template>
            <template #default>
              <VRow>
                <!-- <VAlert type="error" v-show="!allTablesCorrespondancesIdValid || !allIdValid"
              >Tous vos identifiants de paramètres et de tables de correspondances doivent être uniques</VAlert
            > -->
                <VCol cols="12">
                  <!-- eslint-disable-next-line vue/valid-v-for -->
                  <template
                    v-for="(mappingTable, iMappingTables) in precariousnessBonusSheet.value!.mappingTables"
                    :key="iMappingTables"
                  >
                    <VDivider />
                    <VRow>
                      <VCol>
                        <MappingTableEditor
                          v-model="precariousnessBonusSheet.value!.mappingTables[iMappingTables]"
                          :reserved-words-rule="precariousnessReservedWordsRule"
                          :parameters="filteredParametersForMappingTable"
                          :i-mapping-tables="iMappingTables"
                          :disabled="!editable"
                        />
                      </VCol>
                    </VRow>
                  </template>
                </VCol>
              </VRow>
            </template>
          </NjExpansionPanel>

          <VDivider />

          <NjExpansionPanel>
            <template #title>
              <div class="d-flex align-center fill-width">
                Les différents cas de précarités
                <VSpacer />
                <NjIconBtn icon="mdi-plus" size="small" color="primary" @click.stop="addCas"></NjIconBtn>
              </div>
            </template>
            <template #default>
              <VRow>
                <template v-for="(cas, iCas) in precariousnessBonusSheet.value!.casList" :key="iCas">
                  <VCol cols="12">
                    <VDivider />
                  </VCol>
                  <VCol>
                    <VRow>
                      <VCol>
                        <VRow>
                          <VCol cols="12" md="6"><VTextField v-model="cas.label" label="Etiquette"></VTextField></VCol>
                          <VCol cols="12" md="6"
                            ><VTextField v-model="cas.emmyValue" label="Valeur EMMY"></VTextField
                          ></VCol>
                          <!-- <VCol cols="6" md="4"
                      ><VTextField label="Formule Calcul CEE classiques" v-model="cas.ceeClassicalFormula"></VTextField
                    ></VCol>
                    <VCol cols="6" md="4"
                      ><VTextField label="Formule Calcul CEE précarités" v-model="cas.ceePrecariteFormula"></VTextField
                    ></VCol> -->
                        </VRow>
                      </VCol>
                      <VCol class="flex-grow-0">
                        <VBtn
                          v-if="editable"
                          icon="mdi-delete-empty-outline"
                          variant="text"
                          color="primary"
                          size="small"
                          @click="deleteCas(iCas)"
                        />
                      </VCol>
                    </VRow>
                    <VRow>
                      <VCol cols="12">
                        <h3>
                          Paramètres
                          <VBtn
                            v-if="editable"
                            icon="mdi-plus"
                            variant="text"
                            size="small"
                            color="primary"
                            @click="addParametersCas(iCas)"
                          ></VBtn>
                        </h3>
                      </VCol>
                      <VCol cols="12">
                        <!-- <VAlert type="error" v-show="!allParamIdValid || !allIdValid"
                    >Tous vos identifiants de paramètres et de tables de correspondances doivent être uniques</VAlert
                  > -->
                        <!-- </VCol> -->

                        <!-- eslint-disable-next-line vue/valid-v-for -->
                        <VRow v-for="(arg, i) in cas.parameters">
                          <VCol>
                            <VCard>
                              <VCardText>
                                <ParameterCalculEditor
                                  v-model="cas.parameters[i]"
                                  :reserved-rule="precariousnessReservedWordsRule"
                                  :disabled="!editable"
                                  @delete="deleteParametersCas(iCas, i)"
                                />
                              </VCardText>
                            </VCard>
                          </VCol>
                        </VRow>
                      </VCol>
                    </VRow>
                    <VRow>
                      <VCol cols="12">
                        <h3>
                          Paramètres calculés
                          <VBtn
                            v-if="editable"
                            icon="mdi-plus"
                            variant="text"
                            size="small"
                            color="primary"
                            @click="addComputedParametersCas(iCas)"
                          ></VBtn>
                        </h3>
                      </VCol>
                      <VCol cols="12">
                        <!-- <VAlert type="error" v-show="!allParamIdValid || !allIdValid"
                    >Tous vos identifiants de paramètres et de tables de correspondances doivent être uniques</VAlert
                  > -->
                        <!-- </VCol> -->

                        <!-- eslint-disable-next-line vue/valid-v-for -->
                        <VRow v-for="(arg, i) in cas.computedParameters">
                          <VCol>
                            <VSelect
                              v-model="arg.id"
                              label="Paramètre à calculer"
                              :rules="[requiredRule]"
                              :items="parameterNames"
                              :readonly="!editable"
                            />
                          </VCol>
                          <VCol>
                            <FormulaInput
                              v-model="arg.formula"
                              label="Formule de calcul"
                              :readonly="!editable"
                              :predefined-variables="predefinedVariablesInfo"
                            />
                          </VCol>
                          <VCol class="flex-grow-0">
                            <VBtn
                              v-if="editable"
                              icon="mdi-delete-empty-outline"
                              variant="text"
                              size="small"
                              color="primary"
                              @click="deleteComputedParametersCas(iCas, i)"
                            />
                          </VCol>
                        </VRow>
                      </VCol>
                    </VRow>
                  </VCol>
                </template>
              </VRow>
            </template>
          </NjExpansionPanel>

          <VDivider />

          <NjExpansionPanel title="Formules">
            <template #default>
              <VRow>
                <VCol cols="12" md="6">
                  <FormulaInput
                    v-model="precariousnessBonusSheet.value!.ceeClassicalFormula"
                    label="Formule Calcul CEE classiques"
                    :readonly="!editable"
                    :predefined-variables="predefinedVariablesInfo"
                  >
                  </FormulaInput>
                </VCol>
                <VCol cols="12" md="6">
                  <FormulaInput
                    v-model="precariousnessBonusSheet.value!.ceePrecariteFormula"
                    label="Formule Calcul CEE précarités"
                    :readonly="!editable"
                    :predefined-variables="predefinedVariablesInfo"
                  >
                    <template #supplements>
                      <p class="mt-4">
                        Variables prédéfinies: <br />
                        <span class="font-weight-bold">[V]</span> : Volume CEE total (après bonification si Coup de
                        pouce)<br />
                        <span class="font-weight-bold">[Bcpe]</span> : bonus multiplicateur CPE<br />
                        <span class="font-weight-bold">[cas]</span> : bonus multiplicateur CPE<br />
                      </p>
                    </template>
                  </FormulaInput>
                </VCol>
              </VRow>
            </template>
          </NjExpansionPanel>

          <VDivider />

          <NjExpansionPanel>
            <template #title>
              <div class="d-flex align-center fill-width">
                Règles de validations
                <VSpacer />
                <NjIconBtn
                  v-if="editable"
                  icon="mdi-help-circle-outline"
                  size="small"
                  color="primary"
                  rounded="0"
                  @click.stop="openValidationRuleHelpDialog"
                />
                <NjIconBtn
                  v-if="editable"
                  icon="mdi-plus"
                  size="small"
                  color="primary"
                  rounded="0"
                  @click.stop="addFormulaValidationRule"
                >
                </NjIconBtn>
              </div>
            </template>
            <VRow class="flex-column" dense>
              <VCol
                v-for="(formulaValidationRule, index) in precariousnessBonusSheet.value?.formulaValidationRules"
                :key="index"
              >
                <VRow>
                  <VCol>
                    <VTextField v-model="formulaValidationRule.errorMessage" label="Message d'erreur" />
                  </VCol>
                  <VCol>
                    <FormulaInput
                      v-model="formulaValidationRule.formula"
                      label="Formule"
                      :predefined-variables="predefinedVariablesInfo"
                    />
                  </VCol>
                  <VCol class="flex-grow-0">
                    <NjIconBtn rounded="0" icon="mdi-delete" color="primary" @click="deleteValidationRule(index)" />
                  </VCol>
                </VRow>
              </VCol>
            </VRow>
          </NjExpansionPanel>
        </VForm>
        <VDivider />

        <NjExpansionPanel title="Aperçu">
          <template #default>
            <PrecariousnessBonusSheetCalculator
              :precariousness-bonus-sheet="precariousnessBonusSheet.value!"
              mode="preview"
            />
          </template>
        </NjExpansionPanel>
      </VCard>
    </template>
  </NjPage>
  <ConfirmUnsavedDataDialog v-model:unsaved-data-dialog="unsavedDataDialog" @save="save" />
</template>

<script lang="ts" setup>
import { precariousnessBonusSheetApi } from '@/api/precariousnessBonus'
import FormulaInput from '@/components/FormulaInput.vue'
import NjBtn from '@/components/NjBtn.vue'
import NjDatePicker from '@/components/NjDatePicker.vue'
import NjExpansionPanel from '@/components/NjExpansionPanel.vue'
import NjPage from '@/components/NjPage.vue'
import ParameterCalculEditor from '@/components/ParameterCalculEditor.vue'
import router from '@/router'
import { useSnackbarStore } from '@/stores/snackbar'
import { makeEmptyMappingTable } from '@/types/calcul/mappingTable'
import { makeEmptyParameterFormula, predefinedParameters } from '@/types/calcul/parameterFormula'
import {
  makeEmptyPrecariousnessBonusSheet,
  type PrecariousnessBonusSheet,
  makeEmptyFormulaValidationRule,
} from '@/types/precariousnessBonus'
import { requiredRule } from '@/types/rule'
import useAdminSidebar from '@/views/adminSidebar'
import type { VForm } from 'vuetify/components/VForm'
import PrecariousnessBonusSheetCalculator from './PrecariousnessBonusSheetCalculator.vue'
import { VDivider, VIcon, VSwitch, VTextField } from 'vuetify/components'
import type { Pageable } from '@/types/pagination'
import { useDialogStore } from '@/stores/dialog'

const props = defineProps({
  id: {
    type: Number,
    default: undefined,
  },
})

const snackbarStore = useSnackbarStore()
const dialogStore = useDialogStore()

// Refs
const formRef = ref<typeof VForm | null>(null)
// Sidebar
useAdminSidebar()

// Data
const precariousnessBonusSheet = ref(emptyValue<PrecariousnessBonusSheet>())

const { unsavedDataDialog, failedSave, succeedSave } = useUnsavedData(precariousnessBonusSheet)

const filteredParametersForMappingTable = computed(() => {
  return predefinedParameters
    .concat(precariousnessBonusSheet.value.value!.parameters)
    .filter((it) => it.type === 'CHOICE')
})

const precariousnessReservedWords = ['cas']
const precariousnessReservedWordsRule = (v: string): true | string => {
  return !precariousnessReservedWords.includes(v) || 'Mot réservé. Choisissez en un autre.'
}

// Enregistrement
const saving = ref(emptyValue<PrecariousnessBonusSheet>())
const save = async () => {
  if ((await formRef.value!.validate()).valid) {
    if (props.id) {
      handleAxiosPromise(saving, precariousnessBonusSheetApi.update(props.id, precariousnessBonusSheet.value.value!), {
        afterSuccess() {
          snackbarStore.setSuccess('Bonification Précarité bien mise à jour')
          succeedSave()
          certified.value = saving.value.value!.certified
        },
        afterError: failedSave,
      })
    } else {
      handleAxiosPromise(saving, precariousnessBonusSheetApi.create(precariousnessBonusSheet.value.value!), (r) => {
        snackbarStore.setSuccess('Bonification Précarité bien créée')
        succeedSave()
        router.push({
          name: 'PrecariousnessBonusSheetOneView',
          params: { id: r.data.id },
        })
      })
    }
  }
}

const certified = ref(false)
// chargement
watch(
  () => props.id,
  (id) => {
    precariousnessBonusSheet.value.value = makeEmptyPrecariousnessBonusSheet()
    if (id) {
      handleAxiosPromise(precariousnessBonusSheet, precariousnessBonusSheetApi.findOne(id), {
        afterSuccess() {
          precariousnessBonusSheet.value.value?.casList.forEach((it) => {
            if (it.parameters === null) {
              it.parameters = []
            }
            if (it.computedParameters === null) {
              it.computedParameters = []
            }
          })
          succeedSave()
          certified.value = precariousnessBonusSheet.value.value!.certified
        },
      })
    }
  },
  {
    immediate: true,
  }
)

// add cas
const addCas = () => {
  precariousnessBonusSheet.value.value!.casList.push({
    label: '',
    emmyValue: '',
    parameters: [],
    computedParameters: [],
  })
}

const deleteCas = (i: number) => {
  precariousnessBonusSheet.value.value!.casList.splice(i, 1)
}

// Paramètres globaux
// Paramètres
function addParameters() {
  precariousnessBonusSheet.value.value!.parameters.push(makeEmptyParameterFormula())
}

function deleteParameters(index: number) {
  precariousnessBonusSheet.value.value!.parameters.splice(index, 1)
}

function addParametersCas(i: number) {
  precariousnessBonusSheet.value.value!.casList[i].parameters.push(makeEmptyParameterFormula())
}

function deleteParametersCas(iCas: number, i: number) {
  precariousnessBonusSheet.value.value!.casList[iCas].parameters.splice(i, 1)
}

function addComputedParametersCas(i: number) {
  precariousnessBonusSheet.value.value!.casList[i].computedParameters.push({
    id: '',
    formula: '',
  })
}

function deleteComputedParametersCas(iCas: number, i: number) {
  precariousnessBonusSheet.value.value!.casList[iCas].computedParameters.splice(i, 1)
}

const parameterNames = computed(() => {
  const set = new Set()
  precariousnessBonusSheet.value.value!.parameters.forEach((it) => {
    set.add(it.id)
  })
  precariousnessBonusSheet.value.value!.casList.forEach((it) => {
    it.parameters.forEach((it) => {
      set.add(it.id)
    })
  })
  return Array.from(set)
})

const addMappingTable = () => {
  precariousnessBonusSheet.value.value!.mappingTables.push(makeEmptyMappingTable())
}

const editable = computed(() => {
  // return !precariteBonification.value.value?.id
  return true
})

// Emmy Parameter
const findOneEmmyParameter = (v: unknown) => emmyParameterApi.findOne(v as number)
const findAllEmmyParameter = (v: string, pageable: Pageable) => emmyParameterApi.findAll({ label: v }, pageable)
const updateEmmyParameterId = async (emmyParameterId: number) => {
  console.debug('updateEmmyParameter')
  if (emmyParameterId !== precariousnessBonusSheet.value.value?.casEmmyParameterId) {
    if (emmyParameterId) {
      const e = (await findOneEmmyParameter(emmyParameterId)).data
      precariousnessBonusSheet.value.value!.casEmmyParameterId = e.id
    } else {
      precariousnessBonusSheet.value.value!.casEmmyParameterId = 0
    }
  }
}

const addFormulaValidationRule = () => {
  precariousnessBonusSheet.value.value!.formulaValidationRules.push(makeEmptyFormulaValidationRule())
}
const deleteValidationRule = (index: number) => {
  precariousnessBonusSheet.value.value!.formulaValidationRules.splice(index, 1)
}

const openValidationRuleHelpDialog = () => {
  dialogStore.addAlert({
    title: 'Aide règle de validation',
    message:
      "Une règle de validation prend un message d'erreur et une formule. Si la formule retourne une valeur inférieure ou égale à 0 le message d'erreur apparaitra",
    closeable: true,
    positiveButton: 'Ok',
    negativeButton: '',
    maxWidth: '400px',
  })
}

const predefinedVariablesInfo: { id: string; label: string }[] = [
  { id: 'V', label: 'Volume CEE total (après bonification si Coup de pouce)' },
  { id: 'Bcpe', label: 'Bonus multiplicateur CPE' },
  { id: 'cas', label: 'Numéro de cas de précarité (correspond à la poisition dans la liste - 1)' },
]
</script>
