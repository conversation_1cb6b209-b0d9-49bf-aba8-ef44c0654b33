import type { CalculFormula } from './calculFormula'
import type { LocalDate } from './date'
import type { Historisable } from './historisation'

export interface BoostBonusSheet extends Partial<Historisable> {
  id: number
  operationCode: string
  energyInstalled: EnergyInstalled | null
  name: string
  description: string
  otherRequirements: string
  certified: boolean
  signingDate: LocalDate
  commitmentMinDate: LocalDate
  commitmentMaxDate: LocalDate
  endOperationMaxDate: LocalDate

  calculFormula: CalculFormula
}

export interface BoostBonusSimulation {
  boostBonusSheet: BoostBonusSheet
  parameterValues: Record<string, string | number>
}

export const energyInstalled: readonly { title: string; value: string }[] = [
  {
    title: 'Electricité',
    value: 'ELECTRICITY',
  },
  {
    title: 'Gaz',
    value: 'GAS',
  },
  {
    title: 'Réseaux de chaleur',
    value: 'HEAT_NETWORKS',
  },
  {
    title: 'Biomasse',
    value: 'BIOMASS',
  },
] as const

export type EnergyInstalled = (typeof energyInstalled)[number]['value']

export interface BoostBonusSimulationRequest {
  boostBonusSheetId: number
  parameterValues: Record<string, string | number>
}

export interface BoostBonusSheetRequest extends Omit<BoostBonusSheet, 'id'> {}

export const makeEmptyBoostBonusSheetRequest = (): BoostBonusSheetRequest => ({
  operationCode: '',
  energyInstalled: null,
  name: '',
  description: '',
  otherRequirements: '',
  calculFormula: {
    mappingTables: [],
    parameters: [],
    formula: '',
  },
  signingDate: '',
  commitmentMinDate: '',
  commitmentMaxDate: '',
  endOperationMaxDate: '',

  certified: false,
})

export const makeEmptyBoostBonusSheet = (): BoostBonusSheet => ({
  id: 0,
  operationCode: '',
  energyInstalled: null,
  name: '',
  description: '',
  otherRequirements: '',
  calculFormula: {
    mappingTables: [],
    parameters: [],
    formula: '',
  },
  signingDate: '',
  commitmentMinDate: '',
  commitmentMaxDate: '',
  endOperationMaxDate: '',
  certified: false,
})

export const mapToBoostBonusSheetRequest = (boostBonusSheet: BoostBonusSheet): BoostBonusSheetRequest => {
  return {
    operationCode: boostBonusSheet.operationCode,
    energyInstalled: boostBonusSheet.energyInstalled,
    name: boostBonusSheet.name,
    description: boostBonusSheet.description,
    otherRequirements: boostBonusSheet.otherRequirements,
    calculFormula: boostBonusSheet.calculFormula,
    signingDate: boostBonusSheet.signingDate,
    commitmentMinDate: boostBonusSheet.commitmentMinDate,
    commitmentMaxDate: boostBonusSheet.commitmentMaxDate,
    endOperationMaxDate: boostBonusSheet.endOperationMaxDate,
    certified: boostBonusSheet.certified,
  }
}
