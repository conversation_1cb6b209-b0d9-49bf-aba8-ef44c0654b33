<template>
  <div class="content-layout">
    <div class="content-layout__header px-4 pt-4">
      <h1>Liste des valeurs</h1>
      <VTabs>
        <VTab v-for="t in tabs" :key="t.routeName" :to="{ name: t.routeName }">{{ t.label }}</VTab>
      </VTabs>
      <VDivider />
    </div>
    <RouterView class="content-layout__main" />
  </div>
</template>
<script lang="ts" setup>
const tabs: { routeName: string; label: string }[] = [
  { label: 'Documents', routeName: 'DocumentTypeAllView' },
  { label: 'Valorisation', routeName: 'ValuationAllView' },
  { label: 'Étapes', routeName: 'StepAllView' },
  { label: 'Période', routeName: 'PeriodAllView' },
  { label: 'Organisations', routeName: 'EntityAllView' },
  { label: 'Territoires', routeName: 'TerritoryAllView' },
  { label: 'Messages', routeName: 'MessageTemplateAllView' },
  { label: 'Bénéficiaires', routeName: 'BeneficiaryAdminView' },
  { label: 'Sous traitants', routeName: 'SubcontractorAdminView' },
  { label: 'Organismes de contrôle', routeName: 'ControlOrganismAdminView' },
  { label: 'Valeurs', routeName: 'AdminValueView' },
]
</script>
