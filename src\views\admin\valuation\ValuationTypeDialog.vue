<template>
  <VDialog v-model="activeDialog" width="60%">
    <VCard>
      <VCardTitle class="d-flex align-center">
        <span class="d-flex w-100">
          {{ mode === 'create' ? 'Ajout' : mode === 'delete' ? 'Désactiver' : 'Réactiver' }}
          le type de valorisation
        </span>
        <NjIconBtn icon="mdi-window-close" color="primary" @click="activeDialog = false" />
      </VCardTitle>
      <VCardText>
        <VRow align="center">
          <VCol v-if="mode === 'create'">
            <VTextField v-model="typeToSend.name" label="Nom" />
          </VCol>
          <VCol v-if="mode === 'create' || mode === 'activate'">
            <NjDatePicker v-model="typeToSend.startDate" label="Date de début" />
          </VCol>
          <VCol v-if="mode === 'delete'">
            <NjDatePicker v-model="typeToSend.endDate" label="Date de fin" />
          </VCol>
        </VRow>
      </VCardText>
      <VCardActions>
        <VSpacer />
        <NjBtn variant="outlined" @click="activeDialog = false"> Annuler </NjBtn>
        <NjBtn :disabled="disabledRule" @click="save">Sauvegarder</NjBtn>
      </VCardActions>
    </VCard>
  </VDialog>
</template>
<script lang="ts" setup>
import { valuationTypeApi } from '@/api/valuationType'
import NjDatePicker from '@/components/NjDatePicker.vue'
import { useSnackbarStore } from '@/stores/snackbar'
import { makeEmptyValuationType, type ValuationType } from '@/types/valuation'
import type { PropType } from 'vue'

const props = defineProps({
  modelValue: Boolean,
  type: {
    type: Object as PropType<ValuationType>,
    required: true,
  },
  mode: {
    type: String as PropType<'create' | 'delete' | 'activate'>,
    default: 'create',
  },
})

const emit = defineEmits(['update:model-value', 'update:type', 'save'])
const snackbarStore = useSnackbarStore()
const typeToSend = ref(makeEmptyValuationType())

const activeDialog = computed<boolean>({
  get() {
    return props.modelValue
  },
  set(v) {
    emit('update:model-value', v)
  },
})

const savingType = ref(emptyValue<ValuationType>())
const save = () => {
  if (props.mode === 'create') {
    handleAxiosPromise(savingType, valuationTypeApi.create(typeToSend.value), {
      afterSuccess: () => {
        snackbarStore.setSuccess('Le nouveau type de valorisation a bien été créé')
        activeDialog.value = false
        emit('save')
      },
      afterError: () => snackbarStore.setError('Un problème est survenu lors de la création du type de valorisation'),
    })
  } else {
    handleAxiosPromise(savingType, valuationTypeApi.update(typeToSend.value.id, typeToSend.value), {
      afterSuccess: () => {
        snackbarStore.setSuccess(`Ce type de valorisation a bien été ${props.mode === 'delete' ? 'dés' : 'ré'}activé`)
        activeDialog.value = false
        emit('save')
      },
      afterError: () =>
        snackbarStore.setError(
          `Un problème est survenu lors de la ${
            props.mode === 'delete' ? 'dés' : 'ré'
          }activation de ce type de valorisation`
        ),
    })
  }
}

watch(
  () => props.type,
  (v) => (typeToSend.value = v),
  {
    immediate: true,
  }
)

const disabledRule = computed(() => {
  if (props.mode === 'create') {
    return !typeToSend.value.name || !typeToSend.value.startDate
  } else if (props.mode === 'delete') {
    return !typeToSend.value.endDate
  } else {
    return !typeToSend.value.startDate
  }
})
</script>
