import type { Page, Pageable } from '@/types/pagination'
import type { PrecariousnessBonusSheet, PrecariousnessBonusSheetRequest } from '@/types/precariousnessBonus'
import type { AxiosInstance, AxiosPromise } from 'axios'
import axiosInstance from '.'

export interface PrecariousnessBonusSheetFilter {
  search?: string
  certified?: boolean
}
class PrecariousnessBonusSheetApi {
  public constructor(private axios: AxiosInstance) {}

  public create(request: PrecariousnessBonusSheetRequest): AxiosPromise<PrecariousnessBonusSheet> {
    return this.axios.post('/precariousness_bonus_sheets', request)
  }

  public update(id: number, request: PrecariousnessBonusSheetRequest): AxiosPromise<PrecariousnessBonusSheet> {
    return this.axios.put('/precariousness_bonus_sheets/' + id, request)
  }

  public findOne(id: number): AxiosPromise<PrecariousnessBonusSheet> {
    return this.axios.get('/precariousness_bonus_sheets/' + id)
  }

  public findAll(
    pageable: Pageable,
    filter: PrecariousnessBonusSheetFilter
  ): AxiosPromise<Page<PrecariousnessBonusSheet>> {
    return this.axios.get('/precariousness_bonus_sheets', {
      params: { ...pageable, ...filter },
    })
  }
}

export const precariousnessBonusSheetApi = new PrecariousnessBonusSheetApi(axiosInstance)
