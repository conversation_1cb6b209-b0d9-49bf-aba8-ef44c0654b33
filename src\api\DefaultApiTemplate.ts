import type { Page, Pageable } from '@/types/pagination'
import type { AxiosInstance, AxiosPromise } from 'axios'

export class DefaultApiTemplate<Response, Request, Filter> {
  public constructor(
    private axios: AxiosInstance,
    private uri: string
  ) {}

  public create(request: Request): AxiosPromise<Response> {
    return this.axios.post(this.uri, request)
  }

  public findAll(pageable: Pageable, filter: Filter): AxiosPromise<Page<Response>> {
    return this.axios.get(this.uri, {
      params: { ...filter, ...pageable },
    })
  }

  public update(id: number, request: Request): AxiosPromise<Response> {
    return this.axios.put(this.uri + '/' + id, request)
  }

  public findOne(id: number): AxiosPromise<Request> {
    return this.axios.get(this.uri + '/' + id)
  }
}
