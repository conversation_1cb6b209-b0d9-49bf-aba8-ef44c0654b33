/**
 * main.ts
 *
 * Bootstraps Vuetify and other plugins then mounts the App`
 */

// Components
import './assets/main.scss'

// Composables
import { createApp } from 'vue'

// Plugins
import { registerPlugins } from '@/plugins'
import { RouterView } from 'vue-router'
import { initSessionId } from './stores/analytics'

initSessionId()

// TODO voir comment le déplacer
;(BigInt.prototype as any).toJSON = function () {
  return Number(this)
}

const app = createApp(RouterView)

registerPlugins(app)

app.mount('#app')
