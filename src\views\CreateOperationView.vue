<template>
  <OperationFormPage
    v-if="simulationId || standardizedOperationSheetId"
    :simulation="simulation.value!"
    edit
    :loading="simulation.loading"
    :error="simulation.error"
    create-operation
  />
  <CreateOperationAllView v-else />
</template>

<script lang="ts" setup>
import OperationFormPage from '@/views/OperationFormPageView.vue'
import type { Operation } from '@/types/operation'
import type { StandardizedOperationSheet } from '@/types/calcul/standardizedOperationSheet'
import CreateOperationAllView from './CreateOperationAllView.vue'

const props = defineProps({
  simulationId: {
    type: Number,
  },
  standardizedOperationSheetId: {
    type: Number,
  },
})

const simulation = ref(succeedValue<Operation>(makeEmptyOperation()))
const standardizedOperationSheet = ref(emptyValue<StandardizedOperationSheet>())

onMounted(() => {
  if (props.simulationId) {
    handleAxiosPromise(simulation, simulationApi.findById(props.simulationId), (response) => {
      const sim: Operation = response.data
      sim.stepId = 10
      simulation.value.value = response.data
    })
  }
})

watch(
  () => props.standardizedOperationSheetId,
  () => {
    if (props.standardizedOperationSheetId) {
      handleAxiosPromise(
        standardizedOperationSheet,
        standardizedOperationSheetApi.findOne(props.standardizedOperationSheetId),
        (res) => {
          standardizedOperationSheet.value.value = res.data
          simulation.value.value!.standardizedOperationSheet = standardizedOperationSheet.value.value
          simulation.value.value!.stepId = 10
        }
      )
    }
  },
  {
    immediate: true,
  }
)
</script>
