<template>
  <VSheet>
    <VRow class="flex-column" no-gutters>
      <VCol v-for="(titleToken, index) in titleTokens" :key="index">
        <TemplateTokenExpansionPanel :title="titleToken.title" :items="titleToken.tokens" />
      </VCol>
      <VCol>
        <NjExpansionPanel title="Jeton dynamique">
          <VRow class="flex-column">
            <VCol>
              <RemoteAutoComplete
                v-model="standardizedOperationSheet"
                ripple
                label="Fiche opération"
                :query-for-all="
                  (search, pageable) => standardizedOperationSheetApi.findAll(pageable, { search: search })
                "
                :query-for-one="(item) => standardizedOperationSheetApi.findOne(item.id)"
                item-title="operationCode"
                return-object
                clearable
                infinite-scroll
              >
                <template #selection="{ item }">
                  <StandardizedOperationSheetChip
                    :standardized-operation-sheet="item.raw as StandardizedOperationSheet"
                  />
                </template>
                <template #item="{ item, props }">
                  <StandardizedOperationSheetItem
                    v-bind="props"
                    :standardized-operation-sheet="item.raw as StandardizedOperationSheet"
                  />
                </template>
              </RemoteAutoComplete>
            </VCol>
            <VCol>
              <TemplateTokenExpansionPanel
                v-show="standardizedOperationSheet"
                :title="standardizedOperationSheetTokens.title"
                :items="standardizedOperationSheetTokens.tokens"
              />
            </VCol>
            <VCol>
              <RemoteAutoComplete
                v-model="epcBonusSheet"
                ripple
                label="Fiche CPE"
                :query-for-all="(search, pageable) => epcBonusSheetApi.getAll(pageable, { search: search })"
                :query-for-one="(item) => epcBonusSheetApi.getOne(item.id)"
                item-title="name"
                return-object
                clearable
                infinite-scroll
              >
              </RemoteAutoComplete>
            </VCol>
            <VCol>
              <TemplateTokenExpansionPanel
                v-show="epcBonusSheet"
                :title="epcBonusSheetTokens.title"
                :items="epcBonusSheetTokens.tokens"
              />
            </VCol>
            <VCol>
              <RemoteAutoComplete
                v-model="precariousnessBonusSheet"
                ripple
                label="Fiche Précarité"
                :query-for-all="(search, pageable) => precariousnessBonusSheetApi.findAll(pageable, { search: search })"
                :query-for-one="(item) => precariousnessBonusSheetApi.findOne(item.id)"
                item-title="name"
                return-object
                clearable
                infinite-scroll
              >
              </RemoteAutoComplete>
            </VCol>
            <VCol>
              <TemplateTokenExpansionPanel
                v-show="precariousnessBonusSheet"
                :title="precariousnessBonusSheetTokens.title"
                :items="precariousnessBonusSheetTokens.tokens"
              />
            </VCol>
            <VCol>
              <RemoteAutoComplete
                v-model="boostBonusSheet"
                ripple
                label="Fiche coup de pouce"
                :query-for-all="(search, pageable) => boostBonusSheetApi.findAll(pageable, { search: search })"
                :query-for-one="(item) => boostBonusSheetApi.findOne(item.id)"
                item-title="name"
                return-object
                clearable
                infinite-scroll
              >
              </RemoteAutoComplete>
            </VCol>
            <VCol>
              <TemplateTokenExpansionPanel
                v-show="boostBonusSheet"
                :title="boostBonusSheetTokens.title"
                :items="boostBonusSheetTokens.tokens"
              />
            </VCol>
          </VRow>
        </NjExpansionPanel>
      </VCol>
    </VRow>
  </VSheet>
</template>
<script setup lang="ts">
import { VCol } from 'vuetify/components'
import { titleTokens } from './templateToken'
import TemplateTokenExpansionPanel from '../TemplateTokenExpansionPanel.vue'
import type { StandardizedOperationSheet } from '@/types/calcul/standardizedOperationSheet'
import { standardizedOperationSheetApi } from '@/api/standardizedOperationSheet'
import RemoteAutoComplete from '@/components/RemoteAutoComplete.vue'
import { type EpcBonusSheet } from '@/types/epcBonus'
import { epcBonusSheetApi } from '@/api/epcBonus'
import type { ParameterFormula } from '@/types/calcul/parameterFormula'
import type { PrecariousnessBonusSheet } from '@/types/precariousnessBonus'
import { precariousnessBonusSheetApi } from '@/api/precariousnessBonus'
import { boostBonusSheetApi } from '@/api/boostBonus'
import type { BoostBonusSheet } from '@/types/boostBonus'

const getParameterToken = (prefix: string, parameters: ParameterFormula[]): { name: string; value: string }[] => {
  const res: { name: string; value: string }[] = []
  parameters.forEach((p) => {
    res.push({
      name: p.label,
      value: `{{${prefix}_${p.id}}}`,
    })

    if (p.type === 'CHOICE') {
      const choices = p.data.split(';')
      choices.forEach((c: string) => {
        res.push({
          name: `Checkbox ${p.label} : ${c}`,
          value: `{{${prefix}_CHECKBOX_${p.id}_${c}}}`,
        })
      })
    }
  })
  return res
}

const standardizedOperationSheet = ref<StandardizedOperationSheet>()

const standardizedOperationSheetTokens = computed(() => {
  let res: { name: string; value: string }[] = []
  if (standardizedOperationSheet.value?.parameters) {
    res = getParameterToken('PARAMETRE', standardizedOperationSheet.value.parameters)
  }

  return {
    title: 'Fiche opération',
    tokens: res,
  }
})

const epcBonusSheet = ref<EpcBonusSheet>()
const epcBonusSheetTokens = computed(() => {
  let res: { name: string; value: string }[] = []
  if (epcBonusSheet.value?.parameters) {
    res = getParameterToken('PARAMETRE_CPE', epcBonusSheet.value.parameters)
  }

  return {
    title: 'CPE',
    tokens: res,
  }
})

const precariousnessBonusSheet = ref<PrecariousnessBonusSheet>()
const precariousnessBonusSheetTokens = computed(() => {
  let res: { name: string; value: string }[] = []
  if (precariousnessBonusSheet.value?.parameters) {
    res = getParameterToken('PARAMETRE_PRECARITE', precariousnessBonusSheet.value.parameters)
  }

  return {
    title: 'Précarité',
    tokens: res,
  }
})

const boostBonusSheet = ref<BoostBonusSheet>()
const boostBonusSheetTokens = computed(() => {
  let res: { name: string; value: string }[] = []
  if (boostBonusSheet.value?.calculFormula.parameters) {
    res = getParameterToken('PARAMETRE_CDP', boostBonusSheet.value.calculFormula.parameters)
  }

  return {
    title: 'Fiche Coup de pouce',
    tokens: res,
  }
})
</script>
