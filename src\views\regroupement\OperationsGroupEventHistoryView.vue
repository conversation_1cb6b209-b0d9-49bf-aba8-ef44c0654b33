<template>
  <div ref="element" class="content-layout__main pa-3 w-100">
    <OperationsGroupEventHistoryCard
      v-for="event in operationsGroupEvents"
      :key="event.uuid"
      :event="event"
      class="mb-4"
      @reload="reload"
      @treated="emit('treated', $event)"
    />
  </div>
</template>
<script setup lang="ts">
import { type Page } from '@/types/pagination'
import { useInfiniteScroll } from '@vueuse/core'
import OperationsGroupEventHistoryCard from './OperationsGroupEventHistoryCard.vue'
import { type OperationsGroupEvent } from '@/types/operationsGroupEvent'
import { operationsGroupEventApi } from '@/api/operationsGroupEventApi'
import type { AtypicalValuationMessageRequest } from '@/types/message'

const props = defineProps({
  id: {
    type: Number,
    required: true,
  },
})
const emit = defineEmits<{
  treated: [AtypicalValuationMessageRequest]
}>()

const operationsGroupEvents = ref<OperationsGroupEvent[]>([])
const operationsGroupEventsPage = ref(emptyValue<Page<OperationsGroupEvent>>())
const operationsGroupEventsPageNumber = ref(0)

const getOperationsGroupEvents = async () => {
  await handleAxiosPromise(
    operationsGroupEventsPage,
    operationsGroupEventApi.findAll(
      props.id,
      { top: true },
      { sort: ['creationDateTime,DESC'], size: 5, page: operationsGroupEventsPageNumber.value++ }
    )
  )
  operationsGroupEvents.value.push(...operationsGroupEventsPage.value.value!.content)
}

const element = ref<HTMLElement | null>(null)
useInfiniteScroll(
  element,
  async () => {
    if (!operationsGroupEventsPage.value.value?.last) {
      await getOperationsGroupEvents()
    }
  },
  {
    distance: 10,
  }
)

const reload = () => {
  operationsGroupEventsPageNumber.value = 0
  operationsGroupEvents.value = []
  getOperationsGroupEvents()
}

defineExpose({
  reload: reload,
})
</script>
