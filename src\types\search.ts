import { debounce } from 'lodash'
import type { PromisableValue } from './promisableValue'

export const useDebouncedSearch = <F>(
  pageFilter: Ref<F>,
  updateFilter: (filter: F) => void,
  valuable: Ref<PromisableValue<unknown>>
) => {
  const privateDebouncedUpdateSearch = debounce((v: string) => {
    const filter = {
      ...unref(pageFilter),
      search: v,
    }
    updateFilter(filter)
  }, 300)
  const updateSearch = (v: string) => {
    valuable.value.loading = true
    privateDebouncedUpdateSearch(v)
  }

  return { updateSearch }
}
