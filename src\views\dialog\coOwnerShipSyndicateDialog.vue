<template>
  <CardDialog
    :model-value="modelValue"
    title="Choix d'un syndicat de copropriété"
    fixed
    @update:model-value="modelValue = $event"
  >
    <VRow class="flex-column h-100">
      <VCol class="flex-grow-0">
        <VRow>
          <VCol cols="5">
            <SearchInput
              v-model="searchAddress.street"
              :loading="coOwnerShipSyndicateLoading"
              label="Numéro et Voie"
              no-prepend-icon
            />
          </VCol>
          <VCol>
            <SearchInput
              v-model="searchAddress.postalCode"
              :loading="coOwnerShipSyndicateLoading"
              label="Code Postal"
              no-prepend-icon
            />
          </VCol>
          <VCol>
            <SearchInput
              v-model="searchAddress.city"
              :loading="coOwnerShipSyndicateLoading"
              label="Commune"
              no-prepend-icon
            />
          </VCol>
        </VRow>
      </VCol>
      <VCol>
        <CoOwnerShipSyndicateAllView
          v-model:selections="selectedCoOwnerShipSyndicate"
          v-model:loading="coOwnerShipSyndicateLoading"
          :address="searchAddress"
        />
      </VCol>
    </VRow>
    <template #actions>
      <NjBtn variant="outlined" @click="closeCoOwnerShipSyndicateDialog">Annuler</NjBtn>
      <NjBtn :disabled="!selectedCoOwnerShipSyndicate[0]" @click="selectCoOwnerShipSyndicate">Valider</NjBtn>
    </template>
  </CardDialog>
</template>

<script setup lang="ts">
import type { CoOwnerShipSyndicate } from '@/types/coOwnerShipSyndicate'
import CoOwnerShipSyndicateAllView from '../coOwnerShipSyndicateAllView.vue'
import type { PropType } from 'vue'
import type { Address } from '@/types/address'

const modelValue = defineModel<boolean>({ default: false })
const selected = defineModel<CoOwnerShipSyndicate[]>('selected', { default: () => [] })

const props = defineProps({
  address: {
    type: Object as PropType<Address>,
    required: false,
  },
})

const searchAddress = ref<Address>({
  street: '',
  postalCode: '',
  city: '',
  country: null,
})

const selectedCoOwnerShipSyndicate = ref<CoOwnerShipSyndicate[]>([])
const coOwnerShipSyndicateLoading = ref(false)

const initializeSearchFields = () => {
  if (props.address) {
    searchAddress.value.street = props.address.street || ''
    searchAddress.value.postalCode = props.address.postalCode || ''
    searchAddress.value.city = props.address.city || ''
  } else {
    searchAddress.value.street = ''
    searchAddress.value.postalCode = ''
    searchAddress.value.city = ''
  }
}

watch(
  () => modelValue.value,
  (v) => {
    if (v) {
      initializeSearchFields()
      selectedCoOwnerShipSyndicate.value = []
    }
  }
)

watch(
  searchAddress,
  () => {
    coOwnerShipSyndicateLoading.value = true
  },
  { deep: true }
)

const closeCoOwnerShipSyndicateDialog = () => {
  modelValue.value = false
}

const selectCoOwnerShipSyndicate = () => {
  closeCoOwnerShipSyndicateDialog()
  selected.value = selectedCoOwnerShipSyndicate.value
}
</script>
