<template>
  <CardDialog
    :title="title"
    actions-class="justify-end"
    :model-value="modelValue"
    :no-actions="noActions"
    @update:model-value="() => emits('click:negative')"
  >
    <template #title>
      <slot name="title" />
    </template>

    <slot></slot>

    <template #actions>
      <slot name="actions">
        <NjBtn
          v-if="negativeButton"
          variant="outlined"
          :disabled="disabledNegativeButton"
          :loading="loadingNegativeButton"
          @click="$emit('click:negative')"
          >{{ negativeButton }}</NjBtn
        >
        <slot name="positiveButton">
          <NjBtn
            v-if="positiveButton"
            :disabled="disabledPositiveButton"
            :loading="loadingPositiveButton"
            @click="$emit('click:positive')"
            >{{ positiveButton }}</NjBtn
          >
        </slot>
      </slot>
    </template>
  </CardDialog>
</template>

<script setup lang="ts">
withDefaults(
  defineProps<{
    modelValue: boolean
    title?: string
    positiveButton?: false | string
    negativeButton?: false | string
    disabledNegativeButton?: boolean
    disabledPositiveButton?: boolean
    loadingNegativeButton?: boolean
    loadingPositiveButton?: boolean
    noActions?: boolean
  }>(),
  {
    disabledNegativeButton: false,
    disabledPositiveButton: false,
    loadingNegativeButton: false,
    loadingPositiveButton: false,
    positiveButton: 'Valider',
    negativeButton: 'Annuler',
  }
)
const emits = defineEmits<{
  'update:model-value': [boolean]
  'click:positive': []
  'click:negative': []
}>()
</script>
