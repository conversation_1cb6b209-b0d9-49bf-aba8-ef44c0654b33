import type { Page, Pageable } from '@/types/pagination'
import type { Operation, OperationRequest } from '@/types/operation'
import type { AxiosInstance, AxiosPromise } from 'axios'
import axiosInstance from '.'

class SimulationApi {
  public constructor(private axios: AxiosInstance) {}

  public create(simulation: OperationRequest): AxiosPromise<Operation> {
    return this.axios.post('/simulations', simulation)
  }

  public findAll(pageable: Pageable): AxiosPromise<Page<Operation>> {
    return this.axios.get('/simulations', {
      params: pageable,
    })
  }

  public findById(id: number): AxiosPromise<Operation> {
    return this.axios.get('/simulations/' + id)
  }

  public updateSimulation(id: number, simulationRequest: OperationRequest): AxiosPromise<Operation> {
    return this.axios.put('simulations/' + id, simulationRequest)
  }

  public delete(ids: number[]): AxiosPromise<void> {
    return this.axios.delete('simulations', { params: { ids } })
  }

  public cancelEmmyImport(id: number): AxiosPromise<Operation> {
    return this.axios.delete('operations/' + id + '/cancel_emmy_import')
  }
}

export const simulationApi = new SimulationApi(axiosInstance)
