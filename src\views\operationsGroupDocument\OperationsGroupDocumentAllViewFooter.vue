<template>
  <div>
    <VDivider />
    <VRow class="pa-2 value__label content-layout__footer">
      <VCol align-self="center" class="flex-grow-0 text-no-wrap"> Documents ajoutés: {{ totalElements }} </VCol>
      <VCol>
        <NjBtn
          border="0"
          prepend-icon="mdi-download"
          variant="outlined"
          :loading="downloadAllDocumentsLoading"
          @click="downloadAllDocuments"
        >
          Tout télécharger
        </NjBtn>
        <template v-if="displayConventionButton">
          <NjBtn border="0" prepend-icon="mdi-download" variant="outlined" @click="conventionDialog = true">
            Préparer la convention
          </NjBtn>
          <CardDialog v-model="conventionDialog" width="40%" title="Préparer la convention" :closeable="false">
            <VRow class="flex-column my-n2">
              <VCol>
                La convention est éditée pour {{ numberOfProperties }}
                {{ numberOfProperties && numberOfProperties > 1 ? 'adresses' : 'adresse' }}
                et
                {{ numberOfStandardizedOperationSheet }}
                {{
                  numberOfStandardizedOperationSheet && numberOfStandardizedOperationSheet > 1
                    ? "types d'opérations"
                    : "type d'opération"
                }}
              </VCol>
              <VCol>
                <VAlert type="info" variant="outlined">
                  Le document
                  <b>{{
                    `ConventionCEE-${isSubsidiary(operationsGroup.entity) ? 'Tripartite-' : ''}${
                      operationsGroup.name
                    }.docx`
                  }}</b>
                  est disponible. Merci de vérifier les informations avant signature.
                </VAlert>
              </VCol>
            </VRow>
            <template #actions>
              <NjBtn variant="outlined" @click="conventionDialog = false">Annuler</NjBtn>
              <NjBtn :loading="downloading" @click="createConvention">Téléchargement</NjBtn>
            </template>
          </CardDialog>
        </template>
      </VCol>
      <VCol class="flex-grow-0" align-self="center">
        <NjBtn prepend-icon="mdi-plus-circle-outline " @click="addDocumentDialog = true"> Ajouter </NjBtn>
        <SubmitDocumentDialog
          v-model="addDocumentDialog"
          :missing-documents="missingDocumentTypes"
          :operations-group-id="props.operationsGroup?.id"
          hide-missing-list
          @save-document="(saveDocumentResults) => emits('saveDocument', saveDocumentResults)"
        />
      </VCol>
    </VRow>
  </div>
</template>
<script setup lang="ts">
import { useSnackbarStore } from '@/stores/snackbar'
import type { PropType } from 'vue'
import type { DocumentType } from '@/types/documentType'
import type { EnhancedOperationsGroup } from '@/types/operationsGroup'
import { isSubsidiary } from '@/types/entity'
import { useUserStore } from '@/stores/user'
import SubmitDocumentDialog from '../document/SubmitDocumentDialog.vue'
import { trace } from '@/stores/analytics'
import { useAdminConfigurationStore } from '@/stores/adminConfiguration'
import type SendDocumentResult from '../document/sendDocumentResult'

const props = defineProps({
  operationsGroup: {
    type: Object as PropType<EnhancedOperationsGroup>,
    required: true,
  },
  totalElements: Number,
  missingDocumentTypes: Array as PropType<DocumentType[]>,
  numberOfProperties: Number,
  numberOfStandardizedOperationSheet: Number,
})

const emits = defineEmits<{
  sendVf: [void]
  saveDocument: [SendDocumentResult[]]
}>()

const snackbarStore = useSnackbarStore()

const downloadAllDocumentsLoading = ref(false)
const downloadAllDocuments = () => {
  downloadAllDocumentsLoading.value = true
  operationsGroupDocumentApi
    .downloadAll(props.operationsGroup.id)
    .then(async (response) => {
      await awaitResponse(response.data)
        .then(async (response) => {
          downloadFile(
            'documents-' + props.operationsGroup.name + '.zip',
            (await responseHandlerApi.download(response.uuid)).data
          )
          snackbarStore.setSuccess('Le téléchargement des documents a réussi')
        })
        .catch((response) => {
          snackbarStore.setError(response.errorMessage ?? 'Le téléchargement des documents a échoué')
        })
    })
    .catch(async (err) => snackbarStore.setError(await handleAxiosException(err)))
    .finally(() => {
      downloadAllDocumentsLoading.value = false
    })
}

const conventionDialog = ref(false)

const downloading = ref(false)
const userStore = useUserStore()

const displayConventionButton = computed(
  () =>
    props.operationsGroup.step == 30 &&
    userHasRole(userStore.currentUser, 'SUPPORT_AGENCE_PLUS', 'AGENCE_PLUS', 'TERRITOIRE', 'ADMIN', 'ADMIN_PLUS')
)
const adminStore = useAdminConfigurationStore()
const createConvention = () => {
  if (props.operationsGroup) {
    trace('downloadFilledTemplate', {
      documentType: { id: adminStore.conventionDocumentTypeId?.valueAsInt, name: 'Convention' },
      operationGroupId: props.operationsGroup?.id,
    })
    downloading.value = true
    operationsGroupApi
      .createConvention(props.operationsGroup.id)
      .then((v) => {
        if (props.operationsGroup) {
          downloadFile(
            `ConventionCEE-${isSubsidiary(props.operationsGroup.entity) ? 'Tripartite-' : ''}${
              props.operationsGroup.entity.name
            }.docx`,
            v.data
          )
          snackbarStore.setSuccess("L'édition de la convention a réussi")
          conventionDialog.value = false
        }
      })
      .catch(async (err) => {
        snackbarStore.setError(
          await handleAxiosException(err, undefined, {
            defaultMessage: "Une erreur est survenue lors de l'édition de la convention",
          })
        )
      })
      .finally(() => (downloading.value = false))
  }
}
const addDocumentDialog = ref(false)
</script>
