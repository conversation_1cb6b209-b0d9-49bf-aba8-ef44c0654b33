<template>
  <VCard v-for="doc in documents" :key="doc.documentType.name">
    <VCardText>
      <VRow>
        <VCol>
          {{ doc.documentType.name }}
        </VCol>
        <VCol class="flex-grow-0">
          <NjBooleanIcon :condition="doc.location == 'OPERATIONS_GROUP'" />
        </VCol>
      </VRow>
    </VCardText>
  </VCard>
</template>
<script lang="ts" setup>
import { documentLocationApi } from '@/api/documentLocation'
import type { DocumentLocation } from '@/types/document'

const props = defineProps({
  operationId: {
    type: Number,
    required: true,
  },
  operationGroupId: {
    type: Number,
    required: true,
  },
})

const documents = ref<DocumentLocation[]>([])

watch(
  () => props.operationId,
  (v) => {
    if (v) {
      documentLocationApi.findByOperationId(props.operationId).then((response) => (documents.value = response.data))
    }
  },
  {
    immediate: true,
  }
)
</script>
