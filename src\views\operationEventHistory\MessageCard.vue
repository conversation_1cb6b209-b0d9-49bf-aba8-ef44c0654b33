<template>
  <VCard :key="props.message.id" class="mb-4">
    <VCardTitle class="text-primary font-weight-bold" style="font-size: 1rem !important">
      <VRow>
        <VCol> {{ displayFullnameUser(props.message.sender) }} </VCol>
        <VSpacer />
        <VCol class="flex-grow-0">
          {{
            isFinalVersionMessage
              ? 'Envoi de la version finale'
              : message.recipients.length > 0
                ? 'Message'
                : 'Commentaire'
          }}
          {{ message.accounted ? 'comptabilisé' : '' }}
        </VCol>
      </VRow>
    </VCardTitle>
    <VCardSubtitle class="ps-4" style="text-wrap: balance">
      {{ formatHumanReadableLocalDateTime(props.message.sendDateTime) }}
      <span v-if="message.recipients">
        À :
        <span
          v-for="recipient in props.message.recipients.substring(0, props.message.recipients.length - 1).split(';')"
          :key="recipient"
        >
          <VLink
            size="small"
            style="font-weight: initial; font-size: initial"
            @click="
              () => {
                advisedRecipient = [recipient]
                commentaireDialog = true
              }
            "
          >
            {{ recipient }}
          </VLink>
          ,
        </span>
      </span>
      <span v-if="message.copy">
        Cc :
        <span v-for="copy in message.copy.substring(0, message.copy.length - 1).split(';')" :key="copy">
          <VLink
            size="small"
            style="font-weight: initial; font-size: initial"
            @click="
              () => {
                advisedRecipient = [copy]
                commentaireDialog = true
              }
            "
          >
            {{ copy }}
          </VLink>
          ,
        </span>
      </span>
    </VCardSubtitle>
    <VCardText>
      <pre wrap>{{ props.message.message }}</pre>
      <div v-if="props.message.screenshots.length > 0" class="d-flex" style="gap: 16px">
        <ScreenshotDisplay
          v-for="s in props.message.screenshots"
          :key="s.id"
          :message-id="message.id"
          :document-id="s.id"
        />
      </div>
      <span v-if="props.message.concernedDocumentTypes.length > 0 && props.documentsAvailable?.content">
        Documents concernés :
        <div v-for="document in props.message.concernedDocumentTypes" :key="document.id">
          <VLink
            @click="
              downloadDocumentFile(props.documentsAvailable.content.find((doc) => doc.documentType.id === document.id)!)
            "
          >
            {{
              props.documentsAvailable.content.find((doc) => doc.documentType.id === document.id)?.document
                .originalFilename
            }}
          </VLink>
        </div>
      </span>
      <span v-if="props.message.reasons.length > 0">
        Raisons de comptabilisation :
        <div v-for="reason in props.message.reasons" :key="reason.id">
          {{ reason.reason }}
        </div>
      </span>
      <MetaMessageDisplay v-if="message.metaMessage" :message="message" @treated="emits('treated', $event)" />
      <div v-if="message.repliesNumber" class="pt-3 mb-n3">
        <VLink
          size="small"
          :icon="displayReplies ? 'mdi-chevron-down' : 'mdi-chevron-right'"
          style="font-weight: initial; font-size: initial"
          @click="displayReplies = !displayReplies"
        >
          {{
            displayReplies
              ? 'Réduire tout'
              : message.repliesNumber + (message.repliesNumber === 1 ? ' réponse' : ' réponses')
          }}
        </VLink>
      </div>
      <VLink
        v-if="!repliesPage.value?.last && displayReplies"
        size="small"
        class="ps-4 pt-4"
        style="font-weight: initial; font-size: initial"
        @click="getReplies"
      >
        Afficher plus
      </VLink>
      <div v-if="displayReplies">
        <ReplyCard
          v-for="reply in replies"
          :key="reply.id"
          :message="reply"
          :operation-id="operation?.id"
          :documents-available="documentsAvailable"
          class="mt-2"
        />
      </div>
    </VCardText>
    <VDivider />
    <VCardActions>
      <VLink size="small" icon="mdi-message-reply-text" style="font-weight: initial; font-size: initial" @click="reply">
        Répondre
      </VLink>
    </VCardActions>
    <CommentaireDialog
      v-model="commentaireDialog"
      :operation="operation"
      :operation-group-id="operationGroupId"
      :advised-recipient="advisedRecipient"
      :parent-id="replyingTo"
      @send="reload"
      @close="commentaireDialog = false"
    />
  </VCard>
</template>
<script setup lang="ts">
import { displayFullnameUser } from '@/types/user'
import { formatHumanReadableLocalDateTime } from '@/types/date'
import type { PropType } from 'vue'
import type { Message } from '@/types/message'
import CommentaireDialog from '../operation/dialog/CommentaireDialog.vue'
import { VCardActions, type VSpacer } from 'vuetify/components'
import type { EnhancedDocument } from '@/types/document'
import { useSnackbarStore } from '@/stores/snackbar'
import type { Page } from '@/types/pagination'
import MetaMessageDisplay from '../MetaMessageDisplay.vue'
import ReplyCard from './ReplyCard.vue'
import type { Operation } from '@/types/operation'

const props = defineProps({
  isFinalVersionMessage: Boolean,
  operation: Object as PropType<Operation>,
  operationGroupId: Number,
  emmyId: Number,
  documentsAvailable: Object as PropType<Page<EnhancedDocument>>,
  message: {
    type: Object as PropType<Message>,
    required: true,
  },
})

const emits = defineEmits<{
  treated: [any]
}>()
const advisedRecipient = ref<string[]>([])
const commentaireDialog = ref(false)
const displayReplies = ref(false)
const replyingTo = ref<number | undefined>(undefined)
const replies = ref<Message[]>([])
const repliesPage = ref(emptyValue<Page<Message>>())
const repliesPageNumber = ref(0)

const reply = () => {
  advisedRecipient.value = props.message.sender.email ? [props.message.sender.email] : []
  replyingTo.value = props.message.id
  commentaireDialog.value = true
}

const getReplies = async () => {
  await handleAxiosPromise(
    repliesPage,
    messageApi.getAll(
      { sort: ['sendDateTime,DESC'], size: 5, page: repliesPageNumber.value++ },
      { parentId: props.message.id }
    )
  )
  replies.value.splice(0, 0, ...repliesPage.value.value!.content.reverse())
}

const reload = async () => {
  commentaireDialog.value = false
  replies.value = []
  props.message.repliesNumber!++
  await handleAxiosPromise(
    repliesPage,
    messageApi.getAll(
      { sort: ['sendDateTime,DESC'], size: repliesPageNumber.value * 5, page: 0 },
      { parentId: props.message.id }
    )
  )
  replies.value.push(...repliesPage.value.value!.content.reverse())
}

const snackbarStore = useSnackbarStore()
const downloadDocumentFile = (item: EnhancedDocument) => {
  if (props.operation) {
    operationDocumentApi
      .download(item.id)
      .then((response) => {
        downloadFile(item.document.originalFilename, response.data)
        snackbarStore.setSuccess('Le téléchargement a réussi')
      })
      .catch(() =>
        snackbarStore.setError(
          'Le téléchargement du fichier a échoué. Aviez-vous envoyé ce fichier sur boCEE ? Si oui, veuillez nous contacter.'
        )
      )
  } else {
    operationsGroupDocumentApi
      .download(item.id)
      .then((response) => {
        downloadFile(item.document.originalFilename, response.data)
        snackbarStore.setSuccess('Le téléchargement a réussi')
      })
      .catch(() =>
        snackbarStore.setError(
          'Le téléchargement du fichier a échoué. Aviez-vous envoyé ce fichier sur boCEE ? Si oui, veuillez nous contacter.'
        )
      )
  }
}

watch(commentaireDialog, (v) => {
  if (!v) {
    replyingTo.value = undefined
  }
})

watch(displayReplies, (v) => {
  if (v && replies.value.length === 0) {
    getReplies()
  }
})
</script>
