import type { Page } from '@/types/pagination'
import type { User } from '@/types/user'
import { defineStore } from 'pinia'

export const useCommentStore = defineStore('comment', () => {
  const recipients = ref<User[]>([])
  const siege = ref<Page<User>>(makeEmptyPage())

  const load = (operationId?: number) => {
    if (recipients.value) {
      userApi
        .getAllWithSameEntity({ operationId })
        .then((response) => (recipients.value = response.data))
        .catch((e) => logException(e))
    }
    if (siege.value) {
      userApi
        .getAll(
          { size: 100 },
          { roles: ['SIEGE', 'INSTRUCTEUR', 'ADMIN', 'ADMIN_PLUS'], operationId, withoutSelf: true, active: true }
        )
        .then((response) => (siege.value = response.data))
        .catch((e) => logException(e))
    }
  }

  const clear = () => {
    recipients.value = []
    siege.value = makeEmptyPage()
  }

  return { recipients, siege, load, clear }
})
