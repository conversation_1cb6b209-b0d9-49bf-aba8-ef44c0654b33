<template>
  <VContainer fluid>
    <VCard>
      <VCardText class="pa-0">
        <VRow class="flex-column" no-gutters>
          <VCol>
            <NjExpansionPanel title="Document">
              <VRow class="flex-column" dense>
                <VCol v-for="config in documentAdminConfigurations" :key="config.name">
                  <DocumentAdminValueView :model-value="config" @save="load" />
                </VCol>
              </VRow>
            </NjExpansionPanel>
          </VCol>
          <VDivider />
          <VCol>
            <NjExpansionPanel title="Arrêté contrôle">
              <VRow v-for="config in controlOrdersAdminConfigurations" :key="config.name" dense>
                <VCol cols="3">
                  <span> {{ config.name }} : </span>
                  <VTextField
                    v-model="config.data"
                    :type="config.type.valueOf() === 'NUMERIC' ? 'number' : 'text'"
                    :placeholder="config.description"
                  />
                </VCol>
                <VCol cols="2" align-self="end">
                  <NjBtn color="primary" @click="send(config)">Enregistrer</NjBtn>
                </VCol>
              </VRow>
            </NjExpansionPanel>
          </VCol>
          <VDivider />
          <VCol>
            <NjExpansionPanel title="Lien">
              <VRow v-for="config in linkAdminConfigurations" :key="config.name" dense>
                <VCol cols="3">
                  <span> {{ config.name }} : </span>
                  <VTextField
                    v-model="config.data"
                    :type="config.type.valueOf() === 'NUMERIC' ? 'number' : 'text'"
                    :placeholder="config.description"
                  />
                </VCol>
                <VCol cols="2" align-self="end">
                  <NjBtn @click="send(config)">Enregistrer</NjBtn>
                </VCol>
              </VRow>
            </NjExpansionPanel>
          </VCol>
          <VDivider />
          <VCol>
            <NjExpansionPanel title="Type de document">
              <VRow v-for="config in documentTypeAdminConfigurations" :key="config.name" dense>
                <VCol v-if="config.name == 'VF document type id'" cols="3">
                  <RemoteAutoComplete
                    v-model="vfId"
                    label="Document VF à envoyer au bénéficiaire"
                    :query-for-one="(id) => documentTypeApi.getOne(id)"
                    :query-for-all="(v) => documentTypeApi.getAll({}, { search: v })"
                    item-value="id"
                    item-title="name"
                  />
                </VCol>
                <VCol v-else-if="config.name == 'Convention document type id'" cols="3">
                  <RemoteAutoComplete
                    v-model="conventionId"
                    label="Convention à envoyer aux profils simulations"
                    :query-for-one="(id) => documentTypeApi.getOne(id)"
                    :query-for-all="(v) => documentTypeApi.getAll({}, { search: v })"
                    item-value="id"
                    item-title="name"
                  />
                </VCol>
                <VCol v-else-if="config.name == 'Attestation sur l\'honneur document type id'" cols="3">
                  <RemoteAutoComplete
                    v-model="swornStatementId"
                    label="Attestation sur l'honneur"
                    :query-for-one="(id) => documentTypeApi.getOne(id)"
                    :query-for-all="(v) => documentTypeApi.getAll({}, { search: v })"
                    item-value="id"
                    item-title="name"
                  />
                </VCol>
                <VCol v-else-if="config.name == 'PV de reception document type id'" cols="3">
                  <RemoteAutoComplete
                    v-model="pvDeReceptionId"
                    label="PV de réception"
                    :query-for-one="(id) => documentTypeApi.getOne(id)"
                    :query-for-all="(v) => documentTypeApi.getAll({}, { search: v })"
                    item-value="id"
                    item-title="name"
                  />
                </VCol>
                <VCol cols="2" align-self="end">
                  <NjBtn color="primary" @click="send(config)">Enregistrer</NjBtn>
                </VCol>
              </VRow>
            </NjExpansionPanel>
          </VCol>
          <VDivider />
          <VCol>
            <NjExpansionPanel title="Alertes validation étape 50">
              <VRow v-for="config in alertStep50AdminConfigurations" :key="config.name" dense>
                <VCol cols="3">
                  <span> {{ config.name }} : </span>
                  <VTextField
                    v-if="config.type.valueOf() === 'NUMERIC'"
                    v-model="config.data"
                    :type="'number'"
                    :placeholder="config.description"
                  />
                  <VTextarea v-else v-model="config.data" :placeholder="config.description" />
                </VCol>
                <VCol cols="2" align-self="end">
                  <NjBtn @click="send(config)">Enregistrer</NjBtn>
                </VCol>
              </VRow>
            </NjExpansionPanel>
          </VCol>
          <VDivider />
          <VCol>
            <NjExpansionPanel title="Autres">
              <VRow v-for="config in otherAdminConfigurations" :key="config.name" dense>
                <VCol v-if="config.name == 'VF document type id'" cols="3">
                  <RemoteAutoComplete
                    v-model="vfId"
                    label="Document VF à envoyer au bénéficiaire"
                    :query-for-one="(id) => documentTypeApi.getOne(id)"
                    :query-for-all="(v) => documentTypeApi.getAll({}, { search: v })"
                    item-value="id"
                    item-title="name"
                  />
                </VCol>
                <VCol v-else-if="config.name == 'Convention document type id'" cols="3">
                  <RemoteAutoComplete
                    v-model="conventionId"
                    label="Convention à envoyer aux profils simulations"
                    :query-for-one="(id) => documentTypeApi.getOne(id)"
                    :query-for-all="(v) => documentTypeApi.getAll({}, { search: v })"
                    item-value="id"
                    item-title="name"
                  />
                </VCol>
                <VCol v-else cols="3">
                  <span> {{ config.name }} : </span>
                  <VTextField
                    v-model="config.data"
                    :type="config.type.valueOf() === 'NUMERIC' ? 'number' : 'text'"
                    :placeholder="config.description"
                  />
                </VCol>
                <VCol cols="2" align-self="end">
                  <NjBtn color="primary" @click="send(config)">Enregistrer</NjBtn>
                </VCol>
              </VRow>
            </NjExpansionPanel>
          </VCol>

          <VCol>
            <NjExpansionPanel title="Extraire et importer les documents d'administration">
              <VRow class="flex-column" dense>
                <VCol>
                  <NjBtn :loading="downloadAllDocumentsLoading" @click="downloadAllTemplate">
                    Télécharger tout les modèles
                  </NjBtn>
                </VCol>
                <template v-if="displayUploadAdminDocument === 'true'">
                  <VCol cols="6">
                    Uploader les documents d'administration:

                    <NjFileInput
                      v-if="!zipFile"
                      @new-files="
                        (event) => {
                          zipFile = event[0]
                        }
                      "
                    />
                    <template v-else>
                      <i>{{ zipFile.name }}</i>
                      <NjIconBtn
                        color="primary"
                        icon="mdi-delete"
                        :loading="uploadLoading"
                        @click="zipFile = null"
                      ></NjIconBtn>
                      <NjBtn @click="upload">Uploader</NjBtn>
                    </template>
                  </VCol>
                </template>
              </VRow>
            </NjExpansionPanel>
          </VCol>
        </VRow>
      </VCardText>
    </VCard>
  </VContainer>
</template>

<script lang="ts" setup>
import { adminconfigurationApi } from '@/api/adminConfiguration'
import { useSnackbarStore } from '@/stores/snackbar'
import type { AdminConfiguration } from '@/types/adminConfiguration'
import { documentTypeApi } from '@/api/documentType'
import RemoteAutoComplete from '@/components/RemoteAutoComplete.vue'
import { VCard, VCardText, VCol, VContainer, VRow, type VDivider } from 'vuetify/components'
import DocumentAdminValueView from '@/views/DocumentAdminValueView.vue'
import { awaitResponse } from '@/types/responseHandler'

const snackbarStore = useSnackbarStore()

const data = ref(emptyValue<AdminConfiguration[]>())

const vfId = ref(0)
const conventionId = ref(0)
const swornStatementId = ref(0)
const pvDeReceptionId = ref(0)

const load = () => {
  data.value.loading = true
  handleAxiosPromise(
    data,
    adminconfigurationApi.getAll(),
    (response) => {
      data.value.value = response.data.sort((a, b) => (a.name < b.name ? 1 : a.name > b.name ? -1 : 0))
      const vfAdminConfiguration = data.value.value.find((c) => c.name == 'VF document type id')!
      vfId.value = parseInt(vfAdminConfiguration.data)
      conventionId.value = parseInt(data.value.value.find((c) => c.name == 'Convention document type id')!.data)
      swornStatementId.value = parseInt(
        data.value.value.find((c) => c.name == "Attestation sur l'honneur document type id")!.data
      )
      pvDeReceptionId.value = parseInt(data.value.value.find((c) => c.name == 'PV de reception document type id')!.data)
    },
    () => {
      snackbarStore.setError('Une erreur est survenue pendant la récupération des paramètres administrateur')
    }
  )
}

const send = (config: AdminConfiguration) => {
  if (config.name == 'VF document type id') {
    config.data = vfId.value.toString()
  }
  if (config.name == 'PV de reception document type id') {
    config.data = pvDeReceptionId.value.toString()
  }
  if (config.name == "Attestation sur l'honneur document type id") {
    config.data = swornStatementId.value.toString()
  }
  adminconfigurationApi
    .save(config, null)
    .then(() => snackbarStore.setSuccess('Vos modifications ont bien été sauvegardé'))
    .catch((exception) => {
      snackbarStore.setError("Une erreur est survenue lors de l'enregistrement de vos modifications")
      logException(exception)
    })
}

watch(
  data,
  () => {
    load()
  },
  {
    immediate: true,
  }
)

const timerControlOrder = [
  'Alerte délai de validation de l étape 70A',
  'Alerte validation étape 70 dans un lot de contrôle',
  'Alerte délai de validation étape 75C à 75D pour les opérations non satisfaisantes',
  'Alerte délai de validation étape 75 à 80',
  'Alerte délai de validation étape 75 à 75C pour les opérations non satisfaisantes',
  'Alerte délai de validation de l étape 75D à 80 pour les opérations non satisfaisantes',
]

const link = ['Lien précarité', 'Lien cumul', 'Lien PBI TDB operationnel', 'Lien Yammer', 'Lien Sharepoint']
const document = [
  'Document des conditions d éligibilités CPE',
  'Document des conditions d éligibilités au coup de pouce',
]

const documentType = [
  'VF document type id',
  'Convention document type id',
  'PV de reception document type id',
  "Attestation sur l'honneur document type id",
]

const alertStep50 = [
  "Première relance pour valider l'étape 50",
  "Première relance pour valider l'étape 50 corps du mail",
  "Deuxième relance pour valider l'étape 50",
  "Deuxième relance pour valider l'étape 50 corps du mail",
]
const documentTypeAdminConfigurations = computed(() => data.value.value?.filter((i) => documentType.includes(i.name)))

const controlOrdersAdminConfigurations = computed(() =>
  data.value.value?.filter((i) => timerControlOrder.includes(i.name))
)

const documentAdminConfigurations = computed(() => data.value.value?.filter((i) => document.includes(i.name)))

const linkAdminConfigurations = computed(() => data.value.value?.filter((i) => link.includes(i.name)))

const alertStep50AdminConfigurations = computed(() => data.value.value?.filter((i) => alertStep50.includes(i.name)))

const otherAdminConfigurations = computed(() =>
  data.value.value?.filter(
    (i) =>
      !timerControlOrder.includes(i.name) &&
      !link.includes(i.name) &&
      !document.includes(i.name) &&
      !documentType.includes(i.name) &&
      !alertStep50.includes(i.name)
  )
)

const downloadAllDocumentsLoading = ref(false)
const VITE_APP_ENV = import.meta.env.VITE_APP_ENV
const downloadAllTemplate = () => {
  downloadAllDocumentsLoading.value = true
  adminDocumentApi
    .downloadAll()
    .then(async (response) => {
      await awaitResponse(response.data)
        .then(async (response) => {
          downloadFile(
            `document administration ${VITE_APP_ENV}.zip`,
            (await responseHandlerApi.download(response.uuid)).data
          )
          snackbarStore.setSuccess('Le téléchargement des documents a réussi')
        })
        .catch((response) => {
          snackbarStore.setError(response.errorMessage ?? 'Le téléchargement des documents a échoué')
        })
    })
    .catch(async (err) => snackbarStore.setError(await handleAxiosException(err)))
    .finally(() => {
      downloadAllDocumentsLoading.value = false
    })
}

const zipFile = ref<File | null>(null)

const uploadLoading = ref(false)
const upload = () => {
  uploadLoading.value = true
  adminDocumentApi
    .uploadAll(zipFile.value!)
    .then(async (response) => {
      awaitResponse(response.data)
        .then(async () => {
          snackbarStore.setSuccess('Les documents ont bien été uploadé')
        })
        .catch((response) => {
          snackbarStore.setError(response.errorMessage ?? 'Le téléchargement des documents a échoué')
        })
        .finally(() => {
          uploadLoading.value = false
        })
    })
    .catch(async (err) => {
      uploadLoading.value = false
      snackbarStore.setError(await handleAxiosException(err))
    })
}

const displayUploadAdminDocument = import.meta.env.VITE_FEATURE_UPLOAD_ADMIN_DOCUMENT
</script>
