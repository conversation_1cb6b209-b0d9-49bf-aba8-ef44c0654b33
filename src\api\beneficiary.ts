import type { AxiosInstance, AxiosPromise } from 'axios'
import axiosInstance from '.'
import type { Beneficiary, BeneficiaryRequest } from '@/types/beneficiary'
import type { Page, Pageable } from '@/types/pagination'

const beneficiaryUrl = '/beneficiaries'

export interface BeneficiaryFilter {
  search?: string
  ids?: number[]
}

class BeneficiaryApi {
  public constructor(private axios: AxiosInstance) {}

  public create(request: BeneficiaryRequest): AxiosPromise<Beneficiary> {
    return this.axios.post(beneficiaryUrl, request)
  }

  public findAll(pageable: Pageable, filter: BeneficiaryFilter): AxiosPromise<Page<Beneficiary>> {
    return this.axios.get(beneficiaryUrl, {
      params: { ...filter, ...pageable },
    })
  }

  public update(id: number, request: BeneficiaryRequest): AxiosPromise<Beneficiary> {
    return this.axios.put(beneficiaryUrl + '/' + id, request)
  }

  public findOne(id: number): AxiosPromise<Beneficiary> {
    return this.axios.get(beneficiaryUrl + '/' + id)
  }
}

export const beneficiaryApi = new BeneficiaryApi(axiosInstance)
