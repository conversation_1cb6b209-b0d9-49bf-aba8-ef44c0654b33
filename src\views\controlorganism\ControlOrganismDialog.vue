<template>
  <VDialog
    v-model="activeDialog"
    :width="displayMode === 'select' ? '70%' : '40%'"
    :height="displayMode === 'select' ? '100%' : ''"
  >
    <ControlOrganismOneView
      v-if="displayMode === 'form'"
      :id="selected[0]?.id ?? 0"
      v-model:updated-control-organism="updatedControlOrganism"
      :mode="formMode"
      :cancel="displayAll"
      :after-success="displayAll"
    >
      <template #title> Gestion d'un organisme de contrôle </template>
    </ControlOrganismOneView>
    <ControlOrganismDisplayValue
      v-else-if="displayMode === 'display'"
      :model-value="selected[0]"
      expanded
      with-title
      with-actions
    >
      <template #title> Détail d'un organisme de contrôle</template>
      <template #actions> <VSpacer /> <NjBtn @click="displayAll">Ok</NjBtn> </template>
    </ControlOrganismDisplayValue>

    <VCard v-else :class="displayMode === 'select' ? 'content-layout' : ''">
      <VCardTitle style="display: flex; align-items: center" class="content-layout__header">
        <span style="display: flex; width: 100%">
          {{ displayMode === 'select' ? "Choix d'un organisme de contrôle" : "Détail d'un organisme de contrôle" }}
        </span>
        <NjIconBtn icon="mdi-window-close" variant="flat" @click="clear" />
      </VCardTitle>
      <VDivider />
      <VCardText :class="displayMode === 'select' ? 'content-layout content-layout__main' : ''">
        <div class="content-layout">
          <VRow class="content-layout__header">
            <VCol>
              <SearchInput v-model="search" :loading="loading" />
            </VCol>
            <VSpacer />
            <VCol v-if="userStore.isAdminPlus" class="flex-grow-0">
              <NjBtn :disabled="!selected[0]" @click="duplicateControlOrganism"> Dupliquer </NjBtn>
            </VCol>
            <VCol v-if="userStore.isAdminPlus" class="flex-grow-0">
              <NjBtn :disabled="!canEditControlOrganism" @click="editControlOrganism"> Editer </NjBtn>
            </VCol>
            <VCol class="flex-grow-0">
              <NjBtn :disabled="!selected[0]" @click="displayMode = 'display'"> Visualiser </NjBtn>
            </VCol>
            <VCol v-if="userStore.isAdminPlus" class="flex-grow-0">
              <NjBtn @click="addControlOrganism"> Créer </NjBtn>
            </VCol>
          </VRow>
          <ControlOrganismAllView
            v-model="selected"
            v-model:loading="loading"
            class="content-layout__main"
            :search="search"
            fixed
          />
        </div>
      </VCardText>
      <VCardActions style="border-top: lightgrey 1px solid" class="content-layout__footer">
        <VSpacer />
        <NjBtn variant="outlined" @click="clear"> Annuler </NjBtn>
        <NjBtn :disabled="selected.length === 0" @click="selectControlOrganism">Enregistrer</NjBtn>
      </VCardActions>
    </VCard>
  </VDialog>
</template>
<script lang="ts" setup>
import type { ControlOrganism } from '@/types/controlOrganism'
import type { PropType } from 'vue'
import ControlOrganismAllView from './ControlOrganismAllView.vue'
import ControlOrganismOneView from './ControlOrganismOneView.vue'
import { useUserStore } from '@/stores/user'
import ControlOrganismDisplayValue from '@/components/ControlOrganismDisplayValue.vue'

const props = defineProps({
  modelValue: {
    type: Boolean,
  },
  controlOrganism: {
    type: Object as PropType<ControlOrganism | null>,
  },
})

const emit = defineEmits(['update:model-value', 'update:control-organism'])
const userStore = useUserStore()
const selected = ref<ControlOrganism[]>([])
const search = ref('')
const displayMode = ref('select')
const formMode = ref('create')
const loading = ref(false)

const activeDialog = computed<boolean>({
  get() {
    return props.modelValue
  },
  set(v) {
    emit('update:model-value', v)
  },
})

const displayAll = () => {
  displayMode.value = 'select'
  selected.value = []
}

const addControlOrganism = () => {
  selected.value = []
  displayMode.value = 'form'
  formMode.value = 'create'
}

const editControlOrganism = () => {
  displayMode.value = 'form'
  formMode.value = 'edit'
}

//const dialogStore = useDialogStore()

const selectControlOrganism = () => {
  /*
  if (!(selected.value[0].startDate && selected.value[0].endDate)) {
    dialogStore.addAlert2({
      title: 'Impossible de sélectionner cet organisme de contrôle',
      message: "Veuillez renseigner les date d'éligibilité de l'organisme de contrôle avant de le sélectionner",
      maxWidth: '640px',
    })
    return
  }

  if (selected.value[0].startDate && selected.value[0].endDate) {
    const startDate = parseISO(selected.value[0].startDate)
    const endDate = parseISO(selected.value[0].endDate)
    const now = new Date()
    if (!(isAfter(now, startDate) && isBefore(now, endDate))) {
      dialogStore.addAlert2({
        title: 'Impossible de sélectionner cet organisme de contrôle',
        message:
          "L'organisme de contrôle choisi n'est plus dans les accords cadre, et ne peut plus assurer de prestation de contrôle pour ENGIE Solutions. Vous devez sélectionner un organisme de contrôle bénéficiant d'accord cadre.",
        maxWidth: '640px',
      })
      return
    }
  }
  */
  emit('update:control-organism', selected.value[0])
  selected.value = []
  emit('update:model-value', false)
}

const duplicateControlOrganism = () => {
  displayMode.value = 'form'
  formMode.value = 'duplicate'
}

const clear = () => {
  emit('update:model-value', false)
  selected.value = []
}

const canEditControlOrganism = computed(() => {
  if (selected.value[0]) {
    if (selected.value[0].certified) {
      return userStore.isSiege
    } else {
      return true
    }
  }
  return false
})

watch(
  () => props.modelValue,
  (v) => {
    if (v) {
      displayMode.value = 'select'
      selected.value = props.controlOrganism ? [props.controlOrganism] : []
    }
  }
)

const updatedControlOrganism = ref<ControlOrganism | null>()

watch(updatedControlOrganism, (v) => {
  if (v && v.id == props.controlOrganism?.id) {
    emit('update:control-organism', v)
  }
})
</script>
