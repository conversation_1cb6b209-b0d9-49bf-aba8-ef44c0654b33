import type { Page, Pageable } from '@/types/pagination'
import type { AxiosInstance, AxiosPromise } from 'axios'
import axiosInstance from '.'
import type { ControlOrderExportTemplate, ControlOrderExportTemplateRequest } from '@/types/controlOrderExportTemplate'

const uri = '/control_order_export_templates'

export type ControlOrderExportTemplateFilter = Partial<{
  search: string
  ids: number[]
}>
export class ControlOrderExportTemplateApi {
  public constructor(private axios: AxiosInstance) {}

  public findAll(
    pageable: Pageable,
    filter: ControlOrderExportTemplateFilter
  ): AxiosPromise<Page<ControlOrderExportTemplate>> {
    return this.axios.get(uri, {
      params: { ...pageable, ...filter },
    })
  }

  public findOne(id: number): AxiosPromise<ControlOrderExportTemplate> {
    return this.axios.get(uri + '/' + id)
  }

  public save(id: number, controlOrderExportTemplate: ControlOrderExportTemplateRequest) {
    return this.axios.put(uri + '/' + id, controlOrderExportTemplate)
  }

  public create(controlOrderExportTemplate: ControlOrderExportTemplateRequest) {
    return this.axios.post(uri, controlOrderExportTemplate)
  }

  public testExport(controlOrderExportTemplateId: number, operationId: number): AxiosPromise<Blob> {
    return this.axios.post(
      `${uri}/${controlOrderExportTemplateId}/operation/${operationId}/test_export`,
      {},
      {
        responseType: 'blob',
      }
    )
  }
}

export const controlOrderExportTemplateApi = new ControlOrderExportTemplateApi(axiosInstance)
