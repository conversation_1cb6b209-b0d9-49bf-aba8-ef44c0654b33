import type { AdminConfiguration } from '@/types/adminConfiguration'
import type { AxiosInstance, AxiosPromise } from 'axios'
import axiosInstance from '.'

class AdminConfigurationApi {
  public constructor(private axios: AxiosInstance) {}

  public async save(
    adminConfiguration: AdminConfiguration,
    file: File | null
  ): Promise<AxiosPromise<AdminConfiguration>> {
    const formData = new FormData()
    if (file) {
      formData.append('file', file, file.name)
      const hash = await hashFile(file)
      formData.append('hash', hash)
    }

    const request = JSON.stringify(engieFormatRequestTransformKey(adminConfiguration))
    const blob = new Blob([request], {
      type: 'application/json',
    })
    formData.append('request', blob)

    return this.axios.post('/admin_configurations', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    })
  }

  public getAll(): AxiosPromise<AdminConfiguration[]> {
    return this.axios.get('/admin_configurations')
  }

  public getOne(name: string): AxiosPromise<AdminConfiguration> {
    return this.axios.get(`/admin_configurations/${name}`)
  }

  public download(name: string): AxiosPromise<Blob> {
    return this.axios.get(`/admin_configurations/${name}/file`, {
      responseType: 'blob',
    })
  }
}

export const adminconfigurationApi = new AdminConfigurationApi(axiosInstance)
