import { format, isDate, parse, parseISO } from 'date-fns'

export type LocalDate = string

export const dateHumanFormat = 'dd/MM/yyyy'
export const dateTimeHumanFormat = 'dd/MM/yyyy à HH:mm'

export const parseHumanReadableLocalDate = (v: string) => parse(v, dateHumanFormat, 0)
export const parseHumanReadableLocalDateTime = (v: string) => parseISO(v)
export const formatHumanReadableLocalDate = (v: string | undefined | null | Date, noValue = '--/--/----'): string => {
  return v && v !== '-' ? format(isDate(v) ? v : parseISO(v), dateHumanFormat) : noValue
}

export const formatHumanReadableLocalDateTime = (v: string | undefined | null, noValue = '--/--/----'): string =>
  v ? format(parseISO(v), dateTimeHumanFormat) : noValue

export const localDateFormat = 'yyyy-MM-dd'
export const parseLocalDate = (v: LocalDate): Date => parse(v, localDateFormat, 0)
export const parseLocalDateTime = (v: LocalDateTime): Date => parseISO(v)

export type LocalDateTime = string

export const getMonth = (date: LocalDate) => {
  return months.find((m) => m.value == new Date(date).getMonth() + 1)?.title
}

export const getYear = (date: LocalDate) => {
  return new Date(date).getFullYear()
}
export const formatLocalDate = (date: Date) => {
  return format(date, 'yyyy-MM-dd')
}

const months = [
  {
    title: 'Janvier',
    value: 1,
  },
  {
    title: 'Février',
    value: 2,
  },
  {
    title: 'Mars',
    value: 3,
  },
  {
    title: 'Avril',
    value: 4,
  },
  {
    title: 'Mai',
    value: 5,
  },
  {
    title: 'Juin',
    value: 6,
  },
  {
    title: 'Juillet',
    value: 7,
  },
  {
    title: 'Aout',
    value: 8,
  },
  {
    title: 'Septembre',
    value: 9,
  },
  {
    title: 'Octobre',
    value: 10,
  },
  {
    title: 'Novembre',
    value: 11,
  },
  {
    title: 'Décembre',
    value: 12,
  },
]
