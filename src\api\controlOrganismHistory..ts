import type { AxiosInstance, AxiosPromise } from 'axios'
import axiosInstance from '.'
import type { Page, Pageable } from '@/types/pagination'
import type { ControlOrganismHistory } from '@/types/history'

const controlOrganismUrl = '/control_organism_histories'

export interface ControlOrganismHistoryFilter {
  controlOrganismId?: number
}

class ControlOrganismHistoryApi {
  public constructor(private axios: AxiosInstance) {}

  public findAll(pageable: Pageable, filter: ControlOrganismHistoryFilter): AxiosPromise<Page<ControlOrganismHistory>> {
    return this.axios.get(controlOrganismUrl, {
      params: { ...filter, ...pageable },
    })
  }
}

export const controlOrganismHistoryApi = new ControlOrganismHistoryApi(axiosInstance)
