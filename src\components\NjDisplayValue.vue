<template>
  <div v-ripple="editable" class="w-100" style="display: flex; align-items: center" :class="{ clickable: editable }">
    <VSpacer v-if="align === 'end'" />
    <div v-show="label" class="nj-display-value__label" :class="labelClass + ' text-' + colorTitle">
      {{ label }}
    </div>
    <VSpacer v-if="align === 'justify'" />
    <slot name="value">
      <VIcon v-if="checkbox" :icon="value ? 'mdi-checkbox-marked-outline' : 'mdi-checkbox-blank-outline'" />
      <div v-else class="nj-display-value__value" :class="'text-' + colorValue + ' ' + valueClass">{{ value }}</div>
    </slot>
    <VIcon v-if="editable" icon="mdi-pen" size="large" class="ms-2" color="primary"></VIcon>
  </div>
</template>

<script setup lang="ts">
import type { PropType } from 'vue'

defineProps({
  label: {
    type: String,
    required: true,
  },
  value: {},
  colorValue: {
    type: String,
    default: 'black',
  },
  colorTitle: {
    type: String,
    default: '#60798b',
  },
  align: {
    type: String as PropType<'justify' | 'end'>,
    default: 'justify',
  },
  labelClass: String,
  valueClass: String,
  checkbox: Boolean,
  editable: Boolean,
  disposition: {
    type: String as PropType<'default' | '2col'>,
    default: 'default',
  },
})
</script>

<style lang="scss">
@import '@/assets/main.scss';

.nj-display-value {
  &__label {
    @extend .value__label;
    align-self: start;
  }

  &__value {
    @extend .value__value;
    text-align: end;
  }
}
</style>
