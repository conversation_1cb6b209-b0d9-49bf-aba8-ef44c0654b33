<template>
  <VAutocomplete
    v-model="input"
    :error-messages="errorMessage"
    :rules="[() => errorMessage ?? true]"
    :items="countryItems"
    @update:model-value="emit('update:model-value', $event)"
  >
    <!-- <template #item="{ item }">
      {{ item.title }}</template> -->
  </VAutocomplete>
</template>

<script setup lang="ts">
import { VAutocomplete } from 'vuetify/components'

const props = withDefaults(
  defineProps<{
    modelValue: string | null
    required?: boolean
  }>(),
  { required: false }
)

const emit = defineEmits<{
  'update:model-value': [string]
}>()

const input = ref<string | null>(null)
const errorMessage = computed(() => {
  // if (!pn.value) {
  return props.required ? 'Champs requis' : undefined
  // }
})

watch(
  () => props.modelValue,
  (v) => {
    input.value = v
  },
  {
    immediate: true,
  }
)

const countryItems = computed(() => {
  const names = countries.getNames('fr')
  const codes = countries.getAlpha2Codes()
  return Object.keys(codes).map((k) => ({ value: codes[k], title: names[k] }))
})
</script>
