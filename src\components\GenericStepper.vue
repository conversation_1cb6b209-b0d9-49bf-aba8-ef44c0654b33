<template>
  <div class="d-flex">
    <div
      v-for="(step, index) in steps"
      :key="step"
      class="stepper__step"
      :class="{
        'stepper__step--passed': index < currentStepIndex,
        'stepper__step--current': index === currentStepIndex,
      }"
    >
      <div class="w-100 d-flex align-center">
        <VIcon :icon="index === currentStepIndex ? 'mdi-circle-outline' : 'mdi-circle'" size="small" />

        <hr v-if="index < (steps?.length ?? 0) - 1" />
      </div>
      <div class="d-flex flex-column font-weight-bold" :class="index < currentStepIndex ? 'text-primary' : ''">
        <div class="stepper__number">{{ step }}</div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
const props = defineProps({
  steps: Array as PropType<string[]>,
  currentStep: String,
})

const currentStepIndex = computed(() => props.steps?.findIndex((i) => i === props.currentStep) ?? 0)
</script>

<style scoped lang="scss">
.stepper {
  &__step {
    flex-grow: 1;
    color: #c9c9c9;

    .v-icon {
      color: #778c9b;
    }
    hr {
      flex-grow: 1;
      border: none;
      border-top: 4px dashed var(--color-dash, #c9c9c9);
    }
    &:last-child {
      flex-grow: 0;
    }

    &--passed {
      --color-dash: #009de9;

      .v-icon {
        color: #007acd;
      }
    }

    &--current {
      .stepper__number {
        font-size: 1.5rem;
        color: black;
      }
      color: #c9c9c9;

      .v-icon {
        color: #778c9b;
      }
    }
  }
}
</style>
