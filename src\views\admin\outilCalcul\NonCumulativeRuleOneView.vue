<template>
  <VRow class="flex-column">
    <VCol>
      <VRow>
        <VCol>
          <VTextField
            v-model="mutableNonCumulativeRule.operationCode1"
            label="Code opération"
            :rules="[requiredRule]"
          />
        </VCol>
        <VCol>
          <VTextField
            v-model="mutableNonCumulativeRule.operationCode2"
            label="Code opération"
            :rules="[requiredRule]"
          />
        </VCol>
      </VRow>
    </VCol>
    <VCol>
      <VRow>
        <VCol>
          <NjDatePicker v-model="mutableNonCumulativeRule.startDate" label="Date de début" />
        </VCol>
        <VCol>
          <NjDatePicker v-model="mutableNonCumulativeRule.endDate" label="Date de fin" />
        </VCol>
      </VRow>
    </VCol>
    <VCol>
      <VTextarea v-model="mutableNonCumulativeRule.description" label="Description"></VTextarea>
    </VCol>
  </VRow>
</template>
<script setup lang="ts">
import { requiredRule } from '@/types/rule'
import { type NonCumulativeRule } from '@/types/nonCumulativeRule'
import { VCol, VRow, VTextField, VTextarea } from 'vuetify/components'
import { cloneDeep } from 'lodash'

const props = defineProps({
  nonCumulativeRule: {
    type: Object as PropType<NonCumulativeRule>,
  },
})

const mutableNonCumulativeRule = ref<NonCumulativeRule>({
  id: 0,
  operationCode1: '',
  operationCode2: '',
  startDate: '',
  endDate: '',
  description: '',
})

watch(
  () => props.nonCumulativeRule,
  (v) => {
    if (v) {
      mutableNonCumulativeRule.value = cloneDeep(v)
    }
  },
  {
    immediate: true,
  }
)

defineExpose({
  mutableNonCumulativeRule: mutableNonCumulativeRule.value,
})
</script>
