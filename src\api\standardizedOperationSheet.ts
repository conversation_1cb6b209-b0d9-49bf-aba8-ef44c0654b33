import type {
  StandardizedOperationSheet,
  StandardizedOperationSheetFilter,
  StandardizedOperationSheetRequest,
} from '@/types/calcul/standardizedOperationSheet'
import type { Page, Pageable } from '@/types/pagination'
import type { AxiosInstance, AxiosPromise } from 'axios'
import axiosInstance from '.'
import type { Operation } from '@/types/operation'
import type { SwornStatementTemplateFrame } from '@/types/swornStatementTemplateFrame'

class StandardizedOperationSheetApi {
  public constructor(private axios: AxiosInstance) {}

  public create(
    standardizedOperationSheet: StandardizedOperationSheetRequest
  ): AxiosPromise<StandardizedOperationSheet> {
    return this.axios.post('/standardized_operation_sheets', standardizedOperationSheet)
  }

  public update(
    id: number,
    standardizedOperationSheet: StandardizedOperationSheetRequest
  ): AxiosPromise<StandardizedOperationSheet> {
    return this.axios.put('/standardized_operation_sheets/' + id, standardizedOperationSheet)
  }

  public findOne(id: number): AxiosPromise<StandardizedOperationSheet> {
    return this.axios.get('/standardized_operation_sheets/' + id)
  }

  public findAll(
    pageable: Pageable,
    filter?: StandardizedOperationSheetFilter
  ): AxiosPromise<Page<StandardizedOperationSheet>> {
    return this.axios.get('/standardized_operation_sheets', {
      params: { ...pageable, ...filter },
    })
  }

  public export() {
    return this.axios.get('/standardized_operation_sheets/export', {
      responseType: 'blob',
    })
  }

  public testExportEmmy(operation: Operation): AxiosPromise<Blob> {
    return this.axios.post('/standardized_operation_sheets/test_export_emmy', operation, {
      responseType: 'blob',
    })
  }

  public downloadPdfDocument(id: number): AxiosPromise<File> {
    return this.axios.get(`/standardized_operation_sheets/${id}/pdf_document/file`, {
      responseType: 'blob',
    })
  }

  public downloadInternalPdfDocument(id: number): AxiosPromise<File> {
    return this.axios.get(`/standardized_operation_sheets/${id}/internal_pdf_document/file`, {
      responseType: 'blob',
    })
  }

  public findAllTemplateFramesByStandardizedOperationSheet(id: number): AxiosPromise<SwornStatementTemplateFrame[]> {
    return this.axios.get(`/standardized_operation_sheets/${id}/sworn_statement_template_frames`)
  }
}

export const standardizedOperationSheetApi = new StandardizedOperationSheetApi(axiosInstance)
