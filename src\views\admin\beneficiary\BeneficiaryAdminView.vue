<template>
  <NjPage title expend-body v-bind="$attrs">
    <template #subtitle>
      <VRow>
        <VCol>
          <SearchInput v-model="searchBeneficiary" />
        </VCol>
        <VCol class="flex-grow-0">
          <NjBtn
            @click="
              () => {
                editBeneficiary = makeEmptyBeneficiary()
                createBeneficiaryDialog = true
              }
            "
          >
            Créer
          </NjBtn>
          <VDialog v-model="createBeneficiaryDialog" height="100%" width="50%">
            <BeneficiaryForm
              can-certified
              :beneficiary="editBeneficiary!"
              mode="create"
              :cancel="() => (createBeneficiaryDialog = false)"
              :after-success="
                () => {
                  createBeneficiaryDialog = false
                  beneficiaryAllViewRef?.reload()
                }
              "
            />
          </VDialog>
        </VCol>
      </VRow>
    </template>
    <template #body>
      <BeneficiaryAllView
        ref="beneficiaryAllViewRef"
        v-model:selections="localSelected"
        v-model:loading="beneficiaryLoading"
        :search="searchBeneficiary"
        fixed
        class="w-100"
      />
    </template>
  </NjPage>

  <VNavigationDrawer location="end" :model-value="!!localSelected[0]" width="600" disable-resize-watcher>
    <BeneficiaryDisplayValue v-if="!edit" :model-value="localSelected[0]" with-title with-history>
      <template #title>
        <VRow dense>
          <VCol align-self="center"> Détail </VCol>
          <VCol class="flex-grow-0">
            <NjIconBtn
              icon="mdi-pencil"
              color="primary"
              rounded="0"
              @click="
                () => {
                  edit = true
                  mode = 'edit'
                  editBeneficiary = localSelected[0]
                }
              "
            />
          </VCol>
          <VCol class="flex-grow-0">
            <NjIconBtn
              icon="mdi-file-multiple"
              color="primary"
              style="margin-inline: 4px"
              rounded="0"
              @click="duplicateBeneficiary"
            />
          </VCol>

          <VCol class="flex-grow-0">
            <NjIconBtn icon="mdi-close" rounded="0" @click="localSelected = []" />
          </VCol>
        </VRow>
      </template>
    </BeneficiaryDisplayValue>
    <BeneficiaryForm
      v-else
      can-certified
      :beneficiary="editBeneficiary!"
      :mode="mode"
      :cancel="() => (edit = false)"
      :after-success="
        (beneficiary) => {
          edit = false
          beneficiaryAllViewRef?.reload()
          localSelected[0] = { ...beneficiary }
        }
      "
    />
  </VNavigationDrawer>
</template>
<script setup lang="ts">
import SearchInput from '@/components/SearchInput.vue'
import { type Beneficiary, makeEmptyBeneficiary } from '@/types/beneficiary'
import BeneficiaryAllView from '@/views/BeneficiaryAllView.vue'
import BeneficiaryForm, { type BeneficiaryFormAction } from '@/views/BeneficiaryForm.vue'
import { VCol, VDialog, VNavigationDrawer, VRow } from 'vuetify/components'

const beneficiaryAllViewRef = ref<typeof BeneficiaryAllView | null>(null)
const beneficiaryLoading = ref(false)
const localSelected = ref<Beneficiary[]>([])
const searchBeneficiary = ref<string>('')
const edit = ref(false)
const editBeneficiary = ref<Beneficiary | null>(null)
const mode = ref<BeneficiaryFormAction>()

const duplicateBeneficiary = () => {
  mode.value = 'duplicate'
  editBeneficiary.value = {
    ...localSelected.value[0],
    certified: false,
    phoneNumber: '',
    email: '',
    lastName: '',
    firstName: '',
    capacity: '',
  }
  edit.value = true
}

const createBeneficiaryDialog = ref(false)
</script>
