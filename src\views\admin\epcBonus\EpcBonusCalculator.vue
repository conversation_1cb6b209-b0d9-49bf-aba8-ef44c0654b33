<template>
  <VRow class="flex-column" dense>
    <template v-if="edit">
      <!-- eslint-disable-next-line vue/valid-v-for -->
      <VCol v-for="param in requiredParameters" :key="param.id">
        <NjSelect
          v-if="param.type === 'CHOICE'"
          v-model="values[param.id]"
          :label="param.label"
          :items="param.data.split(';')"
          :rules="[requiredRule]"
          required
        />
        <NjTextField
          v-else-if="param.type === 'NUMBER'"
          v-model="values[param.id]"
          :label="param.label"
          :suffix="param.suffix"
          :rules="param.id === 'eff' ? [requiredRule, numericRule, efficiencyRule] : [requiredRule, numericRule]"
          type="number"
          required
        />
      </VCol>
      <VCol v-for="param in optionalParameters" :key="param.id">
        <NjSelect
          v-if="param.type === 'CHOICE'"
          v-model="values[param.id]"
          :label="param.label"
          :items="param.data.split(';')"
          :rules="[requiredRule]"
          recommended
        />
        <NjTextField
          v-else-if="param.type === 'NUMBER'"
          v-model="values[param.id]"
          :label="param.label"
          :suffix="param.suffix"
          :rules="param.id === 'eff' ? [requiredRule, numericRule, efficiencyRule] : [requiredRule, numericRule]"
          type="number"
          recommended
        />
      </VCol>
    </template>
    <template v-else>
      <VCol v-for="param in requiredParameters" :key="param.id">
        <NjDisplayValue
          :label="param.label"
          :value="modelValue![param.id] + (param.suffix ? ' ' + param.suffix : '')"
        />
      </VCol>
      <VCol v-for="param in optionalParameters" :key="param.id">
        <NjDisplayValue
          :label="param.label"
          :value="modelValue![param.id] + (param.suffix ? ' ' + param.suffix : '')"
        />
      </VCol>
    </template>
    <VCol v-if="calculEpc.length > 1">
      <VRow no-gutters class="flex-column">
        <!-- eslint-disable-next-line vue/valid-v-for -->
        <VCol v-for="(v, i) in calculEpc">
          <NjDisplayValue :label="'Opération n°' + (i + 1)" :value="formatNumber(v) + ' kWhc'" />
        </VCol>
      </VRow>
    </VCol>
    <VCol>
      <div v-if="typeof calculEpcTotal === 'string'" style="text-align: end" class="text-error">
        {{ calculEpcTotal }}
      </div>
      <NjDisplayValue
        v-show="typeof calculEpcTotal === 'number'"
        label="Total après bonification CPE"
        :value="typeof calculEpcTotal === 'number' ? formatNumber(calculEpcTotal) + ' kWhc' : calculEpcTotal"
        align="end"
        color-value="primary"
      />
    </VCol>
  </VRow>
</template>
<script lang="ts" setup>
import NjDisplayValue from '@/components/NjDisplayValue.vue'
import type { EpcBonusSheet } from '@/types/epcBonus'
import { evaluateFormula, parsingFormula } from '@/types/calcul/formula'
import type { FormulaMappingTable } from '@/types/calcul/mappingTable'
import type { ParameterFormula } from '@/types/calcul/parameterFormula'
import type { PromisableValue } from '@/types/promisableValue'
import { numericRule, requiredRule } from '@/types/rule'
import { formatNumber } from '@/types/format'
import { cloneDeep, isEmpty, isEqual, sum } from 'lodash'
import type { PropType } from 'vue'

const props = defineProps({
  modelValue: {
    type: Object as PropType<Record<string, string | number> | null>,
    required: true,
  },
  epcBonusSheet: {
    type: Object as PropType<EpcBonusSheet>,
    required: true,
  },
  edit: Boolean,
  volumeCee: Array as PropType<Array<number>>,
})

const emit = defineEmits<{
  'update:model-value': [value: unknown]
  'update:b-cpe': [value: number]
}>()

const values = ref<any>({})
watch(
  () => props.modelValue,
  (v) => {
    if (!isEqual(v, values.value)) {
      values.value = cloneDeep(v)
    }
  },
  {
    immediate: true,
  }
)

const efficiencyRule = (v: string) =>
  !isNaN(parseInt(v)) && parseInt(v) >= (props.epcBonusSheet.minEfficiency ?? 0)
    ? true
    : `L'efficacité doit être supérieur ou égal à  ${props.epcBonusSheet.minEfficiency ?? 0}%`

const neededParameters = computed((): ParameterFormula[] => {
  let p: ParameterFormula[] = []
  p = p.concat(props.epcBonusSheet.parameters.filter((it) => !it.computedFormula))
  return p
})

const epcRate = ref<PromisableValue<number>>(succeedValue(0))
const calculEpc = computed((): number[] => {
  return (
    props.volumeCee?.map((it) => {
      return (epcRate.value.value ?? 0) * it
    }) ?? []
  )
})
const calculEpcTotal = computed((): number | string => {
  const volumeCee = props.volumeCee
  const epcRateValue = epcRate.value.value
  if (sum(volumeCee) > 0) {
    if (epcRateValue === 0) {
      return 'Veuillez renseigner tous les paramètres'
    } else {
      return sum(calculEpc.value)
    }
  } else {
    return sum(volumeCee)
  }
})

const combinations = ref<Record<string, string[][]>>({})
function generateCombinations(table: FormulaMappingTable) {
  const combinaisons: string[][] = []
  const args = table.paramColumns?.map((paramColumn) => {
    const parameter = props.epcBonusSheet.parameters.find((p) => paramColumn === p.id && p.type === 'CHOICE')
    if (parameter == null) {
      throw 'Vous devez sélectionner un paramètre de type choix'
    }
    return parameter
  })
  const allValues = args.map((it) => (it.data as string).split(';'))

  const iterators = cloneDeep(allValues)

  while (iterators.length > 0 && iterators[0].length > 0) {
    combinaisons.push(iterators.map((it) => it[0]))

    let iIterator = iterators.length - 1
    iterators[iIterator].shift()
    while (iterators[iIterator].length === 0 && iIterator > 0) {
      iterators[iIterator] = allValues[iIterator].concat()
      iIterator--
      iterators[iIterator].shift()
    }
  }
  combinations.value[table.id] = combinaisons
}

watch(
  () => props.epcBonusSheet.mappingTables,
  (v) => {
    const formulaTable = v.find((table) => table.id === 'Formules')
    if (formulaTable) {
      generateCombinations(formulaTable)
    }
  },
  {
    immediate: true,
  }
)

watch(
  values,
  async (v) => {
    if (v && !isEmpty(v)) {
      const newValues = cloneDeep(v)

      // Computed Parameters
      props.epcBonusSheet.parameters
        .filter((it) => it.computedFormula)
        .forEach((cp) => {
          const formula = parsingFormula(cp.computedFormula).value
          const result = evaluateFormula(formula, v)
          if (result.value != null) {
            newValues[cp.id] = result.value
          } else {
            logException(result.error)
          }
        })
      const formulaTable = props.epcBonusSheet.mappingTables.find((table) => table.id === 'Formules')
      let isDefined = !!formulaTable
      props.epcBonusSheet.mappingTables
        .find((table) => table.id === 'Formules')
        ?.paramColumns.forEach((name) => (isDefined = isDefined && !!newValues[name]))
      if (isDefined) {
        if ((newValues['eff'] as number) >= (props.epcBonusSheet.minEfficiency ?? 0)) {
          const formula =
            formulaTable!.data[
              combinations.value[formulaTable!.id].findIndex((item) =>
                isEqual(
                  item,
                  formulaTable?.paramColumns.map((it) => newValues[it])
                )
              )
            ]
          epcRate.value = evaluateFormula(parsingFormula(formula).value, newValues)
        } else {
          epcRate.value.value = 1
        }

        emit('update:b-cpe', epcRate.value.value!)
      }
      emit('update:model-value', newValues)
      return
    }
    emit('update:model-value', cloneDeep(v))
  },
  {
    immediate: true,
    deep: true,
  }
)

import { computed } from 'vue'

const requiredParameters = computed(() => {
  return neededParameters.value.filter((param) => !param.optional)
})

const optionalParameters = computed(() => {
  return neededParameters.value.filter((param) => param.optional)
})
</script>
