<template>
  <NjPage
    :title="'Gestion de CPE'"
    :error-message="saving.error"
    :can-go-back="{ name: 'EpcBonusSheetAllView' }"
    v-bind="$attrs"
  >
    <template #header-actions>
      <VRow class="flex-nowrap" dense>
        <VCol>
          <NjBtn variant="outlined" color="error"> Annuler </NjBtn>
        </VCol>
        <VCol>
          <NjBtn @click="save"> Enregistrer </NjBtn>
        </VCol>
      </VRow>
    </template>
    <template #after-title>
      <VIcon v-show="certified" color="#28B750" icon="mdi-shield-check-outline" />
    </template>
    <template #body>
      <VCard>
        <VForm ref="formRef">
          <NjExpansionPanel title="Détails du CPE">
            <VRow>
              <VCol cols="12" md="6">
                <VTextField
                  v-model="epcBonusSheet.value!.targets"
                  label="Opérations concernées"
                  hint="Séparées par des ';'. Saisir les 3 premières lettres"
                  :rules="[requiredRule]"
                  :readonly="!editable"
                ></VTextField>
              </VCol>
              <VCol cols="12" md="6">
                <VTextField v-model="epcBonusSheet.value!.name" label="Nom du CPE" :rules="[requiredRule]" />
              </VCol>
              <VCol cols="12" md="6">
                <NjDatePicker
                  v-model="epcBonusSheet.value!.startDate"
                  label="Date Début validité"
                  :readonly="!editable"
                  :rules="[requiredRule]"
                />
              </VCol>
              <VCol cols="12" md="6">
                <NjDatePicker
                  v-model="epcBonusSheet.value!.endDate"
                  label="Date Fin validité"
                  :rules="[checkEndDateAfterStartDateRule]"
                />
              </VCol>
              <VCol cols="12" md="6">
                <VTextField
                  v-model="epcBonusSheet.value!.minDuration"
                  label="Durée du CPE minimale"
                  suffix="ans"
                  :rules="[requiredRule]"
                />
              </VCol>
              <VCol cols="12" md="6">
                <VTextField
                  v-model="epcBonusSheet.value!.minEfficiency"
                  label="Efficacité du CPE minimale"
                  suffix="%"
                  :readonly="!editable"
                  :rules="[requiredRule]"
                />
              </VCol>
              <VCol cols="12" md="6">
                <VSwitch v-model="epcBonusSheet.value!.certified" ripple label="Certifié" :disabled="certified" />
              </VCol>
            </VRow>
          </NjExpansionPanel>
          <NjExpansionPanel>
            <template #title>
              <div class="d-flex align-center fill-width">
                Paramètres
                <VSpacer />
                <NjIconBtn icon="mdi-plus" size="small" color="primary" @click.stop="addParameters"></NjIconBtn>
              </div>
            </template>

            <VAlert v-show="!allParamIdValid || !allIdValid" type="error"
              >Tous vos identifiants de paramètres et de tables de correspondances doivent être uniques</VAlert
            >

            <!-- eslint-disable-next-line vue/valid-v-for -->
            <VRow v-for="(arg, i) in epcBonusSheet.value!.parameters">
              <VCol>
                <VCard>
                  <VCardText class="align-center">
                    <ParameterCalculEditor
                      v-model="epcBonusSheet.value!.parameters[i]"
                      :mandatory-param="
                        epcBonusSheet.value!.parameters[i].id === 'duree' ||
                        epcBonusSheet.value!.parameters[i].id === 'eff'
                      "
                      :disabled="!editable"
                      @delete="deleteParameters(i)"
                    />
                  </VCardText>
                </VCard>
              </VCol>
            </VRow>
          </NjExpansionPanel>
          <NjExpansionPanel>
            <template #title>
              <div class="d-flex align-center fill-width">
                Tables de correspondances
                <VSpacer />
                <NjIconBtn
                  icon="mdi-plus"
                  size="small"
                  color="primary"
                  @click.stop="ajoutTableCorrespondance"
                ></NjIconBtn>
              </div>
            </template>

            <VAlert v-show="!allTablesCorrespondancesIdValid || !allIdValid" type="error">
              Tous vos identifiants de paramètres et de tables de correspondances doivent être uniques
            </VAlert>
            <!-- eslint-disable-next-line vue/valid-v-for -->
            <VRow v-for="(mappingTable, iMappingTables) in epcBonusSheet.value!.mappingTables">
              <VCol>
                <VRow>
                  <VCol cols="3" class="d-inline-flex">
                    <VTextField
                      label="Identifiant"
                      :model-value="mappingTable.id"
                      :readonly="mappingTable.id === 'Formules' || !editable"
                      @update:model-value="updateIdMappingTable(iMappingTables, $event)"
                    ></VTextField>
                    <NjIconBtn
                      v-if="mappingTable.id !== 'Formules' && !epcBonusSheet.value?.id"
                      icon="mdi-delete"
                      color="primary"
                      @click="deleteMappingTable(iMappingTables)"
                    />
                  </VCol>
                  <VCol v-if="mappingTable.id === 'Formules'" class="d-flex align-center">
                    Cette table est réservée pour associer les formules à la durée
                  </VCol>
                </VRow>
                <VRow>
                  <VCol>
                    <h4>
                      Paramètres de tables
                      <NjBtn
                        v-if="!epcBonusSheet.value?.id"
                        class="ms-4"
                        color="primary"
                        variant="outlined"
                        @click="addParameterTableCorrespondance(mappingTable)"
                      >
                        Ajouter une colonne à ma table
                      </NjBtn>
                    </h4>
                  </VCol>
                </VRow>
                <VRow>
                  <VCol v-for="(paramColumn, i) in mappingTable.paramColumns" :key="paramColumn" cols="3">
                    <VRow>
                      <VCol>
                        <VSelect
                          v-model="mappingTable.paramColumns[i]"
                          :items="fileteredParametersForMappingTable"
                          item-title="label"
                          item-value="id"
                          :readonly="!editable"
                          @update:model-value="generateValues(mappingTable)"
                        />
                      </VCol>
                      <VCol class="flex-grow-0">
                        <VBtn
                          v-if="!epcBonusSheet.value?.id"
                          icon="mdi-delete"
                          color="primary"
                          variant="text"
                          @click="deleteParamColumn(mappingTable, i)"
                        />
                      </VCol>
                    </VRow>
                  </VCol>
                </VRow>
              </VCol>

              <VCol cols="12">
                <h4>Formules</h4>
              </VCol>

              <VCol>
                <!-- eslint-disable-next-line vue/valid-v-for -->
                <VRow v-for="(v, i) in mappingTable.data">
                  <!-- eslint-disable-next-line vue/valid-v-for -->
                  <VCol v-for="p in combinaisonsValues[mappingTable.id][i]">
                    <VTextField readonly :model-value="p" />
                  </VCol>
                  <VCol>
                    <VTextField
                      v-model="mappingTable.data[i]"
                      hint="Valeur"
                      :tabindex="iMappingTables * 1000 + i + 1"
                      :readonly="!editable"
                    />
                  </VCol>
                </VRow>
              </VCol>
            </VRow>
          </NjExpansionPanel>
        </VForm>
      </VCard>
    </template>
  </NjPage>
  <ConfirmUnsavedDataDialog v-model:unsaved-data-dialog="unsavedDataDialog" @save="save" />
</template>
<script lang="ts" setup>
import { epcBonusSheetApi } from '@/api/epcBonus'
import ConfirmUnsavedDataDialog from '@/components/ConfirmUnsavedDataDialog.vue'
import NjIconBtn from '@/components/NjIconBtn.vue'
import NjPage from '@/components/NjPage.vue'
import ParameterCalculEditor from '@/components/ParameterCalculEditor.vue'
import { useSnackbarStore } from '@/stores/snackbar'
import { makeEmptyEpcBonusSheet, type EpcBonusSheet } from '@/types/epcBonus'
import type { FormulaMappingTable } from '@/types/calcul/mappingTable'
import { makeEmptyParameterFormula } from '@/types/calcul/parameterFormula'
import { requiredRule } from '@/types/rule'
import { cloneDeep, uniq } from 'lodash'
import { VCard, VCardText, VIcon, VSwitch } from 'vuetify/components'
import { VForm } from 'vuetify/components/VForm'

const props = defineProps({
  id: {
    type: Number,
    default: undefined,
  },
})

const epcBonusSheet = ref(emptyValue<EpcBonusSheet>())
const router = useRouter()
const snackbarStore = useSnackbarStore()
const { unsavedDataDialog, failedSave, succeedSave } = useUnsavedData(epcBonusSheet)

const formRef = ref<VForm | null>(null)

// Paramètres
function addParameters() {
  epcBonusSheet.value.value!.parameters.push(makeEmptyParameterFormula())
}

function deleteParameters(index: number) {
  epcBonusSheet.value.value!.parameters.splice(index, 1)
}

// Table Correspondance
function ajoutTableCorrespondance() {
  epcBonusSheet.value.value!.mappingTables.push({
    id: '',
    paramColumns: [],
    data: [],
  })
}

function deleteMappingTable(index: number) {
  epcBonusSheet.value.value?.mappingTables.splice(index, 1)
}

function updateIdMappingTable(i: number, newId: string) {
  combinaisonsValues.value[newId] = combinaisonsValues.value[epcBonusSheet.value.value!.mappingTables[i].id]
  epcBonusSheet.value.value!.mappingTables[i].id = newId
}

function addParameterTableCorrespondance(tableCorrespondances: FormulaMappingTable) {
  if (tableCorrespondances.paramColumns == null) {
    tableCorrespondances.paramColumns = []
  }
  tableCorrespondances.paramColumns.push('')
}

function deleteParamColumn(mappingTable: FormulaMappingTable, i: number) {
  mappingTable.paramColumns.splice(i, 1)
  generateValues(mappingTable)
}

const fileteredParametersForMappingTable = computed(() => {
  return epcBonusSheet.value.value!.parameters.filter((it) => it.type === 'CHOICE')
})

const combinaisonsValues = ref<Record<string, string[][]>>({})
function generateValues(tableCorrespondances: FormulaMappingTable, loading: boolean = false) {
  const combinaisons: string[][] = []
  const args = tableCorrespondances.paramColumns?.map((paramColumn) => {
    const parameter = epcBonusSheet.value.value!.parameters.find((p) => paramColumn === p.id && p.type === 'CHOICE')
    if (parameter == null) {
      throw 'Vous devez sélectionner un paramètre de type choix'
    }
    return parameter
  })
  const allValues = args.map((it) => ((it.data ?? '') as string).split(';'))

  const iterators = cloneDeep(allValues)

  while (iterators.length > 0 && iterators[0].length > 0) {
    combinaisons.push(iterators.map((it) => it[0]))

    let iIterator = iterators.length - 1
    iterators[iIterator].shift()
    while (iterators[iIterator].length === 0 && iIterator > 0) {
      iterators[iIterator] = allValues[iIterator].concat()
      iIterator--
      iterators[iIterator].shift()
    }
  }
  combinaisonsValues.value[tableCorrespondances.id] = combinaisons
  if (!loading) {
    tableCorrespondances.data = combinaisons.map(() => '0')
  }
}

const allParamIdValid = computed(() => {
  const allParamIds = epcBonusSheet.value.value!.parameters.map((it) => it.id)
  const uniqParamIds = uniq(allParamIds)
  return allParamIds.length === uniqParamIds.length
})

const allTablesCorrespondancesIdValid = computed(() => {
  const allIds = epcBonusSheet.value.value!.mappingTables.map((it) => it.id)
  const uniqIds = uniq(allIds)
  return allIds.length === uniqIds.length
})

const allIdValid = computed(() => {
  const allParamIds = epcBonusSheet.value.value!.parameters.map((it) => it.id)
  const uniqParamIds = uniq(allParamIds)
  return allParamIds.length === uniqParamIds.length
})

const certified = ref(false)
// Enregistrement
const saving = ref(emptyValue<EpcBonusSheet>())
const save = async () => {
  if ((await formRef.value!.validate()).valid) {
    if (props.id) {
      handleAxiosPromise(saving, epcBonusSheetApi.update(props.id, epcBonusSheet.value.value!), {
        afterSuccess: () => {
          snackbarStore.setSuccess('Bonification CPE bien mis à jour')
          succeedSave()
          certified.value = saving.value.value!.certified
        },
        afterError: () => {
          failedSave()
        },
      })
    } else {
      handleAxiosPromise(
        saving,
        epcBonusSheetApi.create(epcBonusSheet.value.value!),
        (r) => {
          snackbarStore.setSuccess('Bonification CPE bien créée')
          succeedSave()
          router.push({
            name: 'EpcBonusSheetOneView',
            params: { id: r.data.id },
          })
        },
        () => snackbarStore.setError('Une erreur est survenue lors de la création de la bonification CPE')
      )
    }
  }
}

const checkEndDateAfterStartDateRule = (v: string) => {
  return (
    !v ||
    !epcBonusSheet.value.value?.startDate ||
    epcBonusSheet.value.value?.endDate >= epcBonusSheet.value.value.startDate ||
    'Fin de validité avant Début validité'
  )
}

const editable = computed(() => {
  // return !epcBonusSheet.value.value?.id
  return true
})

// chargement
watch(
  () => props.id,
  (id) => {
    epcBonusSheet.value.value = makeEmptyEpcBonusSheet()
    if (id) {
      handleAxiosPromise(epcBonusSheet, epcBonusSheetApi.getOne(id), (v) => {
        epcBonusSheet.value.value = v.data
        succeedSave()
        certified.value = epcBonusSheet.value.value!.certified
        generateValues(epcBonusSheet.value.value.mappingTables[0], true)
      })
    }
  },
  {
    immediate: true,
  }
)
</script>
