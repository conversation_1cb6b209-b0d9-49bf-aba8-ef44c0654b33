<template>
  <NjExpansionPanel :title="title">
    <NjDataTable :headers="headers" :items="items">
      <template #[`item.value`]="{ item }">
        {{ item.value }}
        <NjIconBtn icon="mdi-clipboard-multiple-outline" @click="addToClipBoard(item.value)"></NjIconBtn>
      </template>
    </NjDataTable>
  </NjExpansionPanel>
</template>
<script setup lang="ts">
import type { PropType } from 'vue'
import type { DataTableHeader } from '@/components/okta/NjDataTable.type'
import NjDataTable from '@/components/okta/NjDataTable.vue'
import { useSnackbarStore } from '@/stores/snackbar'

defineProps({
  title: String,
  items: Array as PropType<{ name: string; value: string }[]>,
})

const snackbarStore = useSnackbarStore()
const headers: DataTableHeader[] = [
  {
    title: 'Nom',
    value: 'name',
    width: '50%',
    sortable: false,
  },
  {
    title: 'Valeur',
    value: 'value',
    width: '50%',
    sortable: false,
  },
]

const addToClipBoard = async (value: string) => {
  await navigator.clipboard.writeText(value)
  snackbarStore.setSuccess('Copié dans le presse-papier')
}
</script>
