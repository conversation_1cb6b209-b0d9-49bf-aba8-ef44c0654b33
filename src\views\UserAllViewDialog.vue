<template>
  <CardDialog v-model="active" :title="title">
    <VRow class="flex-column">
      <VCol>
        <SearchInput
          v-model:loading="data.loading"
          :model-value="pageFilter.search"
          @update:model-value="updateSearch"
        />
      </VCol>
      <VCol v-if="beneficiaries.loading" class="d-flex align-center">
        <VProgressCircular class="ms-4" indeterminate />
      </VCol>
      <VCol v-else>
        <NjDataTable
          :headers="beneficiaryHeader"
          :selections="selections"
          :pageable="pageable"
          :page="data.value!"
          @update:selections="updateModelValue"
          @update:pageable="updatePageable"
        >
          <template #[`item.active`]="{ item }">
            <NjBooleanIcon :condition="item.active" />
          </template>
          <template #[`item.entities`]="{ item }">
            {{ item.entities[0]?.name ?? '' }}
            {{ item.entities.length > 1 ? `, +${item.entities.length - 1}` : '' }}
          </template>
        </NjDataTable>
      </VCol>
    </VRow>
    <template #actions>
      <NjBtn color="primary" @click="active = false">OK</NjBtn>
    </template>
  </CardDialog>
</template>
<script setup lang="ts">
import type { UserFilter } from '@/api/user'
import type { DataTableHeader } from '@/components/okta/NjDataTable.type'
import { usePagination, type Page } from '@/types/pagination'
import type { User, UserWithEntities } from '@/types/user'
import type { PropType } from 'vue'

const props = defineProps({
  modelValue: Boolean,
  selections: {
    type: Object as PropType<User[]>,
  },
  title: String,
  filter: {
    type: Object as PropType<UserFilter>,
    default: () => ({}),
  },
})

const emit = defineEmits(['update:selections', 'update:model-value'])
const updateModelValue = (value: UserWithEntities[]) => {
  emit('update:selections', value)
}
const selections = computed(() => props.selections as UserWithEntities[] | undefined)
const active = ref(false)
watch(
  () => props.modelValue,
  (v) => {
    if (v !== active.value) {
      active.value = v
    }
  }
)
watch(active, (v) => emit('update:model-value', v))

const beneficiaries = ref(emptyValue<Page<User>>())

const { data, pageable, pageFilter, updatePageable, updateFilter } = usePagination<UserWithEntities, UserFilter>(
  (filter: UserFilter, pageable) => userApi.getAll(pageable, { ...props.filter, ...filter }),
  {},
  {
    page: 0,
    size: 20,
    sort: [],
  }
)

const beneficiaryHeader: DataTableHeader[] = [
  {
    title: 'Intervenant',
    value: 'lastName',
    formater: (item) => displayFullnameUser(item),
  },
  {
    title: 'Actif',
    value: 'active',
  },
  {
    title: 'Organisation',
    value: 'entities',
    sortable: false,
  },
  {
    title: 'GID',
    value: 'gid',
  },
]

// search
const updateSearch = (v: string) => {
  data.value.loading = true
  const filter = {
    ...unref(pageFilter),
    search: v,
  }
  updateFilter(filter)
}
</script>
