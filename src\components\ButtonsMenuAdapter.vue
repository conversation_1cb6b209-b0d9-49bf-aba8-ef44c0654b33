<template>
  <VContainer ref="containerRef" class="pa-0" fluid>
    <NjBtn v-if="rendering" ref="buttonRef" color="transparent" />
    <!-- Row layout for buttons -->
    <VRow v-if="!useMenu" justify="end" dense>
      <VCol v-for="(btn, index) in props.buttons" :key="index" class="flex-grow-0">
        <NjBtn v-bind="btn">
          {{ btn.label }}
        </NjBtn>
      </VCol>
    </VRow>
    <VRow v-else justify="end" dense>
      <VCol class="flex-grow-0">
        <!-- Menu layout for buttons -->
        <VMenu :close-on-content-click="false">
          <template #activator="{ props }">
            <NjIconBtn icon="mdi-dots-horizontal" color="primary" variant="square-outline" v-bind="props" />
          </template>
          <VList>
            <VListItem v-for="(btn, index) in props.buttons" :key="index">
              <NjBtn v-bind="btn">
                {{ btn.label }}
              </NjBtn>
            </VListItem>
          </VList>
        </VMenu>
      </VCol>
    </VRow>
  </VContainer>
</template>

<script lang="ts" setup>
import { useEventListener, useResizeObserver } from '@vueuse/core'
import { ref, type ComponentPublicInstance, type PropType } from 'vue'
import { VContainer } from 'vuetify/components'

const props = defineProps({
  buttons: {
    type: Array as PropType<
      {
        label: string
        onClick: () => void
        loading?: boolean
        disabled?: boolean
        variant?: 'outlined' | 'filled'
        color?: string
        icon?: string
      }[]
    >,
    required: true,
    default: () => [],
  },
})

const containerRef = ref<ComponentPublicInstance | null>(null)

useResizeObserver(containerRef, () => {
  determineLayout()
})

const containerHeight = ref()

const useMenu = ref(false)

const njBtnHeight = ref(0) // idéalement il faudrait déterminer cela au montage de la page et onResize

const updateWindowSize = () => {
  determineLayout()
}

useEventListener('resize', updateWindowSize)

const rendering = ref(false)
const buttonRef = ref<ComponentPublicInstance | null>(null)

const determineLayout = () => {
  useMenu.value = false
  if (njBtnHeight.value == 0) {
    rendering.value = true
    nextTick(() => {
      const el = buttonRef.value != null ? buttonRef.value['$el'] : null
      njBtnHeight.value = el?.getBoundingClientRect().height ?? 0
      rendering.value = false

      useMenuTick()
    })
  } else {
    useMenuTick()
  }
}

const useMenuTick = () => {
  nextTick(() => {
    if (containerRef.value) {
      containerHeight.value = containerRef.value['$el'].getBoundingClientRect().height
      useMenu.value = containerHeight.value > 2 * njBtnHeight.value
    }
  })
}

useEventListener(window, 'resize', determineLayout)

watch(
  () => [props.buttons, containerRef.value],
  () => {
    determineLayout()
  },
  {
    immediate: true,
  }
)
</script>
