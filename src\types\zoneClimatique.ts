import { predefinedParameters } from './calcul/parameterFormula'

export const zoneClimatiques = ['H1', 'H2', 'H3'] as const

type ZoneClimatiqueType = (typeof zoneClimatiques)[number]

export const resolveZoneClimatique = (codePostale: string | undefined): ZoneClimatiqueType | undefined => {
  let departement = codePostale?.substring(0, 2)
  if (departement == '97') {
    departement = codePostale?.substring(0, 3)
  }
  if (departementByZoneClimatique.H3.find((dep) => dep === departement)) {
    return 'H3'
  } else if (departementByZoneClimatique.H2.find((dep) => dep === departement)) {
    return 'H2'
  } else if (departementByZoneClimatique.H1.find((dep) => dep === departement)) {
    return 'H1'
  }
  return undefined
}

const departementByZoneClimatique: Record<ZoneClimatiqueType, string[]> = {
  H1: [
    '01',
    '02',
    '03',
    '05',
    '08',
    '10',
    '14',
    '15',
    '19',
    '21',
    '23',
    '25',
    '27',
    '28',
    '38',
    '39',
    '42',
    '43',
    '45',
    '51',
    '52',
    '54',
    '55',
    '57',
    '58',
    '59',
    '60',
    '61',
    '62',
    '63',
    '67',
    '68',
    '69',
    '70',
    '71',
    '73',
    '74',
    '75',
    '76',
    '77',
    '78',
    '80',
    '87',
    '88',
    '89',
    '90',
    '91',
    '92',
    '93',
    '94',
    '95',
    '975',
  ],
  H2: [
    '04',
    '07',
    '09',
    '12',
    '16',
    '17',
    '18',
    '22',
    '24',
    '26',
    '29',
    '31',
    '32',
    '33',
    '35',
    '36',
    '37',
    '40',
    '41',
    '44',
    '46',
    '47',
    '48',
    '49',
    '50',
    '53',
    '56',
    '64',
    '65',
    '72',
    '79',
    '81',
    '82',
    '84',
    '85',
    '86',
  ],
  H3: ['06', '11', '13', '20', '30', '34', '66', '83', '971', '972', '973', '974', '976'],
}

const region = (predefinedParameters.find((it) => it.id === 'region')!.data as string).split(';')

export const resolveRegion = (postalCode: string | undefined): string | undefined => {
  let departement = postalCode?.substring(0, 2)
  if (departement == '97') {
    departement = postalCode?.substring(0, 3)
  }
  return region.find((it) => it.startsWith(departement ?? ''))
}
