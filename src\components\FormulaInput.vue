<template>
  <VDialog v-model="dialogActive">
    <template #activator="{ props }">
      <VRow dense>
        <VCol>
          <VTextField
            v-model="copyFormula"
            append-inner-icon="mdi-function-variant"
            :label="label"
            :error-messages="evaluationFormula.error ? evaluationFormula.error : ''"
            :rules="rules"
            :readonly="readonly"
            :disabled="disabled"
          />
        </VCol>
        <VCol class="flex-grow-0">
          <NjIconBtn color="primary" v-bind="props" icon="mdi-help-circle-outline" size="small" />
        </VCol>
      </VRow>
    </template>
    <VCard>
      <VCardTitle class="d-flex">
        Edition de formule <VSpacer />
        <VBtn icon="mdi-close" variant="text" @click="dialogActive = false" />
      </VCardTitle>

      <VCardText>
        <VRow class="flex-column">
          <VCol>
            <VTextarea
              ref="formulaInputRef"
              v-model="copyFormula"
              auto-grow
              :error-messages="evaluationFormula.error ? evaluationFormula.error : ''"
              :rules="rules"
              :readonly="readonly"
              @update:model-value="updateFormula($event)"
            />
          </VCol>
          <VCol class="d-flex justify-end">
            <NjBtn v-if="!readonly" @click="dialogActive = false">Valider</NjBtn>
          </VCol>
          <VCol v-if="parameters && parameters.length > 0">
            <VCard>
              <VCardTitle> Variables disponibles </VCardTitle>

              <VCardText>
                <VRow>
                  <!-- eslint-disable-next-line vue/valid-v-for -->
                  <VCol v-for="p in parameters" md="3" @click="addVariable(p.id)">
                    <VChip rounded="0" color="primary" class="variable-clickable">
                      <b>{{ p.id }}</b> : {{ p.label }}
                    </VChip>
                  </VCol>
                </VRow>
              </VCardText>
            </VCard>
          </VCol>
          <VCol v-if="mappingTables && mappingTables.length > 0">
            <VCard>
              <VCardTitle> Table de correspondances disponibles </VCardTitle>

              <VCardText>
                <VRow>
                  <!-- eslint-disable-next-line vue/valid-v-for -->
                  <VCol v-for="p in mappingTables" md="3" @click="addVariable(p.id)">
                    <VChip rounded="0" color="primary" class="variable-clickable">
                      <b>{{ p.id }}</b>
                    </VChip>
                  </VCol>
                </VRow>
              </VCardText>
            </VCard>
          </VCol>
          <VCol v-if="predefinedVariables && predefinedVariables.length > 0">
            <VCard>
              <VCardTitle> Variables prédéfinis disponibles </VCardTitle>

              <VCardText>
                <VRow>
                  <!-- eslint-disable-next-line vue/valid-v-for -->
                  <VCol v-for="p in predefinedVariables" @click="addVariable(p.id)">
                    <VChip rounded="0" color="primary" class="variable-clickable">
                      <b>{{ p.id }}</b> <i>({{ p.label }})</i>
                    </VChip>
                  </VCol>
                </VRow>
              </VCardText>
            </VCard>
          </VCol>
          <VCol>
            <VCard class="pa-4 formula-input__helper">
              <p>Vous pouvez écrire une formule avec les opérateurs mathématiques classiques (comme +, -, *, /).</p>
              <p class="mt-4">
                Pour faire référence à un de vos paramètres de types nombres ou une de vos tables de correspondance,
                saisissez l'identifiant entre crochets.<br />
                Ex: [table_cumac] ou bien [n_housses_isolantes]
              </p>
              <p class="mt-4">
                Quand vous faites références à un paramètre de type choix, le numéro du choix est renvoyé. <br />
                l'identifiant entre crochets.<br />
                Ex: [zone_climatique] renvoie 1, si vous avez choisi H1, et renvoie 3 si vous avez choisi H3
              </p>

              <p class="mt-4">Vous pouvez également utiliser des fonctions mathématiques préprogrammés :</p>
              <dl class="formula-helper__formula">
                <dt>ppq(a, b)</dt>
                <dd>Fonction "Plus Petit Que" : si a est strictement inférieur à b, il renvoie 1, sinon 0</dd>
                <dt>ppe(a, b)</dt>
                <dd>Fonction "Plus Petit ou Egal" : si a est inférieur ou égal à b, il renvoie 1, sinon 0</dd>
                <dt>pgq(a, b)</dt>
                <dd>Fonction "Plus Grand Que" : si a est strictement supérieur à b, il renvoie 1, sinon 0</dd>
                <dt>pge(a, b)</dt>
                <dd>Fonction "Plus Grand ou Egal" : si a est supérieur ou égal à b, il renvoie 1, sinon 0</dd>
                <dt>egal(a, b)</dt>
                <dd>Fonction "est égal à" : si a est égal à b, il renvoie 1, sinon 0</dd>
                <dt>ne(a, b)</dt>
                <dd>Fonction "non égal à" : si a est non égal à b, il renvoie 1, sinon 0</dd>
                <dt>non(a)</dt>
                <dd>Fonction "inverse de" : si a est faux (égal à 0), alors il renvoie 1, sinon 0</dd>
                <dt>est(a, b, ..., n)</dt>
                <dd>Fonction "tout est vrai" : si a, b, ..., n sont vrai (égal à 1), alors il renvoie 1, sinon 0</dd>
                <dt>ou(a, b, ..., n)</dt>
                <dd>
                  Fonction "une des condition est vrai" : si au moins a, ou b, ..., ou n est vrai (égal à 1), alors il
                  renvoie 1, sinon 0
                </dd>
                <dt>si(condition, a, b)</dt>
                <dd>Fonction "Si ... Alors ... Sinon ..." : si condition est égal à 1, il renvoie a, sinon b</dd>
                <dt>arrondi(nombre, x)</dt>
                <dd>
                  Fonction "Arrondi à X chiffres après la virgule" : arrondi "nombre" à "x" chiffres après la virgule.
                  Si x n'est pas précissé, arrondi à l'entier
                </dd>
                <dt>arrondiInferieur(nombre, x)</dt>
                <dd>
                  Fonction "Arrondi à l'inférieur à X chiffres après la virgule" : arrondi à l'inférieur "nombre" à "x"
                  chiffres après la virgule. Si x n'est pas précissé, arrondi à l'entier
                </dd>
                <dt>arrondiSuperieur(nombre, x)</dt>
                <dd>
                  Fonction "Arrondi au supérieur à X chiffres après la virgule" : arrondi au supérieur "nombre" à "x"
                  chiffres après la virgule. Si x n'est pas précissé, arrondi à l'entier
                </dd>
              </dl>

              <p class="mt-4">Fonctions spécifiques à des fichiers :</p>
              <dl class="formula-helper__formula">
                <dt>SommePuissance()</dt>
                <dd>
                  Fonction "Somme des puissances" : pour une opération multi ligne, il fait la somme d'un champs nommé
                  "puissance_plus" de toutes les lignes opérations
                </dd>
                <dt>SommeEquipementEligible()</dt>
                <dd>
                  Fonction "Somme des puissances eligibles" : pour une opération multi ligne, il fait la somme d'un
                  champs nommé "puissance_plus" de toutes les lignes opérations si le champs "est_equipement_eligible"
                  de la ligne est égale à 1 (le choix doit avoir en première réponse "Oui")
                </dd>
              </dl>

              <slot name="help:supplements"></slot>
            </VCard>
          </VCol>
        </VRow>
      </VCardText>
    </VCard>
  </VDialog>
</template>

<script setup lang="ts">
import { parsingFormula } from '@/types/calcul/formula'
import type { ValidationRule } from '@/types/rule'
import { debounce } from 'lodash'
import type { PropType } from 'vue'
import NjIconBtn from './NjIconBtn.vue'
import { VCol, VRow } from 'vuetify/components'

const props = defineProps({
  modelValue: {
    type: String,
  },
  label: String,
  rules: Array as PropType<ValidationRule[]>,
  parameters: Array as PropType<{ id: string; label: string }[]>,
  mappingTables: Array as PropType<{ id: string }[]>,
  predefinedVariables: Array as PropType<{ id: string; label: string }[]>,
  readonly: Boolean,
  disabled: Boolean,
})

const emit = defineEmits(['update:model-value'])

const evaluationFormula = ref(emptyValue())

// updateValue('formula', v)
const copyFormula = ref('')
const updateFormula = (v: string) => {
  evaluationFormula.value.loading = true
  debounceFormulaUpdate(v)
}
watch(
  () => props.modelValue,
  (v) => {
    console.debug('watch FormulaInput props.modelValue', v)
    if (v !== copyFormula.value) {
      copyFormula.value = v ?? ''
    }
  },
  {
    immediate: true,
  }
)
watch(copyFormula, updateFormula)

const debounceFormulaUpdate = debounce((v: string) => {
  const parsing = parsingFormula(v)
  evaluationFormula.value = parsing
  if (parsing.error) {
    evaluationFormula.value.error = parsing.error
  }
  emit('update:model-value', v)
}, 200)

const formulaInputRef = ref<HTMLTextAreaElement>()
const addVariable = (id: string) => {
  const myField = formulaInputRef.value!
  if (myField.selectionStart || myField.selectionStart === 0) {
    const startPos = myField.selectionStart
    const endPos = myField.selectionEnd
    copyFormula.value =
      myField.value.substring(0, startPos) + '[' + id + ']' + myField.value.substring(endPos, myField.value.length)
  } else {
    copyFormula.value = myField.value + '[' + id + ']'
  }
  myField.focus({})
}

const dialogActive = ref(false)
</script>

<style scoped lang="scss">
.formula-input__helper {
  dt {
    font-weight: 700;
  }
  dd {
    margin-left: 16px;
  }
}
.variable-clickable {
  cursor: pointer;
}
</style>
