import type { Step } from '@/types/steps'
import { isEqual, keyBy } from 'lodash'
import { defineStore } from 'pinia'

export const useStepsStore = defineStore('steps', () => {
  const steps = ref<Step[]>()

  const timeoutId = ref<number>()

  const load = async () => {
    clearTimeouts()

    return await stepsApi
      .getAll()
      .then((v) => {
        v.data.sort((arg1, arg2) => arg1.id - arg2.id)
        if (!isEqual(v, steps.value)) {
          steps.value = v.data
        }

        timeoutId.value = setTimeout(load, 30 * 60 * 1000)
      })
      .catch((e) => {
        logException(e)
        timeoutId.value = setTimeout(load, ((steps.value?.length ?? 0 > 0) ? 30 * 60 : 5) * 1000)
      })
  }

  const clearTimeouts = () => {
    if (timeoutId.value !== undefined) {
      clearTimeout(timeoutId.value)
    }
  }

  const stepsMap = computed(() => {
    return keyBy(steps.value, 'id')
  })

  return { steps, load, clearTimeouts, stepsMap }
})
