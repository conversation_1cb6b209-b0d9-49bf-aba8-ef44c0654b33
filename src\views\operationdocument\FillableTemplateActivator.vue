<template>
  <slot
    v-if="item.documentType.fillableTemplate || renderConvention || renderSwornStatement"
    name="activator"
    :download="() => downloadFillableTemplate(item.documentType)"
  >
  </slot>
  <ConventionDialog v-if="renderConvention" v-model="editConventionDialog" :operation="operation" />
  <CreateSwornStatementDialog v-if="renderSwornStatement" v-model="editSwornStatementDialog" :operation="operation" />
</template>
<script setup lang="ts">
import type { DocumentType } from '@/types/documentType'
import CreateSwornStatementDialog from '../operation/CreateSwornStatementDialog.vue'
import type { Operation } from '@/types/operation'
import { useAdminConfigurationStore } from '@/stores/adminConfiguration'
import { useSnackbarStore } from '@/stores/snackbar'
import ConventionDialog from '../operation/dialog/ConventionDialog.vue'
import { useUserStore } from '@/stores/user'
import type { DocumentDataTableItem } from './types'
import { trace } from '@/stores/analytics'

const props = defineProps({
  item: {
    type: Object as PropType<DocumentDataTableItem>,
    required: true,
  },
  operation: { type: Object as PropType<Operation> },
})

const { conventionDocumentTypeId, swornStatementDocumentTypeId } = useAdminConfigurationStore()
const snackbarStore = useSnackbarStore()

const editConventionDialog = ref(false)
const editSwornStatementDialog = ref(false)

const downloadFillableTemplate = (documentType: DocumentType) => {
  if (props.item.documentType.id == parseInt(conventionDocumentTypeId!.data)) {
    editConventionDialog.value = true
    return
  }

  if (documentType.id == parseInt(swornStatementDocumentTypeId!.data)) {
    editSwornStatementDialog.value = true
    return
  }

  trace('downloadFilledTemplate', {
    documentType: { id: props.item.documentType.id, name: props.item.documentType.name },
    operation: {
      id: props.operation?.id,
      stepId: props.operation?.stepId,
    },
    standardizedOperationSheet: {
      id: props.operation?.standardizedOperationSheet.id,
      operationCode: props.operation?.standardizedOperationSheet.operationCode,
    },
  })

  documentTypeApi
    .downloadFilledDocument(props.item.documentType.id, props.operation!.id!)
    .then((response) => {
      downloadFile(props.item.documentType.fillableTemplate!.originalFilename, response.data)
      snackbarStore.setSuccess('Le téléchargement a réussi')
    })
    .catch(async (err) => snackbarStore.setError(await handleAxiosException(err)))
}

const userStore = useUserStore()

const renderConvention = computed(() => {
  return (
    userHasRole(userStore.currentUser, 'AGENCE_PLUS', 'SUPPORT_AGENCE_PLUS', 'TERRITOIRE', 'ADMIN', 'ADMIN_PLUS') &&
    conventionDocumentTypeId?.valueAsInt == props.item.documentType.id
  )
})

const renderSwornStatement = computed(() => {
  return swornStatementDocumentTypeId?.valueAsInt == props.item.documentType.id
})
</script>
