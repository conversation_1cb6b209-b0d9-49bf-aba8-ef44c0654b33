import type { DataTableHeader } from '@/components/okta/NjDataTable.type'
import type { OperationExportResultDto } from '@/types/operationExportResultDto'
import { formatHumanReadableLocalDate } from '@/types/date'
import { add, format } from 'date-fns'

const firstPartHeader: DataTableHeader<OperationExportResultDto>[] = [
  {
    title: 'Chrono',
    value: 'chronoCode',
  },
  {
    title: 'Organisation',
    value: 'entity.id',
    formater: (item) => item.formatedEntityName ?? '',
  },
  {
    title: 'Réservation / Installation',
    value: 'operationName',
  },
  {
    title: 'Etape en cours',
    value: 'stepId',
    formater: (item) => item.formatedStepName ?? '',
  },
  {
    title: 'Nb.rés.Clas.kWhc',
    value: 'reservedClassicCumac',
    formater: (item) => formatNumber(item.reservedClassicCumac),
    cellClass: 'text-right justify-end',
  },
  {
    title: 'Nb.rés.Préc.kWhc',
    value: 'reservedPrecariousnessCumac',
    formater: (item) => formatNumber(item.reservedPrecariousnessCumac),

    cellClass: 'text-right justify-end',
  },
  {
    title: 'Fin trv.ret.',
    value: 'instructionDelay',
    formater: (item) => formatHumanReadableLocalDate(item.effectiveEndWorksDate!),
    key: 'effectiveEndWorksDate',
  },
]

const secondPartHeader: DataTableHeader<OperationExportResultDto>[] = [
  {
    title: 'Nbr.JR',
    value: 'numberOfDays',
    sortable: false,
    cellClass: 'text-right justify-end',
  },
  {
    title: 'Code opération',
    value: 'standardizedOperationSheetCode',
    sortable: false,
  },
  {
    title: 'Valo.rés.Clas.€',
    value: 'reservedClassicValuationValue',
    formater: (_item, value) => formatPriceNumber(value),
    sortable: false,
    cellClass: 'text-right justify-end',
  },
  {
    title: 'Mnt.rés.Clas.€',
    value: 'reservedClassicAmountValue',
    formater: (_item, value) => formatPriceNumber(value),
    sortable: false,
    cellClass: 'text-right justify-end',
  },
  {
    title: 'Valo.rés.Préc.€',
    value: 'reservedPrecariousnessValuationValue',
    formater: (_item, value) => formatPriceNumber(value),
    sortable: false,
    cellClass: 'text-right justify-end',
  },
  {
    title: 'Mnt.rés.Préc.€',
    value: 'reservedPrecariousnessAmountValue',
    sortable: false,
    formater: (_item, value) => formatPriceNumber(value),
    cellClass: 'text-right justify-end',
  },
  {
    title: 'Type de valorisation',
    value: 'valuationType',
    sortable: false,
  },
  {
    title: 'Statut offre commerciale',
    value: 'commercialStatusLabel',
    sortable: false,
  },
  {
    title: 'Demandeur',
    value: 'displayedReservationUser',
    sortable: false,
  },
  {
    title: 'Autre interlocuteur',
    value: 'secondInterlocutor',
    sortable: false,
  },
  {
    title: 'Nom du client',
    value: 'beneficiarySocialReason',
    sortable: false,
  },
  {
    title: 'N° SIREN',
    value: 'beneficiarySiren',
    sortable: false,
  },
  {
    title: 'Adresse',
    value: 'finalAddressStreet',
    sortable: false,
  },
  {
    title: 'Complément adresse',
    value: 'finallAddressAdditionalPostalAddress',
    sortable: false,
  },
  {
    title: 'Code postal',
    value: 'finallAddressPostalCode',
    sortable: false,
  },
  {
    title: 'Ville',
    value: 'finallAddressCity',
    sortable: false,
  },
  {
    title: 'N° offre',
    value: 'customerOfferNumber',
    sortable: false,
  },
  {
    title: 'Sign.conv.',
    value: 'signedDate',
    sortable: false,
    formater: (_item, value) => formatHumanReadableLocalDate(value),
  },
  {
    title: 'Moins-value €',
    value: 'customerFinancialIncentive',
    formater: (_item, value) => formatPriceNumber(value),
    sortable: false,
    cellClass: 'text-right justify-end',
  },
  {
    title: 'Libellé opération',
    value: 'standardizedOperationSheetDescription',
    sortable: false,
  },
  {
    title: 'Cré.chantier',
    value: 'worksCreationDate',
    sortable: false,
    formater: (_item, value) => formatHumanReadableLocalDate(value),
  },
  {
    title: 'N° installation',
    value: 'propertyCode',
    sortable: false,
  },
  {
    title: 'Type',
    value: 'finalWorksType',
    sortable: false,
  },
  {
    title: 'N° Chantier',
    value: 'worksCode',
    sortable: false,
  },
  {
    title: 'Fin trv. prévisionnelle',
    value: 'estimatedEndOperationDate',
    sortable: false,
    formater: (_item, value) => formatHumanReadableLocalDate(value),
  },
  {
    title: 'N° EMMY',
    value: 'emmyFolderEmmyCode',
    sortable: false,
  },
  {
    title: 'Nom du dossier EMMY',
    value: 'emmyFolderName',
    sortable: false,
  },
  {
    title: 'Env.PNCEE',
    value: 'emmyFolderPnceeSubmissionDate',
    sortable: false,
    formater: (_item, value) => formatHumanReadableLocalDate(value),
  },
  {
    title: 'Nb.dem.Clas.kWhc',
    value: 'askedClassicCumac',
    sortable: false,
    cellClass: 'text-right justify-end',
  },
  {
    title: 'Valo.dem.Clas.€',
    value: 'askedClassicValuationValue',
    formater: (_item, value) => formatPriceNumber(value),
    sortable: false,
    cellClass: 'text-right justify-end',
  },
  {
    title: 'Mnt.dem.Clas.€',
    value: 'askedClassicAmount',
    formater: (_item, value) => formatPriceNumber(value),
    sortable: false,
    cellClass: 'text-right justify-end',
  },
  {
    title: 'Nb.dem.Préc.kWhc',
    value: 'askedPrecariousnessCumac',
    sortable: false,
    cellClass: 'text-right justify-end',
  },
  {
    title: 'Valo.dem.Préc.€',
    value: 'askedPrecariousnessValuationValue',
    formater: (_item, value) => formatPriceNumber(value),
    sortable: false,
    cellClass: 'text-right justify-end',
  },
  {
    title: 'Mnt.dem.Préc.€',
    value: 'askedPrecariousnessAmount',
    formater: (_item, value) => formatPriceNumber(value),
    sortable: false,
    cellClass: 'text-right justify-end',
  },
  {
    title: 'Délivr.PNCEE',
    value: 'emmyFolderPnceeIssuedDate',
    sortable: false,
    formater: (_item, value) => formatHumanReadableLocalDate(value),
  },
  {
    title: 'Mnt.versé €',
    value: 'mnt_verse',
    sortable: false,
  },
  {
    title: 'Versement',
    value: 'versement',
    sortable: false,
  },
  {
    title: 'PAEE',
    value: 'paee',
    sortable: false,
  },
  {
    title: 'Atypique',
    value: 'atypical',
    sortable: false,
    formater: (_item, value) => (value ? 'Oui' : 'Non'),
  },
  {
    title: 'Fin trv.déclarée',
    value: 'actualEndWorksDate',
    sortable: false,
    formater: (_item, value) => formatHumanReadableLocalDate(value),
  },
  {
    title: 'Engag.opérat.',
    value: 'signedDate2',
    sortable: false,
    formater: (item) => formatHumanReadableLocalDate(item.signedDate),
  },
  {
    title: 'Région',
    value: 'regionName',
    sortable: false,
  },
  {
    title: 'Processus',
    value: 'statusValue',
    sortable: false,
  },
  {
    title: 'Etape/retour',
    value: 'maxStepId',
    sortable: false,
  },
  {
    title: 'Date réservation',
    value: 'reservedDate',
    sortable: false,
    formater: (_item, value) => formatHumanReadableLocalDate(value),
  },
  {
    title: 'Date arr.dir.env.',
    value: 'dateStep50',
    formater: (_item, value) => formatHumanReadableLocalDate(value),
  },
  {
    title: 'Période',
    value: 'periodNumber',
    sortable: false,
  },
  {
    title: 'Date enreg',
    value: 'date_enreg',
    sortable: false,
    formater: (_item, value) => formatHumanReadableLocalDate(value),
  },
  {
    title: 'Etape siège',
    value: 'siege_step',
    sortable: false,
  },
  {
    title: 'Date env.offre',
    value: 'offersDispatchDate',
    sortable: false,
    formater: (_item, value) => formatHumanReadableLocalDate(value),
  },
  {
    title: 'Date fin validité',
    value: 'date_fin_validite',
    sortable: false,
    formater: (_item, value) => formatHumanReadableLocalDate(value),
  },
  {
    title: "Instructeur de l'opération",
    value: 'instructorDisplayed',
    sortable: false,
  },
  {
    title: 'Nature logement',
    value: 'nature_logiement',
    sortable: false,
  },
  {
    title: 'Alerte Valo',
    value: 'alerte_valo',
    sortable: false,
  },
  {
    title: 'Com.comptabilisé',
    value: 'accountedMessagesNumber',
    sortable: false,
  },
  {
    title: 'Coup de pouce',
    value: 'boostType',
    sortable: false,
  },
  {
    title: 'CPE - %eco',
    value: 'epcEfficacity',
    sortable: false,
    cellClass: 'text-right justify-end',
  },
  {
    title: 'CPE - Durée',
    value: 'epcDuration',
    sortable: false,
    cellClass: 'text-right justify-end',
  },
  {
    title: '10 Soumettre une réservation CEE',
    value: 'date10',
    sortable: false,
    formater: (_item, value) => formatHumanReadableLocalDate(value),
  },
  {
    title: '20 Valider réservation région',
    value: 'date20',
    sortable: false,
    formater: (_item, value) => formatHumanReadableLocalDate(value),
  },
  {
    title: '30 Préparer et envoyer Offre avec volet CEE au client',
    value: 'date30',
    sortable: false,
    formater: (_item, value) => formatHumanReadableLocalDate(value),
  },
  {
    title: '40 Attente retour signature de la convention du client',
    value: 'date40',
    sortable: false,
    formater: (_item, value) => formatHumanReadableLocalDate(value),
  },
  {
    title: "50 Réaliser les travaux et regrouper les éléments CEE pour l'opération",
    value: 'date50',
    sortable: false,
    formater: (_item, value) => formatHumanReadableLocalDate(value),
  },
  {
    title: "60 Valider l'opération CEE par le BP Envt",
    value: 'date60',
    sortable: false,
    formater: (_item, value) => formatHumanReadableLocalDate(value),
  },
  {
    title: '70 Valider le contrôle du dossier ARRETE CONTROLE',
    value: 'date70',
    sortable: false,
    formater: (_item, value) => formatHumanReadableLocalDate(value),
  },
  {
    title: '80 Regrouper les opérations pour constitution du dossier EMMY',
    value: 'date80',
    sortable: false,
    formater: (_item, value) => formatHumanReadableLocalDate(value),
  },
  {
    title: '90 Envoyer la demande au PNCEE',
    value: 'date90',
    sortable: false,
    formater: (_item, value) => formatHumanReadableLocalDate(value),
  },
  {
    title: '100 Attente de la décision de délivrance',
    value: 'date100',
    sortable: false,
    formater: (_item, value) => formatHumanReadableLocalDate(value),
  },
  {
    title: '110 Mettre en place la note de crédit/Transfert de stock',
    value: 'date110',
    sortable: false,
    formater: (_item, value) => formatHumanReadableLocalDate(value),
  },
]
export const defaultExportHeaders: DataTableHeader<OperationExportResultDto>[] =
  firstPartHeader.concat(secondPartHeader)

export const oshFolderHeader: DataTableHeader<OperationExportResultDto>[] = [
  {
    title: 'Commentaire',
    value: 'oshCommentary',
  },
]
  .concat(firstPartHeader)
  .concat([
    {
      title: 'Date limite dépot PNCEE',
      value: 'limitPnceeDate',
      formater: (item) => format(minusBusinessDay(add(item.effectiveEndWorksDate!, { years: 1 }), 5), dateHumanFormat),
      sortable: false,
    },
  ] as DataTableHeader<OperationExportResultDto>[])
  .concat(secondPartHeader)
