<template>
  <VRow justify="start" align-content="center" class="flex-column nj-expansion-panel ma-0" no-gutters>
    <VCol class="nj-expansion-panel__title">
      <VListItem :prepend-icon="chevronIcon" :height="density === 'default' ? '56px' : '40px'" @click="updateValue">
        <slot name="title">
          <div class="d-flex align-center fill-width">
            {{ props?.title }}
            <VSpacer />
            <slot name="title-actions" />
          </div>
        </slot>
      </VListItem>
    </VCol>
    <!-- ExpensionTransition ralenti énormément la page -->
    <VCol v-show="opened">
      <div class="nj-expansion-panel__content">
        <slot></slot>
      </div>
    </VCol>
  </VRow>
</template>

<script setup lang="ts">
import type { PropType } from 'vue'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: true,
  },
  title: {
    type: String,
  },
  density: {
    type: String as PropType<'comfortable' | 'compact' | 'default'>,
    default: 'default',
  },
})

const emit = defineEmits(['update:model-value'])

const opened = ref(true)

watch(opened, (v) => {
  emit('update:model-value', v)
})

const updateValue = () => {
  opened.value = !opened.value
}

const chevronIcon = computed(() => (opened.value ? 'mdi-chevron-up' : 'mdi-chevron-down'))

watch(
  () => props.modelValue,
  (v) => {
    if (v != opened.value) {
      opened.value = v ?? false
    }
  },
  {
    immediate: true,
  }
)
</script>

<style lang="scss">
.nj-expansion-panel {
  &__title {
    font-size: 18px;
    font-weight: 700;
  }

  &__content {
    padding: 16px;
  }
}
</style>
