import type { AxiosInstance, AxiosPromise } from 'axios'
import axiosInstance from '.'
import type { UsedDocument } from '@/types/document'

class UsedDocumentApi {
  public constructor(private axios: AxiosInstance) {}

  findByOperation(operationId: number): AxiosPromise<UsedDocument[]> {
    return this.axios.get(`operations/${operationId}/used_documents`)
  }
}

export const usedDocumentApi = new UsedDocumentApi(axiosInstance)
