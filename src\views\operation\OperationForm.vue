<template>
  <VForm v-if="operation" ref="formRef" :readonly="promisableSimulation.loading" v-bind="$attrs">
    <VRow style="overflow: auto" :class="expandedDetail ? 'flex-row' : 'flex-column'" no-gutters>
      <VCol v-if="missingEntity" cols="12">
        <VAlert type="warning">
          Vous n'êtes rattaché à aucune agence et ne pourrez donc pas créer une opération. Veuillez contacter un
          administrateur
        </VAlert>
      </VCol>
      <VCol v-show="cumacHasChanged && !manualCumac" cols="12">
        <VAlert type="warning">
          Une donnée saisie a modifié le total des kWh cumacs obtenus pour cette opération.
        </VAlert>
      </VCol>
      <VCol>
        <NjExpansionPanel
          v-if="(mutableOperation.stepId === 0 && edit) || (createOperation && mutableOperation.id > 0)"
          title="Simulation"
        >
          <SimulationDisplayValue
            v-if="mutableOperation.stepId === 0 || mutableOperation.id > 0"
            v-model:operation="mutableOperation"
            :edit="mutableOperation.stepId === 0"
          />
        </NjExpansionPanel>
        <NjDivider v-if="mutableOperation.stepId === 0 && edit" />
        <StandardizedOperationSheetDetail
          expanded-detail
          :step-id="mutableOperation.stepId"
          :standardized-operation-sheet="mutableOperation.standardizedOperationSheet"
          :rge-granted-date="mutableOperation.rgeGrantedDate"
          :rge-end-of-validity-date="mutableOperation.rgeEndOfValidityDate"
          :validate-standardized-operation-sheet-rule="validateStandardizedOperationSheetRule"
          :validate-standardized-operation-sheet-and-commitment-validity-rule="
            validateStandardizedOperationSheetAndCommitmentValidityRule
          "
          :validate-rge-rule="validateRgeRule"
          :boost-bonus-sheets="boostBonusSheets.value"
          :epc-bonus-sheets="epcBonusSheets.value"
        >
          <template #rgeGranted>
            <NjDatePickerDisplayValue
              v-model="mutableOperation.rgeGrantedDate"
              label="Date d'attribution du RGE"
              :rules="
                operation.rgeGrantedDate || operation.rgeEndOfValidityDate || operation.stepId == 50
                  ? [
                      /*requiredRule*/
                    ]
                  : []
              "
              @update:model-value="formRef?.validate()"
            />
          </template>
          <template #rgeEndOfValidity>
            <NjDatePickerDisplayValue
              v-model="mutableOperation.rgeEndOfValidityDate"
              :color-title="!(validateRgeRule == true) ? 'warning' : undefined"
              label="Date de fin de validité du RGE"
              :rules="
                operation.rgeGrantedDate || operation.rgeEndOfValidityDate || operation.stepId == 50
                  ? [/*requiredRule, */ rgeEndOfValidityDateRule]
                  : [rgeEndOfValidityDateRule]
              "
            />
          </template>
        </StandardizedOperationSheetDetail>
        <NjDivider />

        <NjExpansionPanel>
          <template #title>
            <div class="d-flex align-center fill-width">
              <TitleInput :model-value="mutableOperation.property" :rules="propertyRules"> Installation </TitleInput>
              <VSpacer />
              <NjIconBtn
                v-if="mutableOperation.property !== null"
                icon="mdi-delete"
                color="primary"
                @click.stop="deleteProperty"
              />
              <VLink
                v-if="!isCertynergie()"
                size="small"
                icon="mdi-magnify"
                style="font-weight: initial; font-size: initial"
                @click.stop="propertyDialog = true"
              >
                Installations
              </VLink>
            </div>
          </template>
          <VRow class="flex-column">
            <VCol v-if="mutableOperation.property && mutableOperation.property.id > 0">
              <VRow class="flex-column">
                <VCol>
                  <PropertyDisplayValue :model-value="mutableOperation.property" :rules="propertyRules" />
                </VCol>
                <template
                  v-if="
                    currentProperty &&
                    mutableOperation.property &&
                    currentProperty.id === mutableOperation.property.id &&
                    currentProperty.updateDateTime !== mutableOperation.property.updateDateTime
                  "
                >
                  <VCol>
                    <VAlert type="warning">
                      Le référentiel Genesis a été mise à jour. Veuillez vérifier et appliquer les changements et
                      enregistrer les modifications<br />
                      <br />
                      <NjBtn color="black" variant="outlined" @click="updateProperty">Appliquer changements</NjBtn>
                    </VAlert>
                  </VCol>
                  <VCol>
                    <PropertyDisplayValue :model-value="mutableOperation.property" />
                  </VCol>
                </template>
              </VRow>
            </VCol>
            <VCol v-if="mutableOperation.property">
              <VSwitch
                v-model="usingSameAddressProperty"
                label="Adresse retenue identique à l'adresse de l'installation"
              />
            </VCol>

            <template v-if="!usingSameAddressProperty">
              <VCol>
                <VRow dense>
                  <VCol cols="12">
                    <NjTextField
                      v-model="mutableOperation.finalPropertyName"
                      label="Nom d'installation"
                      :rules="finalPropertyRequired ? [requiredRule] : []"
                    />
                  </VCol>
                  <VCol cols="12">
                    <NjTextField
                      v-model="mutableOperation.finalAddress.street"
                      label="Rue"
                      :rules="addressRules"
                      append-inner-icon="mdi-magnify"
                    />
                  </VCol>
                  <VCol cols="4">
                    <NjTextField
                      v-model="mutableOperation.finalAddress.postalCode"
                      label="Code postal"
                      :rules="codePostalRules"
                    />
                  </VCol>
                  <VCol cols="8">
                    <NjTextField v-model="mutableOperation.finalAddress.city" label="Ville" :rules="cityRules" />
                  </VCol>
                  <VCol cols="12">
                    <NjTextarea
                      v-model="mutableOperation.finalAddress.additionalPostalAddress"
                      label="Informations complémentaires"
                      lines
                      rows="1"
                      max-rows="15"
                      auto-grow
                    />
                  </VCol>
                  <VCol v-show="gouvSuggestedAdressRef?.displaySuggestion !== undefined" cols="12">
                    <GouvSuggestedAdress
                      ref="gouvSuggestedAdressRef"
                      v-model:city="mutableOperation.finalAddress.city"
                      v-model:postal-code="mutableOperation.finalAddress.postalCode"
                      v-model:street="mutableOperation.finalAddress.street"
                    />
                  </VCol>
                </VRow>
              </VCol>
            </template>
            <VCol>
              <NjSwitch
                v-model="showSyndicate"
                label="Syndicat de copropriété"
                class="nj-display-value__label"
                @update:model-value="
                  (v: boolean) => {
                    if (!v) {
                      mutableOperation.coOwnerShipSyndicateName = null
                      mutableOperation.coOwnerShipSyndicateImmatriculationNumber = null
                    }
                  }
                "
              />
            </VCol>
            <VCol v-if="showSyndicate">
              <div class="d-flex align-center fill-width">
                <VLink
                  class="flex-grow-1"
                  size="small"
                  icon="mdi-magnify"
                  style="font-weight: initial; font-size: initial"
                  @click.stop="coOwnerShipSyndicateDialog = true"
                >
                  Syndicats de copropriété
                </VLink>
                <NjIconBtn
                  :class="{
                    'opacity-0': !(
                      mutableOperation?.coOwnerShipSyndicateName ||
                      mutableOperation?.coOwnerShipSyndicateImmatriculationNumber
                    ),
                  }"
                  icon="mdi-delete"
                  color="primary"
                  @click.stop="handleUpdateSelectedCoOwnerShipSyndicate([])"
                />
              </div>
              <VCard
                v-if="
                  mutableOperation?.coOwnerShipSyndicateName ||
                  mutableOperation?.coOwnerShipSyndicateImmatriculationNumber
                "
              >
                <VCardText class="pa-2">
                  <template v-if="mutableOperation?.coOwnerShipSyndicateName">
                    <NjDisplayValue
                      label="Syndicat de copropriété"
                      :value="mutableOperation?.coOwnerShipSyndicateName"
                    ></NjDisplayValue>
                  </template>
                  <template v-if="mutableOperation?.coOwnerShipSyndicateImmatriculationNumber">
                    <NjDisplayValue
                      label="Numéro d'immatriculation du syndicat de copropriété"
                      :value="mutableOperation?.coOwnerShipSyndicateImmatriculationNumber"
                    ></NjDisplayValue>
                  </template>
                </VCardText>
              </VCard>
            </VCol>
          </VRow>
        </NjExpansionPanel>
        <NjDivider />
        <OperationField
          v-model:operation="mutableOperation"
          v-model:selected-entity="selectedEntity"
          expanded-detail
          :form-ref="formRef"
          :estimated-end-operation-rules="estimatedEndOperationRules"
          :validate-estimated-commitment-date-rule="validateEstimatedCommitmentDateRule"
          :validate-estimated-end-work-rule="validateEstimatedEndWorkRule"
          :validate-operation="validateOperation"
          :validate-step-duration-rule="validateStepDurationRule"
          :validate-subcontractor-rule="validateSubcontractorRule"
          :validate-rge-rule="validateRgeRule"
          :create-operation="createOperation"
          edit
          @update:selected-entity="
            (event) => {
              mutableOperation.entity = event ? event : makeEmptyEntity()
              if (mutableOperation.id == 0) {
                mutableOperation.instructor = event?.entityDetails.effectiveInstructor ?? makeEmptyUser()
              }
            }
          "
        />
        <NjDivider v-if="!expandedDetail" />
      </VCol>
      <ValidStandardizedOperationSheetDialog
        v-if="changeStandardizedOperationSheet"
        v-model="changeStandardizedOperationSheet"
        v-model:operation="mutableOperation"
        width="100%"
        @update:operation="emit('update:operation', $event)"
      />
      <VCol>
        <NjExpansionPanel>
          <template #title>
            <VRow class="align-center">
              <VCol> Volume et valorisation </VCol>
              <VCol v-if="edit && !preview" class="flex-grow-0">
                <VLink
                  size="small"
                  icon="mdi-format-list-bulleted"
                  style="font-weight: initial; font-size: initial; width: max-content"
                  @click.stop="updateOperationSheet"
                >
                  Fiches opération
                </VLink>
              </VCol>
            </VRow>
          </template>
          <VRow class="flex-column">
            <VCol v-if="operation.atypicalClassicValuationValue || operation.atypicalPrecariousnessValuationValue">
              <VAlert type="info" variant="outlined"> Cette opération est soumise à une valorisation atypique </VAlert>
            </VCol>
            <VCol
              v-if="
                mutableOperation.valuationType.id &&
                valuations === undefined &&
                !(mutableOperation.classicValuationValue && mutableOperation.precariousnessValuationValue)
              "
            >
              <VAlert type="warning" variant="outlined"> Aucune valorisation correspondante n'a été trouvée </VAlert>
            </VCol>
            <VCol>
              <NjSelect
                v-model="mutableOperation.valuationType"
                label="Type de valorisation"
                :items="activeValuationTypes"
                item-title="name"
                return-object
                :rules="[requiredRule]"
              />
            </VCol>
            <VCol v-if="warningMessages.length > 0">
              <VAlert type="warning">
                <template #text>
                  <div v-for="(message, index) in warningMessages" :key="index">
                    <VIcon>mdi-circle-small</VIcon>
                    {{ message }}
                  </div>
                </template>
              </VAlert>
            </VCol>
            <VCol>
              <NjSwitch
                v-model="mutableOperation.headOperation"
                label="Opération chapeau"
                class="nj-display-value__label"
                @update:model-value="
                  (v: boolean) => {
                    if (!v) {
                      mutableOperation.estimatedNumberOfOperationInHead = null
                    }
                  }
                "
              />
            </VCol>
            <VCol v-if="mutableOperation.headOperation">
              <NjTextField
                v-model="mutableOperation.estimatedNumberOfOperationInHead"
                label="Nombre estimé d'opérations"
                :rules="[emptyOrNumericRule]"
              />
            </VCol>
            <VCol>
              <VSwitch v-model="manualCumac" label="Saisir les kWhc manuellement" />
              <AlertDialog title="Attention" v-bind="manualCumacDialog.props" width="40%">
                L'import Emmy ne pourra pas fonctionner avec la saisie manuelle
              </AlertDialog>
            </VCol>
            <VCol v-if="manualCumac" class="pb-0">
              <VRow class="flex-column" dense>
                <VCol>
                  <NjTextField
                    v-model.number="mutableOperation.classicCumac"
                    type="number"
                    label="kWhc classique"
                    :rules="[
                      positiveOrNullNumericRuleGenerator(
                        'Le total ne doit pas être négatif. Veuillez vérifiez votre saisie ou contacter l\'administrateur'
                      ),
                    ]"
                  />
                </VCol>
                <VCol>
                  <NjTextField
                    v-model.number="mutableOperation.precariousnessCumac"
                    type="number"
                    label="kWhc précarite"
                    :rules="[
                      positiveOrNullNumericRuleGenerator(
                        'Le total ne doit pas être négatif. Veuillez vérifiez votre saisie ou contacter l\'administrateur'
                      ),
                    ]"
                  />
                </VCol>
                <VCol>
                  <NjDisplayValue label="Total en kWhc :" :value="classicValuationCalculus" align="end">
                    <template #value>
                      <span class="text-primary font-weight-bold">
                        {{
                          formatNumber(
                            Number(mutableOperation.classicCumac) + Number(mutableOperation.precariousnessCumac)
                          ) + ' kWhc'
                        }}
                      </span>
                    </template>
                  </NjDisplayValue>
                </VCol>
                <VCol>
                  <NjDisplayValue label="Valorisation totale" :value="classicValuationCalculus" align="end">
                    <template #value>
                      <span v-if="!!classicValuationCalculus" class="text-primary font-weight-bold">
                        {{ formatPriceNumber(classicValuationCalculus + precariousnessValuationCalculus) }}
                      </span>
                    </template>
                  </NjDisplayValue>
                </VCol>
              </VRow>
            </VCol>
            <template v-else>
              <VCol v-if="!edit && (!usingSameAddressProperty || mutableOperation.property === null)">
                <NjTextField
                  v-model="mutableOperation.finalAddress.postalCode"
                  label="Code postal"
                  :rules="codePostalRules"
                ></NjTextField>
              </VCol>
              <VCol>
                <StandardizedOperationSheetCalculator
                  v-model="mutableOperation.parameterValues"
                  mode="edit"
                  :standardized-operation-sheet="mutableOperation.standardizedOperationSheet"
                  :predefined-values="predefinedValues"
                  :required="!manualCumac"
                  @update:cumac="volumeCEE = $event"
                />
              </VCol>
              <VDivider />
              <VCol cols="auto">
                <VRow align="center">
                  <VCol class="text-no-wrap">
                    <VLabel> Coup de pouce </VLabel>
                  </VCol>
                  <VCol class="text-end flex-grow-0" style="flex-basis: content">
                    <VProgressCircular v-if="boostBonusSheet.loading" indeterminate />
                    <VAlert
                      v-else-if="boostBonusSheet.error"
                      :type="
                        boostBonusSheet.error.startsWith('Aucun') || boostBonusSheet.error.startsWith('Indispo')
                          ? 'info'
                          : 'error'
                      "
                      density="compact"
                      variant="tonal"
                      :icon="false"
                      >{{ boostBonusSheet.error }}</VAlert
                    >
                    <VSwitch v-else v-model="showBoostBonus" :disabled="showEpcBonus" />
                  </VCol>
                </VRow>
                <VLink
                  v-if="showBoostBonus && adminConfigurationStore.boostBonusEligibilityDocument"
                  @click="downloadAdminConfigurationDocument(adminConfigurationStore.boostBonusEligibilityDocument)"
                >
                  Cliquez ici pour vérifier les conditions d'éligibilité au coup de pouce
                </VLink>
              </VCol>
              <VCol v-if="showBoostBonus && boostBonusSheet.value">
                <BoostBonusSheetCalculator
                  v-model="boostBonusSimulationValues"
                  result-title="Total CEE avec Coup de Pouce : "
                  :calcul-formula="boostBonusSheet.value.calculFormula"
                  :field-required="true"
                  :predefined-value="predefinedValuesForBoostBonusSheet"
                  mode="edit"
                  @update:cumac="volumeCEEAfterBoostBonusSheet = $event"
                />
              </VCol>
              <VDivider />
              <VCol>
                <VRow align="center">
                  <VCol class="text-no-wrap">
                    <VLabel> CPE </VLabel>
                  </VCol>
                  <VCol class="text-end flex-grow-0" style="flex-basis: content">
                    <VProgressCircular v-if="epcBonusSheet.loading" indeterminate />
                    <VAlert
                      v-else-if="epcBonusSheet.error"
                      :type="
                        epcBonusSheet.error.startsWith('Aucun') || epcBonusSheet.error.startsWith('Indispo')
                          ? 'info'
                          : 'error'
                      "
                      density="compact"
                      variant="tonal"
                      :icon="false"
                      >{{ epcBonusSheet.error }}</VAlert
                    >
                    <VSwitch v-else v-model="showEpcBonus" :disabled="showBoostBonus" />
                  </VCol>
                </VRow>
              </VCol>
              <template v-if="showEpcBonus && epcBonusSheet.value">
                <VCol v-if="adminConfigurationStore.epcEligibilityDocument">
                  <VLink @click="downloadAdminConfigurationDocument(adminConfigurationStore.epcEligibilityDocument)">
                    Cliquez ici pour vérifier les conditions d'éligibilité au CPE
                  </VLink>
                </VCol>
                <VCol>
                  <EpcBonusCalculator
                    v-model="epcBonusSimulationValues"
                    :epc-bonus-sheet="epcBonusSheet.value!"
                    edit
                    :volume-cee="volumeCEE"
                    @update:b-cpe="epcBonus = $event"
                  />
                </VCol>
              </template>

              <VDivider />
              <VCol>
                <VRow align="center">
                  <VCol class="text-no-wrap">
                    <VLabel> Précarité </VLabel>
                  </VCol>
                  <VCol class="text-end flex-grow-0" style="flex-basis: content">
                    <VProgressCircular v-if="precariousnessBonusSheet.loading" indeterminate />
                    <VAlert
                      v-else-if="precariousnessBonusSheet.error"
                      :type="
                        precariousnessBonusSheet.error.startsWith('Aucun') ||
                        precariousnessBonusSheet.error.startsWith('Indispo')
                          ? 'info'
                          : 'error'
                      "
                      density="compact"
                      variant="tonal"
                      :icon="false"
                      >{{ precariousnessBonusSheet.error }}</VAlert
                    >
                    <VSwitch v-else v-model="showPrecariousnessBonus" density="comfortable" />
                  </VCol>
                </VRow>
              </VCol>
              <template v-if="showPrecariousnessBonus && precariousnessBonusSheet.value">
                <VCol>
                  <VLink
                    v-if="adminConfigurationStore.precariousnessLink?.data"
                    :href="adminConfigurationStore.precariousnessLink?.data"
                    target="_blank"
                  >
                    Cliquez ici pour vérifier si la copropriété est située en QPV
                  </VLink>
                </VCol>
                <VCol>
                  Précarité appliquée : <span style="font-weight: 700">{{ precariousnessBonusSheet.value.name }}</span>
                </VCol>
                <VCol>
                  <PrecariousnessBonusSheetCalculator
                    v-model:values="precariousnessBonusSimulationValues"
                    v-model:cee-classic="calculatedClassicCumac"
                    v-model:cee-precariousness="calculatedPrecariousnessCumac"
                    :precariousness-bonus-sheet="precariousnessBonusSheet.value"
                    :predefined-values="predefinedValuesForPrecariousnessBonus"
                    :cumacs="volumeCEEForPrecariousness"
                  />
                </VCol>
              </template>
            </template>

            <NjDivider v-if="!manualCumac" />
            <VCol>
              <VRow class="flex-column" dense>
                <template v-if="!manualCumac">
                  <VCol>
                    <VInput
                      :model-value="totalCEEClassique + totalCEEPrecariousness"
                      :rules="[positiveOrNullNumericRuleGenerator()]"
                      hide-details="auto"
                    >
                      <NjDisplayValue label="Total/kWhc" align="end">
                        <template #value>
                          <span class="text-primary font-weight-bold">
                            {{
                              formatNumber(
                                (!isNaN(totalCEEClassique ?? 0) ? (totalCEEClassique ?? 0) : 0) +
                                  (!isNaN(totalCEEPrecariousness ?? 0) ? (totalCEEPrecariousness ?? 0) : 0)
                              ) + ' kWhc'
                            }}
                          </span>
                        </template>
                      </NjDisplayValue>
                      <template #message="{ message }">
                        <div class="text-end">
                          {{ message }}
                        </div>
                      </template>
                    </VInput>
                  </VCol>
                  <VCol>
                    <NjDisplayValue label="Valorisation totale" align="end">
                      <template #value>
                        <span class="text-primary font-weight-bold">
                          {{
                            formatPriceNumber(
                              (!isNaN(classicValuationCalculus ?? 0) ? (classicValuationCalculus ?? 0) : 0) +
                                (!isNaN(precariousnessValuationCalculus ?? 0)
                                  ? (precariousnessValuationCalculus ?? 0)
                                  : 0)
                            )
                          }}
                        </span>
                      </template>
                    </NjDisplayValue>
                  </VCol>
                </template>
                <VCol>
                  <NjDisplayValue label="Total classique kWhc :" align="end">
                    <template #value>
                      <div class="ms-n3">
                        <span class="font-weight-bold">
                          {{
                            formatNumber(
                              manualCumac
                                ? mutableOperation.classicCumac
                                : !isNaN(totalCEEClassique ?? 0)
                                  ? (totalCEEClassique ?? 0)
                                  : 0
                            )
                          }}
                        </span>
                        avec valorisation :
                        <span class="font-weight-bold">
                          {{
                            formatPriceNumber(
                              mutableOperation.atypicalClassicValuationValue ??
                                (mutableOperation.classicValuationValue ||
                                  valuations?.find((valuation) => !valuation.precariousness)?.value) ??
                                0
                            ) + '/MWhc'
                          }}
                        </span>
                      </div>
                    </template>
                  </NjDisplayValue>
                </VCol>
                <VCol>
                  <NjDisplayValue label="Total précarité kWhc :" align="end">
                    <template #value>
                      <div class="ms-n3">
                        <span class="font-weight-bold">
                          {{
                            formatNumber(
                              manualCumac
                                ? mutableOperation.precariousnessCumac
                                : !isNaN(totalCEEPrecariousness ?? 0)
                                  ? (totalCEEPrecariousness ?? 0)
                                  : 0
                            )
                          }}
                        </span>
                        avec valorisation :
                        <span class="font-weight-bold">
                          {{
                            formatPriceNumber(
                              mutableOperation.atypicalPrecariousnessValuationValue ??
                                (mutableOperation.precariousnessValuationValue ||
                                  valuations?.find((valuation) => valuation.precariousness)?.value) ??
                                0
                            ) + '/MWhc'
                          }}
                        </span>
                      </div>
                    </template>
                  </NjDisplayValue>
                </VCol>
                <VCol v-if="!manualCumac && operation.legacyBoostBonusSimulation">
                  <ErrorAlert
                    type="warning"
                    message="Attention le coup de pouce boCEE sera perdu mais vous pouvez le saisir avec l'outil de calcul"
                  />
                </VCol>
                <VCol v-if="operation.legacyBoostBonusSimulation">
                  <LegacyBoostBonusSimulationDisplayValue
                    :legacy-boost-bonus-simulation="operation.legacyBoostBonusSimulation"
                  />
                </VCol>
              </VRow>
            </VCol>
            <NjDivider />
            <VCol>
              <NjExpansionPanel title="Incitation financière">
                <VRow class="flex-column" dense>
                  <VCol>
                    <VTooltip :disabled="!mutableOperation.operationsGroup?.commercialOfferWithoutFinancialIncentive">
                      <template #activator="{ props }">
                        <NjTextField
                          v-bind="props"
                          v-model="mutableOperation.commercialOfferWithoutFinancialIncentive"
                          :readonly="mutableOperation.operationsGroup?.commercialOfferWithoutFinancialIncentive != null"
                          :disabled="mutableOperation.selfWorks"
                          type="number"
                          label="Offre commerciale avant incitation financière  CEE/€ TTC"
                          suffix="€"
                          :rules="mutableOperation.selfWorks ? [] : [financialIncentiveRule]"
                          :append-inner-icon="
                            !!mutableOperation.operationsGroup?.customerFinancialIncentive ? 'mdi-lock' : undefined
                          "
                        />
                      </template>
                      <div>Offre commercial fixée par le regroupement</div>
                    </VTooltip>
                  </VCol>
                  <VCol>
                    <VTooltip :disabled="!mutableOperation.operationsGroup?.customerFinancialIncentive">
                      <template #activator="{ props }">
                        <NjTextField
                          v-bind="props"
                          v-model="mutableOperation.customerFinancialIncentive"
                          :disabled="mutableOperation.selfWorks"
                          :readonly="mutableOperation.operationsGroup?.customerFinancialIncentive != null"
                          type="number"
                          label="Incitation financière CEE client/€ TTC"
                          suffix="€"
                          :rules="mutableOperation.selfWorks ? [] : [financialIncentiveRule]"
                          :append-inner-icon="
                            !!mutableOperation.operationsGroup?.customerFinancialIncentive ? 'mdi-lock' : undefined
                          "
                        />
                      </template>
                      <div>Incitation financière fixée par le regroupement</div>
                    </VTooltip>
                  </VCol>
                  <VCol>
                    <VInput
                      :rules="mutableOperation.selfWorks ? [] : [financialIncentiveRule]"
                      :model-value="commercialOfferWithFinancialIncentive"
                      hide-details="auto"
                    >
                      <NjDisplayValue
                        label="Montant de l'offre commerciale après déduction de l'incitation financière CEE/€ TTC"
                        :value="formatPriceNumber(commercialOfferWithFinancialIncentive)"
                      />
                    </VInput>
                  </VCol>
                  <VCol>
                    <NjDisplayValue
                      label="Charge Pôle CEE"
                      :value="
                        formatPriceNumber(
                          ((manualCumac
                            ? mutableOperation.classicCumac + mutableOperation.precariousnessCumac
                            : totalCEEClassique + totalCEEPrecariousness) /
                            1000) *
                            getPoleChargeCEE(mutableOperation)
                        )
                      "
                    />
                  </VCol>
                  <VCol>
                    <NjDisplayValue label="Marge CEE Nette TTC" :value="formatPriceNumber(netMargin)" />
                  </VCol>
                </VRow>
              </NjExpansionPanel>
            </VCol>
          </VRow>
        </NjExpansionPanel>
        <NjDivider v-if="!expandedDetail" />
      </VCol>

      <VCol v-if="edit">
        <NjExpansionPanel>
          <template #title>
            <div class="d-flex align-center fill-width">
              <TitleInput
                :model-value="mutableOperation.beneficiary"
                :rules="mutableOperation.stepId >= 10 ? [requiredRule] : undefined"
              >
                Bénéficiaire
                <AlertIcon :rules="[validateBeneficiaryRule]" />
              </TitleInput>
              <VSpacer />
              <template v-if="!mutableOperation.operationsGroup">
                <NjIconBtn
                  v-if="mutableOperation.beneficiary !== null && !mutableOperation.operationsGroup"
                  icon="mdi-delete"
                  color="primary"
                  @click.stop="mutableOperation.beneficiary = null"
                />
                <VLink
                  size="small"
                  icon="mdi-format-list-bulleted"
                  style="font-weight: initial; font-size: initial"
                  @click.stop="openBeneficiaryDialog"
                  >Bénéficiaires</VLink
                >
              </template>
              <VTooltip v-else>
                <template #activator="{ props }">
                  <VIcon icon="mdi-information-outline" v-bind="props"></VIcon>
                </template>

                La gestion du bénéficiaire se fait dans le regroupement
              </VTooltip>
            </div>
          </template>
          <BeneficiaryInput
            ref="beneficiaryDisplayValueRef"
            :with-color="operation.stepId >= 30"
            :model-value="mutableOperation.beneficiary"
            :rules="mutableOperation.stepId >= 10 ? [requiredRule] : undefined"
          />
          <NjDisplayValue
            v-if="!mutableOperation.beneficiary && operation.legacyBeneficiary"
            label="Ancienne valeur"
            :value="operation.legacyBeneficiary"
          />
        </NjExpansionPanel>
        <NjDivider />
        <WorksField
          v-model:final-works-type="mutableOperation.finalWorksType"
          :operation="mutableOperation"
          edit
          @update:self-works="
            (v) => {
              mutableOperation.selfWorks = v
              if (v) {
                mutableOperation.customerFinancialIncentive = 0
                mutableOperation.commercialOfferWithoutFinancialIncentive = 0
              }
            }
          "
          @delete="(mutableOperation as any)['works' + $event] = undefined"
          @selected="selectWorks"
        />

        <NjDivider />
        <SubcontractorInputPanel
          v-model:subcontractor="mutableOperation.subcontractor"
          :model-value="true"
          :is-subcontractor-mandatory="mutableOperation.standardizedOperationSheet.subcontractCompulsory"
          :can-certified="canCertified"
        />
        <NjDivider />
        <NjExpansionPanel>
          <template #title>
            <div class="d-flex full-width align-center">
              <div class="flex-grow-1">Organisme de contrôle</div>
              <div class="flex-grow-1 text-end text-no-wrap">
                <NjIconBtn
                  v-if="mutableOperation.controlOrganism !== null"
                  icon="mdi-delete"
                  color="primary"
                  @click.stop="mutableOperation.controlOrganism = null"
                />
                <VLink
                  size="small"
                  icon="mdi-format-list-bulleted"
                  style="font-weight: initial; font-size: initial"
                  @click.stop="displayControlOrganisms = true"
                  >Organismes</VLink
                >
              </div>
            </div>
          </template>
          <!-- <VTextField
            v-model="mutableOperation.orderNumber"
            :rules="
              mutableOperation.stepId >= 60 &&
              mutableOperation.standardizedOperationSheet.controlOrderNature === 'HUNDRED_PERCENT'
                ? [requiredRule]
                : []
            "
            label="Numéro de commande"
            class="mb-4"
          /> -->
          <ControlOrganismDisplayValue :model-value="mutableOperation.controlOrganism" />
        </NjExpansionPanel>
        <ControlOrganismDialog
          v-model="displayControlOrganisms"
          v-model:control-organism="mutableOperation.controlOrganism"
        />
        <template v-if="mutableOperation.stepId >= 20">
          <NjDivider />
          <NjExpansionPanel title="Doublon/cumul">
            <VCheckbox v-model="mutableOperation.eesDuplicate" label="Recherche dans la base EES" />
            <VCheckbox v-model="mutableOperation.customerDuplicate" label="Recherche dans la base du client" />
            <VCheckbox v-model="mutableOperation.cumul" label="Vérification des règles de cumul" />
          </NjExpansionPanel>
        </template>
        <DoublonDialog v-if="createOperation" v-model="checkDoublon">
          <template #activator><span></span></template>
          <template #complements="attrs">
            <VRow v-bind="attrs">
              <VCol cols="3"> Je déclare avoir effectué la recherche dans : </VCol>
              <VCol cols="3">
                <NjSwitch v-model="mutableOperation.eesDuplicate" label="Base EES" inline />
              </VCol>
              <VCol cols="3">
                <NjSwitch v-model="mutableOperation.cumul" label="Règles de cumul" inline />
              </VCol>
              <VCol cols="3">
                <NjSwitch v-model="mutableOperation.customerDuplicate" label="Base du client" inline />
              </VCol>
            </VRow>
          </template>
          <template #actions="attrs">
            <VCardActions v-bind="attrs">
              <VSpacer />
              <NjBtn variant="outlined" @click="cancelSave"> Annuler </NjBtn>
              <NjBtn @click="saveOperation"> Valider </NjBtn>
            </VCardActions>
          </template>
        </DoublonDialog>
        <NjDivider v-if="operation.stepId > 60 && operation.controlOrderBatch" />
        <ControlOrderField
          v-if="operation.stepId > 60 && operation.controlOrderBatch"
          v-model:control-order-details="mutableOperation.controlOrderDetails"
          :operation="mutableOperation"
          edit
        />
        <NjDivider v-if="mutableOperation.emmyFolder !== null" />
        <NjExpansionPanel
          v-if="mutableOperation.emmyFolder !== null"
          title="Dossier EMMY"
          :model-value="expandedDetail"
        >
          <EmmyFolderDisplayValue :operation="mutableOperation" />
        </NjExpansionPanel>
        <NjDivider v-if="mutableOperation.stepId > 0 && !createOperation" />
        <NjExpansionPanel
          v-if="mutableOperation.stepId > 0 && !createOperation"
          title="Simulation"
          :model-value="expandedDetail"
        >
          <SimulationDisplayValue :operation="mutableOperation" />
        </NjExpansionPanel>

        <NjDivider v-if="userStore.isAdmin || mutableOperation.stepId > 10" />
        <NjExpansionPanel
          v-if="userStore.isAdmin || mutableOperation.stepId > 10"
          title="Administration"
          :model-value="expandedDetail"
        >
          <VRow v-if="userStore.isAdmin" dense class="flex-column">
            <VCol>
              <NjDisplayValue
                label="Organisation actuelle"
                :value="displayEntity(mutableOperation.entity)"
                class="align-center"
              >
                <template #value>
                  <div class="w-50">
                    <RemoteAutoComplete
                      v-model="mutableOperation.entity"
                      :query-for-all="(search, pageable) => entityApi.getAll({ search: search, level: 4 }, pageable)"
                      :query-for-one="(org: Entity) => entityApi.getOne(org?.id ?? 0)"
                      :item-title="displayEntity"
                      :rules="[requiredRule]"
                      return-object
                      infinite-scroll
                      clearable
                      @update:model-value="(v) => (mutableOperation.entity = v ?? makeEmptyEntity())"
                    />
                  </div>
                </template>
              </NjDisplayValue>
            </VCol>
            <VCol v-if="userStore.isAdminPlus">
              <NjDisplayValue
                label="Organisation avant réorganisation"
                :value="displayEntity(mutableOperation.entity)"
                class="align-center"
              >
                <template #value>
                  <div class="w-50">
                    <RemoteAutoComplete
                      v-model="mutableOperation.leadingEntity"
                      :query-for-all="(search, pageable) => entityApi.getAll({ search: search, level: 4 }, pageable)"
                      :query-for-one="(org: Entity) => entityApi.getOne(org?.id ?? 0)"
                      :item-title="displayEntity"
                      :rules="[requiredRule]"
                      return-object
                      clearable
                      infinite-scroll
                      @update:model-value="(v) => (mutableOperation.leadingEntity = v ?? makeEmptyEntity())"
                    />
                  </div>
                </template>
              </NjDisplayValue>
            </VCol>
            <VCol>
              <VAlert v-if="mutableOperation.operationsGroup" density="compact" class="mb-2" type="info"
                >Valo. Atypique défini par le regroupement</VAlert
              >
              <NjDisplayValue label="Valorisations atypiques">
                <template #value>
                  <template
                    v-if="
                      userHasRole(userStore.currentUser, 'ADMIN_PLUS') ||
                      userStore.currentUser.id === mutableOperation.entity.effectiveTerritoryReferent!.id
                    "
                  >
                    <div class="w-50">
                      <VTextField
                        v-model="mutableOperation.atypicalClassicValuationValue"
                        type="number"
                        label="Classique"
                        suffix="€/MWhC"
                        class="pb-4"
                        :readonly="!!mutableOperation.operationsGroup"
                        :append-inner-icon="mutableOperation.operationsGroup ? 'mdi-lock' : undefined"
                      />
                      <VTextField
                        v-model="mutableOperation.atypicalPrecariousnessValuationValue"
                        type="number"
                        label="Précarité"
                        suffix="€/MWhC"
                        :readonly="!!mutableOperation.operationsGroup"
                        :append-inner-icon="mutableOperation.operationsGroup ? 'mdi-lock' : undefined"
                      />
                    </div>
                  </template>
                  <template v-else>
                    <div class="d-flex flex-column">
                      <div class="d-flex text-no-wrap justify-end" style="gap: 4px">
                        <span class="nj-display-value__label"> Classique : </span>
                        <span v-if="operation.atypicalClassicValuationValue" class="font-weight-bold">
                          {{ operation.atypicalClassicValuationValue + ' €/MWhc' }}
                        </span>
                        <span v-else> Aucune valorisation atypique </span>
                      </div>
                      <div class="d-flex text-no-wrap justify-end" style="gap: 4px">
                        <span class="nj-display-value__label"> Précarité : </span>
                        <span v-if="operation.atypicalPrecariousnessValuationValue" class="font-weight-bold">
                          {{ operation.atypicalPrecariousnessValuationValue + ' €/MWhc' }}
                        </span>
                        <span v-else> Aucune valorisation atypique </span>
                      </div>
                    </div>
                  </template>
                </template>
              </NjDisplayValue>
            </VCol>
            <VCol>
              <NjDisplayValue label="Charge pôle CEE spécifique">
                <template #value>
                  <div class="w-50">
                    <NjTextField
                      type="number"
                      suffix="€/MWhC"
                      :model-value="mutableOperation.specialFee"
                      clearable
                      @update:model-value="mutableOperation.specialFee = $event == '' ? null : parseFloat($event)"
                    />
                  </div>
                </template>
              </NjDisplayValue>
            </VCol>
            <VCol v-if="mutableOperation.entity.entityDetails.effectiveFee">
              <NjDisplayValue
                label="Charge pôle CEE unitaire de l'organisation"
                :value="mutableOperation.entity.entityDetails.effectiveFee + ' €/MWhc'"
              />
            </VCol>

            <VCol v-if="mutableOperation.stepId > 0 && userIsAdmin(userStore.currentUser)">
              <NjDisplayValue
                label="Date de réservation"
                :value="formatHumanReadableLocalDate(mutableOperation.reservedDate)"
                :color-title="mutableOperation.reservedDate && validateOperationDurationRule !== true ? 'warning' : ''"
              >
                <template v-if="mutableOperation.stepId < 70" #value>
                  <div class="w-50">
                    <NjDatePicker
                      v-model="mutableOperation.reservedDate"
                      :color-title="!(validateOperationDurationRule == true) ? 'warning' : ''"
                      :rules="mutableOperation.stepId > 10 ? [requiredRule] : undefined"
                    />
                  </div>
                </template>
              </NjDisplayValue>
            </VCol>

            <VCol v-if="mutableOperation.stepId > 40">
              <NjDisplayValue
                label="Date de l'arrivée au pôle CAP"
                :value="formatHumanReadableLocalDate(mutableOperation.caseDate)"
              >
                <template v-if="mutableOperation.stepId < 70" #value>
                  <div class="w-50">
                    <NjDatePicker
                      v-model="mutableOperation.caseDate"
                      :rules="mutableOperation.stepId >= 50 ? [requiredRule] : undefined"
                    />
                  </div>
                </template>
              </NjDisplayValue>
            </VCol>
          </VRow>
          <VRow v-else dense class="flex-column">
            <VCol v-if="operation.atypicalClassicValuationValue || operation.atypicalPrecariousnessValuationValue">
              <NjDisplayValue label="Valorisations atypiques">
                <template #value>
                  <div class="w-50">
                    <div class="d-flex text-no-wrap justify-end" style="gap: 4px">
                      <span class="nj-display-value__label"> Classique : </span>
                      <span class="font-weight-bold">
                        {{ operation.atypicalClassicValuationValue + ' €/MWhc' }}
                      </span>
                    </div>
                    <div class="d-flex text-no-wrap justify-end" style="gap: 4px">
                      <span class="nj-display-value__label"> Précarité : </span>
                      <span class="font-weight-bold">
                        {{ operation.atypicalPrecariousnessValuationValue + ' €/MWhc' }}
                      </span>
                    </div>
                  </div>
                </template>
              </NjDisplayValue>
            </VCol>
            <VCol v-if="mutableOperation.specialFee">
              <NjDisplayValue
                v-model="mutableOperation.specialFee"
                label="Charge pôle CEE spécifique"
                suffix="€/MWhC"
                clearable
              />
            </VCol>
            <VCol>
              <NjDisplayValue
                :label="
                  'Charge pôle CEE unitaire de l\'organisation (' +
                  (mutableOperation.feeIssuedEntity
                    ? mutableOperation.feeIssuedEntity.name
                    : resolvedChargePoleCEEEntity.value!.name) +
                  ')'
                "
                :value="mutableOperation.entity.entityDetails.effectiveFee + ' €/MWhc'"
              />
            </VCol>
            <VCol>
              <NjDisplayValue
                label="Date de réservation"
                :value="formatHumanReadableLocalDate(mutableOperation.reservedDate)"
                :color-title="mutableOperation.reservedDate && validateOperationDurationRule !== true ? 'warning' : ''"
              />
            </VCol>

            <VCol v-if="mutableOperation.stepId > 40">
              <NjDisplayValue
                label="Date de l'arrivé au pôle CAP"
                :value="formatHumanReadableLocalDate(mutableOperation.caseDate)"
              />
            </VCol>
          </VRow>
        </NjExpansionPanel>
      </VCol>
    </VRow>
  </VForm>
  <BeneficiaryDialog
    v-model="beneficiaryDialog"
    :can-certified="canCertified"
    :selected="mutableOperation.beneficiary ? [mutableOperation.beneficiary] : ([] as Beneficiary[])"
    @update:selected="handleUpdateSelectedBeneficiary"
  />

  <PropertyDialog
    v-model="propertyDialog"
    :operation-entity="mutableOperation.entity"
    :selected="mutableOperation.property ? [mutableOperation.property] : ([] as Property[])"
    @update:selected="handleUpdateSelectedProperty"
  />

  <CoOwnerShipSyndicateDialog
    v-model="coOwnerShipSyndicateDialog"
    :selected="
      mutableOperation.coOwnerShipSyndicate ? [mutableOperation.coOwnerShipSyndicate] : ([] as CoOwnerShipSyndicate[])
    "
    :operation-entity="mutableOperation.entity"
    :address="{
      street: mutableOperation.finalAddress?.street || mutableOperation.property?.streetName || '',
      postalCode: mutableOperation.finalAddress?.postalCode || mutableOperation.property?.postalCode || '',
      city: mutableOperation.finalAddress?.city || mutableOperation.property?.city || '',
      country: null,
    }"
    @update:selected="handleUpdateSelectedCoOwnerShipSyndicate"
  />

  <AlertDialog
    title="Vous allez changer de zone climatique"
    v-bind="alertChangeZoneClimatiqueComposition.props"
    max-width="640px"
  >
    Vous allez sauvegarder un changement d'adresse qui implique un changement de zone climatique.<br />
    Cela implique notamment un changement dans le calcul des kWh cumac.<br />
    Êtes-vous certain d'approuver ces modifications ?
  </AlertDialog>

  <AlertDialog title="Vous allez changer d'organisation" v-bind="alertChangeEntity.props" max-width="640px">
    Vous avez modifier l'organisation de l'opération (soit manuellement, soit en changeant d'installations).
    <br />
    Êtes-vous certain d'approuver ces modifications ?
  </AlertDialog>
  <ConfirmUnsavedDataDialog v-model:unsaved-data-dialog="unsavedDataDialog" @save="saveOperation" />
  <AlertDialog
    title="Total Cumac modifié"
    max-width="640px"
    v-bind="alertModifyCumacDialog.props"
    :negative-button="false"
  >
    Une donnée saisie a modifié le total des kWh cumacs obtenus pour cette opération.<br />
    Veuillez vérifier les changements, et les valider.
  </AlertDialog>

  <CommentaireDialog
    v-model="commentaireDialog.props.modelValue"
    v-model:mandatory-message="mandatoryMessage"
    :operation="mutableOperation"
    :no-link="!!mandatoryMessage"
    :advised-recipient="advisedRecipient"
    :meta-message-request="metaMessageRequest"
    :persistent="commentaireDialogPersistent"
    @send="commentaireDialog.props['onClick:positive']"
    @close="commentaireDialog.props['onClick:negative']"
  />
</template>
<script lang="ts" setup>
import { adminconfigurationApi } from '@/api/adminConfiguration'
import { entityApi } from '@/api/entity'
import AlertDialog from '@/components/AlertDialog.vue'
import BeneficiaryInput from '@/components/BeneficiaryDisplayValue.vue'
import BoostBonusSheetCalculator from '@/components/BoostBonusSheetCalculator.vue'
import ControlOrganismDisplayValue from '@/components/ControlOrganismDisplayValue.vue'
import GouvSuggestedAdress from '@/components/GouvSuggestedAdress.vue'
import NjBtn from '@/components/NjBtn.vue'
import NjDatePicker from '@/components/NjDatePicker.vue'
import NjDatePickerDisplayValue from '@/components/NjDatePickerDisplayValue.vue'
import NjDisplayValue from '@/components/NjDisplayValue.vue'
import NjDivider from '@/components/NjDivider.vue'
import NjExpansionPanel from '@/components/NjExpansionPanel.vue'
import NjIconBtn from '@/components/NjIconBtn.vue'
import NjSwitch from '@/components/NjSwitch.vue'
import StandardizedOperationSheetCalculator from '@/components/StandardizedOperationSheetCalculator.vue'
import TitleInput from '@/components/TitleInput.vue'
import VLink from '@/components/VLink.vue'
import router from '@/router'
import { useAdminConfigurationStore } from '@/stores/adminConfiguration'
import { useDialogStore } from '@/stores/dialog'
import { useSnackbarStore } from '@/stores/snackbar'
import { useUserStore } from '@/stores/user'
import type { AdminConfiguration } from '@/types/adminConfiguration'
import { useConfirmAlertDialog } from '@/types/alertDialog'
import type { Beneficiary } from '@/types/beneficiary'
import type { BoostBonusSimulation } from '@/types/boostBonus'
import { mapOperationToCeeStockEntryComparable, shouldCreateCeeStockEntry } from '@/types/ceeStockEntry'
import { type CoOwnerShipSyndicate } from '@/types/coOwnerShipSyndicate'
import { formatHumanReadableLocalDate } from '@/types/date'
import { displayEntity, makeEmptyEntity, resolveEntityChargePoleCEE, type Entity } from '@/types/entity'
import { downloadFile } from '@/types/file'
import { formatNumber, formatPriceNumber } from '@/types/format'
import type { MetaMessageRequest } from '@/types/message'
import { getPoleChargeCEE, type Operation, type OperationRequest } from '@/types/operation'
import { type Property } from '@/types/property'
import {
  codePostalRule,
  emptyOrNumericRule,
  positiveNumericRuleGenerator,
  positiveOrNullNumericRuleGenerator,
  requiredRule,
  rgeEndOfValidityDateRule as rgeEndOfValidityDateBaseRule,
  type ValidationRule,
} from '@/types/rule'
import { makeEmptyUser, userHasRole, userIsAdmin } from '@/types/user'
import type { Valuation } from '@/types/valuation'
import { type Works } from '@/types/works'
import { isBefore, parseISO } from 'date-fns'
import { cloneDeep, debounce, isEqual, isUndefined, sum, toString } from 'lodash'
import type { PropType } from 'vue'
import { VAlert, VProgressCircular, type VForm } from 'vuetify/components'
import AlertIcon from '../AlertIcon.vue'
import DoublonDialog from '../DoublonDialog.vue'
import ValidStandardizedOperationSheetDialog from '../ValidStandardizedOperationSheetDialog.vue'
import EpcBonusCalculator from '../admin/epcBonus/EpcBonusCalculator.vue'
import PrecariousnessBonusSheetCalculator from '../admin/precariousnessBonus/PrecariousnessBonusSheetCalculator.vue'
import ControlOrganismDialog from '../controlorganism/ControlOrganismDialog.vue'
import BeneficiaryDialog from '../dialog/BeneficiaryDialog.vue'
import PropertyDialog from '../dialog/PropertyDialog.vue'
import CoOwnerShipSyndicateDialog from '../dialog/coOwnerShipSyndicateDialog.vue'
import { useOperation, useRealPeriod } from '../operationComposition'
import { useValidateOperation } from '../validateOperationComposition'
import ControlOrderField from './ControlOrderField.vue'
import EmmyFolderDisplayValue from './EmmyFolderDisplayValue.vue'
import LegacyBoostBonusSimulationDisplayValue from './LegacyBoostBonusSimulationDisplayValue.vue'
import OperationField from './OperationField.vue'
import PropertyDisplayValue from './PropertyDisplayValue.vue'
import SimulationDisplayValue from './SimulationDisplayValue.vue'
import StandardizedOperationSheetDetail from './StandardizedOperationSheetDetail.vue'
import SubcontractorInputPanel from './SubcontractorInputPanel.vue'
import WorksField from './WorksField.vue'
import CommentaireDialog from './dialog/CommentaireDialog.vue'

import { useValuationTypesStore } from '@/stores/valuationTypes'
import { isCertynergie } from '@/types/debug'

const props = defineProps({
  operation: {
    type: Object as PropType<Operation>,
    default: makeEmptyOperation,
  },
  edit: Boolean,
  expandedDetail: Boolean,
  createOperation: Boolean,
  preview: Boolean,
})

const emit = defineEmits<{
  'update:operation': [value: Operation]
  'update:valuation': [void]
  'update:saving': [value: boolean]
}>()

//Expand Panels
const edit = ref(props.edit)

const snackbarStore = useSnackbarStore()
const userStore = useUserStore()
const { activeValuationTypes } = useValuationTypesStore()
const adminConfigurationStore = useAdminConfigurationStore()

const mutableOperation = ref(makeEmptyOperation())

const changeStandardizedOperationSheet = ref(false)

const selectedEntity = ref<Entity | undefined>(undefined)
onMounted(() => {
  selectedEntity.value = props.operation.entity
})

const codePostalRules = ref<ValidationRule[]>([codePostalRule, requiredRule])
const estimatedEndOperationRules = ref<ValidationRule[]>([])

const finalPropertyRequired = computed(() => {
  return edit.value && !usingSameAddressProperty.value && mutableOperation.value.stepId >= 10
})
const propertyRules = computed(() => {
  if (edit.value && usingSameAddressProperty.value && mutableOperation.value.stepId >= 10) {
    return [requiredRule]
  }
  return []
})
const addressRules = computed(() => {
  if (finalPropertyRequired.value) {
    return [requiredRule]
  }
  return []
})
const cityRules = computed(() => {
  if (finalPropertyRequired.value) {
    return [requiredRule]
  }
  return []
})

const manualCumac = ref(false)
const manualCumacDialog = useConfirmAlertDialog()

watch(manualCumac, (v) => {
  if (v) {
    manualCumacDialog.confirm().then((result) => {
      manualCumac.value = result
    })
  }
})

const displayControlOrganisms = ref(false)
const beneficiaryDialog = ref(false)

const openBeneficiaryDialog = () => {
  beneficiaryDialog.value = true
}

const dialogStore = useDialogStore()
const handleUpdateSelectedBeneficiary = (selected: Beneficiary[]) => {
  if (selected.length) {
    if (
      mutableOperation.value.operationsGroup &&
      mutableOperation.value.beneficiary?.id != selected[0].id &&
      !mutableOperation.value.fromBocee
    ) {
      dialogStore.addAlert2({
        message: "Impossible de changer de bénéficiaire car l'opération est dans un regroupement",
        title: 'Edition de bénéficiaire',
        width: '600px',
      })
      return
    }
    mutableOperation.value.beneficiary = selected[0]
  } else {
    mutableOperation.value.beneficiary = null
  }
}

const beneficiaryDisplayValueRef = ref<typeof BeneficiaryInput | null>(null)

// Chantiers
const selectWorks = (works: Works[]) => {
  mutableOperation.value.works1 = works[0]
  mutableOperation.value.works2 = works[1]
  mutableOperation.value.works3 = works[2]
}
//syndicat
const showSyndicate = ref(false)
const coOwnerShipSyndicateDialog = ref(false)
const handleUpdateSelectedCoOwnerShipSyndicate = (selected: CoOwnerShipSyndicate[]) => {
  if (selected.length > 0) {
    const syndicate = selected[0]
    mutableOperation.value.coOwnerShipSyndicate = syndicate
    mutableOperation.value.coOwnerShipSyndicateName = syndicate.usageName
    mutableOperation.value.coOwnerShipSyndicateImmatriculationNumber = syndicate.registrationNumber
    mutableOperation.value.operationName = syndicate.usageName
  } else {
    mutableOperation.value.coOwnerShipSyndicate = null
    mutableOperation.value.coOwnerShipSyndicateName = ''
    mutableOperation.value.coOwnerShipSyndicateImmatriculationNumber = ''
  }
}
// Installation
const propertyDialog = ref(false)
const usingSameAddressProperty = ref(false)
// const closePropertyDialog = () => {
//   propertyDialog.value = false
// }
const deleteProperty = () => {
  mutableOperation.value.property = null
  usingSameAddressProperty.value = false
}

watch(usingSameAddressProperty, (v) => {
  if (v) {
    if (
      !mutableOperation.value.property?.streetName ||
      !mutableOperation.value.property?.city ||
      !mutableOperation.value.property?.postalCode
    ) {
      usingSameAddressProperty.value = false
      snackbarStore.setWarning("Vous devez compléter l'adresse qui est incomplète.")
    }
    mutableOperation.value.finalPropertyName = mutableOperation.value.property?.name ?? ''
    mutableOperation.value.finalAddress = {
      city: mutableOperation.value.property?.city ?? '',
      postalCode: mutableOperation.value.property?.postalCode ?? '',
      street: (
        toString(mutableOperation.value.property?.streetNumber).trim() +
        ' ' +
        toString(mutableOperation.value.property?.streetName)
      ).trim(),
      additionalPostalAddress: mutableOperation.value.property?.additionalPostalAddress ?? '',
      country: null,
    }
  }
})

watch(
  () => mutableOperation.value.property,
  (v) => {
    if (v) {
      selectedEntity.value = v?.entity
    }
    if (v?.entity) {
      mutableOperation.value.entity = v.entity
      if (mutableOperation.value.id == 0) {
        mutableOperation.value.instructor = v.entity.entityDetails.effectiveInstructor ?? makeEmptyUser()
      }
    }
  }
)

// Check Installation is up-to-date
const propertyToAdd = ref<number>()
const currentProperty = ref<Property>()
// TODO désactivé le temps que l'API GDE soit remis
// watch(installationToAdd, async (v) => {
//   if (v) {
//     currentInstallation.value = mapGtfGdePropertyDtoToInstallation((await entityApi.getOne(v)).data)
//     if (
//       (mutableOperation.value.installation?.gtfId ?? 0) === 0 ||
//       mutableOperation.value.installation?.gtfId !== currentInstallation.value.gtfId
//     ) {
//       mutableOperation.value.installation = cloneDeep(currentInstallation.value!)
//     }
//   } else {
//     currentInstallation.value = undefined
//     mutableOperation.value.installation = null
//   }
// })

const updateProperty = () => {
  mutableOperation.value.property = cloneDeep(currentProperty.value!)
}

const handleUpdateSelectedProperty = (selected: Property[]) => {
  if (selected.length) {
    mutableOperation.value.property = cloneDeep(selected[0])
    if (usingSameAddressProperty.value) {
      if (
        !mutableOperation.value.property.city ||
        !mutableOperation.value.property.postalCode ||
        !mutableOperation.value.property.streetName
      ) {
        usingSameAddressProperty.value = false
        snackbarStore.setWarning("Vous devez compléter l'adresse qui est incomplète.")
      }
      mutableOperation.value.finalPropertyName = mutableOperation.value.property?.name ?? ''
      mutableOperation.value.finalAddress = {
        city: mutableOperation.value.property?.city ?? '',
        postalCode: mutableOperation.value.property?.postalCode ?? '',
        street:
          (toString(mutableOperation.value.property?.streetNumber) + ' ').trim() +
          toString(mutableOperation.value.property?.streetName),
        additionalPostalAddress: mutableOperation.value.property!.additionalPostalAddress ?? '',
        country: null,
      }
    } else if (
      !mutableOperation.value.finalAddress.street &&
      !mutableOperation.value.finalAddress.city &&
      !mutableOperation.value.finalAddress.postalCode &&
      !mutableOperation.value.finalAddress.additionalPostalAddress &&
      !mutableOperation.value.finalPropertyName
    ) {
      usingSameAddressProperty.value = true
    }
  } else {
    mutableOperation.value.property = null
  }
}

// Form

const formRef = ref<VForm | null>(null)

const cancelSaveASimulation = () => {
  edit.value = false
}

const createSimulation = async function () {
  const validation = await formRef.value!.validate()
  if (validation.valid) {
    edit.value = true
  } else {
    nextTick(() => {
      const el = document.querySelector('.v-input--error')
      el?.scrollIntoView({
        behavior: 'smooth',
        block: 'center',
      })
    })
  }
}

const determineStep = () =>
  props.createOperation
    ? mutableOperation.value.eesDuplicate && mutableOperation.value.cumul
      ? 30
      : 20
    : mutableOperation.value.stepId

const checkDoublon = ref(false)
const promisableSimulation = ref(emptyValue<Operation>())
const alertChangeZoneClimatiqueComposition = useConfirmAlertDialog()
const alertChangeEntity = useConfirmAlertDialog()

const commentaireDialog = useConfirmAlertDialog()
const advisedRecipient = ref<string[]>([])
const mandatoryMessage = ref('')
const metaMessageRequest = ref<MetaMessageRequest>()
const { realPeriod } = useRealPeriod(computed(() => mutableOperation.value))
const updateOperationSheet = () => {
  mutableOperation.value.epcBonusParameterValues =
    Object.keys(mutableOperation.value.epcBonusParameterValues ?? {}).length === 0
      ? null
      : mutableOperation.value.epcBonusParameterValues
  changeStandardizedOperationSheet.value = true
}

const cancelSave = () => {
  checkDoublon.value = false
  mutableOperation.value.eesDuplicate = false
  mutableOperation.value.cumul = false
  mutableOperation.value.customerDuplicate = false
}

const saveOperation = async function () {
  if (mutableOperation.value.id === 0 && props.preview) {
    emit('update:operation', mutableOperation.value)
    return
  }

  const validation = await formRef.value!.validate()
  if (!validation.valid) {
    nextTick(() => {
      const el = document.querySelector('.v-input--error')
      el?.scrollIntoView({
        behavior: 'smooth',
        block: 'center',
      })
    })
    return
  }

  if (!edit.value) {
    edit.value = true
    saveOperation()
    return
  }

  if (!mutableOperation.value) {
    return
  }

  const hasZoneClimatiqueChanged =
    resolveZoneClimatique(props.operation.finalAddress?.postalCode) !== undefined &&
    resolveZoneClimatique(mutableOperation.value.finalAddress?.postalCode) !==
      resolveZoneClimatique(props.operation.finalAddress?.postalCode)

  if (hasZoneClimatiqueChanged && !(await alertChangeZoneClimatiqueComposition.confirm())) {
    return
  }

  const hasEntityChanged = props.operation.id > 0 && props.operation.entity.id != mutableOperation.value.entity.id

  if (hasEntityChanged && !(await alertChangeEntity.confirm())) {
    return
  }

  if (
    props.operation.id > 0 &&
    realPeriod.value?.id !== mutableOperation.value.period?.id &&
    !(await dialogStore.addAlert({
      message:
        'Vous avez saisie ou changé la date de signature (prévisionnelle ou réelle) ce qui entraine un changement de période.',
      title: 'Changement Période',
      maxWidth: '640px',
    }))
  ) {
    return
  }

  if (
    props.createOperation &&
    !checkDoublon.value &&
    (!mutableOperation.value.eesDuplicate || !mutableOperation.value.cumul)
  ) {
    checkDoublon.value = true
    return
  }

  const classicCumac = manualCumac.value ? mutableOperation.value.classicCumac : totalCEEClassique.value
  const precariousnessCumac = manualCumac.value
    ? mutableOperation.value.precariousnessCumac
    : totalCEEPrecariousness.value
  if (
    mutableOperation.value.stepId >= 80 &&
    shouldCreateCeeStockEntry(
      mapOperationToCeeStockEntryComparable(props.operation),
      mapOperationToCeeStockEntryComparable({
        ...mutableOperation.value,
        classicCumac: classicCumac,
        precariousnessCumac: precariousnessCumac,
      })
    )
  ) {
    mandatoryMessage.value = dafMail(
      { ...mutableOperation.value, classicCumac: classicCumac, precariousnessCumac: precariousnessCumac },
      false
    )
    advisedRecipient.value = mutableOperation.value.entity.entityDetails.effectiveDafMail

    if (!(await commentaireDialog.confirm())) {
      return
    }
    mandatoryMessage.value = ''
    advisedRecipient.value = []

    const oldEntityDafMail = props.operation!.entity.entityDetails.effectiveDafMail
    const newEntityDafMail = mutableOperation.value!.entity.entityDetails.effectiveDafMail

    if (!isEqual(oldEntityDafMail, newEntityDafMail)) {
      mandatoryMessage.value = dafMail(
        {
          ...mutableOperation.value!,
          classicCumac: 0,
          precariousnessCumac: 0,
        },
        false
      )
      advisedRecipient.value = oldEntityDafMail

      if (!(await commentaireDialog.confirm())) {
        return
      }
      mandatoryMessage.value = ''
      advisedRecipient.value = []
    }
  }

  if (
    mutableOperation.value.id > 0 &&
    mutableOperation.value.stepId === 0 &&
    mutableOperation.value.toProcess &&
    !props.operation.toProcess &&
    !(await sendMailForProcessOperations())
  ) {
    return
  }

  checkDoublon.value = false
  const operationToSave: Operation = {
    ...mutableOperation.value,
    stepId: determineStep(),
    parameterValues: !manualCumac.value ? mutableOperation.value.parameterValues : [],
    boostBonusSimulation:
      showBoostBonus.value && !boostBonusSheet.value.error
        ? {
            boostBonusSheet: boostBonusSheet.value.value!,
            parameterValues: boostBonusSimulationValues.value!,
          }
        : null,
    epcBonusParameterValues:
      showEpcBonus.value && !epcBonusSheet.value.error ? epcBonusSimulationValues.value : undefined,
    precariousnessBonusParameterValues:
      showPrecariousnessBonus.value && !precariousnessBonusSheet.value.error
        ? precariousnessBonusSimulationValues.value
        : undefined,
    issuedFromStandardizedOperationSheetCumac: manualCumac.value
      ? mutableOperation.value.legacyBoostBonusSimulation
        ? props.operation.issuedFromStandardizedOperationSheetCumac
        : null
      : mutableOperation.value.issuedFromStandardizedOperationSheetCumac,
    classicCumac: manualCumac.value ? mutableOperation.value.classicCumac : totalCEEClassique.value,
    precariousnessCumac: manualCumac.value ? mutableOperation.value.precariousnessCumac : totalCEEPrecariousness.value,
    classicValuationValue:
      (mutableOperation.value.classicValuationValue ||
        valuations.value?.find((valuation) => !valuation.precariousness)?.value) ??
      0,
    precariousnessValuationValue:
      (mutableOperation.value.precariousnessValuationValue ||
        valuations.value?.find((valuation) => valuation.precariousness)?.value) ??
      0,
    finalPropertyName:
      !usingSameAddressProperty.value || !mutableOperation.value.property
        ? mutableOperation.value.finalPropertyName
        : mutableOperation.value.property.name,
    finalAddress:
      !usingSameAddressProperty.value || !mutableOperation.value.property
        ? mutableOperation.value.finalAddress
        : {
            city: mutableOperation.value.property.city,
            postalCode: mutableOperation.value.property.postalCode,
            street: (
              toString(mutableOperation.value.property.streetNumber) +
              ' ' +
              toString(mutableOperation.value.property.streetName)
            ).trim(),
            additionalPostalAddress: mutableOperation.value.property.additionalPostalAddress ?? '',
            country: null,
          },
    instructor: mutableOperation.value.instructor,
  }

  const request: OperationRequest = mapToOperationRequest(operationToSave)

  if (mutableOperation.value.entity && !isEqual(mutableOperation.value.entity, makeEmptyEntity())) {
    request.entityId = mutableOperation.value.entity.id
  } else {
    request.entityId = userStore.currentUser.entities.filter((orga) => orga.level === 4)[0].id
  }

  if (mutableOperation.value.id == 0) {
    request.leadingEntityId = request.entityId
  }

  if (!showBoostBonus.value) {
    request.boostBonusSimulation = null
  } else {
    request.boostBonusSimulation = {
      boostBonusSheetId: operationToSave.boostBonusSimulation!.boostBonusSheet.id,
      parameterValues: operationToSave.boostBonusSimulation!.parameterValues,
    }
  }
  if (!showEpcBonus.value) {
    request.epcBonusParameterValues = null
  }

  if (request.works1 == null) {
    request.works1 = request.works2
    request.works2 = undefined
  }
  if (request.works2 == null) {
    request.works2 = request.works3
    request.works3 = undefined
  }

  await handleAxiosPromise(
    promisableSimulation,
    mutableOperation.value.id === 0
      ? simulationApi.create(request)
      : simulationApi.updateSimulation(mutableOperation.value.id, request),
    {
      afterSuccess: async (r) => {
        snackbarStore.setSuccess(
          props.createOperation
            ? "L'opération a bien été créée"
            : mutableOperation.value?.id === 0
              ? 'La simulation a été bien créée'
              : 'Vos modifications ont été enregistrées',
          5000
        )
        succeedSave()
        if (mutableOperation.value.id === 0) {
          mutableOperation.value.id = r.data.id
          commentaireDialogPersistent.value = true
          succeedSave()
          if (
            mutableOperation.value.stepId === 0 &&
            mutableOperation.value.toProcess &&
            !(await sendMailForProcessOperations())
          ) {
            snackbarStore.setWarning("Vous n'avez pas envoyé de mail.")
          }
          commentaireDialogPersistent.value = false
          router.push({
            name: mutableOperation.value.stepId > 0 ? 'OperationAllView' : 'SimulationAllView',
          })
        } else if (props.createOperation) {
          router.push({
            name: 'OperationOneView',
            params: { id: mutableOperation.value.id },
          })
        } else {
          emit('update:operation', r.data)
        }
      },
      afterError: (e) => {
        snackbarStore.setError(e)
      },
    }
  )
}

const classicValuationCalculus = computed(() => {
  if (sum(volumeCEE.value) > 0 || manualCumac.value) {
    return (
      Math.round(
        ((manualCumac.value ? mutableOperation.value.classicCumac : totalCEEClassique.value) / 1000) *
          (mutableOperation.value.atypicalClassicValuationValue ??
            (mutableOperation.value.classicValuationValue ||
              valuations.value?.find((valuation) => !valuation.precariousness)?.value) ??
            0) *
          100
      ) / 100
    )
  }
  return 0
})

const precariousnessValuationCalculus = computed(() =>
  showPrecariousnessBonus.value || manualCumac.value
    ? Math.round(
        ((manualCumac.value ? mutableOperation.value.precariousnessCumac : totalCEEPrecariousness.value!) / 1000) *
          (mutableOperation.value.atypicalPrecariousnessValuationValue ??
            (mutableOperation.value.precariousnessValuationValue ||
              valuations.value?.find((valuation) => valuation.precariousness)?.value) ??
            0) *
          100
      ) / 100
    : 0
)

const displayAlert = computed(() => {
  if (
    (volumeCEE.value || manualCumac.value) &&
    !isUndefined(classicValuationCalculus.value) &&
    !isUndefined(precariousnessValuationCalculus.value)
  ) {
    return (
      edit.value &&
      (classicValuationCalculus.value ?? 0) + (precariousnessValuationCalculus.value ?? 0) <
        parseInt(adminConfigurationStore.profitabilityThreshold?.data ?? '')
    )
  }

  return 0
})

const valuations = ref<Valuation[] | undefined>([])
watch(
  () => mutableOperation.value.valuationType,
  (v) => {
    if (v && v.id !== 0 && !loading.value) {
      console.debug('valuation', v, loading.value)
      valuationApi
        .getAppropriate(mutableOperation.value.id, mutableOperation.value.valuationType.id)
        .then((response) => {
          valuations.value = response.data
          if (
            !mutableOperation.value.atypicalClassicValuationValue &&
            !mutableOperation.value.atypicalPrecariousnessValuationValue
          ) {
            mutableOperation.value.classicValuationValue = valuations.value!.find(
              (valuation) => !valuation.precariousness
            )!.value
            mutableOperation.value.precariousnessValuationValue = valuations.value.find(
              (valuation) => valuation.precariousness
            )!.value
            emit('update:valuation')
          }
        })
        .catch(async (err) => {
          valuations.value = undefined
          if (err.response.status !== 404) {
            snackbarStore.setError(
              await handleAxiosException(err, undefined, {
                defaultMessage: 'Une erreur est survenue lors de la récupération de la valorisation',
              })
            )
          }
        })
    }
  },
  {
    flush: 'sync',
  }
)

const canCertified = computed(() => userHasRole(userStore.currentUser, 'SIEGE', 'INSTRUCTEUR', 'ADMIN', 'ADMIN_PLUS'))

// Calcul
const {
  cumacHasChanged,
  predefinedValues,
  volumeCEE,
  boostBonusSheet: boostBonusSheet,
  predefinedValuesForBoostBonusSheet: predefinedValuesForBoostBonusSheet,
  volumeCEEAfterBoostBonusSheet: volumeCEEAfterBoostBonusSheet,
  boostBonusSheets: boostBonusSheets,
  epcBonus: epcBonus,
  epcBonusSheets: epcBonusSheets,
  epcBonusSheet: epcBonusSheet,
  showBoostBonus: showBoostBonus,
  showEpcBonus: showEpcBonus,
  showPrecariousnessBonus: showPrecariousnessBonus,
  precariousnessBonusSheet: precariousnessBonusSheet,
  predefinedValuesForPrecariousnessBonus: predefinedValuesForPrecariousnessBonus,
  calculatedClassicCumac: calculatedClassicCumac,
  calculatedPrecariousnessCumac: calculatedPrecariousnessCumac,
  totalCEEClassique,
  totalCEEPrecariousness: totalCEEPrecariousness,
  boostBonusSimulationValues: boostBonusSimulationValues,
  epcBonusSimulationValues: epcBonusSimulationValues,
  precariousnessBonusSimulationValues: precariousnessBonusSimulationValues,
  commercialOfferWithFinancialIncentive,
  volumeCEEForPrecariousness,
} = useOperation(mutableOperation)

// Export form save info
watch(
  promisableSimulation,
  (v) => {
    emit('update:saving', v.loading)
  },
  {
    deep: true,
  }
)

const {
  validateStandardizedOperationSheetRule,
  validateStandardizedOperationSheetAndCommitmentValidityRule,
  validateEstimatedCommitmentDateRule,
  validateEstimatedEndWorkRule,
  validateOperationDurationRule,
  validateOperation,
  validateStepDurationRule,
  validateSubcontractorRule,
  validateRgeRule,
  validateBeneficiaryRule,
} = useValidateOperation(
  computed<Operation>(() => mutableOperation.value),
  computed<Valuation[] | undefined>(() => valuations.value)
)

const estimatedEndOperationRule = computed(
  () => (value: string) =>
    !value ||
    !isBefore(
      new Date(parseHumanReadableLocalDate(value)),
      parseISO(mutableOperation.value.signedDate ?? mutableOperation.value.estimatedCommitmentDate)
    )
      ? true
      : "La date de fin de travaux prévisionnelle doit être après la date d'engagement prévisionnelle"
)

const {
  unsavedDataDialog,
  succeedSave,
  check,
  disable: disableUnsavedData,
  disabled: disabledUnsavedData,
  enable: enableUnsavedData,
} = useUnsavedData(mutableOperation, showBoostBonus, showEpcBonus, showPrecariousnessBonus, manualCumac)

// Load
const loading = ref(false)
watch(
  () => props.operation,
  async (v) => {
    if (!isEqual(v, mutableOperation.value)) {
      loading.value = true
      mutableOperation.value = cloneDeep(v)

      if (v.coOwnerShipSyndicateName) {
        showSyndicate.value = true
      }
      if (mutableOperation.value.property && v.entity) {
        mutableOperation.value.property.entity = v.entity
      }

      estimatedEndOperationRules.value = [estimatedEndOperationRule.value]

      if (v.stepId >= 10) {
        estimatedEndOperationRules.value.push(requiredRule)
      }

      usingSameAddressProperty.value =
        v.id === 0 ||
        (v.property === null &&
          v.finalPropertyName === '' &&
          v.finalAddress?.postalCode === '' &&
          v.finalAddress?.city === '' &&
          v.finalAddress?.street === '' &&
          v.finalAddress?.additionalPostalAddress === '') ||
        !(
          v.finalPropertyName !== v.property?.name ||
          !isEqual(v.finalAddress?.postalCode, v.property?.postalCode) ||
          v.finalAddress?.city !== v.property?.city ||
          v.finalAddress?.street.trim() !==
            (toString(v.property?.streetNumber) + ' ' + toString(v.property?.streetName)).trim() ||
          v.finalAddress.additionalPostalAddress !== (v.property?.additionalPostalAddress ?? '')
        )
      boostBonusSimulationValues.value = cloneDeep(
        v.boostBonusSimulation?.parameterValues ?? ({} as BoostBonusSimulation['parameterValues'])
      )
      epcBonusSimulationValues.value = cloneDeep(v.epcBonusParameterValues ?? {})
      precariousnessBonusSimulationValues.value = cloneDeep(v.precariousnessBonusParameterValues ?? {})

      if (mutableOperation.value.parameterValues.length === 0) {
        manualCumac.value = true
        mutableOperation.value.parameterValues = [{}]
      }

      propertyToAdd.value = v.property?.id

      if (
        mutableOperation.value.id == 0 &&
        userStore.currentUser.entities.filter((entity) => entity.level == 4).length == 1
      ) {
        const entity = userStore.currentUser.entities[0]
        mutableOperation.value.entity = entity
        mutableOperation.value.instructor = entity.entityDetails.effectiveInstructor ?? makeEmptyUser()
      }

      if (props.createOperation) {
        mutableOperation.value.operationName = props.operation.simulationName
      }

      loading.value = false
    }

    if (props.operation.id == 0) {
      formRef.value?.validate()
    }

    if (disabledUnsavedData.value) {
      enableUnsavedData()
    } else {
      succeedSave()
    }
  },
  {
    deep: true,
    immediate: true,
  }
)

const missingEntity = computed(() => userStore.currentUser.entities.length === 0)

const warningMessages = computed(() =>
  [
    displayAlert.value ? 'Le coût du traitement est supérieur au montant en € de la réservation' : undefined,
    netMargin.value < 0 ? 'La marge nette de cette opération est négative' : undefined,
    props.operation.parameterValues.length === 0
      ? "Les kWhc ont été saisi manuellement, l'export de cette opération doit donc être complété manuellement dans EMMY"
      : undefined,
    !isStandardizedOperationSheetAndCommitmentCompatible(mutableOperation.value)
      ? "La date d'engagement n'est pas compatible avec la date de fin de validité de l'opération"
      : undefined,
  ].filter((message) => !!message)
)

const netMargin = computed(() => {
  if (isUndefined(classicValuationCalculus.value) || isUndefined(precariousnessValuationCalculus.value)) {
    return 0
  }

  return (
    (classicValuationCalculus.value ?? 0) +
    precariousnessValuationCalculus.value -
    mutableOperation.value.customerFinancialIncentive -
    (((manualCumac.value ? mutableOperation.value.classicCumac : totalCEEClassique.value) +
      (manualCumac.value ? mutableOperation.value.precariousnessCumac : totalCEEPrecariousness.value)) /
      1000) *
      getPoleChargeCEE(mutableOperation.value)
  )
})

const rgeEndOfValidityDateRule = computed(() => rgeEndOfValidityDateBaseRule(mutableOperation.value))

const financialIncentiveRule = computed(() =>
  mutableOperation.value.stepId < 40
    ? positiveOrNullNumericRuleGenerator()
    : mutableOperation.value.stepId < 60
      ? positiveNumericRuleGenerator()
      : () => true
)

const alertModifyCumacDialog = useConfirmAlertDialog()
const updateAlertModifyDialog = debounce((v: boolean) => {
  alertModifyCumacDialog.props.modelValue = v
}, 300)
watch(cumacHasChanged, (v) => {
  if (v && !manualCumac.value) {
    updateAlertModifyDialog(true)
  } else {
    updateAlertModifyDialog(false)
  }
})

const commentaireDialogPersistent = ref(false)
const sendMailForProcessOperations = async (): Promise<boolean> => {
  metaMessageRequest.value = { '@type': 'OperationToProcessMessageRequest' }
  mandatoryMessage.value = toProcessMail(mutableOperation.value)
  advisedRecipient.value = []
  userApi
    .getAll({ size: 1000 }, { roles: ['AGENCE_PLUS'], entityIds: [mutableOperation.value.entity.id], active: true })
    .then((v) => {
      advisedRecipient.value = v.data.content.map((it) => it.email).filter((i) => i) as string[]
    })
    .catch(() =>
      snackbarStore.setError("Nous n'avons pas pu récupérer la liste des contacts pour le traitement de la simulation")
    )
  // advisedRecipient.value = mutableOperation.value.instructor

  if (!(await commentaireDialog.confirm())) {
    return false
  }
  mandatoryMessage.value = ''
  advisedRecipient.value = []
  metaMessageRequest.value = undefined

  return true
}

const resolvedChargePoleCEEEntity = ref(succeedValue(makeEmptyEntity()))
watchEffect(async () => {
  resolvedChargePoleCEEEntity.value = await resolveEntityChargePoleCEE(
    mutableOperation.value.entity,
    mutableOperation.value.entity.entityDetails.effectiveFee!
  )
})

const downloadAdminConfigurationDocument = (adminConfiguration: AdminConfiguration) => {
  adminconfigurationApi
    .download(adminConfiguration.name!)
    .then((v) => downloadFile(adminConfiguration.document.originalFilename, v.data!))
    .catch(async (err) => snackbarStore.setError(await handleAxiosException(err)))
}

const gouvSuggestedAdressRef = ref<typeof GouvSuggestedAdress | null>(null)

defineExpose({
  saveOperation,
  cancelSaveASimulation,
  createSimulation,
  edit,
  valuations,
  check,
  disableUnsavedData,
})
</script>
<style scoped>
.router-link-active {
  color: #007acd;
}
</style>
