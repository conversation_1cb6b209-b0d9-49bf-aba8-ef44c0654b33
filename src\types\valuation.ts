import type { LocalDate } from './date'

export interface Valuation {
  id: number
  startDate: LocalDate
  type: ValuationType
  value: number
  precariousness: boolean
}

export interface ValuationRequest extends Omit<Valuation, 'id' | 'type'> {
  typeId: number
}

export interface ValuationFilter {
  typeId: number
  precariousness: boolean
}

export function makeEmptyValuation(): Valuation {
  return {
    id: 0,
    startDate: '',
    type: makeEmptyValuationType(),
    value: 0,
    precariousness: false,
  }
}

export function mapToRequest(valuation: Valuation): ValuationRequest {
  return {
    startDate: valuation.startDate,
    typeId: valuation.type.id,
    value: valuation.value,
    precariousness: valuation.precariousness,
  }
}

export interface ValuationType {
  id: number
  name: string
  displayOrder: number
  startDate: LocalDate
  endDate: LocalDate
}

export interface ValuationTypeFilter {
  active?: boolean
}

export function makeEmptyValuationType(): ValuationType {
  return {
    id: 0,
    name: '',
    displayOrder: 0,
    startDate: '',
    endDate: '',
  }
}
