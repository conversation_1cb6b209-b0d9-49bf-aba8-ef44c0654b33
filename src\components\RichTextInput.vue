<template>
  <div v-if="label" class="mb-2">{{ label }}</div>
  <div v-if="editor">
    <NjIconBtn
      icon="mdi-format-bold"
      :disabled="!editor.can().chain().focus().toggleBold().run()"
      rounded="0"
      :color="editor.isActive('bold') ? 'primary' : ''"
      size="x-large"
      class="editor-button"
      @click="editor.chain().focus().toggleBold().run()"
    />
    <NjIconBtn
      icon="mdi-format-italic"
      :disabled="!editor.can().chain().focus().toggleItalic().run()"
      rounded="0"
      :color="editor.isActive('italic') ? 'primary' : ''"
      size="x-large"
      class="editor-button"
      @click="editor.chain().focus().toggleItalic().run()"
    />
    <NjIconBtn
      icon="mdi-format-underline"
      :disabled="!editor.can().chain().focus().toggleUnderline().run()"
      rounded="0"
      :color="editor.isActive('underline') ? 'primary' : ''"
      size="x-large"
      class="editor-button"
      @click="editor.chain().focus().toggleUnderline().run()"
    />
    <VMenu v-model="selectTextColor" :close-on-content-click="false">
      <template #activator="{ props }">
        <NjIconBtn rounded="0" icon="mdi-format-color-text" v-bind="props" size="x-large" class="editor-button" />
      </template>
      <VCard>
        <VCardText>
          <VColorPicker v-model="color" show-swatches />
        </VCardText>
        <VCardActions>
          <VSpacer />
          <NjBtn variant="outlined" @click="selectTextColor = false">Annuler</NjBtn>
          <NjBtn @click="applyColor">Valider</NjBtn>
        </VCardActions>
      </VCard>
    </VMenu>
    |
    <NjIconBtn
      icon="mdi-format-strikethrough"
      :disabled="!editor.can().chain().focus().toggleStrike().run()"
      rounded="0"
      :color="editor.isActive('strike') ? 'primary' : ''"
      size="x-large"
      class="editor-button"
      @click="editor.chain().focus().toggleStrike().run()"
    />
    <NjIconBtn
      icon="mdi-code-braces"
      :disabled="!editor.can().chain().focus().toggleCode().run()"
      rounded="0"
      :color="editor.isActive('code') ? 'primary' : ''"
      size="x-large"
      class="editor-button"
      @click="editor.chain().focus().toggleCode().run()"
    />
    <NjIconBtn
      icon="mdi-format-clear"
      rounded="0"
      size="x-large"
      class="editor-button"
      @click="editor.chain().focus().unsetAllMarks().run()"
    />
    |
    <NjIconBtn
      icon="mdi-format-header-1"
      rounded="0"
      :color="editor.isActive('heading', { level: 1 }) ? 'primary' : ''"
      size="x-large"
      class="editor-button"
      @click="editor.chain().focus().toggleHeading({ level: 1 }).run()"
    />
    <NjIconBtn
      icon="mdi-format-header-2"
      rounded="0"
      :color="editor.isActive('heading', { level: 2 }) ? 'primary' : ''"
      size="x-large"
      class="editor-button"
      @click="editor.chain().focus().toggleHeading({ level: 2 }).run()"
    />
    <NjIconBtn
      icon="mdi-format-header-3"
      rounded="0"
      :color="editor.isActive('heading', { level: 3 }) ? 'primary' : ''"
      size="x-large"
      class="editor-button"
      @click="editor.chain().focus().toggleHeading({ level: 3 }).run()"
    />
    <NjIconBtn
      icon="mdi-format-header-4"
      rounded="0"
      :color="editor.isActive('heading', { level: 4 }) ? 'primary' : ''"
      size="x-large"
      class="editor-button"
      @click="editor.chain().focus().toggleHeading({ level: 4 }).run()"
    />
    <NjIconBtn
      icon="mdi-format-header-5"
      rounded="0"
      :color="editor.isActive('heading', { level: 5 }) ? 'primary' : ''"
      size="x-large"
      class="editor-button"
      @click="editor.chain().focus().toggleHeading({ level: 5 }).run()"
    />
    <NjIconBtn
      icon="mdi-format-header-6"
      rounded="0"
      :color="editor.isActive('heading', { level: 6 }) ? 'primary' : ''"
      size="x-large"
      class="editor-button"
      @click="editor.chain().focus().toggleHeading({ level: 6 }).run()"
    />
    |
    <NjIconBtn
      icon="mdi-format-list-bulleted"
      rounded="0"
      :color="editor.isActive('bulletList') ? 'primary' : ''"
      size="x-large"
      class="editor-button"
      @click="editor.chain().focus().toggleBulletList().run()"
    />
    <NjIconBtn
      icon="mdi-format-list-numbered"
      rounded="0"
      :color="editor.isActive('orderedList') ? 'primary' : ''"
      size="x-large"
      class="editor-button"
      @click="editor.chain().focus().toggleOrderedList().run()"
    />
    <NjIconBtn
      icon="mdi-xml"
      rounded="0"
      :color="editor.isActive('codeBlock') ? 'primary' : ''"
      size="x-large"
      class="editor-button"
      @click="editor.chain().focus().toggleCodeBlock().run()"
    />
    <NjIconBtn
      icon="mdi-format-quote-close"
      rounded="0"
      :color="editor.isActive('blockquote') ? 'primary' : ''"
      size="x-large"
      class="editor-button"
      @click="editor.chain().focus().toggleBlockquote().run()"
    />
    <NjIconBtn
      icon="mdi-minus"
      rounded="0"
      size="x-large"
      class="editor-button"
      @click="editor.chain().focus().setHorizontalRule().run()"
    />
    |
    <NjIconBtn
      icon="mdi-undo"
      :disabled="!editor.can().chain().focus().undo().run()"
      rounded="0"
      size="x-large"
      class="editor-button"
      @click="editor.chain().focus().undo().run()"
    />
    <NjIconBtn
      icon="mdi-redo"
      :disabled="!editor.can().chain().focus().redo().run()"
      rounded="0"
      size="x-large"
      class="editor-button"
      @click="editor.chain().focus().redo().run()"
    />
  </div>
  <EditorContent :editor="editor" class="my-2 rich-text" />
</template>

<script setup lang="ts">
import Color from '@tiptap/extension-color'
import FontFamily from '@tiptap/extension-font-family'
import TextStyle from '@tiptap/extension-text-style'
import Underline from '@tiptap/extension-underline'
import StarterKit from '@tiptap/starter-kit'
import { EditorContent, useEditor } from '@tiptap/vue-3'

const props = defineProps({
  modelValue: String,
  label: String,
})
const emit = defineEmits<{
  'update:model-value': [value: string]
}>()

const editor = useEditor({
  content: props.modelValue,
  extensions: [StarterKit, Underline, TextStyle, Color, FontFamily],
  onUpdate: () => emit('update:model-value', editor.value?.getHTML() ?? ''),
})

watch(
  () => props.modelValue,
  (v) => {
    if (v !== editor.value?.getHTML()) {
      editor.value?.commands.setContent(v ?? '')
    }
  }
)

const color = ref('#000000')
const selectTextColor = ref(false)

const applyColor = () => {
  editor.value?.chain().focus().setColor(color.value).run()
  selectTextColor.value = false
}
</script>
<style>
.editor-button {
  height: fit-content !important;
  width: fit-content !important;
  margin-right: 4px;
}

.ProseMirror {
  min-height: 100px;
  max-height: 400px;
  overflow: auto;
  border: solid grey 1px;
  padding-inline: 8px;
  padding-top: 4px;
}
</style>
