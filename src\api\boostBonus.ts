import type { BoostBonusSheet, BoostBonusSheetRequest } from '@/types/boostBonus'
import type { LocalDate } from '@/types/date'
import type { Page, Pageable } from '@/types/pagination'
import type { AxiosInstance, AxiosPromise } from 'axios'
import axiosInstance from '.'

export interface BoostBonusSheetFilter extends Record<string, any> {
  search?: string
  commitmentDate?: LocalDate
  endOperationDate?: LocalDate
}
class BoostBonusSheetApi {
  public constructor(private axios: AxiosInstance) {}

  public create(boostBonusSheet: BoostBonusSheetRequest): AxiosPromise<BoostBonusSheet> {
    return this.axios.post('/boost_bonus_sheets', boostBonusSheet)
  }

  public update(boostBonusSheet: BoostBonusSheetRequest, id: number): AxiosPromise<BoostBonusSheet> {
    return this.axios.put('/boost_bonus_sheets/' + id, boostBonusSheet)
  }

  public findAll(pageable: Pageable, filter: BoostBonusSheetFilter): AxiosPromise<Page<BoostBonusSheet>> {
    return this.axios.get('/boost_bonus_sheets', {
      params: { ...pageable, ...filter },
    })
  }

  public findOne(id: number): AxiosPromise<BoostBonusSheet> {
    return this.axios.get('/boost_bonus_sheets/' + id)
  }
}

export const boostBonusSheetApi = new BoostBonusSheetApi(axiosInstance)
