<template>
  <NjSelect
    v-if="parameterFormula?.type === 'CHOICE'"
    v-model="modelValue"
    :label="label"
    :items="parameterFormula?.data?.split(';')"
    :rules="rules"
    :required
    :optional
  />
  <NjTextField
    v-else-if="parameterFormula?.type === 'NUMBER'"
    v-model="modelValue"
    :label="label"
    :suffix="parameterFormula?.suffix"
    :rules="rules"
    :required
    :optional
    type="number"
  />
  <NjDatePicker
    v-else-if="parameterFormula?.type === 'DATE'"
    v-model="modelValue"
    :label="label"
    :suffix="parameterFormula?.suffix"
    :rules="rules"
    :readonly="!!parameterFormula?.computedFormula"
    :required
    :optional
  />
  <NjTextField
    v-else-if="parameterFormula?.type === 'STRING'"
    v-model="modelValue"
    :label="label"
    :suffix="parameterFormula?.suffix"
    :rules="rules"
    :readonly="!!parameterFormula?.computedFormula"
    :required
    :optional
  />
</template>
<script setup lang="ts">
import type { ParameterFormula } from '@/types/calcul/parameterFormula'
import type { PropType } from 'vue'
import { requiredRule } from '@/types/rule'

const modelValue = defineModel<any>('modelValue')

const props = defineProps({
  parameterFormula: Object as PropType<ParameterFormula>,
  required: Boolean,
  optional: Boolean,
})

// TODO a factoriser ou améliorer avec l'amélioration de l'outil de calcul
const valueBelongToAuthorizedItemsRuleGenerator = (items: string[] | undefined) => {
  return (v: string | undefined): true | string => {
    if (!v) {
      return true
    }
    return items?.find((i) => i == v)
      ? true
      : `${v} ne fait pas partie des valeurs autorisées suivantes: ${items?.join(', ')}`
  }
}

const rules = computed(() => {
  const res = []
  if (!props.parameterFormula?.optional) {
    res.push(requiredRule)
  }

  if (props.parameterFormula?.type === 'NUMBER') {
    res.push(emptyOrNumericRule)
  }

  if (props.parameterFormula?.type === 'CHOICE') {
    res.push(valueBelongToAuthorizedItemsRuleGenerator(props.parameterFormula.data?.split(';')))
  }

  return res
})

const label = computed(() => {
  return `${props.parameterFormula?.label}${props.parameterFormula?.optional ? '' : '*'}`
})
</script>
