<template>
  <VRow ref="root" class="flex-column" dense>
    <VCol v-for="(request, index) in requests" :key="request.localId">
      <VCard v-if="request.success" variant="outlined" color="success">
        <VCardText class="text-center d-flex align-center pa-0">
          <VIcon color="success" class="mx-4 my-3">mdi-check-circle</VIcon>
          <div class="flex-grow-1 text-start">
            <span>'{{ request.file.name }}' a bien été envoyé</span>
          </div>
          <NjIconBtn color="success" icon="mdi-close" rounded="0" @click="remove(index)" />
        </VCardText>
      </VCard>
      <VCard v-else color="#F6F8F9" :border="false">
        <VCardText>
          <VRow class="flex-column" dense>
            <VCol>
              <VRow class="align-center">
                <VCol style="font-size: 1rem">
                  <VIcon color="success" class="rounded-0">mdi-check-circle</VIcon>
                  {{ request.file.name }}
                </VCol>
                <VCol class="flex-grow-0 justify-self-center" align="end">
                  <NjIconBtn
                    color="error"
                    icon="mdi-delete"
                    density="compact"
                    class="rounded-0"
                    :disabled="request.loading"
                    @click="remove(index)"
                  />
                </VCol>
              </VRow>
            </VCol>
            <VCol class="d-flex">
              <VRow>
                <VCol>
                  <RemoteAutoComplete
                    :query-for-all="queryDocumentTypeNotInOperation"
                    :query-for-one="queryOneDocumentType"
                    label="Type de document"
                    item-title="name"
                    :rules="[requiredRule]"
                    :model-value="requests[index].document.documentTypeId"
                    infinite-scroll
                    :disabled="request.loading"
                    @update:model-value="emit('update-document-type-id', { index, value: $event })"
                  >
                    <template #item="{ item, props }">
                      <VListItem v-bind="props" :subtitle="undefined">
                        <template #title>
                          {{ (item.raw as DocumentType).name }}
                          <i>{{ displayExtensionList(getExtensionsInList(item.raw as DocumentType)) }}</i>
                        </template>
                      </VListItem>
                    </template>
                  </RemoteAutoComplete>
                </VCol>
                <VCol>
                  <VTextField
                    label="Description"
                    :model-value="requests[index].document.description"
                    :disabled="request.loading"
                    @update:model-value="emit('update-description', { index, value: $event })"
                  />
                </VCol>
              </VRow>
            </VCol>
            <VCol
              v-if="missingDocuments?.length && !requests[index].document.documentTypeId"
              class="d-flex align-center pa-1 flex-wrap"
              style="row-gap: 8px"
            >
              <span class="mr-2"> Suggestions (documents requis): </span>
              <VChip
                v-for="d in missingDocuments"
                :key="d.id"
                class="mx-1"
                @click="emit('update-document-type-id', { index, value: d.id })"
              >
                {{ d.name }}
              </VChip>
            </VCol>
          </VRow>
        </VCardText>
        <VRow v-if="request.file.size > MAX_FILE_SIZE">
          <VCol>
            <ErrorAlert :message="`La taille du fichier ne doit pas exceder ${MAX_FILE_SIZE / 1_000_000} Mo`" />
          </VCol>
        </VRow>
        <VRow v-if="request.error">
          <VCol>
            <ErrorAlert :message="request.error" />
          </VCol>
        </VRow>
      </VCard>
    </VCol>
  </VRow>
</template>
<script lang="ts" setup>
import type { OperationsGroupDocumentRequest } from '@/types/document'
import { displayExtensionList, getExtensionsInList, type DocumentType } from '@/types/documentType'
import type { Pageable } from '@/types/pagination'
import { requiredRule } from '@/types/rule'
import type { PropType } from 'vue'
import { VListItem } from 'vuetify/components'
import type GenericSubmitDocumentItem from './GenericDocumentItem'
import gsap from 'gsap'

const props = defineProps({
  operationId: Number,
  operationsGroupId: Number,
  emmyFolderId: Number,
  controlOrderBatchId: Number,
  requests: {
    type: Array as PropType<GenericSubmitDocumentItem[]>,
    required: true,
  },
  missingDocuments: {
    type: Array as PropType<DocumentType[]>,
    default: () => [],
  },
})

const emit = defineEmits<{
  submitDialog: [number, OperationsGroupDocumentRequest]
  remove: [number]
  'update-description': [{ index: number; value: string }]
  'update-document-type-id': [{ index: number; value: number }]
}>()

const MAX_FILE_SIZE = 100_000_000

const queryDocumentTypeNotInOperation = (search: string, pageable: Pageable) => {
  return documentTypeApi.getAll(
    pageable,
    props.operationId
      ? {
          search: search,
          requiredInOperationsGroupForOperationInOperationsGroup: props.operationsGroupId ? false : undefined,
        }
      : props.operationsGroupId
        ? {
            search: search,
            shareable: true,
          }
        : {
            search: search,
          }
  )
}

const queryOneDocumentType = (id: number) => {
  return documentTypeApi.getOne(id)
}

const remove = (index: number) => {
  emit('remove', index)
}

const a = useTemplateRef('root')
watch(
  () => props.requests.length,
  (v, oldV) => {
    if (v > oldV) {
      nextTick(() => {
        const children = Array.from(a.value?.$el.children || [])
        const addedElements = children.slice(oldV, v) as HTMLElement[]
        addedElements.forEach((htmlElement) => {
          htmlElement.scrollIntoView({ behavior: 'smooth', block: 'center' })
          gsap.from(htmlElement.children[0], {
            duration: 1,
            ease: 'ease-in',
            backgroundColor: 'rgba(0, 122, 205, 0.5)',
          })
        })
      })
    }
  },
  { deep: true }
)
</script>
