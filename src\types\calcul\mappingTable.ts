import { cloneDeep } from 'lodash'
import type { ParameterFormula } from './parameterFormula'

export type MappingTable = {
  id: string
  data: number[]
  paramColumns: string[]
}

export type FormulaMappingTable = {
  id: string
  data: string[]
  paramColumns: string[]
}

export const makeEmptyMappingTable = (): MappingTable => {
  return {
    id: '',
    data: [],
    paramColumns: [],
  }
}

export const generateMappingTableCombinaisons = (mappingTable: MappingTable, parameters: ParameterFormula[]) => {
  const combinaisons: string[][] = []
  const args = mappingTable.paramColumns?.map((paramColumn) => {
    const parameter = parameters.find((p) => paramColumn === p.id && p.type === 'CHOICE')
    if (parameter == null) {
      throw 'Vous devez sélectionner un paramètre de type choix'
    }
    return parameter
  })
  const allValues = args?.map((it) => (it.data as string).split(';'))

  const iterators = cloneDeep(allValues)

  while (iterators.length > 0 && iterators[0].length > 0) {
    combinaisons.push(iterators.map((it) => it[0]))

    let iIterator = iterators.length - 1
    iterators[iIterator].shift()
    while (iterators[iIterator].length === 0 && iIterator > 0) {
      iterators[iIterator] = allValues[iIterator].concat()
      iIterator--
      iterators[iIterator].shift()
    }
  }

  return combinaisons
}
