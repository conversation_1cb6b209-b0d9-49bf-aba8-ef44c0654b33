<template>
  <VTooltip :disabled="!messages?.length">
    <template #activator="{ props }">
      <VChip :class="styleClass" v-bind="props">
        <VIcon v-if="icon" :icon="icon" :color="iconColor" start />
        {{ text }}
      </VChip>
    </template>
    <p v-for="(message, index) in messages" :key="index">
      {{ message }}
    </p>
  </VTooltip>
</template>
<script lang="ts" setup>
import type { PropType } from 'vue'
import { VChip } from 'vuetify/components'

const props = defineProps({
  text: String,
  status: String as PropType<'success' | 'warning' | 'error' | 'cancelled' | 'done'>,
  messages: Array as PropType<string[]>,
})

const styleClass = computed(() => {
  return 'stepchip__status-' + props.status
})

const icon = computed(() => {
  switch (props.status) {
    case 'success':
      return 'mdi-sync'
    case 'warning':
      return 'mdi-alert'
    case 'error':
      return 'mdi-alert-rhombus'
    case 'done':
      return 'mdi-check-circle-outline'
    default:
      return ''
  }
})

const iconColor = computed(() => {
  switch (props.status) {
    case 'success':
      return '#006725'
    case 'warning':
      return '#8E441A'
    case 'error':
      return '#8E441A'
    case 'done':
      return '#006725'
    default:
      return ''
  }
})
</script>
<style scoped>
.stepchip__status-success {
  background: #aedfb3;
  border: 1px solid #63be74;
  color: #171d21 !important;
}
.stepchip__status-warning {
  background: #ffc89d;
  border: 1px solid #ff8c47;
  color: #171d21 !important;
}
.stepchip__status-error {
  background: #ef9a9a;
  border: 1px solid #db3835;
  color: #171d21 !important;
}
.stepchip__status-cancelled {
  background: #ccd4da;
  border: 1px solid #9eadb8;
  color: #171d21 !important;
}
.stepchip__status-done {
  background: #ccd4da;
  border: 1px solid #9eadb8;
  color: #171d21 !important;
}
</style>
