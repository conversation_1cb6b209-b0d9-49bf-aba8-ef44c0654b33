<template>
  <VRow class="flex-column">
    <VCol>
      <EnhancedDocumentDataTable
        :pageable="pageable"
        :page="data.value"
        :actions="props.actions"
        :on-deactivate="desactivateDocument"
        :on-delete="deleteDocument"
        :on-replace="replaceDocument"
        :on-edit="editDocument"
        type="emmyFolder"
        @update:pageable="updatePageable"
      />
      <CardDialog v-model="editDocumentDialog" width="30%" title="Modifier le type de document" :closeable="false">
        <VForm ref="">
          <RemoteAutoComplete
            v-model="emmyFolderDocumentRequest.documentTypeId"
            :query-for-all="queryDocumentTypeNotInOperation(emmyFolderDocument?.id ?? 0)"
            :query-for-one="queryOneDocumentType"
            item-title="name"
            infinite-scroll
          />
        </VForm>
        <template #actions>
          <NjBtn variant="outlined" @click="editDocumentDialog = false">Annuler</NjBtn>
          <NjBtn @click="saveDocument"> Valider </NjBtn>
        </template>
      </CardDialog>
      <CardDialog v-model="replaceDocumentDialog" width="40%" title="Remplacer le document" :closeable="false">
        <VRow class="flex-column my-n2">
          <VCol>
            <NjFileInput
              @new-files="
                (event) => {
                  replaceFile = []
                  replaceFile.push(...event)
                }
              "
            />
          </VCol>
          <VCol>
            <NjDisplayValue
              v-if="replaceFile.length > 0"
              :label="oldDocument?.documentType.name ?? ''"
              :value="replaceFile[0].name"
            />
          </VCol>
        </VRow>
        <template #actions>
          <NjBtn variant="outlined" @click="replaceDocumentDialog = false">Annuler</NjBtn>
          <NjBtn @click="submitReplaceDocument"> Valider </NjBtn>
        </template>
      </CardDialog>
      <AlertDialog v-bind="deleteDocumentDialog.props" title="Supprimer le document" max-width="640px">
        Êtes-vous sûr de vouloir supprimer le document?
      </AlertDialog>
    </VCol>
  </VRow>
</template>
<script setup lang="ts">
import {
  makeEmptyEmmyFolderDocumentRequest,
  type EmmyFolderDocumentRequest,
  type EnhancedDocument,
} from '@/types/document'
import NjBtn from '@/components/NjBtn.vue'
import RemoteAutoComplete from '@/components/RemoteAutoComplete.vue'
import { useSnackbarStore } from '@/stores/snackbar'
import NjFileInput from '@/components/NjFileInput.vue'
import NjDisplayValue from '@/components/NjDisplayValue.vue'
import { documentTypeApi } from '@/api/documentType'
import type { PropType } from 'vue'
import { emmyFolderDocumentApi, type EmmyFolderDocumentFilter } from '@/api/emmyFolderDocument'
import EnhancedDocumentDataTable from '@/views/EnhancedDocumentDataTable.vue'
import type { Pageable } from '@/types/pagination'

const props = defineProps({
  id: {
    type: Number,
    required: true,
  },
  filter: {
    type: Object as PropType<EmmyFolderDocumentFilter>,
  },
  actions: {
    type: Boolean,
    default: false,
  },
  showDisabledDocuments: Boolean,
})
const snackbarStore = useSnackbarStore()
const { data, pageable, updatePageable, reload, updateFilterByFieldname } = usePagination<EnhancedDocument>(
  (filter, pageable) => emmyFolderDocumentApi.findAll(filter, pageable),
  {
    ...props.filter,
    active: true as boolean | undefined,
  },
  {
    sort: ['document.creationDateTime,DESC'],
  }
)
watch(
  () => props.showDisabledDocuments,
  (v) => {
    updateFilterByFieldname('active', !v ? true : undefined)
  }
)

const editDocumentDialog = ref(false)
const deleteDocumentDialog = useConfirmAlertDialog()
const emmyFolderDocument = ref<EnhancedDocument>()
const emmyFolderDocumentRequest = ref(makeEmptyEmmyFolderDocumentRequest())

const editDocument = (doc: EnhancedDocument) => {
  emmyFolderDocument.value = doc
  emmyFolderDocumentRequest.value = {
    emmyFolderId: props.id,
    documentTypeId: doc.documentType.id,
    description: doc.description,
    active: doc.active,
  }
  editDocumentDialog.value = true
}

const queryDocumentTypeNotInOperation = (operationId: number) => (search: string, pageable: Pageable) => {
  return documentTypeApi.getAll(pageable, {
    search: search,
    notActiveInOperation: operationId,
  })
}

const queryOneDocumentType = (id: number) => {
  return documentTypeApi.getOne(id)
}

const promisableEmmyFolderDocument = ref(emptyValue<EnhancedDocument>())

const saveDocument = () => {
  if (!emmyFolderDocument.value) {
    return
  }
  handleAxiosPromise(
    promisableEmmyFolderDocument,
    emmyFolderDocumentApi.update(emmyFolderDocument.value?.id, emmyFolderDocumentRequest.value),
    {
      afterSuccess: () => {
        snackbarStore.setSuccess('Le document à été mis à jour')
        reload()
        editDocumentDialog.value = false
      },
      afterError: () => {
        snackbarStore.setError('Erreur lors de la mise à jour du document')
      },
    }
  )
}

const deleteDocument = async (item: EnhancedDocument) => {
  if (await deleteDocumentDialog.confirm()) {
    emmyFolderDocumentApi
      .delete(item.id)
      .then(() => {
        snackbarStore.setSuccess('Le document à été supprimé')
        reload()
      })
      .catch(() => {
        snackbarStore.setError('Une erreur est survenue lors de la suppression du document')
      })
  }
}

const promisableDesactivatedEmmyFolderDocument = ref(emptyValue<EnhancedDocument>())
const desactivateDocument = (item: EnhancedDocument) => {
  const request: EmmyFolderDocumentRequest = {
    emmyFolderId: props.id,
    documentTypeId: item.documentType.id,
    description: item.description,
    active: false,
  }
  handleAxiosPromise(promisableDesactivatedEmmyFolderDocument, emmyFolderDocumentApi.update(item.id, request), {
    afterSuccess: () => {
      snackbarStore.setSuccess('Le document à été désactivé')
      reload()
    },
    afterError: () => {
      snackbarStore.setError("Erreur le document n'a pas pu être désactivé")
    },
  })
}

//remplacer dialog
const replaceDocumentDialog = ref(false)
const oldDocument = ref<EnhancedDocument>()
const replaceFile = ref<File[]>([])
const replaceDocument = (old: EnhancedDocument) => {
  oldDocument.value = old
  replaceDocumentDialog.value = true
}

const promisableReplaceEmmyFolderDocument = ref(emptyValue<EnhancedDocument>())

const submitReplaceDocument = () => {
  if (!oldDocument.value) {
    return
  }
  const request: EmmyFolderDocumentRequest = {
    emmyFolderId: props.id,
    documentTypeId: oldDocument.value.documentType.id,
    description: oldDocument.value.description,
    active: true,
  }
  handleAxiosPromise(
    promisableReplaceEmmyFolderDocument,
    emmyFolderDocumentApi.uploadFile(request, replaceFile.value[0], oldDocument.value.id),
    {
      afterSuccess: () => {
        snackbarStore.setSuccess('Le document a été remplacé')
        reload()
        replaceDocumentDialog.value = false
        replaceFile.value = []
      },
      afterError: () => snackbarStore.setError("Erreur le document n'a pas pu être remplacé"),
    }
  )
}

defineExpose({
  reload,
})
</script>
