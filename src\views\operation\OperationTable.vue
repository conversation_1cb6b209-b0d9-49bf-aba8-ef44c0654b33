<template>
  <NjDataTable
    v-bind="$attrs"
    :pageable="pageable"
    :page="data"
    :headers="columnManagerRef?.headers ?? originalHeaders"
    :disabled-row="disabledRow"
    :on-click-row="clickRow"
    :clicked-row="clickedRow"
    :status="(simu: Operation, index: number) => resolveColorStatusDatatable(statusLine[index])"
    :to-row="(item) => ({ name: 'OperationOneView', params: { id: item.id } })"
    :selections="selection"
    checkboxes
    multi-selection
    :fixed
    :hide-checkboxes="hideCheckboxes"
    select-on-id
    @update:pageable="updatePageable"
    @update:selections="emit('update:selection', $event)"
  >
    <template #[`item.property`]="{ item }">
      <div v-if="item.property?.code" class="me-2">Numéro: {{ item.property?.code }},</div>
      <div v-if="displayAddress(item.property, item.finalAddress)">
        Adresse retenue:
        {{
          (item.finalAddress?.street ?? '') +
          ', ' +
          (item.finalAddress?.postalCode ?? '') +
          ' ' +
          (item.finalAddress?.city ?? '')
        }}
      </div>
      <div v-else-if="item.property">
        Adresse:
        {{
          (item.property.streetNumber ?? '') +
          ' ' +
          item.property.streetName +
          ', ' +
          item.property.postalCode +
          ' ' +
          item.property.city
        }}
      </div>
    </template>
    <template #[`item.reservedDate`]="{ item }">
      <span :class="!isOperationDurationValid(item) ? 'text-warning' : ''">
        {{ formatHumanReadableLocalDate(item.reservedDate) }}
      </span>
    </template>
    <template #[`item.stepId`]="{ item, index }">
      <OperationStepChip
        :operation="item"
        :valuations="valuations?.[index]"
        @update:type="statusLine[index] = $event"
      />
    </template>
    <template #[`item.cdp`]="{ item }">
      <NjBooleanIcon :condition="item.boostBonusSimulation != null" />
    </template>
    <template #[`item.epc`]="{ item }">
      <NjBooleanIcon :condition="item.epcBonusParameterValues != null" />
    </template>
    <template #[`item.estimatedCommitmentDate`]="{ item }">
      <span
        :class="
          !isEstimatedCommitmentDateValid(item, adminConfigurationStore.commitmentPeriod?.data) ? 'text-warning' : ''
        "
      >
        {{ formatHumanReadableLocalDate(item.estimatedCommitmentDate) }}
      </span>
    </template>
    <template #[`item.estimatedEndOperationDate`]="{ item }">
      <span :class="!isEstimatedEndWorkValid(item, adminConfigurationStore.workPeriod?.data) ? 'text-warning' : ''">
        {{ formatHumanReadableLocalDate(item.estimatedEndOperationDate) }}
      </span>
    </template>
    <template #[`item.standardizedOperationSheet.operationCode`]="{ item }">
      <span :class="isStandardizedOperationSheetCompatible(item)">
        {{ item.standardizedOperationSheet.operationCode }}
      </span>
    </template>
    <template #[`item.standardizedOperationSheet.startDate`]="{ item }">
      <span :class="isStandardizedOperationSheetCompatible(item)">
        {{ formatHumanReadableLocalDate(item.standardizedOperationSheet.startDate) }}
      </span>
    </template>
    <template #[`item.standardizedOperationSheet.expirationDate`]="{ item }">
      <span :class="isStandardizedOperationSheetCompatible(item)">
        {{ formatHumanReadableLocalDate(item.standardizedOperationSheet.expirationDate) }}
      </span>
    </template>
    <template #[`item.standardizedOperationSheet.controlOrderStartDate`]="{ item }">
      <b :class="isOperationSubjectToControlOrder(item) ? 'text-green' : ''">
        {{ isOperationSubjectToControlOrder(item) ? 'OUI' : 'NON' }}
      </b>
    </template>
    <template #[`item.precariousness`]="{ item }">
      <NjBooleanIcon
        :condition="
          item.parameterValues.length > 0
            ? item.precariousnessBonusParameterValues != null
            : item.precariousnessCumac > 0
        "
      />
    </template>
    <template #[`item.manualCumac`]="{ item }">
      <NjBooleanIcon :condition="(item.parameterValues ?? []).length !== 0" />
    </template>
    <template #[`item.headOperation`]="{ item }">
      <NjBooleanIcon :condition="item.headOperation" />
    </template>
    <template #[`item.chronoCode`]="{ item }">
      <OperationTableChronoCodeClockAlert :item="item" />
      {{ item.chronoCode }}
    </template>
  </NjDataTable>
  <ColumnManagerDialog
    id="operationAllView"
    ref="columnManagerRef"
    :original-headers="originalHeaders"
    :model-value="drawer === 'column'"
    width="300"
    @update:model-value="
      (v) => {
        emit('update:drawer', undefined)
      }
    "
  />
</template>
<script lang="ts" setup>
import {
  isEstimatedCommitmentDateValid,
  isEstimatedEndWorkValid,
  isStandardizedOperationSheetValid,
  isStandardizedOperationSheetAndCommitmentCompatible,
  isOperationDurationValid,
  type Operation,
} from '@/types/operation'
import { displayAddress } from '@/types/address'
import { formatHumanReadableLocalDate } from '@/types/date'
import { useAdminConfigurationStore } from '@/stores/adminConfiguration'
import type { Page, Pageable } from '@/types/pagination'
import OperationStepChip from './OperationStepChip.vue'
import ColumnManagerDialog from '@/components/ColumnManagerDialog.vue'
import { operationTableHeaders } from './operationTableHeader'
import type { PropType } from 'vue'
import type { Valuation } from '@/types/valuation'
import OperationTableChronoCodeClockAlert from './OperationTableChronoCodeClockAlert.vue'

const props = defineProps({
  pageable: Object as PropType<Pageable>,
  updatePageable: Function as PropType<(arg: Pageable) => void>,
  data: Object as PropType<Page<Operation>>,
  selection: Array as PropType<Operation[]>,
  drawer: String,
  hideCheckboxes: Function,
  valuations: {
    type: Array as PropType<Valuation[][]>,
  },
  fixed: {
    type: Boolean,
    default: true,
  },
})

const emit = defineEmits<{
  'update:selection': [value: Operation[]]
  'update:drawer': [value: string | undefined, id?: number]
}>()

const adminConfigurationStore = useAdminConfigurationStore()

const selectedOperationId = ref(0)
const clickedRow = ref<Operation>()
const clickRow = async (item: Operation) => {
  clickedRow.value = item
  if (item.id !== selectedOperationId.value) {
    selectedOperationId.value = item.id
    emit('update:drawer', 'detail', item.id)
    return
  }
  if (props.drawer === 'detail') {
    emit('update:drawer', undefined)
  } else {
    emit('update:drawer', 'detail')
  }
}

const originalHeaders = operationTableHeaders(clickRow)

const disabledRow = (ope: Operation) => ope.status === 'CANCELLED' || ope.status === 'IMPROPER' || ope.status === 'LOST'

const resolveColorStatusDatatable = (status: 'success' | 'warning' | 'error' | 'cancelled') => {
  switch (status) {
    case 'success':
      return '#28B750'
    case 'error':
      return '#DB3B38'
    case 'warning':
      return 'rgb(255, 140, 71)'
    case 'cancelled':
      return '#9EADB8'
  }
}
const statusLine = ref<('error' | 'warning' | 'success' | 'cancelled')[]>([])

watch(
  () => props.drawer,
  (v) => {
    if (v !== 'detail') {
      clickedRow.value = undefined
    }
  }
)

watch(
  () => props.data?.content,
  (v) => {
    if (selectedOperationId.value) {
      const simulation = v?.find((it) => it.id === selectedOperationId.value)
      clickedRow.value = simulation
    }
  }
)

const isStandardizedOperationSheetCompatible = computed(
  () => (ope: Operation) =>
    !isStandardizedOperationSheetAndCommitmentCompatible(ope)
      ? 'text-error'
      : !isStandardizedOperationSheetValid(ope, adminConfigurationStore.expirationPeriod?.data)
        ? 'text-warning'
        : ''
)

const isOperationSubjectToControlOrder = (item: Operation) =>
  item.standardizedOperationSheet.controlOrderStartDate &&
  new Date(item.standardizedOperationSheet.controlOrderStartDate) <=
    new Date(item.signedDate ?? item.estimatedCommitmentDate)
const columnManagerRef = ref<typeof ColumnManagerDialog | null>(null)
</script>
