<template>
  <NjPage expend-body v-bind="$attrs">
    <template #body>
      <VRow class="flex-column" dense>
        <VCol class="flex-grow-0">
          <VRow dense>
            <VCol>
              <SearchInput v-model="pageFilter.search" />
            </VCol>
            <VCol>
              <RemoteAutoComplete
                v-model="operation"
                label="Opération"
                :query-for-one="(id) => operationApi.findById(id)"
                :query-for-all="(v) => operationApi.findAll({ search: v }, {})"
                :item-title="(v) => v.standardizedOperationSheet.operationCode + ' ' + v.operationName ?? ''"
                return-object
                clearable
                @update:model-value="
                  (v) => {
                    updateFilterByFieldname('operationId', v?.id)
                  }
                "
              />
            </VCol>
            <VCol>
              <NjBtn :disabled="!operation" @click="swornStatementDialog = true"> Tester </NjBtn>
              <CreateSwornStatementDialog v-model="swornStatementDialog" :operation="operation" />
            </VCol>
            <VSpacer />
            <VCol class="flex-grow-0">
              <NjBtn variant="outlined" color="error" @click="handleDeleteTemplateFrame">Supprimer</NjBtn>
            </VCol>
            <VCol class="flex-grow-0">
              <NjBtn @click="handleCreateTemplateFrame">Nouveau cadre</NjBtn>
              <AlertDialog title="Créer un cadre" v-bind="createTemplateFrameDialog.props" width="40%">
                <TemplateFrameForm ref="createTemplateFrameForm" />
                <template #positiveButton>
                  <NjBtn @click="handlePositiveClick">Valider</NjBtn>
                </template>
              </AlertDialog>
            </VCol>
          </VRow>
        </VCol>
        <VCol>
          <NjDataTable
            v-model:selections="selections"
            checkboxes
            :pageable="pageable"
            :on-click-row="clickRow"
            :clicked-row="selectedRow"
            :page="data.value!"
            :headers="headers"
            fixed
            multi-selection
            @update:pageable="updatePageable"
          >
          </NjDataTable>
        </VCol>
      </VRow>
    </template>
  </NjPage>
  <VNavigationDrawer v-if="selectedRow" location="right" width="500" permanent>
    <VCard class="h-100 content-layout">
      <VCardTitle class="content-layout__header">
        <VRow>
          <VCol>Détails</VCol>
          <VCol class="flex-grow-0" justify="end">
            <NjIconBtn icon="mdi-pencil" color="primary" rounded="0" @click="edit = !edit" />
            <NjIconBtn
              icon="mdi-window-close"
              rounded="0"
              variant="flat"
              color="primary"
              @click="selectedRow = undefined"
            />
          </VCol>
        </VRow>
      </VCardTitle>
      <VDivider />
      <VCardText class="content-layout__main content-layout">
        <TemplateFrameForm v-if="edit" ref="saveTemplateFrameForm" :model-value="selectedRow" />
        <VRow v-else class="flex-column flex-grow-0" dense>
          <VCol>
            <NjDisplayValue label="Nom" :value="selectedRow.name" />
          </VCol>
          <VCol>
            <NjDisplayValue label="Modèle">
              <template #value>
                <VLink @click="download(selectedRow)">{{ selectedRow.template.originalFilename }}</VLink>
              </template>
            </NjDisplayValue>
          </VCol>
          <VDivider />
          <VCol>
            <OperationTargetRuleDetails
              :id="selectedRow.id"
              :disable-all-inputs="true"
              :model-value="selectedRow.operationTargetRule"
            >
              <template #more>
                <VCol v-if="selectedRow.precariousnessCase2FrameD">
                  <NjDisplayValue
                    label="Précarité cas 2 bis avec QPV ou bailleur sociaux"
                    :value="
                      precariousnessCase2FrameDValueLabel.find((i) => i.value == selectedRow?.precariousnessCase2FrameD)
                        ?.title
                    "
                  />
                </VCol>
              </template>
            </OperationTargetRuleDetails>
          </VCol>
        </VRow>
      </VCardText>
      <VDivider />
      <VCardActions v-if="edit">
        <VSpacer />
        <NjBtn variant="outlined" @click="edit = false">Annuler</NjBtn>
        <NjBtn @click="handleSaveTemplateFrame">Valider</NjBtn>
      </VCardActions>
    </VCard>
  </VNavigationDrawer>
</template>
<script setup lang="ts">
import NjBtn from '@/components/NjBtn.vue'
import NjPage from '@/components/NjPage.vue'
import type { DataTableHeader } from '@/components/okta/NjDataTable.type'
import {
  type SwornStatementTemplateFrame,
  precariousnessCase2FrameDValueLabel,
} from '@/types/swornStatementTemplateFrame'
import TemplateFrameForm from './TemplateFrameForm.vue'
import {
  swornStatementTemplateFrameApi,
  type SwornStatementTemplateFrameFilter,
} from '@/api/swornStatementTemplateFrame'
import { useSnackbarStore } from '@/stores/snackbar'
import {
  VCard,
  VCardActions,
  VCardText,
  VCardTitle,
  VCol,
  VDivider,
  VNavigationDrawer,
  VRow,
  VSpacer,
} from 'vuetify/components'
import OperationTargetRuleDetails from '../OperationTargetRuleDetails.vue'
import { cloneDeep } from 'lodash'
import { useDialogStore } from '@/stores/dialog'
import type { Operation } from '@/types/operation'
import RemoteAutoComplete from '@/components/RemoteAutoComplete.vue'
import { operationApi } from '@/api/operation'
import CreateSwornStatementDialog from '@/views/operation/CreateSwornStatementDialog.vue'

const { pageable, pageFilter, updateFilterByFieldname, updatePageable, data, reload } = usePagination<
  SwornStatementTemplateFrame,
  SwornStatementTemplateFrameFilter
>((filter, pageable) => swornStatementTemplateFrameApi.getAll(pageable, filter), {}, { sort: ['name,asc'] })

const headers: DataTableHeader<SwornStatementTemplateFrame>[] = [
  {
    title: 'Nom',
    value: 'name',
  },
  {
    title: 'Modèle',
    value: 'template.originalFilename',
  },
]

const clickRow = (row: SwornStatementTemplateFrame) => {
  if (row.id == selectedRow.value?.id) {
    selectedRow.value = undefined
  } else {
    selectedRow.value = cloneDeep(row)
  }
}

const createTemplateFrameDialog = useConfirmAlertDialog()

const snackbarStore = useSnackbarStore()
const createTemplateFrameForm = ref<typeof TemplateFrameForm | null>(null)
const handlePositiveClick = async () => {
  if (!(await createTemplateFrameForm.value!.form.validate()).valid) {
    return
  }
  createTemplateFrameDialog.props['onClick:positive']()
}
const handleCreateTemplateFrame = async () => {
  if (!(await createTemplateFrameDialog.confirm())) {
    return
  }
  swornStatementTemplateFrameApi
    .create(
      unref(createTemplateFrameForm.value!.mutableTemplateFrame),
      unref(createTemplateFrameForm.value!.templateFile)
    )
    .then(() => {
      snackbarStore.setSuccess('Le modèle a bien été ajouté')
      reload()
    })
    .catch(async (err) => {
      snackbarStore.setError(await handleAxiosException(err))
    })
}

const saveTemplateFrameForm = ref<typeof TemplateFrameForm | null>(null)
const selectedRow = ref<SwornStatementTemplateFrame>()
const edit = ref(false)

const handleSaveTemplateFrame = async () => {
  if (!(await saveTemplateFrameForm.value!.form.validate()).valid) {
    return
  }
  swornStatementTemplateFrameApi
    .update(
      selectedRow.value!.id,
      unref(saveTemplateFrameForm.value!.mutableTemplateFrame),
      unref(saveTemplateFrameForm.value!.templateFile)
    )
    .then((res) => {
      selectedRow.value = res.data
      snackbarStore.setSuccess('Le modèle a bien été mis à jour')
      edit.value = false
      reload()
    })
    .catch(async (err) => {
      snackbarStore.setError(await handleAxiosException(err))
    })
}

const download = (templateFrame: SwornStatementTemplateFrame) => {
  swornStatementTemplateFrameApi
    .downloadTemplate(templateFrame.id)
    .then((res) => {
      downloadFile(templateFrame.template.originalFilename, res.data)
    })
    .catch(async (err) => {
      snackbarStore.setError(await handleAxiosException(err, JSON.parse(await err.response.data.text()).message))
    })
}

const dialogStore = useDialogStore()
const deleteLoading = ref(false)

const selections = ref<SwornStatementTemplateFrame[]>([])
const handleDeleteTemplateFrame = async () => {
  if (
    await dialogStore.addAlert({
      title: 'Supprimer des cadres',
      message: 'Etes vous sure de vouloir supprimer des cadres?',
      maxWidth: 500,
    })
  ) {
    deleteLoading.value = true
    swornStatementTemplateFrameApi
      .delete(selections.value.map((i) => i.id))
      .then(() => reload())
      .catch(async (e) => {
        snackbarStore.setError(await handleAxiosException(e))
      })
      .finally(() => {
        deleteLoading.value = false
      })
  }
}

const operation = ref<Operation>()
const swornStatementDialog = ref(false)
</script>
