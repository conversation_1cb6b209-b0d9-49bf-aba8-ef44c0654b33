<template>
  <VMenu>
    <template #activator="{ props }">
      <NjIconBtn rounded="0" icon="mdi-dots-horizontal" v-bind="props" density="comfortable" />
    </template>
    <VList v-if="item">
      <VListItem v-if="userIsAdmin(userStore.currentUser)" prepend-icon="mdi-pencil" @click="onEdit(item)">
        Modifier le type de document
      </VListItem>
      <VListItem v-if="item.active" prepend-icon="mdi-swap-horizontal" @click="onReplace(item)"> Remplacer </VListItem>
      <VListItem v-if="item.active" prepend-icon="mdi-cancel" @click="onDeactivate(item)"> Désactiver </VListItem>
      <VListItem v-if="userIsAdmin(userStore.currentUser)" prepend-icon="mdi-delete-outline" @click="onDelete(item)">
        Supprimer
      </VListItem>
    </VList>
  </VMenu>
</template>
<script setup lang="ts">
import { useUserStore } from '@/stores/user'
import type { EnhancedDocument } from '@/types/document'
import { userIsAdmin } from '@/types/user'
import type { PropType } from 'vue'
import { VList, VListItem } from 'vuetify/components'

defineProps({
  item: Object as PropType<EnhancedDocument>,
  onDeactivate: { type: Function as PropType<(item: EnhancedDocument) => any>, required: true },
  onDelete: { type: Function as PropType<(item: EnhancedDocument) => any>, required: true },
  onReplace: { type: Function as PropType<(item: EnhancedDocument) => any>, required: true },
  onEdit: { type: Function as PropType<(item: EnhancedDocument) => any>, required: true },
})

const userStore = useUserStore()
</script>
