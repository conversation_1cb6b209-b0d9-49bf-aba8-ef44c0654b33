import type { AxiosInstance, AxiosPromise } from 'axios'
import axiosInstance from '.'
import type { Period } from '@/types/period'
import type { Page, Pageable } from '@/types/pagination'

export interface PeriodFilter {
  active?: boolean
}

class PeriodApi {
  public constructor(private axios: AxiosInstance) {}

  public create(period: Period): AxiosPromise<Period> {
    return this.axios.post('periods', period)
  }

  public update(period: Period): AxiosPromise<Period> {
    return this.axios.put('periods/' + period.id, period)
  }

  public getAll(pageable: Pageable, filter: PeriodFilter): AxiosPromise<Page<Period>> {
    return this.axios.get('periods', { params: { ...pageable, ...filter } })
  }

  public changeStatus(id: number, period: Period): AxiosPromise<void> {
    return this.axios.put(`periods/${id}`, period)
  }
}

export const periodApi = new PeriodApi(axiosInstance)
