<template>
  <VDialog v-model="value" width="75%" height="100%">
    <VCard class="content-layout h-100">
      <VCardTitle class="content-layout__header">
        <div class="d-flex align-center">
          Sociétés
          <NjIconBtn variant="square" icon="mdi-plus" class="ms-4" @click="showAddCompanyDialog = true"></NjIconBtn>
          <VSpacer />
          <NjSwitch
            label="Afficher que les actifs"
            class="mx-2"
            :model-value="pageFilter.enabled"
            :false-value="null"
            @update:model-value="updateFilterByFieldname('enabled', $event)"
          ></NjSwitch>
          <VRow class="flex-grow-0">
            <VCol>
              <NjBtn :loading="refreshCompanyLoading" @click="refreshPageCompany">Mettre à jour les sociétés</NjBtn>
            </VCol>
            <VCol>
              <NjBtn @click="updateAll">Mettre à jour les organisations</NjBtn>
            </VCol>
            <VCol>
              <NjIconBtn icon="mdi-close" rounded="0" @click="value = false" />
            </VCol>
          </VRow>
        </div>
      </VCardTitle>

      <VCardText class="content-layout__main">
        <ErrorAlert :message="refreshingError" class="flex-grow-0" />
        <NjDataTable v-model:pageable="pageable" :headers="headers" :page="data.value" fixed>
          <template #[`item.action`]="{ item }">
            <NjIconBtn
              icon="mdi-refresh"
              :disabled="!!refreshingEntity && refreshingEntity !== item.id"
              :loading="!!refreshingEntity && refreshingEntity === item.id"
              @click="refreshEntitiesFromUI(item)"
            ></NjIconBtn>
          </template>
        </NjDataTable>
      </VCardText>
    </VCard>

    <CardDialog
      v-model="showAddCompanyDialog"
      :persistent="creatingCompany.loading"
      width="40%"
      title="Ajouter une société"
      closeable
    >
      <RemoteAutoComplete
        v-model="companyToAdd"
        :query-for-all="queryAllGtfCompany"
        :query-for-one="queryOneGtfCompany"
        :disabled="creatingCompany.loading"
        infinite-scroll
      >
        <template #item="{ props, item }">
          <VListItem v-bind="props" :title="(item.raw as GtfCompanyDto).name">
            {{ (item.raw as GtfCompanyDto).name }} ({{ (item.raw as GtfCompanyDto).short_name }})
          </VListItem>
        </template>
        <template #selection="{ item }">
          <div v-if="item">{{ (item.raw as GtfCompanyDto).name }} ({{ (item.raw as GtfCompanyDto).short_name }})</div>
        </template>
      </RemoteAutoComplete>

      <div v-if="company">
        <NjDisplayValue label="nom" :value="company.name" />
        <NjDisplayValue label="Nom Court" :value="company!.short_name" />
        <NjDisplayValue label="SIRET" :value="company!.siret" />
      </div>
      <template #actions>
        <NjBtn :loading="creatingCompany.loading" @click="createCompany">Valider</NjBtn>
      </template>
    </CardDialog>
  </VDialog>
</template>

<script setup lang="ts">
import { companyApi, type CompanyFilter } from '@/api/company'
import { gtfOrgsApi, type GtfCompanyDto, type GtfEntityDto } from '@/api/external/gtf/gtfOrgs'
import { mapAxiosResponse, type GtfPage } from '@/api/external/gtf/type'
import CardDialog from '@/components/CardDialog.vue'
import ErrorAlert from '@/components/ErrorAlert.vue'
import NjDisplayValue from '@/components/NjDisplayValue.vue'
import NjIconBtn from '@/components/NjIconBtn.vue'
import RemoteAutoComplete from '@/components/RemoteAutoComplete.vue'
import type { DataTableHeader } from '@/components/okta/NjDataTable.type'
import NjDataTable from '@/components/okta/NjDataTable.vue'
import { useSnackbarStore } from '@/stores/snackbar'
import type { Company } from '@/types/company'
import type { EntityRequest } from '@/types/entity'
import type { Pageable } from '@/types/pagination'
import { isAxiosError, type AxiosResponse } from 'axios'

const props = defineProps({
  modelValue: Boolean,
})
const emit = defineEmits<{
  (e: 'update:model-value', v: boolean): void
  (e: 'saved'): void
  (e: 'reload'): void
}>()

const value = computed({
  get: () => props.modelValue,
  set: (v) => {
    emit('update:model-value', v)
  },
})

const { data, reload, pageable, updateFilterByFieldname, pageFilter } = usePagination(
  (filter: CompanyFilter, pageable) => companyApi.getAll(pageable, { ...filter, toObserve: true }),
  undefined,
  {
    sort: ['name'],
  }
)

const headers: DataTableHeader[] = [
  {
    title: 'Nom',
    value: 'name',
  },
  {
    title: 'Nom Court',
    value: 'shortName',
  },
  {
    title: 'SIRET',
    value: 'siret',
  },
  {
    title: 'Capital',
    value: 'capital',
    formater: (_, value) => formatPriceNumber(value),
    cellClass: 'text-right justify-end',
  },
  {
    title: 'Régime communautaire',
    value: 'frRegimeCommunautaire',
  },
  {
    title: 'Action',
    value: 'action',
  },
]

const showAddCompanyDialog = ref(false)
const queryAllGtfCompany = (v: string, pageable: Pageable) => {
  console.debug('queryAllGtfCompany', pageable)
  return gtfOrgsApi.getAllCompanies({ search: v }, { ...pageable, size: 20 }).then(mapAxiosResponse)
}
const queryOneGtfCompany = (id: number) => {
  return gtfOrgsApi.getOneCompany(id)
}
const companyToAdd = ref(0)
const company = ref<GtfCompanyDto | undefined>()
watch(companyToAdd, async (v) => {
  if (v) {
    company.value = (await gtfOrgsApi.getOneCompany(v)).data
  } else {
    company.value = undefined
  }
})

const creatingCompany = ref(emptyValue<Company>())

const snackbarStore = useSnackbarStore()

const createCompany = () => {
  handleAxiosPromise(
    creatingCompany,
    companyApi.create({ ...mapGtfCompanyDtoToCompany(company.value!)!, toObserve: true }),
    {
      async afterSuccess() {
        creatingCompany.value = loadingValue()
        refreshEntities({ ...mapGtfCompanyDtoToCompany(company.value!)! })
          .then(() => {
            showAddCompanyDialog.value = false
            reload()
          })
          .catch(() => {
            snackbarStore.setError(creatingCompany.value.error ?? 'Erreur lors de la création')
          })
          .finally(() => {
            creatingCompany.value.loading = false
          })
      },
      afterError() {
        snackbarStore.setError(creatingCompany.value.error ?? 'Erreur lors de la création')
      },
    }
  )
}

const refreshingEntity = ref(0)
const refreshingError = ref('')
const companyPromiseCaches: Record<number, Promise<unknown>> = {}
const checkCompany = (companyId: number): Promise<unknown> => {
  // console.debug('checkCompany', companyId)
  if (companyPromiseCaches[companyId] === undefined) {
    companyPromiseCaches[companyId] = new Promise((resolve, reject) => {
      companyApi
        .getOne(companyId)
        .then(resolve, () => {
          gtfOrgsApi
            .getOneCompany(companyId)
            .then((r) => {
              const company = r.data
              companyApi.create(mapGtfCompanyDtoToCompany(company)).then(resolve).catch(reject)
            })
            .catch(reject)
        })
        .catch(reject)
    })
  }
  return companyPromiseCaches[companyId]
}
// let entitycompanyPromiseCaches: Record<string, Promise<unknown>> = {} // Permet la synchronisation entre promises
const checkEntity = (entityId: string): Promise<EntityRequest> => {
  // console.debug('checkEntity', entityId)
  if (!entityId) {
    return Promise.reject()
  }
  return new Promise((resolve, reject) => {
    return gtfOrgsApi
      .getOneEntity(entityId)
      .then(async (r): Promise<EntityRequest> => {
        const entity = r.data
        await checkCompany(entity.company_id)
        // await checkEntity(entity.parent_id)
        const level = parseInt(entity.nav_level) as 0 | 1 | 2 | 3 | 4
        return {
          companyId: entity.company_id,
          name: entity.name,
          enabled: entity.enabled === 1 ? true : false,
          id: entity.id,
          navFullId: entity.nav_full_id,
          level,
          // gtfUpdateDateTime: entity['@last_update_datetime'],
          address: {
            street: (entity.address_route_number ?? '') + ' ' + (entity.address_route ?? '').trim(),
            city: entity.address_city!,
            postalCode: entity.address_postal_code!,
            additionalPostalAddress: entity.address_complement,
            country: null,
          },
          territoryId: entity.territory_id,
          territoryDescription: entity.territory_description,
          siret: entity.insee_siret,
          phoneNumber: entity.phone_number,
          natureCode: entity.nature_code,
          natureDescription: entity.nature_description,
        }
      })
      .then(resolve, reject)
  })
}
const refreshEntitiesFromUI = (item: Company) => {
  console.debug('refreshEntitiesFromUI', item.id, item.name)
  // entitycompanyPromiseCaches = {}
  return refreshEntities(item)
}
const refreshEntities = async (item: Company) => {
  console.debug('refreshEntities', item.id, item.name)
  refreshingEntity.value = item.id
  refreshingError.value = ''
  let page: GtfPage<GtfEntityDto> = {
    count: 0,
    hasMore: true,
    items: [],
    limit: 0,
    offset: 0,
  }
  let offset = 0
  while (page.hasMore) {
    await gtfOrgsApi
      .getAllEntities(
        {
          offset,
        },
        {
          company_id: item.id,
        }
      )
      .then(async (v) => {
        offset += v.data.count
        page = v.data
        const step = 20
        for (let i = 0, n = v.data.count; i < n; i += step) {
          await entityApi
            .updateFromGtf(
              await Promise.all(
                page.items.slice(i, i + 20).map((it) => {
                  return checkEntity(it.id)
                })
              )
            )
            .catch((e) => {
              console.error(e)
              refreshingError.value =
                'Erreur lors de la récupération des organisations (' + handleAxiosException(e) + ')'
            })
        }
        if (!page.hasMore) {
          emit('reload')
          refreshingEntity.value = 0
        }
      })
      .catch((e) => {
        console.error(e)
        refreshingError.value = 'Erreur lors de la récupération des organisations (' + handleAxiosException(e) + ')'
      })
  }

  refreshingEntity.value = 0

  console.debug('>-- refreshEntities', item.id, item.name)
}

const updateAll = async () => {
  for (const company of data.value.value?.content ?? []) {
    await refreshEntities(company)
  }
}

watch(showAddCompanyDialog, (v) => {
  if (v) {
    companyToAdd.value = 0
  }
})

const refreshCompanyLoading = ref(false)
const refreshPageCompany = async () => {
  refreshCompanyLoading.value = true
  for (let i = 0; i < data.value.value!.content.length; i++) {
    const companyId = data.value.value!.content[i].id
    refreshingEntity.value = companyId
    const gtfCompanyDto = (await gtfOrgsApi.getOneCompany(companyId)).data
    data.value.value!.content[i] = (await companyApi.create(mapGtfCompanyDtoToCompany(gtfCompanyDto))).data
    await gtfOrgsApi
      .getCompanyLogo(companyId)
      .catch((e) => {
        if (isAxiosError(e) && e.code === '404') {
          return {
            data: undefined,
          } as AxiosResponse<Blob | undefined>
        } else {
          throw e
        }
      })
      .then(async (v) => {
        await companyApi.updateLogo(companyId, v.data)
      })
      .catch(async (e) => {
        snackbarStore.setError(await handleAxiosException(e))
      })
  }
  refreshCompanyLoading.value = false
  refreshingEntity.value = 0
}
</script>
