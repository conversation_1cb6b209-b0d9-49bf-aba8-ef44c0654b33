import type { Page, Pageable } from '@/types/pagination'
import type { AxiosInstance, AxiosPromise } from 'axios'
import axiosInstance from '.'
import type { TerritoryDetailsHistory } from '@/types/history'

const entityDetailsUrl = '/territory_details_histories'

export interface TerritoryDetailsHistoryFilter {
  territoryId?: number
}

class TerritoryDetailsHistoryApi {
  public constructor(private axios: AxiosInstance) {}

  public findAll(
    pageable: Pageable,
    filter: TerritoryDetailsHistoryFilter
  ): AxiosPromise<Page<TerritoryDetailsHistory>> {
    return this.axios.get(entityDetailsUrl, {
      params: { ...filter, ...pageable },
    })
  }
}

export const territoryDetailsHistoryApi = new TerritoryDetailsHistoryApi(axiosInstance)
