<template>
  <div class="d-flex pa-4 align-start justify-center">
    <VCard class="" max-width="840px">
      <VCardTitle class="d-flex align-center"
        >Bonjour {{ userStore.currentUser.firstName }} {{ userStore.currentUser.lastName }} ({{
          userStore.currentUser.gid
        }}), bienvenue sur <img :src="logoSvg" style="height: 3rem; display: inline-block; vertical-align: bottom"
      /></VCardTitle>

      <VCardText>
        Formulaire de demande d'accès

        <VForm ref="formRef" class="mt-4" :disabled="profileRequest.loading" :readonly="!!profileRequest.value?.id">
          <VRow>
            <VCol cols="12">
              <VRadioGroup
                v-model="request.userRole"
                direction="horizontal"
                class="nj-user-profile-request__user-role-selection"
              >
                <VRow class="flex-column">
                  <VCol>
                    <VCard
                      class="px-3 py-2 border-sm h-100"
                      :class="{
                        'border-primary': request.userRole === 'AGENCE',
                        'border-opacity-100': request.userRole === 'AGENCE',
                      }"
                      @click="setUserRole('AGENCE')"
                    >
                      <VRadio label="Profil Simulation" value="AGENCE"> </VRadio>
                      <div class="font-weight-bold">
                        Utilisateurs impliqués dans la quantification des CEE dans le cadre de projets, tels que : les
                        ingénieurs d’études, chefs de projet, IEE, RD responsables des travaux, le commerce…
                      </div>
                    </VCard>
                  </VCol>
                  <VCol v-if="false">
                    <VCard
                      class="px-3 py-2 border-sm"
                      :class="{
                        'border-primary': request.userRole === 'AGENCE_PLUS',
                        'border-opacity-100': request.userRole === 'AGENCE_PLUS',
                      }"
                      @click="setUserRole('AGENCE_PLUS')"
                    >
                      <VRadio label="Profil Correspondant CEE" value="AGENCE_PLUS"> </VRadio>
                      <div class="text-subtitle-2">Gestion des simulations et des opérations</div>
                    </VCard>
                  </VCol>
                </VRow>
              </VRadioGroup>
            </VCol>
            <VCol class="font-weight-bold">
              Si votre mission est de gérer le processus complet de l'opération c'est à dire de la phase commerciale
              jusqu'à la fin de travaux y compris la gestion des justificatifs, veuillez vous adresser directement à
              votre référent territoire
            </VCol>
            <VCol v-if="userStore.currentUser?.roles.length" cols="12">
              <ErrorAlert
                type="warning"
                message="Nous détectons que votre compte a été désactivé. Si vous avez besoin de réutiliser CAPTE, vous pouvez vous réinscrire ou bien contacter votre référent territoire pour qu'il réactive votre compte."
              ></ErrorAlert>
            </VCol>
            <VCol cols="12">
              <VTextField v-model="request.function" label="Fonction occupée" :rules="[requiredRule]"></VTextField>
            </VCol>
            <VCol cols="12">
              <VRow>
                <VCol cols="12">
                  <RemoteAutoComplete
                    v-model="requestedTerritory"
                    label="Périmètre Territoire"
                    :query-for-one="(v) => territoryApi.findPublicOne(v)"
                    :query-for-all="(v, p) => territoryApi.findPublicAll({ search: v }, p)"
                    :item-title="(it) => formatTerritory(it as any)"
                    :rules="[requiredRule]"
                    item-value="id"
                    infinite-scroll
                    clearable
                    :page-size="50"
                    chips
                    closable-chips
                    return-object
                  />
                </VCol>
                <VCol cols="12">
                  <RemoteAutoComplete
                    v-model="request.entityId"
                    label="Périmètre Organisation"
                    :query-for-one="(v) => entityApi.getOne(v)"
                    :query-for-all="(v, p, args) => entityApi.getAllPublic({ search: v, visible: true, ...args }, p)"
                    :args-for-query-for-all="{ territoryIds: requestedTerritory ? [requestedTerritory.id] : undefined }"
                    :item-title="displayEntity"
                    item-value="id"
                    infinite-scroll
                    clearable
                    :page-size="50"
                    chips
                    closable-chips
                    :disabled="!requestedTerritory"
                    :rules="[
                      () =>
                        !!requestedTerritory || 'Vous devez sélectionner un territoire pour choisir une organisation',
                      requiredRule,
                    ]"
                  />
                </VCol>
              </VRow>
            </VCol>
            <VCol cols="12">
              <VTextarea v-model="request.requestComment" hide-details label="Commentaire" counter="500" rows="4" />
            </VCol>
            <!-- <VCol cols="12"> (* Champs obligatoire) </VCol> -->
          </VRow>
        </VForm>
      </VCardText>
      <VCardActions>
        <VSpacer />
        <span v-if="profileRequest.value?.id" class="text-success">
          Demande envoyée. Vous recevrez votre réponse par mail.
        </span>
        <NjBtn v-if="!profileRequest.value?.id" :loading="profileRequest.loading" @click="update">Envoyer</NjBtn>
      </VCardActions>
    </VCard>
  </div>
</template>

<script setup lang="ts">
import axiosInstance from '@/api'
import { entityApi } from '@/api/entity'
import { territoryApi } from '@/api/territory'
import logoSvg from '@/assets/capte-logo/CAPTE-couleur.png'
import { useSnackbarStore } from '@/stores/snackbar'
import { useUserStore } from '@/stores/user'
import { displayEntity, type Entity } from '@/types/entity'
import type { Territory } from '@/types/territory'
import { formatTerritory } from '@/types/territory'
import type { VForm } from 'vuetify/components/VForm'
import useClearSideBar from './clearSidebar'
import type { UpdateSelfUserProfileRequest } from '@/api/userProfileRequest'
import type { UserProfileRequest } from '@/types/userProfileRequest'
import { requiredRule } from '@/types/rule'
import ErrorAlert from '@/components/ErrorAlert.vue'

const formRef = ref<typeof VForm | null>(null)
const request = ref<UpdateSelfUserProfileRequest>({
  userRole: 'AGENCE',
  function: '',
  requestComment: '',
  entityId: '',
})
const requestedTerritory = ref<Territory>()
const requestedEntity = ref<Entity>()

const userStore = useUserStore()

const { succeedSave, onSave } = useUnsavedData(request, requestedEntity, requestedTerritory)
const snackbarStore = useSnackbarStore()

const profileRequest = ref(emptyValue<UserProfileRequest>())
const update = async () => {
  if ((await formRef.value!.validate()).valid) {
    handleAxiosPromise(profileRequest, axiosInstance.put('/users/me/profile_request', request.value), {
      afterSuccess() {
        succeedSave()
      },
      async afterError(e) {
        snackbarStore.setError(await handleAxiosException(e))
      },
    })
  }
}

onMounted(() => {
  profileRequest.value.loading = true
  handleAxiosPromise(profileRequest, axiosInstance.get('/users/me/profile_request'))
    .then(async (v) => {
      request.value = {
        function: v.data.function,
        requestComment: v.data.requestComment,
        userRole: v.data.userRole,
        entityId: v.data.entity.id,
      }
      requestedEntity.value = v.data.entity
      onSave()
    })
    .finally(() => {
      profileRequest.value.loading = false
    })
})

const router = useRouter()
onMounted(() => {
  if (userStore.currentUser.active) {
    router.push('/')
  }
})

useClearSideBar()

const setUserRole = (userRole: UserProfileRequest['userRole']) => {
  if (!profileRequest.value.value?.id) {
    request.value.userRole = userRole
  }
}
</script>

<style lang="scss">
.nj-user-profile-request__user-role-selection .v-selection-control-group {
  flex-direction: row;
  gap: 16px;
}
</style>
