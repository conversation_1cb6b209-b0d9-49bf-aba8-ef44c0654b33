<template>
  <div class="content-layout">
    <VProgressCircular v-if="beneficiaries.loading" class="ms-4" indeterminate />
    <NjDataTable
      v-else
      select-on-id
      :headers="beneficiaryHeader"
      :selections="props.selections"
      :pageable="pageable"
      :page="data.value!"
      fixed
      @update:selections="updateModelValue"
      @update:pageable="updatePageable"
    >
      <template #[`item.certified`]="{ item }">
        <VIcon v-show="item.certified" color="#28B750" icon="mdi-shield-check-outline" />
      </template>
    </NjDataTable>
  </div>
</template>
<script setup lang="ts">
import type { PropType } from 'vue'
import { type Page, usePagination } from '@/types/pagination'
import type { DataTableHeader } from '@/components/okta/NjDataTable.type'
import type { Beneficiary } from '@/types/beneficiary'
import { beneficiaryApi, type BeneficiaryFilter } from '@/api/beneficiary'
import { debounce } from 'lodash'
import NjDataTable from '@/components/okta/NjDataTable.vue'

const props = defineProps({
  selections: {
    type: Object as PropType<Beneficiary[]>,
  },
  search: {
    type: String,
  },
  fixed: Boolean,
})

const emit = defineEmits(['update:selections', 'update:loading'])

const defaultSort =
  (props.selections ?? []).length > 0
    ? ['"ids:' + props.selections?.map((b) => b.id).join(';') + '",DESC', 'certified,DESC']
    : ['certified,DESC']

const updateModelValue = (value: Beneficiary[]) => {
  emit('update:selections', value)
}

const beneficiaries = ref(emptyValue<Page<Beneficiary>>())

const { data, pageable, pageFilter, updatePageable, updateFilter, reload } = usePagination<
  Beneficiary,
  BeneficiaryFilter
>(
  (filter, pageable) => beneficiaryApi.findAll(pageable, filter),
  {},
  {
    page: 0,
    size: 100,
    sort: defaultSort,
  }
)

const beneficiaryHeader: DataTableHeader[] = [
  {
    title: 'Nom',
    value: 'lastName',
  },
  {
    title: 'Prénom',
    value: 'firstName',
  },
  {
    title: 'Raison sociale',
    value: 'socialReason',
  },
  {
    title: 'Siren',
    value: 'siren',
  },
  {
    title: 'Ville',
    value: 'city',
  },
  {
    title: 'Certifié',
    value: 'certified',
  },
  {
    title: 'Certifié par',
    value: 'certifyingUser',
    formater: (item) => (item.certified ? displayFullnameUser(item.updateUser) : ''),
    sortable: false,
  },
  {
    title: 'Certifié le',
    value: 'certifiedDateTime',
    formater: (item) => (item.certified ? formatHumanReadableLocalDateTime(item.updateDateTime) : ''),
    sortable: false,
  },
  {
    title: 'Créateur',
    value: 'creationUser',
    formater: (_, value) => displayFullnameUser(value),
  },
  {
    title: 'Date de création',
    value: 'creationDateTime',
    formater: (_, value) => (value ? formatHumanReadableLocalDateTime(value) : ''),
  },
  {
    title: 'Mis à jour par',
    value: 'updateUser',
    formater: (_, value) => displayFullnameUser(value),
  },
  {
    title: 'Date de mis à jour',
    value: 'updateDateTime',
    formater: (_, value) => (value ? formatHumanReadableLocalDateTime(value) : ''),
  },
]

const debounceSearch = debounce((v: string | undefined) => {
  updateFilter({ ...pageFilter.value, search: v })
}, 300)

watch(
  () => props.search,
  (v) => {
    data.value.loading = true
    debounceSearch(v)
  }
)

watch(
  () => data.value.loading,
  (v) => {
    emit('update:loading', v)
  }
)

onMounted(() => {
  if (props.search) {
    pageFilter.value.search = props.search
  }
})

defineExpose({ reload })
</script>
