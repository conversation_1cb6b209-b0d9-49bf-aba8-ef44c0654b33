<template>
  <NjExpansionPanel title="Fiche Opération" :model-value="expandedDetail">
    <template #title>
      <VRow class="align-center">
        <VCol> Fiche Opération </VCol>
        <VCol v-if="standardizedOperationSheet.rgeMandatory" class="flex-grow-0">
          <img :src="rgeLogo" style="height: 1.75rem" />
        </VCol>
        <VCol
          v-if="
            !(validateStandardizedOperationSheetAndCommitmentValidityRule === true) ||
            !(validateStandardizedOperationSheetRule == true) ||
            !(validateRgeRule == true)
          "
          class="flex-grow-0"
        >
          <AlertIcon
            :rules="[
              validateStandardizedOperationSheetRule,
              validateStandardizedOperationSheetAndCommitmentValidityRule,
              validateRgeRule,
            ]"
          />
        </VCol>
      </VRow>
    </template>

    <VRow class="flex-column" dense>
      <VCol>
        <NjDisplayValue label="Code" class="align-center" :color-title="isStandardizedOperationSheetCompatible">
          <template #value>
            <div
              class="d-flex flex-column align-end"
              :class="isStandardizedOperationSheetCompatible ? 'text-' + isStandardizedOperationSheetCompatible : ''"
            >
              <div>
                <b>{{ standardizedOperationSheet.operationCode }}</b>
              </div>
              <div style="font-size: 0.75rem">
                <AutoShrinkText :text="standardizedOperationSheet.description" />
              </div>
            </div>
          </template>
        </NjDisplayValue>
      </VCol>
      <VCol>
        <NjDisplayValue
          label="Coup de Pouce"
          class="align-center"
          :value="boostBonusSheets?.totalElements ? 'OUI' : 'NON'"
        />
      </VCol>
      <VCol>
        <NjDisplayValue
          label="CPE"
          class="align-center"
          :value="standardizedOperationSheet.epcBonusEligible && epcBonusSheets?.totalElements ? 'OUI' : 'NON'"
        />
      </VCol>
      <VCol>
        <NjDisplayValue
          label="Précarité"
          class="align-center"
          :value="standardizedOperationSheet.precariousnessBonusEnabled ? 'OUI' : 'NON'"
        />
      </VCol>
      <VCol>
        <NjDisplayValue
          label="Sous traitance"
          class="align-center"
          :value="standardizedOperationSheet.subcontractCompulsory ? 'OUI' : 'NON'"
        />
      </VCol>
      <VCol>
        <NjDisplayValue
          label="Fonds chaleur"
          class="align-center"
          :value="standardizedOperationSheet.heatFund ? 'OUI' : 'NON'"
        />
      </VCol>
      <VCol>
        <NjDisplayValue
          label="Arrêté Contrôle"
          class="align-center"
          :value="formatHumanReadableLocalDate(standardizedOperationSheet.controlOrderStartDate)"
        />
      </VCol>

      <template v-if="standardizedOperationSheet.controlOrderStartDate">
        <VCol>
          <NjDisplayValue
            label="Type de contrôle"
            class="align-center"
            :value="
              controlOrderTypeLabel.find((i) => i.value == standardizedOperationSheet.controlOrderType)?.label ?? ''
            "
          />
        </VCol>
        <VCol>
          <NjDisplayValue
            label="Nature du Contrôle"
            class="align-center"
            :value="
              controlOrderNatureLabel.find((i) => i.value == standardizedOperationSheet.controlOrderNature)?.label ?? ''
            "
          />
        </VCol>
      </template>
      <VCol>
        <NjDisplayValue label="Fiches">
          <template #value>
            <VLink
              v-if="standardizedOperationSheet.pdfDocument"
              icon="mdi-open-in-new"
              target="_blank"
              class="me-4"
              @click="downloadPdfDocument(standardizedOperationSheet)"
            >
              Fiche Officielle
            </VLink>
            <VLink
              v-if="standardizedOperationSheet.internalPdfDocument"
              icon="mdi-open-in-new"
              target="_blank"
              @click="downloadInternalPdfDocument(standardizedOperationSheet)"
            >
              Fiche interne
            </VLink>
          </template>
        </NjDisplayValue>
      </VCol>
      <VCol>
        <NjDisplayValue
          label="Validité de l'opération"
          class="align-center"
          :color-title="isStandardizedOperationSheetCompatible"
        >
          <template #value>
            <div>
              <div
                class="text-end"
                :class="isStandardizedOperationSheetCompatible ? 'text-' + isStandardizedOperationSheetCompatible : ''"
              >
                <b>Début</b> : {{ formatHumanReadableLocalDate(standardizedOperationSheet.startDate) }}
              </div>
              <div
                class="text-end"
                :class="isStandardizedOperationSheetCompatible ? 'text-' + isStandardizedOperationSheetCompatible : ''"
              >
                <b>Fin</b> :
                {{ formatHumanReadableLocalDate(standardizedOperationSheet.expirationDate) }}
              </div>
            </div>
          </template>
        </NjDisplayValue>
      </VCol>
      <template v-if="stepId >= 40 && standardizedOperationSheet.rgeMandatory">
        <VCol>
          <slot name="rgeGranted">
            <NjDisplayValue
              label="Date d'attribution du RGE"
              :value="formatHumanReadableLocalDate(rgeGrantedDate ?? undefined)"
            />
          </slot>
        </VCol>
        <VCol>
          <slot name="rgeEndOfValidity">
            <NjDisplayValue
              :color-title="!(validateRgeRule == true) ? 'warning' : undefined"
              :color-value="!(validateRgeRule == true) ? 'warning' : undefined"
              label="Date de fin de validité du RGE"
              :value="formatHumanReadableLocalDate(rgeEndOfValidityDate ?? undefined)"
            />
          </slot>
        </VCol>
      </template>
      <VCol v-if="nonCumulativeRulePage.value?.totalElements">
        <NjDisplayValue label="Règle de non cumul" label-class="text-no-wrap">
          <template #value>
            <div class="text-end">
              {{ nonCumulativeRulePage.value.totalElements }} règle(s) de non cumul avec
              {{ operationCodeForCumul.join(', ') }}
            </div>
            <div>
              <VDialog>
                <template #activator="{ props }">
                  <NjIconBtn icon="mdi-information-outline" color="primary" v-bind="props" />
                </template>
                <template #default="{ isActive }">
                  <VCard>
                    <VCardTitle class="d-flex">
                      Règles de non cumuls
                      <VSpacer />
                      <NjIconBtn icon="mdi-close" @click="isActive.value = !isActive.value" />
                    </VCardTitle>
                    <VDivider />
                    <VCardText>
                      <VRow class="flex-column" dense>
                        <VCol v-for="(item, index) in nonCumulativeRulePage.value.content" :key="index">
                          <div class="nj-display-value__value text-start">
                            {{ formatDisplayNonCumulativeRule(item) }}
                          </div>
                          <div v-if="item.description">
                            {{ item.description }}
                          </div>
                        </VCol>
                      </VRow>
                    </VCardText>
                  </VCard>
                </template>
              </VDialog>
            </div>
          </template>
        </NjDisplayValue>
      </VCol>
    </VRow>
  </NjExpansionPanel>
</template>
<script setup lang="ts">
import type { BoostBonusSheet } from '@/types/boostBonus'
import {
  controlOrderNatureLabel,
  controlOrderTypeLabel,
  type StandardizedOperationSheet,
} from '@/types/calcul/standardizedOperationSheet'
import { formatHumanReadableLocalDate, type LocalDate } from '@/types/date'
import { downloadInternalPdfDocument, downloadPdfDocument } from '@/types/operation'
import type { EpcBonusSheet } from '@/types/epcBonus'
import type { Page } from '@/types/pagination'
import rgeLogo from '@/assets/rge-logo/RGE.png'
import AlertIcon from '../AlertIcon.vue'
import type { NonCumulativeRule } from '@/types/nonCumulativeRule'

const props = defineProps({
  standardizedOperationSheet: { type: Object as PropType<StandardizedOperationSheet>, required: true },
  stepId: {
    type: Number,
    required: true,
  },

  rgeGrantedDate: String as PropType<LocalDate | null>,
  rgeEndOfValidityDate: String as PropType<LocalDate | null>,
  validateStandardizedOperationSheetRule: [String, Boolean] as PropType<true | string>,
  validateStandardizedOperationSheetAndCommitmentValidityRule: [String, Boolean] as PropType<true | string>,
  validateRgeRule: [String, Boolean] as PropType<true | string>,
  boostBonusSheets: Object as PropType<Page<BoostBonusSheet>>,
  epcBonusSheets: Object as PropType<Page<EpcBonusSheet>>,
  expandedDetail: Boolean,
})

const isStandardizedOperationSheetCompatible = computed(() =>
  !(props.validateStandardizedOperationSheetAndCommitmentValidityRule === true)
    ? 'error'
    : !(props.validateStandardizedOperationSheetRule == true)
      ? 'warning'
      : ''
)

const nonCumulativeRulePage = ref(emptyValue<Page<NonCumulativeRule>>())
watch(
  () => props.standardizedOperationSheet,
  (v) => {
    if (v.id) {
      handleAxiosPromise(
        nonCumulativeRulePage,
        nonCumulativeRuleApi.getAll({ size: 100 }, { standardizedOperationSheetId: v.id })
      )
    }
  },
  {
    immediate: true,
  }
)

const formatDisplayNonCumulativeRule = (rule: NonCumulativeRule) => {
  const operationCode =
    props.standardizedOperationSheet.operationCode == rule.operationCode1 ? rule.operationCode2 : rule.operationCode1

  if (rule.startDate || rule.endDate) {
    return `${operationCode} - (${formatHumanReadableLocalDate(rule.startDate)} - ${formatHumanReadableLocalDate(
      rule.endDate
    )})`
  } else {
    return operationCode
  }
}

const operationCodeForCumul = computed((): string[] => {
  const result = new Set<string>()
  nonCumulativeRulePage.value.value?.content.forEach((it) => {
    result.add(it.operationCode1)
    result.add(it.operationCode2)
  })
  result.delete(props.standardizedOperationSheet.operationCode)
  return Array.from(result).sort()
})
</script>
