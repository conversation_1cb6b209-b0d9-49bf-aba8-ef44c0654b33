import type { LocalDate } from '../date'
import type { OperationExportResultDto } from '../operationExportResultDto'
import type { Period } from '../period'

export interface UpdateOperationOshPriorityRequest {
  oshPriority: number | null
}

export interface OshFolderId {
  period: Period
  oshPriority: number
}

export const oshCommentaryLabel = [
  {
    label: 'Attente retour agence ',
    value: 'WAITING_AGENCY_RETURN',
  },
  {
    label: 'Attente validation instructrice 2 ',
    value: 'WAITING_INSTRUCTOR_2_VALIDATION',
  },
  {
    label: 'Repassé en étape 50 ',
    value: 'BACK_TO_STEP_50',
  },
  {
    label: 'Non conforme ',
    value: 'IMPROPER',
  },
  {
    label: 'Abandon agence ',
    value: 'AGENCY_GIVE_UP',
  },
] as const

export type OshCommentaryType = (typeof oshCommentaryLabel)[number]['value']

export interface OshFolder {
  oshFolderId: OshFolderId
}

export interface OshFolderSummary {
  emmyFolderIdWithName: { id: number; name: string }[] | null
  operationsNumber: number
  classicCumacSum: number
  precariousnessCumacSum: number
  minEndWorksDate: LocalDate
}

export const NUMBER_OF_DAY_AFTER_EFFECTIVE_END_WORKS_FOR_INSTRUCTION_DELAY = 91
export const NUMBER_OF_DAY_BEFORE_PNCEE_EXPIRATION = 364

export interface OshSelectedSummary {
  totalCumac: number
  classicCumac: number
  precariousnessCumac: number
  operationsNumber: number
}

export const getSelectedSummary = (selections: OperationExportResultDto[]) => {
  return selections.reduce(
    (acc: OshSelectedSummary, i) => {
      return {
        totalCumac: acc.totalCumac + (i.askedClassicCumac ?? 0) + (i.askedPrecariousnessCumac ?? 0),
        classicCumac: acc.classicCumac + (i.askedClassicCumac ?? 0),
        precariousnessCumac: acc.precariousnessCumac + (i.askedPrecariousnessCumac ?? 0),
        operationsNumber: acc.operationsNumber + 1,
      }
    },
    {
      totalCumac: 0,
      classicCumac: 0,
      precariousnessCumac: 0,
      operationsNumber: 0,
    }
  )
}
