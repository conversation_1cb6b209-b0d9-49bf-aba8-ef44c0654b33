<template>
  <CardDialog
    title="Ajouter à un lot de controle"
    :model-value="modelValue"
    style="max-width: 1300px"
    @update:model-value="emit('update:model-value', $event)"
  >
    <VRow class="flex-column content-layout">
      <VCol class="content-layout">
        <NjDataTable
          v-model:selections="selection"
          class="content-layout_main"
          :headers="headers"
          :pageable="pageable"
          :page="data.value!"
          @update:pageable="updatePageable"
        />
      </VCol>
    </VRow>

    <template #actions>
      <NjBtn variant="outlined" @click="emit('update:model-value', false)">Annuler</NjBtn>
      <NjBtn
        :disabled="!selection?.length"
        @click="addOperationInControlOrderBatch(selection[0].controlOrderBatch.id, operationIds!)"
        >Enregistrer</NjBtn
      >
    </template>
  </CardDialog>
</template>
<script setup lang="ts">
import type { ControlOrderBatchFilter } from '@/api/controlOrderBatchApi'
import CardDialog from '@/components/CardDialog.vue'
import type { DataTableHeader } from '@/components/okta/NjDataTable.type'
import { useSnackbarStore } from '@/stores/snackbar'
import type { ControlOrderBatchDto } from '@/types/controlOrder'
import type { LocalDate } from '@/types/date'
import type { PropType } from 'vue'
import type { VCol } from 'vuetify/components'

const props = defineProps({
  modelValue: Boolean,
  operationIds: Array as PropType<number[]>,
  standardizedOperationSheetId: Number,
  signedDate: {
    type: String as PropType<LocalDate | undefined>,
    default: undefined,
  },
})

const emit = defineEmits<{
  'update:model-value': [boolean]
  addInControlOrderBatch: [void]
}>()

watch(
  () => props.modelValue,
  (v) => {
    if (v) {
      updateFilter({
        withoutStep: true,
        standardizedOperationSheetId: props.standardizedOperationSheetId,
        signedDate: props.signedDate,
      })
    }
  }
)

const { data, pageable, updatePageable, updateFilter } = usePagination<ControlOrderBatchDto, ControlOrderBatchFilter>(
  (filter, pageable) => controlOrderBatchApi.findAll(filter, pageable),
  {},
  {},
  {
    lazyLoad: true,
  }
)

const headers: DataTableHeader[] = [
  {
    title: 'Numéro de lot',
    value: 'batchCode',
    formater: (item) => item.controlOrderBatch.batchCode,
  },
  {
    title: "Type d'OS",
    value: 'standardizedOperationSheets',
    sortable: false,
  },
  {
    title: 'Organisme de contrôle',
    value: 'controlOrganism',
    sortable: false,
  },
  {
    title: 'Volume classique',
    value: 'classicCumac',
    formater: (_, value) => formatNumber(value),
    cellClass: 'text-right',
  },
  {
    title: 'Volume précarité',
    value: 'precariousnessCumac',
    formater: (_, value) => formatNumber(value),
    cellClass: 'text-right',
  },
  {
    title: 'Volume total',
    value: 'totalCumac',
    formater: (item) => formatNumber(item.classicCumac + item.precariousnessCumac),
    cellClass: 'text-right',
    sortable: false,
  },
  {
    title: 'Taux de satisfaction',
    value: 'complianceRate',
    formater: (item) =>
      item.controlOrderBatch.complianceRate ? formatNumber(item.controlOrderBatch.complianceRate) + ' %' : '',
    cellClass: 'text-right',
  },
]

const selection = ref<ControlOrderBatchDto[]>([])

const addOperationInControlOrderBatch = async (controlOrderBatchId: number, operationIds: number[]) => {
  Promise.all(operationIds.map((operationId) => controlOrderBatchApi.addOperation(controlOrderBatchId, operationId)))
    .then(() => {
      emit('addInControlOrderBatch')
      emit('update:model-value', false)
      snackbarStore.setSuccess(
        operationIds.length == 1
          ? "L'opération a bien été ajouté au lot de contrôle"
          : 'Les opérations ont bien été ajouté au lot de contrôle'
      )
    })
    .catch(async (e) => {
      snackbarStore.setError(
        (await handleAxiosException(e)) ?? "Certaines opérations n'ont pas pu être ajouté au lot de contrôle"
      )
    })
}

const snackbarStore = useSnackbarStore()
</script>
