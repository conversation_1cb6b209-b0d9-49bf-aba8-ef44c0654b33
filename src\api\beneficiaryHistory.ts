import type { AxiosInstance, AxiosPromise } from 'axios'
import axiosInstance from '.'
import type { Page, Pageable } from '@/types/pagination'
import type { BeneficiaryHistory } from '@/types/history'

const beneficiaryUrl = '/beneficiary_histories'

export interface BeneficiaryHistoryFilter {
  beneficiaryId?: number
}

class BeneficiaryHistoryApi {
  public constructor(private axios: AxiosInstance) {}

  public findAll(pageable: Pageable, filter: BeneficiaryHistoryFilter): AxiosPromise<Page<BeneficiaryHistory>> {
    return this.axios.get(beneficiaryUrl, {
      params: { ...filter, ...pageable },
    })
  }
}

export const beneficiaryHistoryApi = new BeneficiaryHistoryApi(axiosInstance)
