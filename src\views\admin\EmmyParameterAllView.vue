<template>
  <NjPage title="Gestion des paramètres EMMY" :error-message="data.error" :loading="data.loading" expend-body>
    <template #header-actions>
      <NjBtn @click="addParameter = true">Nouveau paramètre EMMY</NjBtn>
      <VDialog v-model="addParameter" height="80%" width="30%">
        <VCard class="content-layout" :border="false">
          <EmmyParameterOneView :id="0" ref="parameterViewRef" />
          <VCardActions>
            <VSpacer />
            <NjBtn variant="outlined" @click="addParameter = false">Annuler</NjBtn>
            <NjBtn @click="save">Valider</NjBtn>
          </VCardActions>
        </VCard>
      </VDialog>
    </template>
    <template #sub-header>
      <VRow>
        <VCol cols="3">
          <SearchInput
            v-model:loading="data.loading"
            :model-value="pageFilter.label"
            @update:model-value="updateFilterByFieldname('label', $event)"
          />
        </VCol>
      </VRow>
    </template>
    <template #body>
      <VRow>
        <VCol>
          <NjDataTable
            v-model:selections="selection"
            :pageable="pageable"
            :page="data.value!"
            :headers="headers"
            fixed
            @update:pageable="updatePageable"
          >
            <template #[`item.staticValue`]="{ item }">
              {{ item.staticValue ? operationStaticValueLabel[item.staticValue as OperationStaticValue].label : '' }}
            </template>
          </NjDataTable>
        </VCol>
      </VRow>
      <VNavigationDrawer v-model="openDrawer" location="right" width="500" permanent>
        <EmmyParameterOneView :id="selection[0]?.id ?? 0" v-model="openDrawer" />
      </VNavigationDrawer>
    </template>
  </NjPage>
</template>
<script lang="ts" setup>
import { emmyParameterApi } from '@/api/emmyParameter'
import NjPage from '@/components/NjPage.vue'
import type { DataTableHeader } from '@/components/okta/NjDataTable.type'
import NjDataTable from '@/components/okta/NjDataTable.vue'
import { type EmmyParameter, operationStaticValueLabel, type OperationStaticValue } from '@/types/emmyParameter'
import { usePaginationInQuery } from '@/types/pagination'
import EmmyParameterOneView from './EmmyParameterOneView.vue'

const { data, pageable, pageFilter, updatePageable, updateFilterByFieldname, reload } = usePaginationInQuery(
  (filter, pageable) => emmyParameterApi.findAll(filter as any, pageable),
  {
    defaultPageFilter: {
      label: '',
    },
    saveFiltersName: 'EmmyParameterAllView',
  }
)

const addParameter = ref(false)
const openDrawer = ref(false)
const selection = ref<EmmyParameter[]>([])
const parameterViewRef = ref<typeof EmmyParameterOneView | null>(null)

const headers: DataTableHeader[] = [
  {
    title: 'Nom',
    value: 'label',
  },
  {
    title: 'Position',
    value: 'position',
  },
  {
    title: 'Référence',
    value: 'referenceText',
    sortable: false,
  },
  {
    title: 'Valeur statique',
    value: 'staticValue',
  },
  {
    title: 'Valeur par défaut',
    value: 'defaultValue',
  },
]

const save = async () => {
  await parameterViewRef.value?.save()
  addParameter.value = false
  reload()
}

watch(selection, (v) => {
  if (v) {
    openDrawer.value = v.length > 0
  }
})

watch(openDrawer, (v) => {
  if (!v) {
    selection.value = []
    reload()
  }
})
</script>
