<template>
  <div class="content-layout">
    <div class="content-layout__header px-4 pt-4">
      <h1>Modèle de document</h1>
      <VTabs>
        <VTab v-for="t in tabs" :key="t.routeName" :to="{ name: t.routeName }">{{ t.label }}</VTab>
      </VTabs>
      <VDivider />
    </div>
    <RouterView class="content-layout__main" />
  </div>
</template>

<script setup lang="ts">
const tabs: { routeName: string; label: string }[] = [
  { label: 'Convention', routeName: 'ConventionAllView' },
  { label: "Attestation sur l'honneur", routeName: 'SwornStatementAllView' },
  { label: 'Token', routeName: 'ConventionTokenAllView' },
]
</script>
