import type { EnhancedDocument, OperationsGroupDocumentRequest } from '@/types/document'
import type { ResponseHandler } from '@/types/responseHandler'
import type { Page, Pageable } from '@/types/pagination'
import type { AxiosInstance, AxiosPromise } from 'axios'
import axiosInstance from '.'

export interface OperationsGroupDocumentFilter extends Record<string, any> {
  operationsGroupId?: number
  notInOperation?: number
  active?: boolean
}

const operationsGroupDocumentsUri = '/operations_group_documents'
class OperationsGroupDocumentApi {
  public constructor(private axios: AxiosInstance) {}

  public findAll(filter: OperationsGroupDocumentFilter, pageable: Pageable): AxiosPromise<Page<EnhancedDocument>> {
    return this.axios.get(operationsGroupDocumentsUri, {
      params: { ...pageable, ...filter },
    })
  }

  public update(id: number, request: OperationsGroupDocumentRequest): AxiosPromise<EnhancedDocument> {
    return this.axios.put(`${operationsGroupDocumentsUri}/${id}`, request)
  }

  public delete(id: number): AxiosPromise {
    return this.axios.delete(`${operationsGroupDocumentsUri}/${id}`)
  }

  public download(id: number): AxiosPromise<File> {
    return this.axios.get(`${operationsGroupDocumentsUri}/${id}/file`, {
      responseType: 'blob',
    })
  }

  public downloadAll(id: number): AxiosPromise<ResponseHandler> {
    return this.axios.get(`/operations_groups/${id}/files`)
  }

  public async uploadFile(
    operationsGroupDocumentRequest: OperationsGroupDocumentRequest,
    file: File,
    documentToReplaceId?: number
  ): AxiosPromise<EnhancedDocument> {
    const formData = new FormData()
    formData.append('file', file, file.name)
    const hash = await hashFile(file)
    formData.append('hash', hash)

    const request = JSON.stringify(engieFormatRequestTransformKey(operationsGroupDocumentRequest))
    const blob = new Blob([request], {
      type: 'application/json',
    })
    formData.append('request', blob)

    return this.axios.post(operationsGroupDocumentsUri, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      params: {
        documentToReplaceId,
      },
    })
  }
}

export const operationsGroupDocumentApi = new OperationsGroupDocumentApi(axiosInstance)
