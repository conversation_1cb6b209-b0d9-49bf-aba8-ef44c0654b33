<template>
  <VTextField
    v-model="search"
    :prepend-inner-icon="noPrependIcon ? undefined : prependIcon"
    placeholder="Rechercher"
    :loading="loading || internalLoading"
    clearable
    class="bg-white"
    v-bind="$attrs"
    @update:model-value="updateSearch"
  >
    <template #loader="{ isActive }">
      <VProgressLinear :active="isActive" color="primary" absolute height="4" indeterminate></VProgressLinear>
      <div style="height: 4px"></div>
    </template>

    <template v-for="(_, slot) of $slots as Record<keyof VTextField['$slots'], Slot | undefined>" #[slot]="scope"
      ><slot :name="slot" v-bind="scope"
    /></template>
  </VTextField>
</template>

<script setup lang="ts">
import { debounce } from 'lodash'
import type { Slot } from 'vue'
import { VProgressLinear, VTextField } from 'vuetify/components'

const internalLoading = defineModel<boolean>('internal-loading')

const props = defineProps({
  modelValue: String,
  loading: Boolean,
  prependIcon: {
    type: String,
    required: false,
    default: 'mdi-magnify',
  },
  debounceTime: {
    type: Number,
    default: 300,
  },
  noPrependIcon: Boolean,
})

const emit = defineEmits<{
  'update:model-value': [any]
}>()

const search = ref<string>()

watch(
  () => props.modelValue,
  (v) => {
    if (v !== search.value) {
      search.value = v
    }
  },
  {
    immediate: true,
  }
)

const privateDebouncedUpdateSearch = debounce((v: string) => {
  emit('update:model-value', v)
  internalLoading.value = false
}, props.debounceTime)

const updateSearch = (v: string) => {
  internalLoading.value = true
  privateDebouncedUpdateSearch(v)
}
</script>
