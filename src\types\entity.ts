import type { GtfEntityDto } from '@/api/external/gtf/gtfOrgs'
import { makeEmptyCompany, type Company } from './company'
import type { LocalDateTime } from './date'
import type { User } from './user'
import type { Address } from './address'
import type { Territory } from './territory'
import type { ValuePromise } from './promisableValue'
import { useEntityStore } from '@/stores/entity'

export const entityLevels = [0, 1, 2, 3, 4] as const
export type EntityLevel = (typeof entityLevels)[number]

export const entityLevelLabels = ['Groupe', 'Société', 'Direction', 'Région', 'Agence']

export interface EntityDetails {
  id: string

  visible: boolean
  dafMail: string[]
  effectiveDafMail: string[]
  firstNameAgent?: string
  lastNameAgent?: string
  officeAgent?: string
  civilityAgent?: string
  possessivePronounAgent?: string
  instructor?: User
  effectiveInstructor?: User
  territoryReferent?: User
  fee?: number
  effectiveFee?: number
  canSellCumacs: boolean

  regionalDirector?: User
  financialManager?: User
  directorOfOperationalActivities?: User
  industryOperationsAndPerformanceManager?: User

  chiefExecutive?: User
  chairman?: User

  updateDateTime: LocalDateTime
}
export interface Entity {
  id: string
  name: string
  navFullId: string
  parentId?: string
  level: EntityLevel
  enabled: boolean
  siret?: string
  address: Address
  phoneNumber?: string
  natureCode?: string
  natureDescription?: string

  company: Company
  territory?: Territory

  effectiveTerritoryReferent?: User

  entityDetails: EntityDetails
}

export type EntityRequest = Omit<
  Entity,
  'company' | 'instructor' | 'territoryReferent' | 'canSellCumacs' | 'address' | 'territory' | 'entityDetails'
> & {
  companyId: number
  address?: Address
  territoryId?: number
  territoryDescription?: string
}

export type UpdateEntityRequest = Omit<Entity, 'id' | 'entityDetails'>
export type UpdateEntityDetailsRequest = Omit<
  EntityDetails,
  | 'id'
  | 'regionalDirector'
  | 'financialManager'
  | 'directorOfOperationalActivities'
  | 'industryOperationsAndPerformanceManager'
  | 'chiefExecutive'
  | 'chairman'
  | 'effectiveDafMail'
> & {
  instructorUserId?: number
  territoryReferentUserId?: number
  address?: Address
  territoryId?: number

  regionalDirectorUserId?: number
  financialManagerUserId?: number
  directorOfOperationalActivitiesUserId?: number
  industryOperationsAndPerformanceManagerUserId?: number

  chiefExecutiveUserId?: number
  chairmanUserId?: number
}

export const makeEmptyEntity = (): Entity => ({
  id: '',
  company: makeEmptyCompany(),
  enabled: false,
  navFullId: '',
  level: 0,
  name: '',
  address: makeEmptyAddress(),
  entityDetails: makeEmptyEntityDetails(),
})

export const makeEmptyEntityDetails = (): EntityDetails => ({
  id: '',
  visible: false,
  canSellCumacs: false,
  effectiveDafMail: [],
  dafMail: [],
  updateDateTime: '',
})

export const makeUpdateEntityDetailsRequest = (entityDetails: EntityDetails): UpdateEntityDetailsRequest => ({
  // id: entityDetails.id,
  visible: entityDetails.visible,
  dafMail: entityDetails.dafMail,
  firstNameAgent: entityDetails.firstNameAgent,
  lastNameAgent: entityDetails.lastNameAgent,
  officeAgent: entityDetails.officeAgent,
  civilityAgent: entityDetails.civilityAgent,
  possessivePronounAgent: entityDetails.possessivePronounAgent,
  instructorUserId: entityDetails.instructor?.id,
  territoryReferentUserId: entityDetails.territoryReferent?.id,
  fee: entityDetails.fee,
  canSellCumacs: entityDetails.canSellCumacs,
  updateDateTime: entityDetails.updateDateTime,

  directorOfOperationalActivitiesUserId: entityDetails.directorOfOperationalActivities?.id,
  financialManagerUserId: entityDetails.financialManager?.id,
  industryOperationsAndPerformanceManagerUserId: entityDetails.industryOperationsAndPerformanceManager?.id,
  regionalDirectorUserId: entityDetails.regionalDirector?.id,

  chiefExecutiveUserId: entityDetails.chiefExecutive?.id,
  chairmanUserId: entityDetails.chairman?.id,
})

export const displayEntity = (entity: Entity | undefined | null) => {
  return entity ? `(${entity.id}) ${entity.name}` : ''
}

export const mapGtfEntityDtoToEntity = (gtfEntity: GtfEntityDto, company: Company): Entity => ({
  id: gtfEntity.id,
  company: company,
  enabled: gtfEntity.enabled === 1,
  navFullId: gtfEntity.nav_full_id,
  level: parseInt(gtfEntity.nav_level) as 0 | 1 | 2 | 3 | 4,
  name: gtfEntity.name,
  // visible: true,
  // canSellCumacs: true,
  address: makeEmptyAddress(),
  phoneNumber: gtfEntity.phone_number,
  // updateDateTime: '',
  natureCode: gtfEntity.nature_code,
  natureDescription: gtfEntity.nature_description,
  entityDetails: makeEmptyEntityDetails(),
})

export const isSubsidiary = (entity: Entity) => {
  return !entity.siret?.startsWith('*********')
}

const entityStore = useEntityStore()

export const resolveEntityChargePoleCEE = (org: Entity | null, fee: number): ValuePromise<Entity> => {
  if (!org) {
    return Promise.resolve(emptyValue())
  }
  if (org.entityDetails.fee == org.entityDetails.effectiveFee) {
    return Promise.resolve(succeedValue(org))
  }
  return entityStore
    .getOne(org.parentId)
    .then((el) => resolveEntityChargePoleCEE(el, fee))
    .catch(() => errorValue("Erreur lors de la récupération de l'organisation"))
}
