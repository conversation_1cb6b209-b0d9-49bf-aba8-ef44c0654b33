import type { Page, Pageable } from '@/types/pagination'
import type { AxiosInstance, AxiosPromise } from 'axios'
import axiosInstance from '.'
import type { RemainingMail } from '@/types/remainingMail'
import type { LocalDateTime } from '@/types/date'

export type RemainingMailFilter = Partial<{
  creationDateTimeLower: LocalDateTime
}>

class RemainingMailApi {
  public constructor(private axios: AxiosInstance) {}

  public getAll(pageable: Pageable, filter: RemainingMailFilter): AxiosPromise<Page<RemainingMail>> {
    return this.axios.get('remaining_mails', {
      params: { ...pageable, ...filter },
    })
  }
}

export const remainingMailApi = new RemainingMailApi(axiosInstance)
