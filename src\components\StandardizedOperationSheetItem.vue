<template>
  <VListItem v-bind="$attrs" :subtitle="duration">
    <template #title>
      {{ standardizedOperationSheet.operationCode }}
      <VIcon v-if="!standardizedOperationSheet.visible && !hideEyeOff" icon="mdi-eye-off" class="ms-2" size="small" />
    </template>
  </VListItem>
</template>

<script setup lang="ts">
import type { StandardizedOperationSheet } from '@/types/calcul/standardizedOperationSheet'
import type { PropType } from 'vue'

const props = defineProps({
  standardizedOperationSheet: {
    type: Object as PropType<StandardizedOperationSheet>,
    required: true,
  },
  hideEyeOff: Boolean,
})

const duration = computed(() => {
  if (!props.standardizedOperationSheet.startDate && !props.standardizedOperationSheet.expirationDate) {
    return ''
  }
  return (
    (props.standardizedOperationSheet.startDate
      ? formatHumanReadableLocalDate(props.standardizedOperationSheet.startDate)
      : 'N/A') +
    ' ➡ ' +
    (props.standardizedOperationSheet.expirationDate
      ? formatHumanReadableLocalDate(props.standardizedOperationSheet.expirationDate)
      : 'N/A')
  )
})
</script>
