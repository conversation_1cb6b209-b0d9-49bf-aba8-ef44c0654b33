<template>
  <VCol>
    <VIcon>mdi-circle-small</VIcon> {{ missingDocumentTypes.name }}
    <i>{{ displayExtensionList(getExtensionsInList(missingDocumentTypes)) }}</i>
    <span v-if="operationsGroupId && missingDocumentTypes?.requiredInOperationsGroupForOperationInOperationsGroup">
      (Doit être ajouté à partir du
      <VLink
        :to="{
          name: 'OperationsGroupOneView',
          params: { id: operationsGroupId },
        }"
      >
        regroupement </VLink
      >)</span
    >
    <VExpandTransition>
      <div v-if="instructionsDisplay[missingDocumentTypes.name]" class="ps-7">
        <RichTextDisplayValue :html="missingDocumentTypes.instructions" />
      </div>
    </VExpandTransition>
    <VDivider v-if="instructionsDisplay[missingDocumentTypes.name]" />
  </VCol>
  <VCol v-if="missingDocumentTypes.instructions" class="flex-grow-0">
    <VIcon
      :icon="instructionsDisplay[missingDocumentTypes.name] ? 'mdi-information' : 'mdi-information-outline'"
      size="small"
      color="primary"
      @click="instructionsDisplay[missingDocumentTypes.name] = !instructionsDisplay[missingDocumentTypes.name]"
    />
  </VCol>
</template>
<script setup lang="ts">
import type { DocumentType } from '@/types/documentType'
import type { PropType } from 'vue'
import { getExtensionsInList, displayExtensionList } from '@/types/documentType'

const props = defineProps({
  operationsGroupId: Number,
  missingDocumentTypes: {
    type: Object as PropType<DocumentType>,
    required: true,
  },
})

const instructionsDisplay = ref<Record<string, boolean>>({})
watch(
  () => props.missingDocumentTypes,
  () => {
    instructionsDisplay.value = {}
  }
)
</script>
