<template>
  <CardDialog v-model="activeDialog" width="70%" title="Sélectionner une ou plusieurs FOS">
    <VRow class="flex-column">
      <VCol cols="4">
        <VTextField v-model="search" label="Recherche" prepend-inner-icon="mdi-magnify" />
      </VCol>
      <VCol>
        <StandardizedOperationSheetDataTable
          v-model:selections="localSelected"
          :page="data.value"
          :pageable="pageable"
          :headers="headers"
          checkboxes
          multi-selection
          @update:pageable="updatePageable"
        />
      </VCol>
    </VRow>
    <template #actions>
      <NjBtn variant="outlined" @click="activeDialog = false"> Annuler </NjBtn>
      <NjBtn @click="updateFilter"> Valider </NjBtn>
    </template>
  </CardDialog>
</template>
<script lang="ts" setup>
import type {
  StandardizedOperationSheet,
  StandardizedOperationSheetFilter,
} from '@/types/calcul/standardizedOperationSheet'
import NjBtn from '@/components/NjBtn.vue'
import type { DataTableHeader } from '@/components/okta/NjDataTable.type'
import { cloneDeep, debounce } from 'lodash'
import type { PropType } from 'vue'
import CardDialog from '@/components/CardDialog.vue'
import type { LocalDate } from '@/types/date'
import StandardizedOperationSheetDataTable from '@/views/StandardizedOperationSheetDataTable.vue'

const props = defineProps({
  modelValue: Boolean,
  selected: {
    type: Array as PropType<StandardizedOperationSheet[]>,
    default: () => [],
  },
  filter: {
    type: Object as PropType<StandardizedOperationSheetFilter>,
    default: () => {},
  },
})
const emit = defineEmits(['update:model-value', 'update:selected'])
const activeDialog = computed<boolean>({
  get() {
    return props.modelValue
  },
  set(v) {
    emit('update:model-value', v)
  },
})

const localSelected = ref<StandardizedOperationSheet[]>([])

watch(
  () => props.selected,
  (v) => {
    localSelected.value = v
  },
  {
    immediate: true,
  }
)

watch(activeDialog, (v) => {
  if (v) {
    reload()
  }
})

const search = ref('')
const { data, pageable, pageFilter, updatePageable, reload } = usePagination<
  StandardizedOperationSheet,
  StandardizedOperationSheetFilter
>(
  (filter, pageable) => standardizedOperationSheetApi.findAll(pageable, filter),
  {
    search: search.value,
    ...props.filter,
  },
  {
    page: 0,
    size: 10,
    sort: ['operationCode'],
  },
  {
    lazyLoad: true,
  }
)

const headers: DataTableHeader[] = [
  {
    title: 'Code opération',
    value: 'operationCode',
  },
  {
    title: 'Arrêté contrôle',
    value: 'controlOrderStartDate',
    formater: (_: any, value: LocalDate) => (value ? formatHumanReadableLocalDate(value) : ''),
  },
  {
    title: 'RGE',
    value: 'rgeMandatory',
  },
  {
    title: 'Fonds chaleur',
    value: 'heatFund',
  },
  {
    title: 'Description',
    value: 'description',
  },
  {
    title: 'Début de validité',
    value: 'startDate',
    formater: (_: any, value: LocalDate) => (value ? formatHumanReadableLocalDate(value) : ''),
  },
  {
    title: 'Fin de validité',
    value: 'expirationDate',
    formater: (_: any, value: LocalDate) => (value ? formatHumanReadableLocalDate(value) : ''),
  },
]

const updateFilter = () => {
  emit('update:model-value', false)
  emit('update:selected', cloneDeep(localSelected.value))
}

const debounceSearch = debounce((v: string | undefined) => {
  pageFilter.value.search = v!
}, 300)

watch(
  () => search.value,
  (v) => {
    if (v || v === '') {
      data.value.loading = true
      debounceSearch(v)
    }
  },
  {
    immediate: true,
  }
)
</script>
