import { cloneDeep } from 'lodash'

import { useDialogStore, type DialogStoreRequest } from '@/stores/dialog'

export const useConfirmAlertDialog = (options?: { dialogStoreRequest?: DialogStoreRequest }) => {
  let resolve: (v: boolean) => void = () => {}
  const props = reactive({
    'onClick:positive': async () => {
      if (options?.dialogStoreRequest) {
        resolve(await useDialogStore().addAlert(options?.dialogStoreRequest))
        props.modelValue = false
      } else {
        props.modelValue = false
        resolve(true)
      }
    },
    'onClick:negative': () => {
      props.modelValue = false
      resolve(false)
    },
    modelValue: false,
  })

  const confirm = () => {
    props.modelValue = true
    return new Promise<boolean>((r) => {
      resolve = r
    })
  }

  const onUpdateModelValue = (v: boolean) => {
    props.modelValue = v
  }

  return { props, confirm, 'onUpdate:model-value': onUpdateModelValue }
}

export const useEditDialog = <T>(
  initialValue: Ref<T>,
  options?: {
    dialogStoreRequest?: DialogStoreRequest
  }
) => {
  const value = ref<T>()

  const confirmAlertDialog = useConfirmAlertDialog(options)

  watch(
    () => confirmAlertDialog.props.modelValue,
    (v) => {
      if (v) {
        value.value = cloneDeep(initialValue.value)
      }
    }
  )

  return { ...confirmAlertDialog, value }
}
