<template>
  <VRow dense>
    <VCol cols="5">
      <span> {{ modelValue?.name }} : </span>
      <template v-if="!modelValue?.document"><i>Pas de document</i></template>
      <VLink v-else @click="download">{{ modelValue?.document.originalFilename }}</VLink>
      <NjFileInput :multiple="false" @new-files="handleNewFile" />
      {{ file?.name }}
    </VCol>
    <VCol cols="2" align-self="end">
      <NjBtn :disabled="!file" color="primary" @click="updateConfig">Enregistrer</NjBtn>
    </VCol>
  </VRow>
</template>
<script setup lang="ts">
import { type PropType, ref } from 'vue'
import type { AdminConfiguration } from '@/types/adminConfiguration'
import NjFileInput from '@/components/NjFileInput.vue'
import { VCol, VRow } from 'vuetify/components'
import { adminconfigurationApi } from '@/api/adminConfiguration'
import { useSnackbarStore } from '@/stores/snackbar'
import { downloadFile } from '@/types/file'

const props = defineProps({ modelValue: Object as PropType<AdminConfiguration> })

const emits = defineEmits<{
  save: [void]
}>()

const snackbarStore = useSnackbarStore()

const handleNewFile = (arg: FileList) => {
  file.value = arg[0]
}

const file = ref<File>()
const updateConfig = () => {
  adminconfigurationApi
    .save(props.modelValue!, file.value!)
    .then(() => {
      snackbarStore.setSuccess('Le document à bien été mis à jour')
      emits('save')
      file.value = undefined
    })
    .catch(async (err) => {
      snackbarStore.setError(await handleAxiosException(err))
    })
}

const download = () => {
  adminconfigurationApi
    .download(props.modelValue?.name!)
    .then((v) => downloadFile(props.modelValue!.document.originalFilename, v.data))
    .catch(async (err) => snackbarStore.setError(await handleAxiosException(err)))
}
</script>
