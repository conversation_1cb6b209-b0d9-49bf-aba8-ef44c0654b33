import type { AxiosInstance, AxiosPromise } from 'axios'
import axios from 'axios'
import QueryString from 'qs'

export interface Feature {
  type: 'Feature'
  geometry: {
    type: 'Point'
    coordinates: [number, number]
  }
  properties: {
    label: string
    score: number
    housenumber: string
    id: string
    name: string
    postcode: string
    citycode: string
    x: number
    y: number
    city: string
    district?: string
    context: string
    type: unknown
    importance: number
    street: string
  }
}

export interface FeatureCollection {
  type: 'FeatureCollection'
  version: unknown
  query: string
  limit: number
  features: Feature[]
}

class FrAdresseGouvApi {
  public constructor(private axios: AxiosInstance) {}

  public search(search: string, limit: number, postalCode?: string): AxiosPromise<FeatureCollection> {
    return this.axios.get('/search', {
      params: {
        q: search,
        limit,
        postcode: postalCode,
        autocomplete: 0,
      },
    })
  }
}

export const frAdresseGouvApi = new FrAdresseGouvApi(
  axios.create({
    baseURL: 'https://api-adresse.data.gouv.fr',
    headers: {
      'Content-Type': 'application/json',
    },
    paramsSerializer: {
      serialize: (params) => QueryString.stringify(params, { arrayFormat: 'repeat' }),
    },
  })
)
