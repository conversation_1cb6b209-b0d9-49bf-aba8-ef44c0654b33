import type { Page, Pageable } from '@/types/pagination'
import type { AxiosInstance, AxiosPromise } from 'axios'
import axiosInstance from '.'
import type {
  ControlOrderBatch,
  ControlOrderBatchDto,
  ControlOrderBatchRequest,
  ControlOrderBatchSummaryBeforeCreation,
} from '@/types/controlOrder'
import type { OperationFilter } from './operation'
import type { Operation } from '@/types/operation'
import type { LocalDate } from '@/types/date'

export type ControlOrderBatchFilter = Partial<{
  withoutStep: boolean
  standardizedOperationSheetId: number
  signedDate: LocalDate
}>

const controlOrderBatchUri = 'control_order_batches'
class ControlOrderBatchApi {
  public constructor(private axios: AxiosInstance) {}

  public create(request: ControlOrderBatchRequest): AxiosPromise<ControlOrderBatch> {
    return this.axios.post(controlOrderBatchUri, request)
  }

  public update(id: number, request: ControlOrderBatchRequest): AxiosPromise<ControlOrderBatch> {
    return this.axios.put(controlOrderBatchUri + '/' + id, request)
  }

  public delete(id: number): AxiosPromise<ControlOrderBatch> {
    return this.axios.delete(controlOrderBatchUri + '/' + id)
  }

  public findAll(filter: ControlOrderBatchFilter, pageable: Pageable): AxiosPromise<Page<ControlOrderBatchDto>> {
    return this.axios.get(controlOrderBatchUri, {
      params: { ...filter, ...pageable },
    })
  }

  public findOne(id: number): AxiosPromise<ControlOrderBatch> {
    return this.axios.get(controlOrderBatchUri + '/' + id)
  }

  public getSummaryBeforeCreation(
    operationFilter: OperationFilter
  ): AxiosPromise<ControlOrderBatchSummaryBeforeCreation> {
    return this.axios.get(controlOrderBatchUri + '/summary', { params: { ...operationFilter } })
  }

  public exportExcelFile(id: number): AxiosPromise<Blob> {
    return this.axios.get(controlOrderBatchUri + '/' + id + '/export_to_excel', {
      responseType: 'blob',
    })
  }

  public exportCsvFile(id: number): AxiosPromise<Blob> {
    return this.axios.get(controlOrderBatchUri + '/' + id + '/export_to_csv', {
      responseType: 'blob',
    })
  }

  public addOperation(controlOrderBatchId: number, operationId: number): AxiosPromise<Operation> {
    return this.axios.post(controlOrderBatchUri + '/' + controlOrderBatchId + '/operations/' + operationId)
  }

  public removeOperation(controlOrderBatchId: number, operationId: number): AxiosPromise<Operation> {
    return this.axios.delete(controlOrderBatchUri + '/' + controlOrderBatchId + '/operations/' + operationId)
  }
}

export const controlOrderBatchApi = new ControlOrderBatchApi(axiosInstance)
