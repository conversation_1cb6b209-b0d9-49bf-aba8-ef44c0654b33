# Module Capte-SPA

## Recommended IDE Setup

[VSCode](https://code.visualstudio.com/) + [Volar](https://marketplace.visualstudio.com/items?itemName=Vue.volar) (et désactiver bien Vetur).
Vous pouvez également installer les autres plugins proposer par le Projet.

## Commandes essentiels pour le développeur

### Project Setup

```sh
pnpm install
```

### Compile and Hot-Reload for Development

```sh
pnpm dev
```

### Lint check with [ESLint](https://eslint.org/) + Type check with TS

```sh
pnpm verify
```

### Lint check and fix with [ESLint](https://eslint.org/) + Type check with TS

```sh
pnpm verifyfix
```

## Déploiement

Pour déployer dans un environnement, vous utiliserez la pipeline de gitlab.
Vous lancerez les tâches `build_rec`, `build_ppr`, et `build_prd`, disponible sur les branches `dev` et `hotfix`.
Chacune des tâches lancera sa tâche `deploy_<nom de l'env>` équivalent

Sur la branch `dev`, vous devez déployer dans l'ordre suivant : `rec`, `ppr` et `prd`.
Déployer en `prd` via le CI/CD de Gitlab mettra à jour la branche `master` et `hotfix`.

Sur la branch `hotfix`, vous devez déployer dans l'ordre suivant : `ppr` et `prd`.
Déployer en `prd` via le CI/CD de Gitlab mettra à jour la branche `master`.

En cas de besoin de déploiement manuelle exceptionnel, vous pouvez pusher sur une branche du type `deploy/<nom de l'environnement>` (non recommandé).

Après chaque déploiement, cela mettra à jour les branches `state/<nom de l'environnement>`, pour connaitre rapidement depuis Git dans quel état est chaque environnement.



## Recommended Maintenance operation
After '2028-01-01', maintainer have to update bankHolliday in src\types\bankHolidaysData.ts with GET "https://apis.svc.engie-solutions.net/gouv_calendrier/api/v1/jours-feries/metropole.json" response body
