<template>
  <VSwitch
    v-bind="$attrs"
    :label="label"
    :inline="inline"
    :style="inline ? 'width: fit-content' : ''"
    :model-value="localModelValue"
    :readonly="false"
    @update:model-value="updateModelValue"
  >
    <template v-if="inline" #label>
      <div class="pe-3">{{ props.label }}</div>
    </template>

    <!-- eslint-disable-next-line prettier/prettier -->
    <template v-for="(_, slot) of $slots as Record<keyof VSwitch['$slots'], Slot | undefined>" #[slot]="scope">
      <slot :name="slot" v-bind="scope" />
    </template>
  </VSwitch>
</template>
<script setup lang="ts">
import type { Slot } from 'vue'
import type { VSwitch } from 'vuetify/components'

const props = withDefaults(
  defineProps<{
    inline?: boolean
    label?: string
    readonly?: boolean
    modelValue: any
  }>(),
  {
    inline: false,
    readonly: false,
  }
)
const emit = defineEmits<{
  'update:model-value': [any]
}>()
const localModelValue = ref(props.modelValue)
watch(
  () => props.modelValue,
  (v) => {
    localModelValue.value = v
  }
)

const updateModelValue = (v: any) => {
  if (props.readonly) {
    localModelValue.value = v
    localModelValue.value = props.modelValue
  } else {
    emit('update:model-value', v)
  }
}
</script>
