import type { SideBarElement } from '@/components/SideBar.type'
import { useSidebarStore } from '@/stores/sidebar'
import { useUserStore } from '@/stores/user'

export default function useSimulationSideBar() {
  const userStore = useUserStore()
  const sidebarStore = useSidebarStore()
  watch(
    () => userStore.currentUser.roles,
    () => {
      const items: SideBarElement[] = [
        {
          icon: 'mdi-format-list-bulleted',
          title: 'Liste des simulations CEE',
          routeName: 'SimulationAllView',
          includedExactRoutes: ['SimulationOneView'],
        },
        {
          icon: 'mdi-briefcase-outline',
          title: 'Business Plans',
          routeName: 'BusinessPlanAllView',
        },
      ]

      if (userCanManageSimulation(userStore.currentUser)) {
        items.splice(1, 0, {
          icon: 'mdi-pencil',
          title: 'Initier une simulation CEE',
          routeName: 'CreateSimulationView',
        })
      }

      sidebarStore.setItems(items)
    },
    {
      immediate: true,
    }
  )
}
