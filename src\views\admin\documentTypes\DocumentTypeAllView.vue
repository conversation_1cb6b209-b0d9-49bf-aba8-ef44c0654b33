<template>
  <NjPage title expend-body>
    <template #subtitle>
      <VRow>
        <VCol cols="4">
          <SearchInput @update:model-value="updateSearch" />
        </VCol>
      </VRow>
    </template>
    <template #header-actions>
      <NjBtn @click="dialog = true">Nouveau document</NjBtn>
    </template>
    <template #body>
      <NjDataTable
        :headers="headers"
        :pageable="pageable"
        :page="data.value"
        :on-click-row="onClickRow"
        fixed
        class="w-100"
        @update:pageable="updatePageable"
      >
        <template #[`item.shareable`]="{ item }">
          <NjBooleanIcon :condition="item.shareable" />
        </template>
        <template #[`item.inFinalVersion`]="{ item }">
          <NjBooleanIcon :condition="item.inFinalVersion" />
        </template>
        <template #[`item.inInitialVersion`]="{ item }">
          <NjBooleanIcon :condition="item.inInitialVersion" />
        </template>
        <template #[`item.inOtherVersion`]="{ item }">
          <NjBooleanIcon :condition="item.inOtherVersion" />
        </template>
        <template #[`item.requiredInOperationsGroupForOperationInOperationsGroup`]="{ item }">
          <NjBooleanIcon :condition="item.requiredInOperationsGroupForOperationInOperationsGroup" />
        </template>
        <template #[`item.uniqueDoc`]="{ item }">
          <NjBooleanIcon :condition="item.uniqueDoc" />
        </template>
        <template #[`item.requiredForSubsidiary`]="{ item }">
          <NjBooleanIcon :condition="item.requiredForSubsidiary" />
        </template>
        <template #[`item.requiredForSubcontracting`]="{ item }">
          <NjBooleanIcon :condition="item.requiredForSubcontracting" />
        </template>
        <template #[`item.ignoreForSelfWorks`]="{ item }">
          <NjBooleanIcon :condition="item.ignoreForSelfWorks" />
        </template>
      </NjDataTable>
      <VDialog v-model="dialog" max-width="700px" height="100%">
        <DocumentTypeCard
          @cancel="dialog = false"
          @saved="
            () => {
              dialog = false
              reload()
            }
          "
        />
      </VDialog>
      <VNavigationDrawer :model-value="drawer.active" width="700" location="end" disable-resize-watcher>
        <DocumentTypeCard :id="drawer.id" ref="editDocumentTypeRef" @cancel="drawer.active = false" @saved="reload" />
      </VNavigationDrawer>
    </template>
  </NjPage>
</template>
<script lang="ts" setup>
import { documentTypeApi } from '@/api/documentType'
import NjPage from '@/components/NjPage.vue'
import type { DataTableHeader } from '@/components/okta/NjDataTable.type'
import NjDataTable from '@/components/okta/NjDataTable.vue'
import type { DocumentType } from '@/types/documentType'
import { usePagination } from '@/types/pagination'
import { VNavigationDrawer, VRow } from 'vuetify/components'
import DocumentTypeCard from './DocumentTypeCard.vue'

const { data, pageable, updatePageable, pageFilter, updateFilter, reload } = usePagination<DocumentType>(
  (filter, pageable) => documentTypeApi.getAll(pageable, filter),
  {},
  {
    sort: ['name'],
  }
)

const dialog = ref(false)
const drawer = ref({
  active: false,
  id: 0,
})
const editDocumentTypeRef = ref<typeof DocumentTypeCard | null>(null)

const onClickRow = (documentType: DocumentType) => {
  editDocumentTypeRef.value!.checkUnsaved(() => {
    if (!drawer.value.active || drawer.value.id !== documentType.id) {
      drawer.value = {
        active: true,
        id: documentType.id,
      }
    } else {
      drawer.value.active = false
    }
  })
}

const headers: DataTableHeader[] = [
  {
    title: 'Nom',
    value: 'name',
  },
  {
    title: 'Partageable',
    value: 'shareable',
    sortable: false,
  },
  {
    title: 'Ordre de fusion',
    value: 'fusionOrder',
  },
  {
    title: 'Éligible pour version finale',
    value: 'inFinalVersion',
  },
  {
    title: 'Éligible pour version initiale',
    value: 'inInitialVersion',
  },
  {
    title: 'Éligible pour autre version',
    value: 'inOtherVersion',
  },
  {
    title: "Autorisé dans une opération d'un regroupement",
    value: 'requiredInOperationsGroupForOperationInOperationsGroup',
  },
  {
    title: 'Unique',
    value: 'uniqueDoc',
  },
  {
    title: 'Obligatoire pour les filiales',
    value: 'requiredForSubsidiary',
  },
  {
    title: 'Obligatoire pour de la sous-traitance',
    value: 'requiredForSubcontracting',
  },
  {
    title: 'Ignoré si travaux en propre',
    value: 'ignoreForSelfWorks',
  },
]

const updateSearch = (value: string) => {
  const filter = {
    ...unref(pageFilter),
    search: value,
  }
  updateFilter(filter)
}
</script>
