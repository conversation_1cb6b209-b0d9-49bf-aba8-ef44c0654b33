import type { Page } from '@/types/pagination'
import type { PrecariousnessBonusSheet } from '@/types/precariousnessBonus'
import type { PromisableValue } from '@/types/promisableValue'
import type { Operation } from '@/types/operation'
import type { Ref } from 'vue'

const DEFAULT_DATE_MAX_BONIFICATION = '2050-01-01'

export const useFindCorrectPrecariousnessBonus = (simulation: Ref<Operation | undefined>) => {
  const precariousnessBonusSheet = computed((): PromisableValue<PrecariousnessBonusSheet> => {
    const commitmentDate = simulation.value?.signedDate || simulation.value?.estimatedCommitmentDate
    const endOperation = simulation.value?.actualEndWorksDate || simulation.value?.estimatedEndOperationDate
    if (precariousnessBonusSheets.value.loading) {
      return loadingValue()
    } else if (precariousnessBonusSheets.value.error) {
      return errorValue(precariousnessBonusSheets.value.error)
    } else if (!simulation.value) {
      return loadingValue()
    } else if (!simulation.value.standardizedOperationSheet.precariousnessBonusEnabled) {
      return errorValue('Indispo. pour cette fiche')
    } else if (!commitmentDate) {
      return errorValue('Date eng. prév. ou sign. requise')
    } else if (!endOperation) {
      return errorValue('Date fin travaux prév. ou réel. requise')
    } else if (!precariousnessBonusSheets.value.value?.totalElements) {
      return errorValue('Aucune bonification précarité pour ces dates')
    } else {
      const v = precariousnessBonusSheets.value.value.content
        .sort((op1, op2) =>
          (op1.commitmentDate ?? DEFAULT_DATE_MAX_BONIFICATION).localeCompare(
            op2.commitmentDate ?? DEFAULT_DATE_MAX_BONIFICATION
          )
        )
        .filter(
          (it) =>
            (commitmentDate ?? DEFAULT_DATE_MAX_BONIFICATION) <= (it.commitmentDate ?? DEFAULT_DATE_MAX_BONIFICATION) &&
            (endOperation ?? DEFAULT_DATE_MAX_BONIFICATION) <= (it.endOperationDate ?? DEFAULT_DATE_MAX_BONIFICATION)
        )
      if (v.length === 0) {
        return errorValue('Aucune bonification précarité pour ces dates')
      } else if (v.length > 1) {
        return errorValue("Erreur 1 imprévue survenue. Contactez l'administrateur")
      } else {
        return succeedValue(v[0])
      }
    }
  })
  const precariousnessBonusSheets = ref(loadingValue<Page<PrecariousnessBonusSheet>>())
  const loadingTimeoutId = ref<number>()
  onUnmounted(() => clearTimeout(loadingTimeoutId.value))
  const load = () => {
    return handleAxiosPromise(
      precariousnessBonusSheets,
      precariousnessBonusSheetApi.findAll({ page: 0, size: 20 }, { certified: true })
    ).catch(() => {
      loadingTimeoutId.value = setTimeout(() => load(), 5000)
    })
  }
  onMounted(() => {
    load()
  })

  return { precariousnessBonusSheet: precariousnessBonusSheet, precariousnessBonusSheets: precariousnessBonusSheets }
}
