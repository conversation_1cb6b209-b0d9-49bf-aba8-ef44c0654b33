import type { OperationStaticValue } from './emmyParameter'

export interface ControlOrderExportTemplate {
  id: number
  name: string
  fullfillByApplicantMergedColumnLabel: string
  fullfillByApplicantColumnValues: ColumnValue[]
  fullfillByControlOrganismMergedColumnLabel: string
  fullfillByControlOrganismColumnValues: ColumnValue[]
  fullfillByContactControlOrganismMergedColumnLabel: string
  fullfillByContactControlOrganismColumnValues: ColumnValue[]
  completedByApplicantMergedColumnLabel: string
  completedByApplicantColumnValues: ColumnValue[]
}

export interface ControlOrderExportTemplateRequest extends Omit<ControlOrderExportTemplate, 'id'> {}

export const mapToControlOrderExportTemplateRequest = (
  value: ControlOrderExportTemplate
): ControlOrderExportTemplateRequest => ({
  name: value.name,
  fullfillByApplicantMergedColumnLabel: value.fullfillByApplicantMergedColumnLabel,
  fullfillByApplicantColumnValues: value.fullfillByApplicantColumnValues,
  fullfillByControlOrganismMergedColumnLabel: value.fullfillByControlOrganismMergedColumnLabel,
  fullfillByControlOrganismColumnValues: value.fullfillByControlOrganismColumnValues,
  fullfillByContactControlOrganismMergedColumnLabel: value.fullfillByContactControlOrganismMergedColumnLabel,
  fullfillByContactControlOrganismColumnValues: value.fullfillByContactControlOrganismColumnValues,
  completedByApplicantMergedColumnLabel: value.completedByApplicantMergedColumnLabel,
  completedByApplicantColumnValues: value.completedByApplicantColumnValues,
})

export interface ColumnValue {
  label: string
  value: OperationStaticValue | null
  defaultValue: string
}

export const makeEmptyControlOrderExportTemplate = (): ControlOrderExportTemplate => ({
  id: 0,
  name: '',
  fullfillByControlOrganismMergedColumnLabel: '',
  fullfillByContactControlOrganismMergedColumnLabel: '',
  fullfillByApplicantMergedColumnLabel: '',
  completedByApplicantMergedColumnLabel: '',
  fullfillByApplicantColumnValues: [] as ColumnValue[],
  fullfillByControlOrganismColumnValues: [] as ColumnValue[],
  fullfillByContactControlOrganismColumnValues: [] as ColumnValue[],
  completedByApplicantColumnValues: [] as ColumnValue[],
})
