import type { EnhancedDocument, OperationDocumentRequest } from '@/types/document'
import type { ResponseHandler } from '@/types/responseHandler'
import type { Page, Pageable } from '@/types/pagination'
import type { AxiosInstance, AxiosPromise } from 'axios'
import axiosInstance from '.'

export interface OperationDocumentFilter extends Record<string, any> {
  operationId?: number
  active?: boolean
  documentTypeId?: number
}
class OperationDocumentApi {
  public constructor(private axios: AxiosInstance) {}

  public findAll(filter: OperationDocumentFilter, pageable: Pageable): AxiosPromise<Page<EnhancedDocument>> {
    return this.axios.get(`/operation_documents`, {
      params: { ...pageable, ...filter },
    })
  }

  public update(id: number, request: OperationDocumentRequest): AxiosPromise<EnhancedDocument> {
    return this.axios.put(`/operation_documents/${id}`, request)
  }

  public delete(id: number): AxiosPromise {
    return this.axios.delete(`/operation_documents/${id}`)
  }

  public download(id: number): AxiosPromise<Blob> {
    return this.axios.get(`/operation_documents/${id}/file`, {
      responseType: 'blob',
    })
  }

  public async uploadFile(
    operationDocumentRequest: OperationDocumentRequest,
    file: File,
    documentToReplaceId?: number
  ): AxiosPromise<EnhancedDocument> {
    const formData = new FormData()
    formData.append('file', file, file.name)
    const hash = await hashFile(file)
    formData.append('hash', hash)

    const request = JSON.stringify(engieFormatRequestTransformKey(operationDocumentRequest))
    const blob = new Blob([request], {
      type: 'application/json',
    })
    formData.append('request', blob)

    return this.axios.post(`operation_documents`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      params: {
        documentToReplaceId,
      },
    })
  }

  public downloadAll(operationId: number): AxiosPromise<ResponseHandler> {
    return this.axios.get(`/operations/${operationId}/files`)
  }
}

export const operationDocumentApi = new OperationDocumentApi(axiosInstance)
