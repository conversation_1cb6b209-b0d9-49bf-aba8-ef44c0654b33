import type { DocumentType, DocumentTypeRequest } from '@/types/documentType'
import type { Page, Pageable } from '@/types/pagination'
import type { StepDocumentLink } from '@/types/stepDocumentLink'
import type { AxiosInstance, AxiosPromise } from 'axios'
import axiosInstance from '.'
import { type StepDocumentLinkRequest } from '../types/stepDocumentLink'

export type DocumentTypeFilter = Partial<{
  ids: number[]
  activeInOperation: number
  notActiveInOperation: number
  activeInOperationGroup: number
  notActiveInOperationsGroup: number
  search: string
  missingToValidateOperation: number
  missingToValidateOperationsGroupForStep30: number
  missingToValidateOperationsGroupForStep40: number
  missingToValidateEmmyFolder: number
  stepId: number
  operationCode: number
  requiredInOperationsGroupForOperationInOperationsGroup: boolean
  shareable: boolean
  withTemplate: boolean
  ignoreForSelfWorks: boolean
}>

export interface StepDocumentLinkFilter {
  stepId?: number
  operationId?: number
}

class DocumentTypeApi {
  public constructor(private axios: AxiosInstance) {}

  public getAll(pageable: Pageable, filter: DocumentTypeFilter): AxiosPromise<Page<DocumentType>> {
    return this.axios.get('/document_types', {
      params: { ...pageable, ...filter },
    })
  }

  public getOne(id: number): AxiosPromise<DocumentType> {
    return this.axios.get(`/document_types/${id}`)
  }

  public async create(
    document: DocumentTypeRequest,
    template: File | null,
    fillableTemplate: File | null
  ): AxiosPromise<DocumentType> {
    const formData = new FormData()
    if (template) {
      formData.append('templateFile', template, template.name)
      const hash = await hashFile(template)
      formData.append('templateFileHash', hash)
    }
    if (fillableTemplate) {
      formData.append('fillableTemplateFile', fillableTemplate, fillableTemplate.name)
      const hash = await hashFile(fillableTemplate)
      formData.append('fillableTemplateFileHash', hash)
    }

    const request = JSON.stringify(engieFormatRequestTransformKey(document))
    const blob = new Blob([request], {
      type: 'application/json',
    })

    formData.append('request', blob)

    return this.axios.post('/document_types', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    })
  }

  public async update(
    id: number,
    document: DocumentTypeRequest,
    template: File | null,
    fillableTemplate: File | null
  ): AxiosPromise<DocumentType> {
    const formData = new FormData()
    if (template) {
      formData.append('templateFile', template, template.name)
      const hash = await hashFile(template)
      formData.append('templateFileHash', hash)
    }
    if (fillableTemplate) {
      formData.append('fillableTemplateFile', fillableTemplate, fillableTemplate.name)
      const hash = await hashFile(fillableTemplate)
      formData.append('fillableTemplateFileHash', hash)
    }

    const request = JSON.stringify(engieFormatRequestTransformKey(document))
    const blob = new Blob([request], {
      type: 'application/json',
    })

    formData.append('request', blob)

    return this.axios.put(`/document_types/${id}`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    })
  }

  public downloadTemplate(id: number): AxiosPromise<File> {
    return this.axios.get(`/document_types/${id}/template`, {
      responseType: 'blob',
    })
  }

  public downloadFillableTemplate(id: number): AxiosPromise<File> {
    return this.axios.get(`/document_types/${id}/fillable_template`, {
      responseType: 'blob',
    })
  }

  public downloadFilledDocument(documentTypeId: number, operationId: number): AxiosPromise<File> {
    return this.axios.post(
      `/document_types/${documentTypeId}/filled_document`,
      {
        operationId: operationId,
      },
      {
        responseType: 'blob',
      }
    )
  }

  public removeFromStep(document: StepDocumentLink): AxiosPromise<void> {
    return this.axios.delete(`/document_types/${document.documentType.id}/step/${document.stepId}`)
  }

  public getAssignedDocuments(
    pageable: Pageable,
    filter: StepDocumentLinkFilter
  ): AxiosPromise<Page<StepDocumentLink>> {
    return this.axios.get(`document_types/assigned`, {
      params: { ...pageable, ...filter },
    })
  }

  public updateLink(request: StepDocumentLinkRequest): AxiosPromise<StepDocumentLink> {
    return this.axios.put(`/document_types/assigned/${request.documentTypeId}`, request)
  }

  public deleteTemplate(id: number): AxiosPromise<void> {
    return this.axios.delete(`document_types/${id}/template`)
  }
}

export const documentTypeApi = new DocumentTypeApi(axiosInstance)
