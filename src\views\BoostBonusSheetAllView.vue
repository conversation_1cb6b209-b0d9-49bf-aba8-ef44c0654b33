<template>
  <NjPage :error="data.error" :loading="data.loading">
    <template #sub-header>
      <VRow>
        <VCol cols="3">
          <SearchInput
            v-model:loading="data.loading"
            :model-value="pageFilter.search"
            @update:model-value="updateSearch"
          />
        </VCol>
        <VSpacer />
        <VCol class="flex-grow-0">
          <NjBtn :to="{ name: 'BoostBonusSheetOneNewView' }">Nouveau coup de pouce</NjBtn>
        </VCol>
      </VRow>
    </template>
    <template #body>
      <NjDataTable
        :pageable="pageable"
        :page="data.value!"
        :headers="headers"
        :on-click-row="
          (value) =>
            router.push({
              name: 'BoostBonusSheetOneView',
              params: { id: value.id },
            })
        "
        fixed
        @update:pageable="updatePageable"
      >
        <template #[`item.certified`]="{ item }">
          <VIcon v-show="item.certified" color="#28B750" icon="mdi-shield-check-outline" />
        </template>
      </NjDataTable>
    </template>
  </NjPage>
</template>

<script lang="ts" setup>
import { boostBonusSheetApi, type BoostBonusSheetFilter } from '@/api/boostBonus'
import NjPage from '@/components/NjPage.vue'
import type { DataTableHeader } from '@/components/okta/NjDataTable.type'
import NjDataTable from '@/components/okta/NjDataTable.vue'
import type { BoostBonusSheet } from '@/types/boostBonus'
import { usePaginationInQuery } from '@/types/pagination'
import { VIcon } from 'vuetify/components'

const router = useRouter()

const { data, pageable, updatePageable, updateFilter, pageFilter } = usePaginationInQuery<
  BoostBonusSheet,
  BoostBonusSheetFilter
>((filter, pageable) => boostBonusSheetApi.findAll(pageable, filter), {
  saveFiltersName: 'BoostBonusSheetAllView',
})

const headers: DataTableHeader[] = [
  {
    title: 'Coup de Pouce',
    value: 'name',
  },
  {
    title: 'Opération',
    value: 'operationCode',
  },
  {
    title: 'Description',
    value: 'description',
  },
  {
    title: 'Date de signature de la charte',
    value: 'signingDate',
    formater: (_, value) => formatHumanReadableLocalDate(value),
  },
  {
    title: "Date d'engagement",
    value: 'commitmentMaxDate',
    formater: (_, value) => formatHumanReadableLocalDate(value),
  },
  {
    title: 'Date de Fin',
    value: 'endOperationMaxDate',
    formater: (_, value) => formatHumanReadableLocalDate(value),
  },
  {
    title: 'Certifié',
    value: 'certified',
  },
  {
    title: 'MAJ par',
    value: 'updateUser',
    formater(_, value) {
      return displayFullnameUser(value)
    },
  },
  {
    title: 'Date MAJ',
    value: 'updateDateTime',
    formater(_, value) {
      return formatHumanReadableLocalDateTime(value)
    },
  },
]

const updateSearch = (value: string) => {
  updateFilter({ search: value })
}
</script>
