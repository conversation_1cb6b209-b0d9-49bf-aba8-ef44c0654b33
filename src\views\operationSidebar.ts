import type { SideBarElement } from '@/components/SideBar.type'
import { useSidebarStore } from '@/stores/sidebar'
import { useUserStore } from '@/stores/user'
import type { ProfileType } from '@/types/user'

export default function useOperationSideBar() {
  const userStore = useUserStore()
  const sidebarStore = useSidebarStore()
  watch(
    () => userStore.currentUser.roles,
    (v) => {
      const items: SideBarElement[] = [
        {
          icon: 'mdi-format-list-bulleted',
          title: 'Liste des Opérations CEE',
          routeName: 'OperationAllView',
          includedExactRoutes: ['OperationOneView', 'OperationAllView'],
        },
      ]

      if (userCanManageOperation(userStore.currentUser)) {
        items.push({
          icon: 'mdi-folder-outline',
          title: 'Liste des regroupements',
          routeName: 'OperationsGroupAllView',
        })
      }

      const agencePlusRoles: ProfileType[] = ['AGENCE_PLUS', 'SUPPORT_AGENCE_PLUS', 'ADMIN', 'ADMIN_PLUS']
      if (agencePlusRoles.some((it) => v.includes(it))) {
        items.splice(1, 0, {
          icon: 'mdi-pencil',
          title: 'Nouvelle Opération CEE',
          routeName: 'OperationOneNewView',
        })
      }

      const siegeRoles: ProfileType[] = ['SIEGE', 'INSTRUCTEUR', 'ADMIN', 'ADMIN_PLUS']

      if (siegeRoles.some((it) => v.includes(it))) {
        items.push({
          icon: 'mdi-folder-upload-outline',
          title: 'Liste des Dossiers EMMY',
          routeName: 'EmmyFolderAllView',
        })
      }
      const controlOrderRoles: ProfileType[] = ['ARRETE_CONTROLE_SIEGE', 'ADMIN_PLUS']
      if (controlOrderRoles.some((it) => v.includes(it)) && import.meta.env.VITE_FEATURE_CONTROL_ORDER === 'true') {
        items.push({
          icon: 'mdi-folder-clock-outline',
          title: 'Arrêté contrôle',
          routeName: 'ControlOrderView',
        })
      }

      const canSeeOsh = siegeRoles.concat(['SEE_OSH'])
      if (canSeeOsh.some((it) => v.includes(it)) && import.meta.env.VITE_FEATURE_OSH === 'true') {
        items.push({
          icon: 'mdi-camera-outline',
          title: 'OSH',
          routeName: 'OshView',
        })
      }

      sidebarStore.setItems(items)
    },
    {
      immediate: true,
    }
  )
}
