<template>
  <VOverlay v-model="overlay" class="align-center justify-center">
    <template #activator="{ props }">
      <div v-bind="props" style="border: 1px solid grey; max-width: 100px; max-height: 100px">
        <VProgressCircular v-if="loading" indeterminate />
        <div v-else-if="error">{{ error }}</div>
        <img v-else :src="src" style="width: 100%; height: 100%; object-fit: contain" />
      </div>
    </template>
    <img :src="src" style="width: 100%; height: 100%; object-fit: contain; padding: 64px" @click="overlay = false" />
  </VOverlay>
</template>

<script setup lang="ts">
import { VOverlay, type VProgressCircular } from 'vuetify/components'

const props = defineProps<{
  messageId: number
  documentId: number
}>()

const loading = ref(true)
const error = ref('')
const src = ref()
const overlay = ref(false)

onMounted(() => {
  loading.value = true
  error.value = ''
  messageApi
    .downloadScreenshot(props.messageId, props.documentId)
    .then((v) => {
      src.value = URL.createObjectURL(v.data)
    })
    .catch(async (e) => {
      error.value = await handleAxiosException(e)
    })
    .finally(() => {
      loading.value = false
    })
})
watch(src, (_, oldV) => {
  if (oldV) {
    URL.revokeObjectURL(src.value)
  }
})
onUnmounted(() => {
  if (src.value) {
    URL.revokeObjectURL(src.value)
  }
})
</script>
