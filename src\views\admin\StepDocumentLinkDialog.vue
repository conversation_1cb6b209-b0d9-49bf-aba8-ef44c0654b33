<template>
  <VDialog v-model="activeDialog" width="700px">
    <VCard v-click-outside="{ handler: () => emit('update:model-value', false), include: includeForClickOutside }">
      <VCardTitle>
        <VRow class="align-center">
          <VCol class="text-wrap"> Informations complémentaires - {{ props.link?.documentType.name }} </VCol>
          <VCol class="flex-grow-0">
            <NjIconBtn icon="mdi-close" rounded="0" @click="emit('update:model-value', false)" />
          </VCol>
        </VRow>
      </VCardTitle>
      <VDivider />
      <VCardText>
        <div class="text-section-title mb-2">Description</div>
        <VTextField v-model="link!.description" placeholder="Description" />
        <VDivider class="mt-4" />
        <OperationTargetRuleField v-model="link!.operationTargetRule" />
      </VCardText>
      <VCardActions style="border-top: lightgrey 1px solid">
        <VSpacer />
        <NjBtn variant="outlined" @click="emit('update:model-value', false)"> Annuler </NjBtn>
        <NjBtn @click="save">Sauvegarder</NjBtn>
      </VCardActions>
    </VCard>
  </VDialog>
</template>
<script lang="ts" setup>
import { documentTypeApi } from '@/api/documentType'
import { useSnackbarStore } from '@/stores/snackbar'
import type { StandardizedOperationSheetReduced } from '@/types/calcul/standardizedOperationSheet'
import type { StepDocumentLink, StepDocumentLinkRequest } from '@/types/stepDocumentLink'
import type { PropType } from 'vue'
import NjBtn from '../../components/NjBtn.vue'
import OperationTargetRuleField from '@/views/admin/OperationTargetRuleField.vue'

const props = defineProps({
  modelValue: {
    type: Boolean,
  },
  link: {
    type: Object as PropType<StepDocumentLink>,
  },
  operationList: {
    type: Array as PropType<StandardizedOperationSheetReduced[]>,
    default: () => [],
  },
})
const emit = defineEmits(['update:model-value', 'update:link', 'save'])
const snackbarStore = useSnackbarStore()

const activeDialog = computed<boolean>({
  get() {
    return props.modelValue
  },
  set(v) {
    emit('update:model-value', v)
  },
})

const promisableLink = ref(emptyValue<StepDocumentLink>())
const save = () => {
  const newLink: StepDocumentLinkRequest = {
    description: props.link!.description,
    documentTypeId: props.link!.documentType.id,
    stepId: props.link!.stepId,
    operationTargetRule: props.link!.operationTargetRule,
  }
  handleAxiosPromise(promisableLink, documentTypeApi.updateLink(newLink), {
    afterSuccess: () => {
      snackbarStore.setSuccess('Vos modifications ont bien été enregistrées')
      emit('update:link', promisableLink.value.value)
      emit('update:model-value', false)
    },
    afterError: () => snackbarStore.setError("Une erreur est survenue lors de l'enregistrement de vos modifications"),
  })
}
const includeForClickOutside = () => {
  return [document.querySelector('.v-menu')]
}
</script>
