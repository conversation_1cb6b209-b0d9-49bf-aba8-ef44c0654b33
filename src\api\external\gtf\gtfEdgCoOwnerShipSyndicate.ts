import type { AxiosInstance, AxiosPromise } from 'axios'
import { gtfAxiosInstance } from './index'
import type { Pageable, Page } from '@/types/pagination'
import type { Address } from '@/types/address'

const baseUrl = 'https://tabular-api.data.gouv.fr/api'
const prefixApiUrl = '/resources/3ea8e2c3-0038-464a-b17e-cd5c91f65ce2/data/'

export type CoOwnerShipSyndicateFilter = Partial<{
  usageName: string
  address: Address
}>

export interface CoOwnerShipSyndicateDto {
  registrationNumber: string
  usageName: string
  address: Address
  totalElements?: number
}

const fieldMapping = {
  registrationNumber: "Numéro d'immatriculation",
  usageName: 'Nom d’usage de la copropriété',
  'address.street': 'Numéro et Voie (adresse de référence)',
  'address.postalCode': 'Code postal (adresse de référence)',
  'address.city': 'Commune (adresse de référence)',
} as const

export class CoOwnerShipSyndicateApi {
  public constructor(private axios: AxiosInstance) {}

  public getAllSyndicates(filter: CoOwnerShipSyndicateFilter, pageable: Pageable): AxiosPromise<any> {
    const params: Record<string, string | number> = {
      page_size: pageable.size ?? 20,
      page: (pageable.page ?? 0) + 1,
    }

    if (filter.address?.street) {
      params[fieldMapping['address.street'] + '__contains'] = filter.address.street
    }

    if (filter.address?.postalCode) {
      params[fieldMapping['address.postalCode'] + '__contains'] = filter.address.postalCode
    }

    if (filter.address?.city) {
      params[fieldMapping['address.city'] + '__contains'] = filter.address.city
    }

    if (pageable.sort?.length) {
      pageable.sort.forEach((sortField) => {
        const [field, direction] = sortField.split(',')
        const mappedField = fieldMapping[field as keyof typeof fieldMapping] || field
        params[`${mappedField}__sort`] = direction.toLowerCase()
      })
    }

    return this.axios.get(baseUrl + prefixApiUrl, { params }).then((response) => {
      const data = response.data

      const syndicates = data.data.map((item: any) => ({
        registrationNumber: item[fieldMapping.registrationNumber],
        usageName: item[fieldMapping.usageName],
        address: {
          street: item[fieldMapping['address.street']] || '',
          postalCode: item[fieldMapping['address.postalCode']] || '',
          city: item[fieldMapping['address.city']] || '',
          country: null,
        },
      }))

      const meta = data.meta
      const pageNumber = meta.page - 1
      const pageSize = meta.page_size
      const totalElements = meta.total
      const totalPages = Math.ceil(totalElements / pageSize)

      const page: Page<CoOwnerShipSyndicateDto> = {
        content: syndicates,
        first: pageNumber === 0,
        last: pageNumber === totalPages - 1,
        empty: syndicates.length === 0,
        number: pageNumber,
        size: pageSize,
        sort: {
          sorted: false,
          unsorted: true,
          empty: true,
        },
        totalElements,
        totalPages,
      }
      return Promise.resolve({
        ...response,
        data: page,
      })
    })
  }
}
export const coOwnerShipSyndicateApi = new CoOwnerShipSyndicateApi(gtfAxiosInstance)
