<template>
  <NjPage v-bind="$attrs">
    <template #body>
      <VRow class="flex-column h-100">
        <VCol class="flex-grow-0">
          <VRow>
            <VCol>
              <SearchInput
                :model-value="pageFilter.search"
                @update:model-value="(v: string) => updateFilterByFieldname('search', v)"
              />
            </VCol>
            <VCol class="flex-grow-0">
              <NjBtn @click="handleCreateNonCumulativeRule">Nouvelle règle de non cumul</NjBtn>
              <CardDialog
                v-model="createNonCumulativeRuleDialog"
                title="Créer une règle de non cumul"
                max-width="900px"
              >
                <VForm ref="nonCumulativeRuleFormRef">
                  <NonCumulativeRuleOneView ref="nonCumulativeRuleOneViewRef" />
                </VForm>

                <template #actions>
                  <NjBtn variant="outlined">Annuler</NjBtn>
                  <NjBtn @click="submitCreateNonCumulativeRule">Créer</NjBtn>
                </template>
              </CardDialog>
            </VCol>
          </VRow>
        </VCol>
        <VCol>
          <NjDataTable
            v-model:selections="selections"
            :headers="headers"
            :pageable="pageable"
            :page="data.value!"
            fixed
            @update:pageable="updatePageable"
          />
        </VCol>
      </VRow>
    </template>
  </NjPage>
  <VNavigationDrawer location="end" :model-value="!!selections[0]" width="600" disable-resize-watcher>
    <VCard v-if="selections.length" class="content-layout">
      <VCardTitle class="content-layout__header">
        <VRow dense>
          <VCol align-self="center"> Détail </VCol>
          <VCol class="flex-grow-0">
            <NjIconBtn icon="mdi-pencil" color="info" rounded="0" :active="editRule" @click="editRule = !editRule" />
          </VCol>
          <VCol class="flex-grow-0">
            <NjIconBtn icon="mdi-delete" color="error" rounded="0" @click="deleteRule" />
          </VCol>
          <VCol class="flex-grow-0">
            <NjIconBtn icon="mdi-close" rounded="0" @click="selections = []" />
          </VCol>
        </VRow>
      </VCardTitle>
      <VDivider />
      <VCardText class="content-layout__main">
        <VForm v-if="editRule" ref="editNonCumulativeRuleFormRef">
          <NonCumulativeRuleOneView ref="editNonCumulativeRuleOneViewRef" :non-cumulative-rule="selections[0]" />
        </VForm>
        <VRow v-else class="flex-column" dense>
          <VCol>
            <NjDisplayValue label="Code Opération" :value="selections[0].operationCode1" />
          </VCol>
          <VCol>
            <NjDisplayValue label="Code Opération" :value="selections[0].operationCode2" />
          </VCol>
          <VCol>
            <NjDisplayValue label="Date de début" :value="formatHumanReadableLocalDate(selections[0].startDate)" />
          </VCol>
          <VCol>
            <NjDisplayValue label="Date de fin" :value="formatHumanReadableLocalDate(selections[0].endDate)" />
          </VCol>
          <VCol>
            <NjDisplayValue label="Description" :value="selections[0].description" />
          </VCol>
        </VRow>
      </VCardText>
      <VDivider />
      <VCardActions v-if="editRule" class="content-layout__footer">
        <VSpacer />
        <NjBtn variant="outlined" @click="editRule = false">Annuler</NjBtn>
        <NjBtn @click="save">Enregistrer</NjBtn>
      </VCardActions>
    </VCard>
  </VNavigationDrawer>
</template>
<script setup lang="ts">
import type { DataTableHeader } from '@/components/okta/NjDataTable.type'
import {
  VCard,
  VCardActions,
  VCardText,
  VCardTitle,
  VCol,
  VDivider,
  VForm,
  VNavigationDrawer,
  VRow,
  VSpacer,
} from 'vuetify/components'
import type { NonCumulativeRule } from '@/types/nonCumulativeRule'
import type { NonCumulativeRuleFilter } from '@/api/nonCumulativeRule'
import NonCumulativeRuleOneView from './NonCumulativeRuleOneView.vue'
import { useDialogStore } from '@/stores/dialog'
import { useSnackbarStore } from '@/stores/snackbar'
import { formatHumanReadableLocalDate } from '@/types/date'
import SearchInput from '@/components/SearchInput.vue'

const headers: DataTableHeader<NonCumulativeRule>[] = [
  {
    title: 'Fiche opération',
    value: 'operationCode1',
    width: '500px',
  },
  {
    title: 'Fiche opération',
    value: 'operationCode2',
    width: '500px',
  },
  {
    title: 'Date de début',
    value: 'startDate',
    width: '500px',
    formater: (i, v) => formatHumanReadableLocalDate(v),
  },
  {
    title: 'Date de fin',
    value: 'endDate',
    width: '500px',
    formater: (i, v) => formatHumanReadableLocalDate(v),
  },
  {
    title: 'Description',
    value: 'description',
  },
]

const createNonCumulativeRuleDialog = ref(false)

const handleCreateNonCumulativeRule = () => {
  createNonCumulativeRuleDialog.value = true
}

const { data, pageable, pageFilter, updatePageable, updateFilterByFieldname, reload } = usePagination<
  NonCumulativeRule,
  NonCumulativeRuleFilter
>(
  (filter, pageable) => nonCumulativeRuleApi.getAll(pageable, filter),
  {},
  {
    page: 0,
    size: 50,
  }
)

const selections = ref<NonCumulativeRule[]>([])

const nonCumulativeRuleFormRef = ref<VForm | null>(null)
const nonCumulativeRuleOneViewRef = ref<typeof NonCumulativeRuleOneView | null>(null)

const snackbarStore = useSnackbarStore()

const submitCreateNonCumulativeRule = async () => {
  if ((await nonCumulativeRuleFormRef.value?.validate())?.valid) {
    const nonCumulativeRule = nonCumulativeRuleOneViewRef.value!.mutableNonCumulativeRule
    nonCumulativeRuleApi
      .create({
        operationCode1: nonCumulativeRule.operationCode1,
        operationCode2: nonCumulativeRule.operationCode2,
        startDate: nonCumulativeRule.startDate,
        endDate: nonCumulativeRule.endDate,
        description: nonCumulativeRule.description,
      })
      .then(() => {
        reload()
        createNonCumulativeRuleDialog.value = false
        snackbarStore.setSuccess('La règle a bien été créé')
      })
  }
}

const dialogStore = useDialogStore()
const deleteRule = () => {
  dialogStore
    .addAlert({
      title: 'Supprimer une règle de non cumul',
      message: 'Êtes vous sure de vouloir supprimer la règle?',
    })
    .then(() => {
      nonCumulativeRuleApi.delete(selections.value[0]!.id).then(() => {
        selections.value = []
        reload()
        snackbarStore.setSuccess('La règle a bien été supprimé')
      })
    })
}

const editRule = ref(false)
const editNonCumulativeRuleFormRef = ref<VForm | null>(null)
const editNonCumulativeRuleOneViewRef = ref<typeof NonCumulativeRuleOneView | null>(null)

const save = async () => {
  if ((await editNonCumulativeRuleFormRef.value?.validate())?.valid) {
    const nonCumulativeRule = editNonCumulativeRuleOneViewRef.value!.mutableNonCumulativeRule
    nonCumulativeRuleApi
      .save(nonCumulativeRule.id, {
        operationCode1: nonCumulativeRule.operationCode1,
        operationCode2: nonCumulativeRule.operationCode2,
        startDate: nonCumulativeRule.startDate,
        endDate: nonCumulativeRule.endDate,
        description: nonCumulativeRule.description,
      })
      .then(() => {
        reload()
        createNonCumulativeRuleDialog.value = false
        snackbarStore.setSuccess('La règle a bien été mis à jour')
      })
  }
}
</script>
