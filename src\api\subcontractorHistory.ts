import type { AxiosInstance, AxiosPromise } from 'axios'
import axiosInstance from '.'
import type { Page, Pageable } from '@/types/pagination'
import type { SubcontractorHistory } from '@/types/history'

const subcontractorUrl = '/subcontractor_histories'

export interface SubcontractorHistoryFilter {
  subcontractorId?: number
}

class SubcontractorHistoryApi {
  public constructor(private axios: AxiosInstance) {}

  public findAll(pageable: Pageable, filter: SubcontractorHistoryFilter): AxiosPromise<Page<SubcontractorHistory>> {
    return this.axios.get(subcontractorUrl, {
      params: { ...filter, ...pageable },
    })
  }
}

export const subcontractorHistoryApi = new SubcontractorHistoryApi(axiosInstance)
