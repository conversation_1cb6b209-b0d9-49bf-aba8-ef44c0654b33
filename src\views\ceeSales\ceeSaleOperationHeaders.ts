import type { DataTableHeader } from '@/components/okta/NjDataTable.type'
import type { Operation } from '@/types/operation'

export const ceeSalesOperationHeaders: DataTableHeader<Operation>[] = [
  {
    title: 'Date 100',
    value: 'lastValidatedStepDateTime',
    formater: (_, value) => formatHumanReadableLocalDate(value),
  },
  {
    title: 'Chrono',
    value: 'chronoCode',
  },
  {
    title: 'Nom de dossier',
    value: 'operationName',
  },
  {
    title: 'kWhc dem. Class',
    value: 'classicCumac',
    formater: (_, value) => formatNumber(value),
    cellClass: 'text-right',
  },
  {
    title: 'kWhc dem. Préc',
    value: 'precariousnessCumac',
    formater: (_, value) => formatNumber(value),
    cellClass: 'text-right',
  },
  {
    title: 'kWhc dispo. Class',
    value: 'availableClassicCumac',
    formater: (_, value) => formatNumber(value),
    cellClass: 'text-right',
  },
  {
    title: 'kWhc dispo. Préc',
    value: 'availablePrecariousnessCumac',
    formater: (_, value) => formatNumber(value),
    cellClass: 'text-right',
  },
  {
    title: 'kWhc dispo. Total',
    value: 'cumacAvailable',
    formater: (item) => formatNumber(item.availableClassicCumac + item.availablePrecariousnessCumac),
    cellClass: 'text-right',
    sortable: false,
  },
  {
    title: 'Étape',
    value: 'pas convaincu',
    sortable: false,
  },
  {
    title: 'Processus',
    value: 'pas très utile',
    sortable: false,
  },
  {
    title: 'Période',
    value: 'period.name',
  },
  {
    title: 'Code EMMY',
    value: 'emmyFolder.emmyCode',
  },
  {
    title: 'Désignation EMMY',
    value: 'emmyFolder.name',
    sortable: false,
  },
  {
    title: 'kWhc vendus Class.',
    value: 'soldClassicCumac',
    formater: (_, value) => formatNumber(value),
    cellClass: 'text-right',
  },
  {
    title: 'Mnt vendus Class.',
    value: 'na',
    sortable: false,
  },
  {
    title: 'kWhc vendus Préc.',
    value: 'soldPrecariousnessCumac',
    formater: (_, value) => formatNumber(value),
    cellClass: 'text-right',
  },
  {
    title: 'Mnt vendus Préc.',
    value: 'na',
    sortable: false,
  },
  {
    title: 'Nombre ventes',
    value: 'na',
    sortable: false,
  },
  {
    title: '1ère vente',
    value: 'na',
    sortable: false,
  },
  {
    title: 'Dernière vente',
    value: 'na',
    sortable: false,
  },
  {
    title: 'Envoi PNCEE',
    value: 'emmyFolder.pnceeSubmissionDate',
    formater: (_, value) => formatHumanReadableLocalDate(value),
  },
  {
    title: 'Numéro de délivrance classique PNCEE',
    value: 'emmyFolder.pnceeClassicIssuedNumber',
  },
  {
    title: 'Numéro de délivrance précarité PNCEE',
    value: 'emmyFolder.pnceePrecariousnessIssuedNumber',
  },
  {
    title: 'Délivrance PNCEE',
    value: 'emmyFolder.pnceeIssuedDate',
    formater: (_, value) => formatHumanReadableLocalDate(value),
  },
  {
    title: 'Nom agence',
    value: 'entity.name',
    formater: (item) => `${item.entity.name} (${item.entity.id})`,
  },
  {
    title: 'Valo Class.',
    value: 'classicValuationValue',
    formater: (item, value) => formatPriceNumber(item.atypicalClassicValuationValue ?? value) + '/MWhc',
    cellClass: 'text-right',
    sortable: false,
  },
  {
    title: 'Valo Préc.',
    value: 'precariousnessValuationValue',
    formater: (item, value) => formatPriceNumber(item.atypicalPrecariousnessValuationValue ?? value) + '/MWhc',
    cellClass: 'text-right',
    sortable: false,
  },
  {
    title: 'PU Vente',
    value: 'na',
    sortable: false,
  },
  {
    title: 'Dif. Class.',
    value: 'na',
    sortable: false,
  },
  {
    title: 'Dif. Préc.',
    value: 'na',
    sortable: false,
  },
]
