<template>
  <VRow class="flex-column">
    <VCol>
      <NjExpansionPanel>
        <template #title>
          <VRow>
            <VCol>
              <h2>{{ label }}</h2>
            </VCol>
            <VCol class="flex-grow-0">
              <NjBtn @click.stop="addColumn"> Ajouter une colonne </NjBtn>
            </VCol>
          </VRow>
        </template>
        <VRow class="flex-column" dense>
          <VCol>
            <VTextField
              label="Label de la zone"
              :model-value="mergedColumnLabel"
              @update:model-value="emit('update:merged-column-label', $event)"
            />
          </VCol>
          <VCol>
            <VList ref="listRef">
              <VListItem v-for="(column, index) in columnValueIdList" :key="column.id">
                <VCard border="false">
                  <VCardText>
                    <VRow class="align-center">
                      <VCol class="flex-grow-0">
                        {{ decimalToBase26((offset ?? 0) + index + 1) }}
                      </VCol>
                      <VCol class="flex-grow-0">
                        <VIcon class="sortable-handle">mdi-drag</VIcon>
                      </VCol>
                      <VCol>
                        <VTextarea v-model="column.columnValue.label" auto-grow label="Label" rows="1" />
                      </VCol>
                      <VCol cols="3">
                        <VAutocomplete
                          v-model="column.columnValue.value"
                          label="Valeur"
                          :items="operationStaticValue"
                          :item-value="(e) => e"
                          :item-title="(e: OperationStaticValue) => operationStaticValueLabel[e].label"
                          clearable
                        />
                      </VCol>
                      <VCol cols="2">
                        <VTextarea v-model="column.columnValue.defaultValue" auto-grow label="Défaut" rows="1" />
                      </VCol>
                      <VCol class="flex-grow-0">
                        <NjIconBtn color="primary" icon="mdi-delete" size="small" @click="removeColumn(index)" />
                      </VCol>
                    </VRow>
                  </VCardText>
                </VCard>
              </VListItem>
            </VList>
          </VCol>
        </VRow>
      </NjExpansionPanel>
    </VCol>
  </VRow>
</template>
<script setup lang="ts">
import type { ColumnValue } from '@/types/controlOrderExportTemplate'
import type { PropType } from 'vue'
import { type OperationStaticValue, operationStaticValue, operationStaticValueLabel } from '@/types/emmyParameter'
import {
  VAutocomplete,
  VCard,
  VCardText,
  VCol,
  VIcon,
  VList,
  VListItem,
  VRow,
  VTextField,
  VTextarea,
} from 'vuetify/components'
import { useSortable } from '@vueuse/integrations/useSortable'
import { cloneDeep } from 'lodash'

const props = defineProps({
  label: String,
  offset: Number,
  modelValue: {
    type: Array as PropType<ColumnValue[]>,
    required: true,
  },
  mergedColumnLabel: String,
})

const emit = defineEmits<{
  'update:model-value': [ColumnValue[]]
  'update:merged-column-label': [string]
}>()

interface ColumnValueWithId {
  id: number
  columnValue: ColumnValue
}

const mapToColumnValueId = (columnValueList: ColumnValue[]): ColumnValueWithId[] => {
  const result = [] as ColumnValueWithId[]
  for (let i = 0; i < columnValueList.length; i++) {
    result.push({
      id: i,
      columnValue: columnValueList[i],
    })
  }
  return result
}

const columnValueIdList = ref<ColumnValueWithId[]>(mapToColumnValueId(cloneDeep(props.modelValue)))

const listRef = ref(null)

useSortable(listRef, columnValueIdList, {
  handle: '.sortable-handle',
})

function decimalToBase26(decimalNumber: number) {
  const base26Chars = 'ZABCDEFGHIJKLMNOPQRSTUVWXY'
  let result = ''
  while (decimalNumber > 0) {
    const remainder = decimalNumber % 26
    result = base26Chars[remainder] + result
    decimalNumber = Math.floor(decimalNumber / 26)
  }
  return result || 'Z'
}

const addColumn = () => {
  columnValueIdList.value.push({
    id: columnValueIdList.value.length,
    columnValue: {
      label: '',
      value: null,
      defaultValue: '',
    },
  })
}

const removeColumn = (index: number) => {
  columnValueIdList.value.splice(index, 1)
}

const mapToColumnValue = (columnValueList: ColumnValueWithId[]): ColumnValue[] => {
  const result = [] as ColumnValue[]
  for (let i = 0; i < columnValueList.length; i++) {
    result.push(columnValueList[i].columnValue)
  }
  return result
}

watch(
  columnValueIdList,
  (v) => {
    emit('update:model-value', mapToColumnValue(v))
  },
  {
    deep: true,
  }
)
</script>
