<template>
  <NjExpansionPanel title="Coup de pouce boCEE">
    <VRow class="flex-column" no-gutters>
      <VCol>
        <NjDisplayValue label="Type de coup de pouce" :value="legacyBoostBonusSimulation?.boostType ?? ''" />
      </VCol>
      <VCol>
        <NjDisplayValue
          label="Secteur d'activité"
          :value="activitySectors.find((i) => i.value == legacyBoostBonusSimulation?.activitySector)?.label"
        />
      </VCol>
      <VCol>
        <NjDisplayValue label="Nombre de batiment" :value="legacyBoostBonusSimulation?.buildingsNumber" />
      </VCol>
      <VCol>
        <NjDisplayValue label="Surface" :value="legacyBoostBonusSimulation?.surface" />
      </VCol>
      <VCol>
        <NjDisplayValue
          label="Ancienne énergie"
          :value="oldEnergyTypes.find((i) => i.value == legacyBoostBonusSimulation?.oldEnergyType)?.label"
        />
      </VCol>
      <VCol>
        <NjDisplayValue
          label="Ancienne énergie 2"
          :value="oldEnergyTypes.find((i) => i.value == legacyBoostBonusSimulation?.oldEnergyType2)?.label"
        />
      </VCol>
      <VCol>
        <NjDisplayValue
          label="Nouvelle énergie"
          :value="energyTypes.find((i) => i.value == legacyBoostBonusSimulation?.newEnergyType)?.label"
        />
      </VCol>
      <VCol>
        <NjDisplayValue
          label="Nouvelle énergie 2"
          :value="energyTypes.find((i) => i.value == legacyBoostBonusSimulation?.newEnergyType2)?.label"
        />
      </VCol>
      <VCol>
        <NjDisplayValue label="Ancienne puissance" :value="legacyBoostBonusSimulation?.oldPower" />
      </VCol>
      <VCol>
        <NjDisplayValue label="Ancienne puissance 2" :value="legacyBoostBonusSimulation?.oldPower2" />
      </VCol>
      <VCol>
        <NjDisplayValue label="Nouvelle puissance" :value="legacyBoostBonusSimulation?.newPower" />
      </VCol>
      <VCol>
        <NjDisplayValue label="Nouvelle puissance 2" :value="legacyBoostBonusSimulation?.newPower2" />
      </VCol>
    </VRow>
  </NjExpansionPanel>
</template>
<script setup lang="ts">
import type { LegacyBoostBonusSimulation } from '@/types/operation'
import { oldEnergyTypes, energyTypes, activitySectors } from '@/types/operation'

defineProps({
  legacyBoostBonusSimulation: Object as PropType<LegacyBoostBonusSimulation>,
})
</script>
