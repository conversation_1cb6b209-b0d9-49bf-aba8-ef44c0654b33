<template>
  <VTextField
    :loading="computedLoading"
    :error-messages="errorMessages"
    :rules="[(v) => errorMessages ?? (!props.required || !!props.modelValue || 'Champs requis')]"
    :maxlength="length"
    :validation-value="queringSirene"
    :model-value="props.modelValue"
    @update:model-value="$emit('update:model-value', $event)"
  >
    <template v-if="isTooManyRequestError" #details>
      <div class="d-flex align-center">
        Vérification indispo. Veuillez retentez dans une minute
        <VBtn size="small" variant="tonal" class="ms-2">Réessayer</VBtn>
      </div>
    </template>
  </VTextField>
</template>

<script lang="ts" setup>
import axiosInstance from '@/api'
import { isAxiosError } from 'axios'
import { debounce } from 'lodash'
import { watch } from 'vue'
import { VTextField } from 'vuetify/components/VTextField'

const props = withDefaults(
  defineProps<{
    type?: 'siren' | 'siret'
    required?: boolean
    modelValue?: string
  }>(),
  {
    type: 'siret',
    required: false,
  }
)

defineEmits(['update:model-value'])

const length = computed(() => (props.type === 'siren' ? 9 : 14))
const attrs = useAttrs()

const queringSirene = ref(false)
const isTooManyRequestError = ref(false)
const errorMessages = ref<string>()
const debouncedWatch = debounce((v) => {
  if (v.length === (props.type === 'siret' ? 14 : 9)) {
    axiosInstance
      .get('proxy-apis/sirene/' + props.type + '/' + v)
      .then((d) => {
        errorMessages.value = undefined
        console.debug(d)
      })
      .catch(async (e) => {
        if (isAxiosError(e) && e.response?.status === 404) {
          errorMessages.value = 'Le ' + props.type.toLocaleUpperCase() + " n'existe pas selon l'INSEE"
        } else {
          // Volontairement, on bloque même en cas d'erreur réseau, car on ne veut pas pouvoir mettre des SIREN faux
          errorMessages.value = await handleAxiosException(e)
        }
      })
      .finally(() => {
        queringSirene.value = false
      })
  } else if (v.length > 0) {
    errorMessages.value = 'Format ' + props.type.toLocaleUpperCase() + ' incorrect : ' + length.value + ' caractères'
    queringSirene.value = false
  } else {
    errorMessages.value = undefined
    queringSirene.value = false
  }
}, 1000)

const computedLoading = computed((): boolean => {
  return (attrs.loading as boolean) || queringSirene.value
})

watch(
  () => props.modelValue,
  (v) => {
    queringSirene.value = true
    debouncedWatch(v)
  }
)
</script>
