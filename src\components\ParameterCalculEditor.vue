<template>
  <VRow align="center">
    <VCol>
      <VRow>
        <VCol cols="3">
          <RemoteAutoComplete
            label="Colonne EMMY"
            :model-value="copyValue.emmyParameterId"
            :query-for-one="findOneEmmyParameter"
            :query-for-all="findAllEmmyParameter"
            :clearable="!disabled"
            item-title="label"
            item-value="id"
            :readonly="disabled"
            infinite-scroll
            @update:model-value="updateEmmyParameterId"
          />
        </VCol>
        <VCol>
          <VTextField
            v-model="copyValue.id"
            label="Identifiant pour le paramètre"
            :rules="[variableNameRule, reservedWordsRule]"
            :readonly="mandatoryParam || disabled"
            :hint="variableNameRuleHint"
          ></VTextField>
        </VCol>
        <VCol cols="3">
          <VTextField
            v-model="copyValue.label"
            label="Texte à afficher pour le paramètre"
            :rules="[requiredRule]"
            :readonly="mandatoryParam"
          />
        </VCol>
        <VCol cols="3">
          <VSelect
            v-model="copyValue.type"
            label="Type du paramètre"
            :items="variableTypes"
            :rules="[requiredRule]"
            :readonly="disabled"
          ></VSelect>
        </VCol>
        <VCol cols="3">
          <VTextField
            v-if="copyValue.type === 'CHOICE'"
            v-model="copyValue.data"
            label="Liste des valeurs possibles"
            persistent-hint
            :rules="[requiredRule]"
            readonly
            @click="choicesDialog.active = true"
            @focus="choicesDialog.active = true"
          />
          <VTextField
            v-else-if="copyValue.type === 'NUMBER'"
            v-model="copyValue.suffix"
            label="Suffixe (pour affichage)"
            :readonly="disabled"
          />
        </VCol>
        <VCol cols="3">
          <FormulaInput
            v-model="copyValue.conditionalFormula"
            label="Condition"
            :error-messages="parsingCondition.error"
            :readonly="disabled"
            :parameters="parameters"
            :mapping-tables="mappingTables"
          />
        </VCol>
        <VCol cols="3">
          <FormulaInput
            v-model="copyValue.computedFormula"
            label="Paramètre à calculer"
            :disabled="copyValue.sharedParameter"
            :error-messages="parsingComputedFormula.error"
            :readonly="disabled"
            :parameters="parameters"
            :mapping-tables="mappingTables"
          />
        </VCol>
        <VCol cols="3">
          <VTextField v-model="copyValue.emmyDefaultValue" label="Valeur par défaut Emmy" />
        </VCol>
        <VCol v-if="multipleOperation" cols="3">
          <VSwitch v-model="copyValue.sharedParameter" label="(Si multiligne) Paramètre commun" :readonly="disabled" />
        </VCol>
        <VCol cols="3">
          <VSwitch v-model="copyValue.optional" label="Optionnel" :readonly="disabled" />
        </VCol>
      </VRow>
    </VCol>
    <VCol class="flex-grow-0">
      <VBtn
        v-if="!mandatoryParam && !disabled"
        color="primary"
        icon="mdi-delete-empty-outline"
        size="small"
        variant="text"
        @click="emit('delete')"
      />
    </VCol>

    <VDialog v-model="choicesDialog.active" :persistent="!disabled">
      <VCard>
        <VCardTitle
          >Valeurs du choix
          <NjIconBtn v-if="!disabled" class="ms-2" variant="square" icon="mdi-plus" @click="addChoiceItem"
        /></VCardTitle>
        <VCardText>
          <VForm ref="choiceFormRef">
            <VRow v-for="(c, i) in choicesDialog.items" :key="i">
              <VCol>
                <VRow>
                  <VCol cols="6">
                    <VTextField v-model="c[0]" label="Choix" :rules="[requiredRule]" :readonly="disabled" />
                  </VCol>
                  <VCol cols="6">
                    <VTextField v-model="c[1]" label="Valeur EMMY" :rules="[requiredRule]" :readonly="disabled" />
                  </VCol>
                </VRow>
              </VCol>
              <VCol class="flex-grow-0">
                <NjIconBtn v-if="!disabled" variant="square" icon="mdi-delete" @click="removeChoiceItem(i)" />
              </VCol>
            </VRow>
          </VForm>
        </VCardText>

        <VCardActions>
          <VSpacer />
          <NjBtn variant="outlined" @click="choicesDialog.active = false">Annuler</NjBtn>
          <NjBtn @click="valideChoices">Valider</NjBtn>
        </VCardActions>
      </VCard>
    </VDialog>
  </VRow>
</template>

<script setup lang="ts">
import RemoteAutoComplete from '@/components/RemoteAutoComplete.vue'
import { makeEmptyParameterFormula, type ParameterFormula } from '@/types/calcul/parameterFormula'
import { requiredRule, variableNameRule, variableNameRuleHint, type ValidationRule } from '@/types/rule'
import { cloneDeep, isEqual, snakeCase } from 'lodash'
import type { PropType } from 'vue'
import NjBtn from './NjBtn.vue'
import type { VForm } from 'vuetify/components/VForm'
import NjIconBtn from './NjIconBtn.vue'
import { parsingFormula } from '@/types/calcul/formula'
import FormulaInput from './FormulaInput.vue'
import type { Pageable } from '@/types/pagination'
import { VCol, VRow, VTextField } from 'vuetify/components'

const props = defineProps({
  modelValue: {
    type: Object as PropType<ParameterFormula>,
    default: makeEmptyParameterFormula,
  },
  reservedWordsRule: {
    type: Function as PropType<ValidationRule>,
    default: () => () => true,
  },
  mandatoryParam: {
    type: Boolean,
  },
  multipleOperation: Boolean,
  disabled: Boolean,
  parameters: Array as PropType<{ id: string; label: string }[]>,
  mappingTables: Array as PropType<{ id: string }[]>,
})
const emit = defineEmits(['update:model-value', 'delete'])
const copyValue = ref(makeEmptyParameterFormula())
watch(
  copyValue,
  (v) => {
    emit('update:model-value', v)
  },
  {
    deep: true,
  }
)
watch(
  () => props.modelValue,
  (v) => {
    if (!isEqual(v, copyValue.value)) {
      copyValue.value = cloneDeep(v)
    }
  },
  {
    immediate: true,
  }
)
const findOneEmmyParameter = (v: unknown) => emmyParameterApi.findOne(v as number)
const findAllEmmyParameter = (v: string, pageable: Pageable) =>
  emmyParameterApi.findAll({ label: v }, { ...pageable, sort: ['id,ASC'] })

const variableTypes = [
  {
    title: 'Nombre',
    value: 'NUMBER',
  },
  {
    title: 'Choix',
    value: 'CHOICE',
  },
  {
    title: 'Date',
    value: 'DATE',
  },
  {
    title: 'Texte',
    value: 'STRING',
  },
]

const updateEmmyParameterId = async (emmyParameterId: number) => {
  console.debug('updateEmmyParameter')
  if (emmyParameterId !== copyValue.value.emmyParameterId) {
    if (emmyParameterId) {
      const e = (await findOneEmmyParameter(emmyParameterId)).data

      copyValue.value.emmyParameterId = e.id
      if (!copyValue.value.id) {
        copyValue.value.id = snakeCase(e.label)
      }
      if (!copyValue.value.label) {
        copyValue.value.label = e.label
      }
    } else {
      copyValue.value.emmyParameterId = undefined
    }
  }
}

const choicesDialog = ref({
  active: false,
  items: [] as [string, string][],
})
const addChoiceItem = () => {
  choicesDialog.value.items.push(['', ''])
}
const removeChoiceItem = (i: number) => {
  choicesDialog.value.items.splice(i, 1)
}
const choiceFormRef = ref<typeof VForm | null>(null)
const valideChoices = async () => {
  if ((await choiceFormRef.value!.validate()).valid) {
    const choices = choicesDialog.value.items.map((it) => it[0])
    const emmyValues = choicesDialog.value.items.map((it) => it[1])
    copyValue.value.data = choices.join(';')
    copyValue.value.emmyMappedValues = emmyValues.join(';')
    choicesDialog.value.active = false
  }
}
const parsingCondition = computed(() => {
  return parsingFormula(copyValue.value.conditionalFormula)
})
const parsingComputedFormula = computed(() => {
  return parsingFormula(copyValue.value.computedFormula)
})
watch(
  () => choicesDialog.value.active,
  (v) => {
    if (v) {
      const values = (copyValue.value.data?.split(';') ?? []) as string[]
      const emmyValues = copyValue.value.emmyMappedValues.split(';') as string[]
      choicesDialog.value.items = values.map((it, i) => [it, emmyValues[i]])
    }
  }
)
watch(
  () => copyValue.value.sharedParameter,
  (v) => {
    if (v) {
      copyValue.value.conditionalFormula = ''
      copyValue.value.computedFormula = ''
    }
  }
)
</script>
