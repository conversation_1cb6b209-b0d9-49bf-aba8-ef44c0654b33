import type { Address } from './address'
import type { LocalDateTime } from './date'
import type { User } from './user'

export interface Subcontractor {
  id: number
  socialReason: string
  siret: string
  address: Address
  certified: boolean
  creationUser: User
  creationDateTime: LocalDateTime
  updateUser: User
  updateDateTime: LocalDateTime
}

export interface SubcontractorRequest
  extends Omit<Subcontractor, 'id' | 'creationUser' | 'creationDateTime' | 'updateUser'> {}

export const makeEmptySubcontractor = (): Subcontractor => ({
  id: 0,
  address: makeEmptyAddress(),
  certified: false,
  siret: '',
  socialReason: '',
  creationDateTime: '',
  creationUser: makeEmptyUser(),
  updateDateTime: '',
  updateUser: makeEmptyUser(),
})

export const mapToSubcontractorRequest = (subcontractor: Subcontractor): SubcontractorRequest => ({
  address: subcontractor.address,
  certified: subcontractor.certified,
  siret: subcontractor.siret,
  socialReason: subcontractor.socialReason,
  updateDateTime: subcontractor.updateDateTime,
})

export interface SubcontractorFilter {
  search?: string
}
