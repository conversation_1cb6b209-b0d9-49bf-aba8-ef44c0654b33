<template>
  <VCard>
    <VCardTitle>
      <slot name="title"> </slot>
    </VCardTitle>
    <VDivider />
    <VCardText>
      <VForm v-if="controlOrganism.value" ref="formRef" class="overflow-hidden">
        <VRow class="flex-column mt-1">
          <VCol>
            <VTextField v-model="controlOrganism.value!.socialReason" label="Raison Sociale" :rules="[requiredRule]" />
          </VCol>
          <VCol>
            <NjSiretInput v-model="controlOrganism.value!.siren" label="SIREN" type="siren" required />
          </VCol>
          <VCol>
            <VSelect
              v-model="controlOrganism.value!.controlType"
              label="Type de contrôle"
              :items="controlTypeLabel"
              item-value="value"
              item-title="label"
              :rules="controlOrganism.value?.certified ? [requiredRule] : []"
            />
          </VCol>
          <VCol>
            <VTextField
              v-model="controlOrganism.value!.address"
              label="Rue"
              :rules="controlOrganism.value?.certified ? [requiredRule] : []"
            />
          </VCol>
          <VCol>
            <VTextField
              v-model="controlOrganism.value!.postalCode"
              label="Code postal"
              :rules="controlOrganism.value?.certified ? [requiredRule, codePostalRule] : []"
            />
          </VCol>
          <VCol>
            <VTextField
              v-model="controlOrganism.value!.city"
              label="Ville"
              :rules="controlOrganism.value?.certified ? [requiredRule] : []"
            />
          </VCol>
          <VCol v-show="gouvSuggestedAdressRef?.displaySuggestion">
            <GouvSuggestedAdress
              ref="gouvSuggestedAdressRef"
              v-model:city="controlOrganism.value!.city"
              v-model:postal-code="controlOrganism.value!.postalCode"
              v-model:street="controlOrganism.value!.address"
            />
          </VCol>
          <VCol>
            <VTextField v-model="controlOrganism.value!.contactName" label="Nom" />
          </VCol>
          <VCol>
            <NjPhoneInput
              v-model="controlOrganism.value!.contactPhoneNumber"
              label="Numéro de téléphone"
              :included-region-codes="['FR']"
            />
          </VCol>
          <VCol>
            <VTextField
              v-model="controlOrganism.value!.contactEmail"
              label="Email"
              :rules="controlOrganism.value?.certified ? [emptyOrEmail] : []"
            />
          </VCol>
          <VCol>
            <NjDatePicker v-model="controlOrganism.value!.startDate" label="Date de début" :rules="[requiredRule]" />
          </VCol>
          <VCol>
            <NjDatePicker v-model="controlOrganism.value!.endDate" label="Date de fin" :rules="[requiredRule]" />
          </VCol>
          <VCol v-if="userHasRole(userStore.currentUser, 'SIEGE', 'INSTRUCTEUR', 'ADMIN', 'ADMIN_PLUS')">
            <VSwitch v-model="controlOrganism.value!.certified" label="Certifié" />
          </VCol>
          <VDivider />
        </VRow>
        <VRow class="content-layout__footer">
          <VSpacer />
          <VCol class="flex-grow-0">
            <NjBtn variant="outlined" @click="cancel"> Annuler </NjBtn>
          </VCol>
          <VCol class="flex-grow-0">
            <NjBtn @click="save">Sauvegarder</NjBtn>
          </VCol>
        </VRow>
        <ConfirmUnsavedDataDialog v-model:unsaved-data-dialog="unsavedDataDialog" @save="save" />
      </VForm>
    </VCardText>
  </VCard>
</template>
<script lang="ts" setup>
import { controlOrganismApi } from '@/api/controlOrganism'
import type GouvSuggestedAdress from '@/components/GouvSuggestedAdress.vue'
import NjDatePicker from '@/components/NjDatePicker.vue'
import { useSnackbarStore } from '@/stores/snackbar'
import { useUserStore } from '@/stores/user'
import { controlTypeLabel, makeEmptyControlOrganism, type ControlOrganism } from '@/types/controlOrganism'
import { codePostalRule, emptyOrEmail, requiredRule } from '@/types/rule'
import { userHasRole } from '@/types/user'
import type { PropType } from 'vue'
import {
  VCard,
  VCardText,
  VCardTitle,
  VCol,
  VDivider,
  VRow,
  VSelect,
  VSpacer,
  VSwitch,
  VTextField,
} from 'vuetify/components'
import { VForm } from 'vuetify/components/VForm'

const props = defineProps({
  modelValue: {
    type: Boolean,
  },
  mode: {
    type: String,
    default: 'create',
  },
  id: {
    type: Number,
    default: undefined,
  },
  cancel: {
    type: Function as PropType<(item: any) => void>,
    default: () => {},
  },
  afterSuccess: {
    type: Function as PropType<() => void>,
    default: () => {},
  },
  updatedControlOrganism: {
    type: Object as PropType<ControlOrganism | null>,
  },
})

const emit = defineEmits(['update:updatedControlOrganism'])

const controlOrganism = ref(emptyValue<ControlOrganism>())
const snackbarStore = useSnackbarStore()
const userStore = useUserStore()
const { unsavedDataDialog, failedSave, succeedSave } = useUnsavedData(controlOrganism)

const formRef = ref<VForm | null>(null)

const save = async () => {
  if ((await formRef.value!.validate()).valid) {
    if (props.id && props.mode !== 'duplicate') {
      controlOrganismApi
        .update(props.id, controlOrganism.value.value!)
        .then((response) => {
          emit('update:updatedControlOrganism', response.data)
          controlOrganism.value.value = response.data
          snackbarStore.setSuccess('Organisme de contrôle bien mis à jour')
          succeedSave()
          props.afterSuccess()
        })
        .catch(async (err) => {
          snackbarStore.setError(
            await handleAxiosException(err, undefined, {
              defaultMessage: "Une erreur est survenue lors de la création de l'organisme de contrôle",
            })
          )
          failedSave()
        })
    } else {
      controlOrganismApi
        .create(controlOrganism.value.value!)
        .then(() => {
          snackbarStore.setSuccess('Organisme de contrôle bien créé')
          props.afterSuccess()
        })
        .catch(async (error) =>
          snackbarStore.setError(
            await handleAxiosException(error, undefined, {
              defaultMessage: "Une erreur est survenue lors de la création de l'organisme de contrôle",
            })
          )
        )
    }
  }
}

// chargement
watch(
  () => props.id,
  (id) => {
    if (id) {
      handleAxiosPromise(controlOrganism, controlOrganismApi.getOne(id), (v) => {
        controlOrganism.value.value = {
          ...v.data,
          siren: props.mode === 'duplicate' ? '' : v.data.siren,
          certified: false,
        }
        succeedSave()
      })
    } else {
      controlOrganism.value.value = makeEmptyControlOrganism()
    }
  },
  {
    immediate: true,
  }
)

const gouvSuggestedAdressRef = ref<typeof GouvSuggestedAdress | null>(null)
</script>
