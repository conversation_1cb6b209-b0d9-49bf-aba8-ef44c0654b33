<template>
  <NjPage title expend-body v-bind="$attrs">
    <template #header-actions>
      <NjBtn v-if="!isCertynergie()" @click="showCompanyDialog = true">Gestion des sociétés</NjBtn>
    </template>
    <template #subtitle>
      <VRow>
        <VCol cols="3">
          <SearchInput
            label="Recherche"
            :model-value="pageFilter.search"
            @update:model-value="updateFilterByFieldname('search', $event)"
          />
        </VCol>
        <VCol cols="3">
          <NjSwitch
            label="Organisations valides"
            :model-value="pageFilter.enabled"
            inline
            :false-value="null"
            @update:model-value="updateFilterByFieldname('enabled', $event)"
          />
        </VCol>
        <VCol cols="3">
          <NjSwitch
            label="Organisations visibles"
            :model-value="pageFilter.visible"
            inline
            :false-value="null"
            @update:model-value="updateFilterByFieldname('visible', $event)"
          />
        </VCol>
        <VCol cols="3">
          <RemoteAutoComplete
            label="Territoire"
            :model-value="pageFilter.territoryIds?.[0]"
            :query-for-one="findOneTerritory"
            :query-for-all="findAllTerritory"
            :item-title="formatTerritory"
            item-value="id"
            infinite-scroll
            clearable
            @update:model-value="updateFilterByFieldname('territoryIds', $event ? [parseInt($event)] : undefined)"
          />
        </VCol>
      </VRow>
    </template>
    <template #body>
      <NjDataTable
        :headers="headers"
        :pageable="pageable"
        :page="data.value"
        :selections="selection"
        :on-click-row="clickRow"
        :disabled-row="disableRow"
        fixed
        class="w-100"
        @update:pageable="updatePageable"
      >
        <template #[`item.name`]="{ item }">
          <span style="white-space: nowrap" :style="{ 'margin-inline-start': 24 * item.level + 'px' }">{{
            item.name
          }}</span>
        </template>

        <template #[`item.entityDetails.visible`]="{ item }">
          <VIcon v-if="item.entityDetails.visible">mdi-eye-outline</VIcon>
          <VIcon v-else color="grey">mdi-eye-off</VIcon>
        </template>
      </NjDataTable>
    </template>
  </NjPage>
  <VNavigationDrawer location="end" :model-value="drawer.active" width="500" disable-resize-watcher>
    <EntityCardForm
      :id="drawer.id"
      ref="entityCardFormRef"
      @click:parent-id="
        (entityId) => {
          selection = [{ ...makeEmptyEntity(), id: entityId }]
          drawer.id = entityId!
        }
      "
      @saved="reload"
      @close="drawer.active = false"
    />
  </VNavigationDrawer>
  <CompanyDialog v-model="showCompanyDialog" @reload="reload" />
</template>
<script lang="ts" setup>
import { type EntityFilter } from '@/api/entity'
import NjPage from '@/components/NjPage.vue'
import type { DataTableHeader } from '@/components/okta/NjDataTable.type'
import NjDataTable from '@/components/okta/NjDataTable.vue'
import { entityLevelLabels, isSubsidiary, makeEmptyEntity, type Entity } from '@/types/entity'
import type { Pageable } from '@/types/pagination'
import { formatTerritory } from '@/types/territory'
import { displayFullnameUser } from '@/types/user'
import { VNavigationDrawer } from 'vuetify/components'
import CompanyDialog from './CompanyDialog.vue'
import EntityCardForm from './EntityCardForm.vue'
import { isCertynergie } from '@/types/debug'

const selection = ref<Entity[]>([])

const { data, pageable, updatePageable, reload, pageFilter, updateFilterByFieldname } = usePaginationInQuery<
  Entity,
  EntityFilter
>((filter, pageable) => entityApi.getAll(filter, pageable), {
  defaultPageablePartial: {
    page: 0,
    size: 20,
    sort: ['navFullId'],
  },
  saveFiltersName: 'EntityAllView',
  queryToFilterMapper(query) {
    return {
      ...query,
      territoryIds: mapQueryToTable(query.territoryIds, true),
    }
  },
})

const headers: DataTableHeader<Entity>[] = [
  {
    title: 'Désignation',
    value: 'name',
  },
  {
    title: 'Charge pôle CEE unitaire',
    value: 'entityDetails.effectiveFee',
    cellClass: 'text-right justify-end',
    formater: (item) => formatNumber(item.entityDetails.effectiveFee) + ' €/mwhC',
  },
  {
    title: 'Niveau',
    value: 'level',
    formater: (_, value) => entityLevelLabels[value],
  },
  {
    title: 'Code',
    value: 'id',
  },
  {
    title: 'Hiérarchie',
    value: 'navFullId',
  },
  {
    title: 'Valide',
    value: 'enabled',
    formater: (_, value) => (value ? 'OUI' : 'NON'),
  },
  {
    title: 'Visible',
    value: 'entityDetails.visible',
    formater: (_, value) => (value ? 'OUI' : 'NON'),
  },
  {
    title: 'SIRET',
    value: 'siret',
  },
  {
    title: 'Instructeur',
    value: 'entityDetails.instructor',
    formater: (_, value) => displayFullnameUser(value),
    sortable: false,
  },
  {
    title: 'Mail DAF',
    value: 'entityDetails.dafMail',
  },
  {
    title: 'Représentant',
    value: 'entityDetails.lastNameAgent',
  },
  {
    title: 'Référent',
    value: 'entityDetails.effectiveTerritoryReferent',
    formater: (_, value) => displayFullnameUser(value),
    sortable: false,
  },
  {
    title: 'Filiale',
    value: 'siret',
    formater: (item: Entity) => (isSubsidiary(item) ? 'Oui' : 'Non'),
    sortable: false,
  },
  {
    title: 'Territoire',
    value: 'territory.description',
  },
  {
    title: 'Capital',
    value: 'company.capital',
    formater: (_, value) => formatPriceNumber(value),
    cellClass: 'text-right justify-end',
  },
  {
    title: 'Régime communautaire',
    value: 'company.frRegimeCommunautaire',
  },
  {
    title: 'Métier',
    value: 'natureCode',
    formater: (item) => (item.natureCode ? `(${item.natureCode}) ${item.natureDescription}` : ''),
  },
]

const disableRow = (orga: Entity) => !orga.enabled && orga.entityDetails.visible

const showCompanyDialog = ref(false)

const drawer = ref({
  active: false,
  id: '',
})
const clickRow = (item: Entity) => {
  entityCardFormRef.value!.check(() => {
    if (!drawer.value.active || item.id !== drawer.value.id) {
      drawer.value = {
        active: true,
        id: item.id,
      }
    } else {
      drawer.value.active = false
    }
  })
}

const findOneTerritory = (v: unknown) => territoryApi.findOne(v as number)
const findAllTerritory = (v: string, pageable: Pageable) =>
  territoryApi.findAll({ search: v }, { ...pageable, sort: ['description,ASC'] })

const entityCardFormRef = ref<typeof EntityCardForm | null>(null)
</script>
