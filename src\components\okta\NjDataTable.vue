<template>
  <div
    class="nj-data-table nj-data-table__wrapper"
    :class="{ 'content-layout': fixed, 'nj-data-table--fixed': fixed, 'nj-data-table--column-fixed': columnFixed }"
    style="position: relative"
  >
    <VProgressLinear v-if="loading" indeterminate color="primary" style="position: absolute; top: 0; z-index: 100" />
    <VTable
      class="njdatatable__border d-body"
      :class="{ 'content-layout__main': fixed, 'content-layout': fixed, 'text-no-wrap': !wrapText }"
      v-bind="$attrs"
    >
      <thead :class="{ 'content-layout__header': fixed }">
        <tr>
          <th v-if="status" class="px-0"></th>
          <th v-if="checkboxes" style="color: currentColor">
            <VCheckboxBtn style="vertical-align: middle" @change="selectAll" />
          </th>
          <th
            v-for="(h, index) in headers"
            :key="h.key ?? h.value"
            v-ripple="h.sortable ?? true"
            :class="{ clickable: h.sortable ?? true }"
            :style="{ width: h.width }"
            @click=";(h.sortable ?? true) && onClickHeader(h.value)"
          >
            <NjIconBtn v-if="download && index == 0" icon="mdi-download" @click.stop="onDownload()"></NjIconBtn>
            {{ h.title }}
            <VIcon v-show="options.sortBy[0] === h.value && options.sortDesc[0]"> mdi-arrow-down </VIcon>
            <VIcon v-show="options.sortBy[0] === h.value && !options.sortDesc[0]">mdi-arrow-up</VIcon>
          </th>
        </tr>
      </thead>
      <tbody :class="{ 'content-layout__main': fixed }">
        <!-- TODO a déplacer ailleur la logique du disable row -->
        <!-- eslint-disable-next-line vue/require-v-for-key -->
        <tr
          v-for="(i, index) in page?.content ?? items"
          :class="{
            'disabled-row': disabledRow(i),
            'row-clickable': hasRowClickable,
            'nj-data-table__row--selectable':
              (selectable && !checkboxes && selectedLines[index]) || clickedLines[index],
          }"
          @click="clickRow(i)"
        >
          <td v-if="status" class="px-0" :style="{ 'background-color': status(i, index) }">
            <div style="width: 3px"></div>
          </td>
          <td v-if="checkboxes">
            <VCheckboxBtn
              v-show="!hideCheckboxes(i)"
              :model-value="selectedLines[index]"
              style="vertical-align: middle"
              @click.stop="selectionItem(i)"
            />
          </td>
          <td
            v-for="h in headers"
            :key="h.key ?? h.value"
            :class="{
              'p-relative': !!(h.to ?? toRows[index]),
            }"
            :style="{ width: h.width }"
            @click.capture="clickCell(h, i[h.value], i, $event)"
          >
            <component
              :is="
                (h.to ?? toRows[index]) && !disabledRow(i) && !!getItemValue(i, h.value)?.toString()
                  ? RouterLink
                  : 'div'
              "
              v-ripple="!!(h.to ?? toRows[index]) && !disabledRow(i) && !!getItemValue(i, h.value)?.toString()"
              :to="h.to?.(i) ?? toRows?.[index] ?? ''"
              :class="{
                'a-in-td': !!(h.to ?? toRows?.[index]) && !disabledRow(i),
                'w-100': columnFixed,
                ...handleClasses(h.cellClass),
              }"
            >
              <slot :item="i" :index="index" :name="'item.' + (h.key ?? h.value)" :header-title="h.title">
                <AutoShrinkText
                  :text="h.formater ? h.formater(i, getItemValue(i, h.value)) : getItemValue(i, h.value)?.toString()"
                  :max-length="h.maxLength"
                />
              </slot>
            </component>
          </td>
        </tr>
      </tbody>
    </VTable>

    <VContainer
      v-if="page !== undefined"
      class="px-2 py-1 nj-data-table__footer"
      :class="{ 'content-layout__footer': fixed }"
      fluid
    >
      <VRow align="center" dense>
        <VCol v-if="!hideTotal" class="flex-grow-0 flex-basis-content">
          Nombre de données : {{ page.totalElements }}
          <template v-if="localSelections.length > 0">({{ localSelections.length }} éléments sélectionnées)</template>
        </VCol>
        <VCol class="flex-grow-0">
          <slot name="footer.prepend" />
        </VCol>
        <VSpacer />
        <VCol v-if="!hideElements" class="flex-grow-0 flex-basis-content text-no-wrap"> Eléments par page : </VCol>
        <VCol v-if="!hideElements" cols="2">
          <VSelect v-model="options.itemsPerPage" hide-details :items="pageSizes"></VSelect>
        </VCol>
        <VCol class="flex-grow-0">
          <VPagination
            density="compact"
            :length="page.totalPages"
            total-visible="3"
            rounded="0"
            :model-value="options.page + 1"
            color="primary"
            @update:model-value="options.page = $event - 1"
          />
        </VCol>
      </VRow>
    </VContainer>
  </div>
</template>

<script setup lang="ts" generic="T extends Record<string, any>">
import type { Page, Pageable } from '@/types/pagination'
import { clone, isArray, isEqual, reduce } from 'lodash'
import { computed, type PropType } from 'vue'
import { RouterLink, type RouteLocationRaw } from 'vue-router'
import { VSelect } from 'vuetify/components'
import type { DataOptions, DataTableHeader } from './NjDataTable.type'
import { useDialogStore } from '@/stores/dialog'
const props = defineProps({
  pageable: {
    type: Object as PropType<Pageable>,
  },
  page: {
    type: Object as PropType<Page<T>>,
  },
  items: {
    type: Object as PropType<T[]>,
  },
  headers: {
    default: () => [],
    type: Array as PropType<DataTableHeader[]>,
  },
  fixed: Boolean,
  fixedHeader: Boolean,
  fixedFooter: Boolean,
  columnFixed: Boolean,
  wrapText: Boolean,
  loading: {
    type: Boolean,
    default: false,
  },

  onClickRow: {
    type: Function as PropType<(item: T) => void>,
  },
  selections: {
    type: Array as PropType<T[]>,
  },
  clickedRow: {},
  disabledRow: {
    type: Function as PropType<(item: T) => boolean>,
    default: () => false,
  },
  multiSelection: Boolean,
  checkboxes: Boolean,
  toRow: {
    type: Function as PropType<(item: T) => RouteLocationRaw | undefined>,
  },

  hideTotal: Boolean,
  hideElements: Boolean,
  hideCheckboxes: {
    type: Function,
    default: () => false,
  },
  download: Function,
  status: {
    type: Function,
    default: undefined,
  },
  selectOnId: Boolean,
  pageSizes: {
    type: Array as PropType<number[]>,
    default: () => [10, 20, 50],
  },
})

const emit = defineEmits<{
  'update:pageable': [options: Pageable]
  'update:options': [options: DataOptions]
  'update:selections': [value: any[]]
}>()

const options = ref<DataOptions>({
  page: 0,
  itemsPerPage: 0,
  sortBy: [],
  sortDesc: [],
  groupBy: [],
  groupDesc: [],
  multiSort: false,
  mustSort: false,
})

watch(
  () => props.pageable,
  (v) => {
    const optionsTemp = {
      page: v?.page ?? 0,
      itemsPerPage: v?.size ?? 20,
      sortBy: v?.sort?.map((it) => it.split(',')[0]) ?? [],
      sortDesc: v?.sort?.map((it) => it.split(',')[1] === 'DESC') ?? [],
      groupBy: [],
      groupDesc: [],
      multiSort: false,
      mustSort: false,
    }
    if (!isEqual(options.value, optionsTemp)) {
      options.value = optionsTemp
    }
  },
  {
    immediate: true,
    deep: true,
  }
)

const selectable = computed(() => props.selections !== undefined)

watch(
  options,
  (v) => {
    emit('update:pageable', {
      page: v.page,
      size: v.itemsPerPage,
      sort: v.sortBy.map((it, i) => it + ',' + (v.sortDesc[i] ? 'DESC' : 'ASC')),
    })
  },
  {
    deep: true,
  }
)

function onClickHeader(valueHeader: string) {
  const oldSortBy = options.value.sortBy[0]
  const sameColumnSort = oldSortBy === valueHeader

  if (sameColumnSort && options.value.sortDesc[0]) {
    options.value = {
      ...options.value,
      sortBy: [],
      sortDesc: [],
    }
  } else {
    options.value = {
      ...options.value,
      sortBy: [valueHeader],
      sortDesc: [oldSortBy === valueHeader],
    }
  }
}

const getItemValue = (item: any, value: string) => {
  const parsedValue = value.split('.')
  let result = item
  for (const property of parsedValue) {
    result = result?.[property]
  }
  return result
}

const hasRowClickable = computed(() => !!props.onClickRow)

const localSelections = ref<T[]>([]) as Ref<T[]>

watch(
  () => props.selections,
  (v) => {
    if (!isEqual(v, localSelections.value)) {
      localSelections.value = [...(v ?? [])]
    }
  },
  {
    immediate: true,
  }
)

const clickRow = (item: T) => {
  // if (props.disabledRow(item)) {
  //   return
  // }

  if (props.selections !== undefined && !props.checkboxes) {
    selectionItem(item)
  }
  props.onClickRow?.(item)
}
const selectionItem = (item: T) => {
  const index = localSelections.value.findIndex((it) =>
    props.selectOnId ? isEqual(it.id, item.id) : isEqual(it, item)
  )
  if (index >= 0) {
    localSelections.value.splice(index, 1)
  } else {
    if (props.multiSelection) {
      localSelections.value.push(item)
    } else {
      localSelections.value = [item]
    }
  }
  emit('update:selections', clone(localSelections.value))
}

const selectedLines = computed(() => {
  const selecteds: Record<number, boolean> = {}
  const items = props.page?.content ?? props.items
  localSelections.value.forEach((item) => {
    let index
    if (props.selectOnId) {
      index = items?.findIndex((it) => isEqual(it.id, item.id))
    } else {
      index = items?.findIndex((it) => isEqual(it, item))
    }
    selecteds[index ?? -1] = true
  })
  return selecteds
})
const clickedLines = computed(() => {
  const clickeds: Record<number, boolean> = {}
  const items = props.page?.content ?? props.items
  const index = items?.findIndex((it) =>
    props.selectOnId ? isEqual(it.id, (props.clickedRow as any)?.id) : isEqual(it, props.clickedRow)
  )
  clickeds[index ?? -1] = true
  return clickeds
})

const clickCell = (h: DataTableHeader<any>, columnValue: any, item: T, e: Event) => {
  if (h.onclick) {
    h.onclick(item, columnValue)
    e.preventDefault()
    e.stopPropagation()
    return
  }

  if (props.toRow) {
    clickRow(item)
    e.preventDefault()
    e.stopPropagation()
  }
}

const toRows = computed(function (): (RouteLocationRaw | undefined)[] {
  if (props.toRow) {
    return (props.page?.content ?? props.items)?.map((it) => props.toRow!(it)) ?? []
  } else {
    return []
  }
})

const selectAllCheckbox = ref(false)
const selectAll = () => {
  selectAllCheckbox.value = !selectAllCheckbox.value
  if (selectAllCheckbox.value) {
    ;(props.page?.content ?? props.items ?? []).forEach((item) => {
      const index = localSelections.value.findIndex((it) =>
        props.selectOnId ? isEqual(it.id, item.id) : isEqual(it, item)
      )
      if (props.hideCheckboxes(item)) {
        return
      }

      if (index < 0) {
        if (props.multiSelection) {
          localSelections.value.push(item)
        } else {
          localSelections.value = [item]
        }
      }
    })
  } else {
    ;(props.page?.content ?? props.items ?? []).forEach((item) => {
      const index = localSelections.value.findIndex((it) =>
        props.selectOnId ? isEqual(it.id, item.id) : isEqual(it, item)
      )
      if (index >= 0) {
        localSelections.value.splice(index, 1)
      }
    })
  }
  emit('update:selections', clone(localSelections.value))
}

const handleClasses = (
  v: string | string[] | Record<string, boolean> | undefined | null
): Record<string, boolean> | undefined => {
  let arr: string[] | undefined = undefined
  if (!v) {
    return undefined
  }
  if (typeof v === 'string') {
    arr = v.split(' ')
  } else if (isArray(v)) {
    arr = v
  }

  if (arr !== undefined) {
    return reduce(
      arr,
      (acc, name) => ({
        ...acc,
        [name]: true,
      }),
      {}
    )
  } else {
    return v as Record<string, boolean>
  }
}

const dialogStore = useDialogStore()

const onDownload = async () => {
  if (
    await dialogStore.addAlert({
      title: 'Téléchargement',
      message: `Attention vous allez télécharger des données sensibles et confidentielles, merci de suivre les règles suivantes :
                - Ces données ne doivent pas être partagées à l’extérieur de l’entreprise
                - Après utilisation pensez à supprimer le fichier Excel
                Merci de noter que tous les téléchargements réalisés sont historisés.
                `,
      positiveButton: 'Télécharger',
      negativeButton: 'Annuler',
      maxWidth: '480px',
    })
  ) {
    props.download!()
  }
}
</script>

<style lang="scss">
.nj-data-table {
  &__wrapper {
    background-color: white;
    border: 1px solid #ccc;
  }
  &__header {
    background-color: white;
  }
  &__footer {
    border-top: 1px solid #ccc;
  }
  tr > th {
    color: #60798b;
    font-weight: 700 !important;
    white-space: nowrap;
  }

  &--column-fixed {
    > .v-table > .v-table__wrapper > table {
      table-layout: fixed;
    }

    td {
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }

  &__row {
    &--selectable {
      > td {
        // border-top: 2px solid blue;
        // border-bottom: 2px solid blue;
        // margin-top: -2px;
        // margin-bottom: -2px;
        background-color: #0af5;
      }
    }
  }

  &--fixed thead th {
    position: sticky; /* make the table heads sticky */
    background-color: white;
    top: 0px; /* table head will be placed from the top of the table and sticks to it */
    z-index: 5;
  }

  th {
    white-space: nowrap;
  }
}
.disabled-row {
  background-color: #eee;
  color: lightslategrey;
}

.row-clickable {
  transition: background 0.3s;
  &:hover {
    background-color: rgb(235, 238, 241);
    cursor: pointer;

    .cell-clickable:hover {
      background-color: white;
    }
  }
}

.cell-clickable {
  transition: background 0.3s;
  background-position: center;

  &:hover {
    background-color: rgb(235, 238, 241);
    cursor: pointer;
  }
}

.a-in-td {
  width: calc(100% + 32px);
  height: 100%;
  margin: 0 -16px;
  padding: 0 16px;
  display: flex;
  align-items: center;
  color: inherit;
  text-decoration: inherit;
  // font-weight: 700;
}
</style>
