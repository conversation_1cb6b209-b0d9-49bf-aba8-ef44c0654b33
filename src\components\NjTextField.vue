<template>
  <div class="nj-text-field nj-field">
    <label>{{ $attrs.label }}</label>
    <VTextField
      v-bind="{ ...$attrs, label: undefined, class: undefined, style: undefined }"
      :class="fieldClass"
      :style="fieldStyle"
    >
      <template v-for="(_, slot) of $slots as {}" #[slot]="scope">
        <slot :name="slot" v-bind="scope as any" />
      </template>
      <template v-if="required" #prepend-inner>
        <div class="nj-field__required-prepend"><VIcon icon="mdi-asterisk" size="x-small" /></div>
      </template>
      <template v-else-if="recommended" #prepend-inner>
        <div class="nj-field__recommended-prepend">✦</div>
      </template>
      <template v-else #prepend-inner>
        <slot name="prepend-inner"></slot>
      </template>
    </VTextField>
  </div>
</template>

<script setup lang="ts">
import { VTextField } from 'vuetify/components'

defineProps<{
  required?: boolean
  recommended?: boolean
  fieldClass?: any
  fieldStyle?: any
}>()
// const props = defineProps<typeof VTextField['$props']>()
// props.
</script>

<style lang="scss">
.nj-field {
  .v-field--prepended {
    padding-inline-start: 0px;
  }

  &__required-prepend {
    background-color: #e7eefc;
    color: #007acd;
    height: 100%;
    font-weight: bold;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 8px 4px;
  }

  &__recommended-prepend {
    background-color: #f2ecf7;
    color: #744299;
    font-weight: bold;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 8px 4px;
  }
}
</style>
