import { useEventListener } from '@vueuse/core'
import type { AxiosPromise } from 'axios'
import {
  cloneDeep,
  debounce,
  isArray,
  isEmpty,
  isEqual,
  isNull,
  isString,
  isUndefined,
  mapValues,
  omit,
  omitBy,
  overSome,
  pick,
} from 'lodash'
import type { Ref } from 'vue'
import type { LocationQuery, LocationQueryValue } from 'vue-router'
import type { PromisableValue } from './promisableValue'
import { useSnackbarStore } from '@/stores/snackbar'
import { trace } from '@/stores/analytics'

export interface Pageable {
  page?: number
  size?: number
  sort?: string[]
}
export interface Page<T> {
  content: T[]
  sort: PageSort
  size: number
  number: number
  totalPages: number
  totalElements: number
  first: boolean
  last: boolean
  empty: boolean
}

interface PageSort {
  empty: boolean
  sorted: boolean
  unsorted: boolean
}

export function makeEmptyPage<T>(): Page<T> {
  return {
    content: [],
    first: true,
    last: true,
    empty: true,
    number: 0,
    size: 0,
    sort: {
      sorted: false,
      unsorted: true,
      empty: true,
    },
    totalElements: 0,
    totalPages: 0,
  }
}

export function makePage<T>(data: T[]): Page<T> {
  return {
    content: data,
    first: true,
    last: true,
    empty: false,
    number: 0,
    size: data.length,
    sort: {
      sorted: false,
      unsorted: true,
      empty: true,
    },
    totalElements: data.length,
    totalPages: 1,
  }
}

const indexesPageableQuery = ['page', 'size', 'sort']

function extractInt(v: string | null | (string | null)[], defaultValue: number): number {
  let value: string | undefined | null
  if (isArray(v)) {
    value = v[0]
  } else {
    value = v
  }

  if (value) {
    return parseInt(value, 10)
  } else {
    return defaultValue
  }
}

export function usePagination<T, F extends object = Record<string, unknown>>(
  loadDataTableItems: (filter: F, pageable: Pageable) => AxiosPromise<Page<T>>,
  defaultPageFilter: F = {} as F,
  defaultPageablePartial: Partial<Pageable> = { page: 0, size: 20 },
  options: {
    filterDebounceMilliseconds?: number
    lazyLoad?: boolean
    onErrorLoading?: ((e: unknown) => Promise<unknown>) | 'snackbar' | string
    saveFiltersName?: string
  } = {}
) {
  const snackbarStore = useSnackbarStore()

  const finalOptions = {
    filterDebounceMilliseconds: 300,
    ...options,
  }

  const defaultPageable: Pageable = {
    ...{
      page: 0,
      size: 50,
    },
    ...defaultPageablePartial,
  }
  const data: Ref<PromisableValue<Page<T>>> = ref<PromisableValue<Page<T>>>(succeedValue(makeEmptyPage<T>())) as Ref<
    PromisableValue<Page<T>>
  >

  const pageable = ref({ ...defaultPageable })
  const pageFilter = ref<F>({ ...defaultPageFilter }) as Ref<F>

  const updatePageable = (pPageable: Pageable) => {
    pageable.value = pPageable
  }

  const updateFilter = (pFilter: F) => {
    pageFilter.value = pFilter
  }

  function updateFilterByFieldname(k: keyof F, value: unknown) {
    updateFilter({
      ...toRaw(pageFilter.value),
      [k]: value,
    })
  }

  let lastRequestDateTime: Date | undefined = undefined

  const reload = () => {
    data.value.error = ''
    data.value.loading = true
    const myRequestDateTime = new Date()
    lastRequestDateTime = myRequestDateTime
    return loadDataTableItems(pageFilter.value, pageable.value)
      .then((v) => {
        if (lastRequestDateTime === myRequestDateTime) {
          data.value.loading = false
          data.value.value = v.data
        }
      })
      .catch(async (e) => {
        if (lastRequestDateTime === myRequestDateTime) {
          data.value.loading = false
          data.value.error = await handleAxiosException(e)
          if (options?.onErrorLoading === 'snackbar') {
            snackbarStore.setError("Une erreur s'est produite lors du chargement des donnéess")
          } else if (isString(options?.onErrorLoading)) {
            snackbarStore.setError(options.onErrorLoading)
          } else if (options?.onErrorLoading) {
            options.onErrorLoading(e)
          }
        }
      })
  }

  const privateDebouncedUpdateFilter = debounce(updateFilter, finalOptions.filterDebounceMilliseconds)
  const debouncedUpdateFilter = (filter: F) => {
    data.value.loading = true
    privateDebouncedUpdateFilter(filter)
  }

  const onChangePage = () => {
    if (options.saveFiltersName) {
      sessionStorage.setItem(
        'filter-' + options.saveFiltersName,
        JSON.stringify({ ...pageFilter.value, ...pageable.value })
      )
    }
    reload()
  }

  watch(
    pageFilter,
    () => {
      // console.debug('watch pageFilter')
      if (pageable.value.page !== 0) {
        pageable.value.page = 0
        return
      }
      onChangePage()
    },
    {
      deep: true,
    }
  )
  watch(
    pageable,
    () => {
      // console.debug('watch pageable', pageFilter.value)
      onChangePage()
    },
    {
      deep: true,
    }
  )

  onMounted(() => {
    if (options.lazyLoad !== true) {
      if (options.saveFiltersName) {
        // console.debug('aaaaa', sessionStorage.getItem('filter-' + options.saveFiltersName))
        const newValue = JSON.parse(sessionStorage.getItem('filter-' + options.saveFiltersName) ?? '{}')

        pageable.value = { ...defaultPageable, ...pick(newValue, 'page', 'size', 'sort') }
        pageFilter.value = { ...defaultPageFilter, ...omit(newValue, 'page', 'size', 'sort') }

        // console.debug('aaaaa', pageable.value, pageFilter.value)
      } else {
        reload()
      }
    }
  })

  return {
    pageable,
    pageFilter,
    data,
    updatePageable,
    reload,
    updateFilter,
    updateFilterByFieldname,
    debouncedUpdateFilter,
  }
}

export function usePaginationInQuery<T, F extends object = Record<string, unknown>>(
  loadDataTableItems: (filter: F, pageable: Pageable) => AxiosPromise<Page<T>>,
  options: {
    defaultPageFilter?: F
    defaultPageablePartial?: Partial<Pageable>
    queryToFilterMapper?: (query: LocationQuery) => Record<string, unknown>
    saveFiltersName?: string
    onErrorLoading?: (e: unknown) => Promise<unknown>
  } = {}
) {
  const snackbarStore = useSnackbarStore()

  const defaultPageable: Pageable = {
    ...{
      page: 0,
      size: 50,
    },
    ...options.defaultPageablePartial,
  }
  const route = useRoute()
  const router = useRouter()
  const data: Ref<PromisableValue<Page<T>>> = ref<PromisableValue<Page<T>>>(succeedValue(makeEmptyPage<T>())) as Ref<
    PromisableValue<Page<T>>
  >
  const pageable = ref({ ...defaultPageable })
  const pageFilter = ref({ ...options.defaultPageFilter }) as Ref<F>

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  function search(filter: F): void {
    updateFilter(filter)
  }

  function updatePageable(newPageable: Pageable): void {
    if (!isEqual(newPageable, pageable.value)) {
      // on créé ce système car sur le tri, la méthode est appelé 2 fois d'affilés
      updatePage(newPageable, pageFilter.value)
    }
  }

  function updateFilter(filter: F): void {
    if (!isEqual(pageFilter.value, filter)) {
      if (options.saveFiltersName) {
        trace('updateFilter', {
          name: options.saveFiltersName,
          filter: omitBy(toRaw(filter), (it) => isEmpty(it) || it == null || it === false),
        })
      }
      const newOptions = cloneDeep(pageable.value)
      newOptions.page = 0
      updatePage(newOptions, filter)
    }
  }

  function updateFilterByFieldname(k: keyof F, value: unknown) {
    updateFilter({
      ...pageFilter.value,
      [k]: value,
    })
  }

  const privateDebouncedUpdateFilter = debounce(updateFilter, 300)
  const debouncedUpdateFilter = (filter: F) => {
    data.value.loading = true
    privateDebouncedUpdateFilter(filter)
  }

  function updatePage(pageable: Pageable, filter: F): void {
    let query = {
      ...omitBy(filter, (v: any, k: keyof F) =>
        k in (options.defaultPageFilter ?? {})
          ? v === options.defaultPageFilter?.[k] && !isArray(v)
          : isNull(v) || (isString(v) && isEmpty(v))
      ),
      ...omitBy(
        pageable,
        overSome((_: unknown, k: keyof Pageable) => pageable[k] === defaultPageable[k], isNull, isUndefined)
      ),
    }
    query = { ...query, page: query.page !== undefined ? query.page + 1 : undefined }
    if (options.saveFiltersName) {
      sessionStorage.setItem('filter-' + options.saveFiltersName, JSON.stringify(query))
    }
    router.push({ query })
  }

  let lastRequestDateTime: Date | undefined = undefined
  const reload = () => {
    data.value.error = ''
    data.value.loading = true
    const myRequestDateTime = new Date()
    lastRequestDateTime = myRequestDateTime
    return loadDataTableItems(pageFilter.value, pageable.value)
      .then((v) => {
        if (lastRequestDateTime === myRequestDateTime) {
          data.value.loading = false
          data.value.value = v.data
          if (v.data.totalElements < (pageable.value.page ?? 0) * (pageable.value.size ?? 0)) {
            updatePageable({ ...pageable.value, page: 0 })
          }
        }
      })
      .catch(async (e) => {
        if (lastRequestDateTime === myRequestDateTime) {
          data.value.loading = false
          data.value.error = await handleAxiosException(e)
          ;(
            options.onErrorLoading ??
            (() => snackbarStore.setError("Une erreur s'est produite lors du chargement des donnéess"))
          )?.(e)
        }
      })
  }

  const queryToFilterMapper = options.queryToFilterMapper ?? ((query) => query)
  watch(
    [() => route.name, () => route.query],
    (v, oldV) => {
      const [newRouteName, newValue] = v
      const [oldRouteName, oldValue] = oldV
      const mustLoad = options.saveFiltersName && isEmpty(newValue)

      if (newRouteName !== oldRouteName && oldRouteName !== undefined) {
        // On quitte la page, pas besoin de recharger, et le chargement des filtres en mémoire peut perturber le résultat
        return
      }

      if (mustLoad) {
        const newValue = JSON.parse(sessionStorage.getItem('filter-' + options.saveFiltersName) ?? '{}')
        if (
          !isEmpty(
            omitBy(
              newValue,
              overSome(isUndefined, isNull, (v) => isArray(v) && isEmpty(v))
            )
          )
        ) {
          router.push({ query: newValue })
          return
        }
      }

      const dataPageable = { ...newValue, ...pageable.value }
      const filter = omitBy(queryToFilterMapper(omit(newValue, indexesPageableQuery)), isUndefined)

      if (
        oldValue === undefined ||
        !isEqual(dataPageable, pageable.value) ||
        !isEqual(filter, omitBy(pageFilter, overSome(isUndefined, isNull)))
      ) {
        pageFilter.value = {
          ...(options.defaultPageFilter as F),
          ...mapValues(unref(filter), (v: any) => (v === 'true' ? true : v === 'false' ? false : v)),
        }
        pageable.value = {
          page: extractInt(newValue.page, (defaultPageable.page ?? 0) + 1) - 1,
          size: extractInt(newValue.size, defaultPageable.size ?? 20),
          sort:
            typeof newValue.sort === 'string'
              ? ([newValue.sort] as string[])
              : ((newValue.sort as string[]) ?? defaultPageable.sort),
        }
        // this.displayFilters = isEmpty(pageFilter)
        data.value = loadingValue(data.value.value)
        reload()
      }
    },
    {
      immediate: true,
    }
  )

  useEventListener(document, 'refresh', reload)

  return {
    pageable,
    pageFilter,
    data,
    updatePageable,
    updateFilter,
    updateFilterByFieldname,
    reload,
    debouncedUpdateFilter,
  }
}

export const mapQueryToTable = (
  value: LocationQueryValue | LocationQueryValue[],
  isInt: boolean
): unknown[] | undefined =>
  isArray(value)
    ? isInt
      ? value.filter((it) => (it?.length ?? 0) > 0).map((v) => parseInt(v?.toString() ?? ''))
      : value
    : value == null
      ? undefined
      : !isNaN(parseInt(value?.toString() ?? ''))
        ? [parseInt(value?.toString() ?? '')]
        : isInt
          ? []
          : [value]
