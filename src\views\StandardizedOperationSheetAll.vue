<template>
  <VRow class="flex-column w-100">
    <VCol class="flex-grow-0">
      <VRow>
        <VCol cols="3">
          <SearchInput v-model="search" v-model:loading="data.loading" @update:model-value="updateSearch" />
        </VCol>
        <VCol cols="3">
          <VSelect
            class="bg-white"
            label="Utilisation bonus pour IND"
            :model-value="pageFilter.usageBonusIntSheetType"
            :items="usageBonusIntSheetTypes"
            clearable
            @update:model-value="updateFilterByFieldname('usageBonusIntSheetType', $event)"
          ></VSelect>
        </VCol>
        <VCol class="d-flex align-center" style="gap: 16px">
          <div><VIcon color="#FFF9C4" icon="mdi-card" /> Fiche utilisable pour secteur RES</div>
          <div><VIcon color="#B2EBF2" icon="mdi-card" /> Fiche utilisable pour secteur TERTIAIRE</div>
        </VCol>
      </VRow>
    </VCol>
    <VCol>
      <StandardizedOperationSheetDataTable
        :pageable="pageable"
        :page="data.value!"
        :headers="headers"
        :on-click-row="clickRow"
        :disabled-row="disabledRow"
        fixed
        @update:pageable="updatePageable"
      >
      </StandardizedOperationSheetDataTable>
      <CardDialog v-model="confirmationDialog.active" persistent max-width="640" title="Attention !">
        <RichTextDisplayValue :html="confirmationDialog.standardizedOperationSheet.warningText" />
        <template #actions>
          <NjBtn variant="outlined" @click="confirmationDialog.active = false">Annuler</NjBtn>
          <NjBtn color="primary" @click="confirmSelection">Confirmer</NjBtn>
        </template>
      </CardDialog>
    </VCol>
  </VRow>
</template>
<script lang="ts" setup>
import CardDialog from '@/components/CardDialog.vue'
import NjBtn from '@/components/NjBtn.vue'
import SearchInput from '@/components/SearchInput.vue'
import type { DataTableHeader } from '@/components/okta/NjDataTable.type'
import router from '@/router'
import {
  makeEmptyStandardizedOperationSheet,
  type StandardizedOperationSheet,
  type UsageBonusIntSheetType,
  usageBonusIntSheetTypes,
} from '@/types/calcul/standardizedOperationSheet'
import { usePaginationInQuery } from '@/types/pagination'
import { compareAsc } from 'date-fns'
import { cloneDeep, isArray } from 'lodash'
import StandardizedOperationSheetDataTable from './StandardizedOperationSheetDataTable.vue'

const props = defineProps({
  route: {
    type: String,
    required: true,
  },
})

const search = ref('')

const { data, pageFilter, pageable, updatePageable, updateFilter, updateFilterByFieldname } = usePaginationInQuery(
  (filter, pageable) => standardizedOperationSheetApi.findAll(pageable, filter),
  {
    defaultPageFilter: {
      visible: true,
      usageBonusIntSheetType: undefined as UsageBonusIntSheetType | undefined,
    },
    defaultPageablePartial: {
      sort: ['operationCode', 'startDate'],
      size: 100,
    },
    saveFiltersName: 'StandardizedOperationSheetAllView',
  }
)

const updateSearch = (v: string) => {
  data.value.loading = true
  const filter = {
    ...unref(pageFilter),
    search: v,
  }
  updateFilter(filter)
}

const headers: DataTableHeader[] = [
  {
    title: 'Opération',
    value: 'operationCode',
  },
  {
    title: 'Description',
    value: 'description',
  },
  {
    title: 'Début Validité',
    value: 'startDate',
    formater: (_, value) => formatHumanReadableLocalDate(value),
  },
  {
    title: 'Fin Validité',
    value: 'expirationDate',
    formater: (_, value) => formatHumanReadableLocalDate(value),
  },
  {
    title: 'Liens',
    value: 'action',
    sortable: false,
  },
  {
    title: 'RGE',
    value: 'rgeMandatory',
  },
  {
    title: 'Fonds chaleur',
    value: 'heatFund',
  },
  {
    title: 'Arrêté contrôle',
    value: 'controlOrderStartDate',
    formater: (_, value) => (value ? 'OUI' : 'NON'),
  },
  {
    title: 'Date de début',
    value: 'controlOrderStartDate',
    formater: (_, value) => formatHumanReadableLocalDate(value),
  },
  {
    title: 'Eligible au coup de pouce',
    value: 'boostEligibilities',
    formater: (_, value) => {
      let result = ''
      let index = 0
      for (const eligibility of value) {
        result += `engagée entre le ${formatHumanReadableLocalDate(
          eligibility.commitmentMinDate
        )} et le ${formatHumanReadableLocalDate(
          eligibility.commitmentMaxDate
        )} et se terminant avant le ${formatHumanReadableLocalDate(eligibility.endOperationMaxDate)}`
        if (index != value.length - 1) {
          result += ' ou '
        }
        index++
      }
      return result
    },
    sortable: false,
  },
]

const confirmationDialog = ref({
  active: false,
  standardizedOperationSheet: makeEmptyStandardizedOperationSheet(),
})
const confirmSelection = () => {
  confirmationDialog.value.active = false
  router.push({
    name: props.route,
    query: { standardizedOperationSheetId: confirmationDialog.value.standardizedOperationSheet.id },
  })
}
const clickRow = (standardizedOperationSheet: StandardizedOperationSheet) => {
  confirmationDialog.value.standardizedOperationSheet = standardizedOperationSheet
  if (!standardizedOperationSheet.warningText || standardizedOperationSheet.warningText === '<p></p>') {
    confirmSelection()
  } else {
    confirmationDialog.value.active = true
  }
}

const disabledRow = (standardizedOperationSheet: StandardizedOperationSheet): boolean =>
  !!standardizedOperationSheet.expirationDate &&
  compareAsc(new Date(standardizedOperationSheet.expirationDate), new Date()) === -1

watch(
  () => pageFilter.value,
  (v) => {
    const tempFilter = cloneDeep(v) as any
    Object.keys(tempFilter).forEach((key) => {
      if (isArray(tempFilter[key]) && (tempFilter[key] as any[]).length !== 0) {
        ;(tempFilter[key] as any[]).forEach((val: string, index: number) => {
          if (!Number.isNaN(parseInt(val))) {
            ;(tempFilter[key] as any[])[index] = parseInt(val)
          }
        })
      }
    })
    const filter = tempFilter as any
    if (search.value !== filter.search) {
      search.value = filter.search
    }
  },
  {
    immediate: true,
    deep: true,
  }
)
</script>
