import type { Page, Pageable } from '@/types/pagination'
import type { AxiosInstance, AxiosPromise } from 'axios'
import axiosInstance from '.'
import type { AnnualNotSatisfyingRate, AnnualNotSatisfyingRateRequest } from '@/types/annualNotSatisfyingRate'

class AnnualNotSatisfyingRateApi {
  public constructor(private axios: AxiosInstance) {}

  public update(request: AnnualNotSatisfyingRateRequest): AxiosPromise<AnnualNotSatisfyingRate> {
    return this.axios.put(`/annual_not_satifying_rates`, { ...request })
  }

  public findAll(pageable: Pageable): AxiosPromise<Page<AnnualNotSatisfyingRate>> {
    return this.axios.get('/annual_not_satifying_rates', {
      params: { ...pageable },
    })
  }

  public findByYear(year: number): AxiosPromise<AnnualNotSatisfyingRate> {
    return this.axios.get('/annual_not_satifying_rates/' + year)
  }

  public delete(years: number[]): AxiosPromise {
    return this.axios.delete('/annual_not_satifying_rates', {
      params: { years },
    })
  }
}

export const annualNotSatisfyingRateApi = new AnnualNotSatisfyingRateApi(axiosInstance)
