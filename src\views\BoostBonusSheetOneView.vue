<template>
  <NjPage
    :title="'Coup de pouce' + ($props.id ? ' pour ' + (boostBonusSheetRequest.operationCode || '...') : '')"
    :error-message="pageError"
    :can-go-back="{ name: 'BoostBonusSheetAllView' }"
  >
    <template #after-title>
      <VIcon v-show="certified" color="#28B750" icon="mdi-shield-check-outline" />
    </template>
    <template #header-actions>
      <VRow class="flex-nowrap" dense>
        <VCol>
          <NjBtn variant="outlined" color="error" :disabled="saving.loading"> Annuler </NjBtn>
        </VCol>
        <VCol>
          <NjBtn variant="outlined" @click="duplicate"> Dupliquer </NjBtn>
        </VCol>
        <VCol v-if="props.id">
          <NjBtn @click="editBoostBonusSheet">Enregistrer</NjBtn>
        </VCol>
        <VCol v-else>
          <NjBtn @click="createBoostBonusSheet">Créer</NjBtn>
        </VCol>
      </VRow>
    </template>
    <template #body>
      <VCard>
        <VForm ref="formRef">
          <NjExpansionPanel title="Détails coup de pouce">
            <VRow class="flex-column">
              <VCol>
                <VTextField
                  v-model="boostBonusSheetRequest.operationCode"
                  label="Fiche d'opération"
                  :rules="[requiredRule]"
                  :readonly="!editable"
                />
              </VCol>
              <VCol>
                <VSelect
                  v-model="boostBonusSheetRequest.energyInstalled"
                  label="Energie installée"
                  :items="energyInstalled"
                  clearable
                />
              </VCol>
              <VCol>
                <VTextField
                  v-model="boostBonusSheetRequest.name"
                  label="Nom du coup de Pouce"
                  :rules="[requiredRule]"
                />
              </VCol>
              <VCol>
                <VTextarea
                  v-model="boostBonusSheetRequest.description"
                  label="Description du coup de Pouce"
                  :rules="[requiredRule]"
                />
              </VCol>
              <VCol>
                <VTextarea v-model="boostBonusSheetRequest.otherRequirements" label="Exigence reglementaire" />
              </VCol>
              <VCol>
                <NjDatePicker
                  v-model="boostBonusSheetRequest.signingDate"
                  label="Date de signature de la charte"
                  :rules="[requiredRule]"
                  :readonly="!editable"
                />
              </VCol>
              <VCol>
                <NjDatePicker
                  v-model="boostBonusSheetRequest.commitmentMinDate"
                  label="Date minimum d'engagement"
                  :rules="[requiredRule]"
                  :readonly="!editable"
                />
              </VCol>
              <VCol>
                <NjDatePicker
                  v-model="boostBonusSheetRequest.commitmentMaxDate"
                  label="Date maximum d'engagement"
                  :rules="[requiredRule]"
                />
              </VCol>
              <VCol>
                <NjDatePicker
                  v-model="boostBonusSheetRequest.endOperationMaxDate"
                  label="Opération achevée avant"
                  :rules="[requiredRule]"
                />
              </VCol>
              <VCol>
                <VRow>
                  <VCol>
                    <VSwitch v-model="boostBonusSheetRequest.certified" ripple label="Certifié" :disabled="certified" />
                  </VCol>
                </VRow>
              </VCol>
            </VRow>
          </NjExpansionPanel>
          <CalculFormulaEditor v-model="boostBonusSheetRequest.calculFormula" :readonly="!editable" />
        </VForm>
      </VCard>
    </template>
  </NjPage>
</template>
<script setup lang="ts">
import type { BoostBonusSheetRequest } from '@/types/boostBonus'
import { makeEmptyBoostBonusSheetRequest, mapToBoostBonusSheetRequest, energyInstalled } from '@/types/boostBonus'
import { VForm } from 'vuetify/components/VForm'
import { requiredRule } from '@/types/rule'
import { boostBonusSheetApi } from '@/api/boostBonus'
import { useSnackbarStore } from '@/stores/snackbar'
import NjPage from '@/components/NjPage.vue'
import { VIcon, VSelect, VSwitch, VTextField } from 'vuetify/components'

const props = defineProps({
  id: {
    type: Number,
    default: undefined,
  },
})

const formRef = ref<VForm | null>(null)

const boostBonusSheetRequest = ref<BoostBonusSheetRequest>(makeEmptyBoostBonusSheetRequest())

const snackBarStore = useSnackbarStore()
const router = useRouter()

const pageError = ref('')

const createBoostBonusSheet = async function () {
  const validation = await formRef.value!.validate()
  if (validation.valid) {
    boostBonusSheetApi
      .create(boostBonusSheetRequest.value)
      .then(() => {
        snackBarStore.setSuccess('Le coup de pouce a bien été créé')
        router.push({ name: 'BoostBonusSheetAllView' })
      })
      .catch(async (e) => {
        pageError.value = await handleAxiosException(
          e,
          (r) => r.data?.message ?? "Le coup de pouce n'a pas pu être créé"
        )
      })
  }
}
const certified = ref(false)

const saving = ref(emptyValue<any>())
const editBoostBonusSheet = async function () {
  const validation = await formRef.value!.validate()
  if (validation.valid) {
    handleAxiosPromise(saving, boostBonusSheetApi.update(boostBonusSheetRequest.value, props.id!), {
      afterSuccess() {
        snackBarStore.setSuccess('Fiche Opération bien mis à jour')
        certified.value = saving.value.value.certified
      },
      afterError() {
        pageError.value = saving.value.error ?? "Le coup de pouce n'a pas pu être édité"
      },
    })
  }
}

const editable = computed(() => {
  // return !id
  return true
})

const duplicate = async () => {
  if ((await formRef.value!.validate()).valid) {
    handleAxiosPromise(
      saving,
      boostBonusSheetApi.create({
        ...boostBonusSheetRequest.value,
        name: boostBonusSheetRequest.value.name + ' - Copie',
        description: 'Copie de ' + boostBonusSheetRequest.value.name + '\n' + boostBonusSheetRequest.value.description,
        certified: false,
      }),
      {
        afterSuccess: (r) => {
          snackBarStore.setSuccess('Coup de pouce dupliqué avec success')
          router.push({
            name: 'BoostBonusSheetOneView',
            params: { id: r.data.id },
          })
        },
        afterError: () => {
          snackBarStore.setError(saving.value.error ?? '')
        },
      }
    )
  }
}

watch(
  () => props.id,
  (id) => {
    if (id) {
      boostBonusSheetApi
        .findOne(id)
        .then((r) => {
          boostBonusSheetRequest.value = mapToBoostBonusSheetRequest(r.data)
          certified.value = boostBonusSheetRequest.value.certified
        })
        .catch(async (e) => {
          pageError.value = await handleAxiosException(e)
        })
    }
  },
  {
    immediate: true,
  }
)
</script>
