import { isEqual, toString } from 'lodash'
import type { Property } from './property'

export interface BaseAddressable {
  additionalPostalAddress?: string
  postalCode: string
  city: string
  country: string | null
}

export interface AddressWithNumber extends BaseAddressable {
  streetName: string
  streetNumber: string | null | undefined
}

export interface Address extends BaseAddressable {
  street: string
}

export type Addressable = Address | AddressWithNumber

export function formatAddressable(address: Addressable | null | undefined): string {
  if (address == null) {
    return ''
  } else if ('street' in address) {
    return (
      address.street +
      (address.additionalPostalAddress ? '(' + address.additionalPostalAddress + ')' : '').trim() +
      (!address.postalCode && !address.city
        ? ''
        : ' - ' + (address.postalCode ?? '') + ' ' + (address.city ?? '')
      ).trim()
    )
  } else {
    return (
      (address.streetNumber ? address.streetNumber + ' ' : '') +
      address.streetName +
      (address.additionalPostalAddress ? '(' + address.additionalPostalAddress + ')' : '').trim() +
      (!address.postalCode && !address.city
        ? ''
        : ' - ' + (address.postalCode ?? '') + ' ' + (address.city ?? '')
      ).trim()
    )
  }
}

export const displayAddress = (property: Property | null, address: Address) =>
  !(
    (property === null &&
      address?.postalCode === '' &&
      address?.city === '' &&
      address?.street === '' &&
      address?.additionalPostalAddress === '') ||
    !(
      !isEqual(address?.postalCode, property?.postalCode) ||
      address?.city !== property?.city ||
      address?.street.trim() !== (toString(property?.streetNumber) + ' ' + toString(property?.streetName)).trim() ||
      address.additionalPostalAddress !== (property?.additionalPostalAddress ?? '')
    )
  )
