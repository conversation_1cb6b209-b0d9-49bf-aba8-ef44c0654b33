<template>
  <VInput :model-value="modelValue" :rules="rules" hide-details="auto">
    <VCard v-if="modelValue" class="w-100" :border="border">
      <template v-if="withTitle">
        <VCardTitle>
          <slot name="title"></slot>
        </VCardTitle>
        <VDivider />
      </template>

      <VCardText class="pa-2">
        <VRow class="flex-column" :no-gutters="!expanded">
          <VCol>
            <NjDisplayValue label="Raison sociale" :value="modelValue.socialReason" />
          </VCol>
          <VCol>
            <NjDisplayValue label="SIREN" :value="modelValue.siren" />
          </VCol>
          <VCol>
            <NjDisplayValue
              label="Adresse"
              :value="`${modelValue.address}, ${modelValue.postalCode}, ${modelValue.city}`"
            />
          </VCol>
          <VCol>
            <NjDisplayValue label="Type de contrôle" :value="getControlTypeLabel(modelValue.controlType)" />
          </VCol>
          <VCol>
            <NjDisplayValue label="Nom" :value="modelValue.contactName" />
          </VCol>
          <VCol>
            <NjDisplayValue label="Numéro de téléphone" :value="modelValue.contactPhoneNumber" />
          </VCol>
          <VCol>
            <NjDisplayValue label="Email" :value="modelValue.contactEmail" />
          </VCol>
          <VCol v-if="withHistory">
            <NjExpansionPanel title="Traçabilité" :loading-value="histories.loading">
              <VProgressCircular v-show="histories.loading" indeterminate size="24" color="primary" />
              <VRow dense class="flex-column">
                <VCol v-for="history in histories.value?.content" :key="history.uuid">
                  <HistoryCard :display-properties="controlOrganismHistoryDisplayProperties" :model-value="history" />
                </VCol>
                <VCol v-if="!histories.value?.totalElements">
                  <i>Aucun historique</i>
                </VCol>
              </VRow>
            </NjExpansionPanel>
          </VCol>
        </VRow>
      </VCardText>
      <template v-if="withActions">
        <VDivider />
        <VCardActions>
          <slot name="actions"></slot>
        </VCardActions>
      </template>
    </VCard>
    <div v-else>
      <i>Aucun organisme de contrôle sélectionné</i>
    </div>
  </VInput>
</template>
<script lang="ts" setup>
import { type ControlOrganism, getControlTypeLabel } from '@/types/controlOrganism'
import { type ControlOrganismHistory, controlOrganismHistoryDisplayProperties } from '@/types/history'
import type { Page } from '@/types/pagination'
import type { ValidationRule } from '@/types/rule'
import type { PropType } from 'vue'
import type { VCardActions, VDivider } from 'vuetify/components'
import { controlOrganismHistoryApi } from '@/api/controlOrganismHistory.'

const props = defineProps({
  modelValue: {
    type: Object as PropType<ControlOrganism | null>,
  },
  rules: {
    type: Array as PropType<Array<ValidationRule>>,
  },
  expanded: Boolean,
  border: {
    type: Boolean,
    default: true,
  },
  withTitle: Boolean,
  withActions: Boolean,
  withHistory: Boolean,
})

const histories = ref(emptyValue<Page<ControlOrganismHistory>>())

watch(
  [() => props.withHistory, () => props.modelValue],
  (v) => {
    histories.value = emptyValue<Page<ControlOrganismHistory>>()
    if (v[0] && v[1]) {
      handleAxiosPromise(
        histories,
        controlOrganismHistoryApi.findAll(
          { size: 1000, sort: ['creationDateTime,DESC'] },
          { controlOrganismId: v[1].id }
        )
      )
    }
  },
  {
    immediate: true,
  }
)
</script>
