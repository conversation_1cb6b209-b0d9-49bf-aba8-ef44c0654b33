import type { UserTablePersonalization, UserTablePersonalizationRequest } from '@/types/userTablePersonalization'
import type { AxiosInstance, AxiosPromise } from 'axios'
import axiosInstance from '.'

class UserTablePersonalizationApi {
  public constructor(private axios: AxiosInstance) {}

  private baseUri = '/user_table_personalizations'

  public save(request: UserTablePersonalizationRequest): AxiosPromise<UserTablePersonalization> {
    return this.axios.put(this.baseUri, request)
  }

  public getOne(tableName: string): AxiosPromise<UserTablePersonalization> {
    return this.axios.get(this.baseUri + '/me/' + tableName)
  }
}

export const userTablePersonalizationApi = new UserTablePersonalizationApi(axiosInstance)
