import type { AxiosInstance, AxiosPromise } from 'axios'
import axiosInstance from '.'
import type { ResponseHandler } from '@/types/responseHandler'

const uri = '/response_handlers'
class ResponseHandlerApi {
  public constructor(private axios: AxiosInstance) {}

  public findOne(uuid: string): AxiosPromise<ResponseHandler> {
    return this.axios.get(uri + '/' + uuid)
  }

  public download(uuid: string) {
    return this.axios.get(uri + '/' + uuid + '/file', {
      responseType: 'blob',
    })
  }
}

export const responseHandlerApi = new ResponseHandlerApi(axiosInstance)
