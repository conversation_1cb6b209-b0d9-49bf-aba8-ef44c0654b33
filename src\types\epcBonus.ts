import type { LocalDate } from './date'
import type { FormulaMappingTable } from './calcul/mappingTable'
import type { ParameterFormula } from './calcul/parameterFormula'
import type { Historisable } from './historisation'

export interface EpcBonusSheet extends Partial<Historisable> {
  id: number
  name: string
  targets: string
  startDate: LocalDate
  endDate: LocalDate
  minDuration?: number
  minEfficiency?: number
  parameters: ParameterFormula[]
  mappingTables: FormulaMappingTable[]
  certified: boolean
}

export function makeEmptyEpcBonusSheet(): EpcBonusSheet {
  return {
    id: 0,
    name: '',
    targets: '',
    startDate: '',
    endDate: '',
    certified: false,
    parameters: [
      {
        id: 'duree',
        label: 'Durée',
        suffix: '',
        type: 'CHOICE',
        data: null,
        emmyParameterId: 0,
        emmyMappedValues: '',
        localId: 0,
        conditionalFormula: '',
        computedFormula: '',
        sharedParameter: false,
        optional: false,
      },
      {
        id: 'eff',
        label: "Niveau d'économie d'énergie finale",
        suffix: '%',
        type: 'NUMBER',
        data: null,
        emmyParameterId: 0,
        emmyMappedValues: '',
        localId: 0,
        conditionalFormula: '',
        computedFormula: '',
        sharedParameter: false,
        optional: false,
      },
      {
        id: 'eff_export',
        label: "Niveau d'économie d'énergie finale Export",
        suffix: '',
        type: 'NUMBER',
        data: null,
        emmyParameterId: 0,
        emmyMappedValues: '',
        localId: 0,
        conditionalFormula: '',
        computedFormula: '[eff] / 100',
        sharedParameter: false,
        optional: false,
      },
    ],
    mappingTables: [
      {
        id: 'Formules',
        paramColumns: [],
        data: [],
      },
    ],
  }
}
