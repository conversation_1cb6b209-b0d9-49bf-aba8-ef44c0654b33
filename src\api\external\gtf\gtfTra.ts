import type { Pageable } from '@/types/pagination'
import type { AxiosInstance, AxiosPromise } from 'axios'
import { gtfAxiosInstance } from '.'
import { mapPageableToOrderByGtfPageable, type GtfPage, type OrdsQueryParams, mapPageableToGtfPageable } from './type'

const baseUrl = import.meta.env.VITE_API_GTF_WORKS
const prefixApiUrl = 'tra/api/v3/'

export interface GtfTraWorkDto {
  id: number
  id_string: string
  type_service_id: string
  property_id: number
  property_code: number
  property_name: string
  name: string
  works_site_track_number: any
  works_site_track_name: any
  works_site_additionnal_address: any
  works_site_postal_code: any
  works_site_city: any
  works_site_country: any
  closure_date: any
  cancellation_date: any
  // address_type: string
  // building?: string
  // road_number: string
  // road_name: string
  // additional_address: string
  // postal_code?: number
  // city: string
  // country: string
  // latitude?: number
  // longitude?: number
  // code: string
  // name: string
  // short_name: string
  // enabled: 0 | 1
  // parent_entity_id: string
  entity_nav_full_id: string
  // business_entity_id: string
  closed: 'O' | 'N'
}

export interface GtfTraWorkFilter {
  search?: string
  propertyCode?: string
  ids?: number[]
  entityNavFullIds?: string[]
  closed?: boolean
  workType?: string
}

class GtfTraApi {
  public constructor(private axios: AxiosInstance) {}

  public getAllWorks(filter: GtfTraWorkFilter, pageable: Pageable): AxiosPromise<GtfPage<GtfTraWorkDto>> {
    const ordsFilter: OrdsQueryParams<GtfTraWorkDto> = {
      q: {},
    }
    if (filter.workType != null) {
      ordsFilter.q.type_service_id = {
        $instr: filter.workType,
      }
    }

    if (filter.propertyCode) {
      ordsFilter.q.property_code = {
        $eq: filter.propertyCode.trim(),
      }
    }
    if (filter.entityNavFullIds && filter.entityNavFullIds.length > 0) {
      ordsFilter.q['entity_nav_full_id'] = {
        $or: filter.entityNavFullIds.map((it) => ({ $instr: it })),
      }
    }
    if (filter.ids) {
      ordsFilter.q.id = {
        $or: filter.ids.map((it) => ({
          $eq: it,
        })),
      }
    }

    if (filter.closed != null) {
      ordsFilter.q.closed = filter.closed ? 'O' : 'N'
    }

    if (filter.search) {
      ordsFilter.q.$or = [
        {
          id_string: {
            $instr: filter.search.trim(),
          },
        },
        {
          name: {
            $instr: filter.search.trim(),
          },
        },
        {
          property_name: {
            $instr: filter.search.trim(),
          },
        },
      ]
    }

    ordsFilter.q.$orderby = mapPageableToOrderByGtfPageable(pageable.sort)
    return this.axios.get(baseUrl + prefixApiUrl + 'works_lite', {
      params: { q: JSON.stringify(ordsFilter.q), ...mapPageableToGtfPageable(pageable) },
    })
  }

  public getOneProperty(id: number): AxiosPromise<GtfTraWorkDto> {
    return this.axios.get(prefixApiUrl + 'works/' + id)
  }
}

export const gtfTraApi = new GtfTraApi(gtfAxiosInstance)
