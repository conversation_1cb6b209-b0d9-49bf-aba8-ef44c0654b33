<template>
  <div>
    <VCard elevation="0" border>
      <VCardTitle>Base</VCardTitle>
      <NjDataTable :items="items" :headers="headers" />
    </VCard>

    <VCard elevation="0" border>
      <VCardTitle>Base with Selection - {{ selectedItems.map((it) => it.index) }}</VCardTitle>
      <NjDataTable v-model:selections="selectedItems" :items="items" :headers="headers" />
    </VCard>
  </div>
</template>

<script setup lang="ts">
import type { DataTableHeader } from '@/components/okta/NjDataTable.type'
import NjDataTable from '@/components/okta/NjDataTable.vue'

const selectedItems = ref<any[]>([])

const headers: DataTableHeader[] = [
  {
    title: 'GUID',
    value: 'guid',
  },
  {
    title: 'Nom complet',
    value: 'name',
  },
  {
    title: 'Sexe',
    value: 'gender',
  },
  {
    title: 'Solde',
    value: 'balance',
  },
]

const items = [
  {
    _id: '642c3e5a4865915893b5280a',
    index: 0,
    guid: '103513bd-49bf-4fc3-b537-f8494098af47',
    isActive: false,
    balance: '$1,317.07',
    age: 31,
    eyeColor: 'green',
    name: 'Penny Justice',
    gender: 'female',
    company: 'ECOSYS',
    email: '<EMAIL>',
    about:
      'Dolore labore nostrud id minim excepteur cupidatat velit aute voluptate tempor occaecat voluptate. Velit ullamco sit culpa minim reprehenderit occaecat in duis incididunt sunt est do. Culpa id proident occaecat non irure commodo nostrud. Enim sint ipsum quis minim qui et dolore fugiat.\r\n',
  },
  {
    _id: '642c3e5aea836bbf93d92649',
    index: 1,
    guid: '1ea4a9c7-15cb-41fd-b842-54996e85546a',
    isActive: true,
    balance: '$3,937.46',
    age: 23,
    eyeColor: 'green',
    name: 'Keisha Mosley',
    gender: 'female',
    company: 'ANARCO',
    email: '<EMAIL>',
    about:
      'Proident excepteur mollit culpa consectetur excepteur mollit et adipisicing Lorem laboris. Veniam enim reprehenderit et pariatur irure deserunt cupidatat minim nisi consequat magna. Proident pariatur quis eiusmod eiusmod consequat pariatur consequat elit labore magna et nostrud labore qui. Non aliquip adipisicing do tempor laboris officia veniam Lorem. Mollit proident laboris elit esse. Eiusmod eiusmod elit reprehenderit excepteur occaecat qui mollit.\r\n',
  },
  {
    _id: '642c3e5a09f6ea4b5cb0bac5',
    index: 2,
    guid: 'ac063652-0a13-494c-97ba-9ada5ad949a8',
    isActive: false,
    balance: '$3,949.19',
    age: 40,
    eyeColor: 'brown',
    name: 'Blackwell Sweet',
    gender: 'male',
    company: 'ADORNICA',
    email: '<EMAIL>',
    about:
      'Consequat excepteur enim anim ut do do non officia aliquip amet. Quis veniam laborum minim est ad voluptate irure. Pariatur sit reprehenderit ipsum velit fugiat non in veniam nisi pariatur sint sit deserunt. Tempor dolore labore ex qui esse proident excepteur officia laborum adipisicing nulla. Nulla pariatur cillum nostrud dolore est duis et sunt ullamco adipisicing.\r\n',
  },
  {
    _id: '642c3e5ab9d9e164ebaad602',
    index: 3,
    guid: 'bd6508f7-8efe-4084-aeec-0f9fac19cd28',
    isActive: false,
    balance: '$2,224.99',
    age: 30,
    eyeColor: 'green',
    name: 'Shana Hoover',
    gender: 'female',
    company: 'EARTHPLEX',
    email: '<EMAIL>',
    about:
      'Cillum id veniam sunt fugiat cillum officia et dolor mollit. Ipsum consequat elit enim amet culpa consequat occaecat ipsum ullamco mollit et est non. Exercitation fugiat est et sint pariatur esse enim sunt tempor duis dolore sunt deserunt. Dolore reprehenderit ex esse consectetur aliqua enim ullamco anim excepteur quis voluptate aliqua. Esse consectetur ut deserunt labore ullamco mollit dolor et laboris labore.\r\n',
  },
  {
    _id: '642c3e5ad11671056d4a6e02',
    index: 4,
    guid: '55387357-4db5-4ec6-b295-1284ed60727f',
    isActive: true,
    balance: '$3,760.73',
    age: 21,
    eyeColor: 'brown',
    name: 'Sosa Hopkins',
    gender: 'male',
    company: 'COMVERGES',
    email: '<EMAIL>',
    about:
      'In enim nisi sit laborum amet duis aute aute nulla incididunt exercitation. Nostrud deserunt excepteur adipisicing in proident officia ex anim laboris. Aute nostrud mollit non deserunt. Et fugiat ipsum et pariatur dolore aute qui quis sunt ullamco. Consequat adipisicing quis sunt cillum voluptate pariatur nulla.\r\n',
  },
  {
    _id: '642c3e5a7bae07b059189765',
    index: 5,
    guid: '84aaab8b-56da-4b73-ad9a-287259b65c0c',
    isActive: true,
    balance: '$1,282.89',
    age: 27,
    eyeColor: 'blue',
    name: 'Conley Oneil',
    gender: 'male',
    company: 'ENTALITY',
    email: '<EMAIL>',
    about:
      'Eu laborum id esse et minim amet sit aute consequat incididunt labore. Esse voluptate ad et aliqua. Cupidatat ipsum veniam esse cupidatat velit. In occaecat irure laborum in tempor ullamco non labore. Esse consequat ad sint quis sunt cillum anim. Cupidatat veniam ullamco ad pariatur ex occaecat est ut dolor ad in dolor tempor velit. Cillum magna velit aliquip adipisicing ullamco excepteur id aliquip aliqua cupidatat ipsum anim.\r\n',
  },
]
</script>
