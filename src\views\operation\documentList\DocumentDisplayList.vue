<template>
  <VProgressCircular v-if="documents?.loading" color="primary" class="ms-4" indeterminate />
  <span v-else-if="documents?.value?.empty || !documents?.value?.content"> {{ noDocumentLabel }} </span>
  <VRow v-else dense class="flex-column">
    <VCol v-for="document in documents?.value!.content" :key="document.id">
      <NjDisplayValue :label="document.documentType.name">
        <template #value>
          <VLink @click="downloadDocument(document)">
            <AutoShrinkText :text="document.document.originalFilename ?? ''" />
          </VLink>
        </template>
      </NjDisplayValue>
    </VCol>
  </VRow>
</template>
<script setup lang="ts">
import { useSnackbarStore } from '@/stores/snackbar'
import type { EnhancedDocument } from '@/types/document'
import type { Page } from '@/types/pagination'
import type { PromisableValue } from '@/types/promisableValue'
import type { AxiosResponse } from 'axios'
import type { PropType } from 'vue'
import { VCol } from 'vuetify/components'

const props = defineProps({
  documents: Object as PropType<PromisableValue<Page<EnhancedDocument>>>,
  type: String as PropType<'OperationsGroupDocument' | 'OperationDocument'>,
})

const snackbarStore = useSnackbarStore()

const downloadDocument = (item: EnhancedDocument) => {
  if (props.type == 'OperationsGroupDocument') {
    downloadOperationsGroupDocument(item)
  } else if (props.type == 'OperationDocument') {
    downloadOperationDocumentFile(item)
  }
}

const noDocumentLabel = ref('')
watch(
  () => props.type,
  (v) => {
    if (v == 'OperationDocument') {
      noDocumentLabel.value = "Il n'y a pas de document dans l'opération"
    } else if (v == 'OperationsGroupDocument') {
      noDocumentLabel.value = "Il n'y a pas de document issu du regroupement"
    }
  },
  {
    immediate: true,
  }
)

const downloadOperationDocumentFile = (item: EnhancedDocument) => {
  operationDocumentApi
    .download(item.id)
    .then((response) => {
      onDownloadFileSuccess(item, response)
    })
    .catch(() => onDownloadFileError())
}

const downloadOperationsGroupDocument = (item: EnhancedDocument) => {
  operationsGroupDocumentApi
    .download(item.id)
    .then((response) => {
      onDownloadFileSuccess(item, response)
    })
    .catch(() => onDownloadFileError())
}

const onDownloadFileSuccess = (item: EnhancedDocument, response: AxiosResponse<Blob, any>) => {
  downloadFile(item.document.originalFilename, response.data)
  snackbarStore.setSuccess('Le téléchargement a réussi')
}

const onDownloadFileError = () => {
  snackbarStore.setError(
    'Le téléchargement du fichier a échoué. Aviez-vous envoyé ce fichier sur boCEE ? Si oui, veuillez nous contacter.'
  )
}
</script>
