import type { AxiosInstance, AxiosPromise } from 'axios'
import axiosInstance from '.'
import type { MergeDocumentToPdfRequest } from '@/types/document'
import type { ResponseHandler } from '@/types/responseHandler'

class MergeDocumentToPdfApi {
  public constructor(private axios: AxiosInstance) {}

  public merge(operationId: number, request: MergeDocumentToPdfRequest): AxiosPromise<ResponseHandler> {
    return this.axios.post(`/operations/${operationId}/merge_document_to_pdf`, request)
  }
}

export const mergeDocumentToPdfApi = new MergeDocumentToPdfApi(axiosInstance)
