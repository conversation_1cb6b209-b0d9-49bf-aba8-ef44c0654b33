/**
 * plugins/vuetify.ts
 *
 * Framework documentation: https://vuetifyjs.com`
 */

// Styles
import '@mdi/font/css/materialdesignicons.css'
import 'vuetify/styles'
import './vuetify_migration_fluid_system.scss'

// Composables
import { createVuetify } from 'vuetify'

// https://vuetifyjs.com/en/introduction/why-vuetify/#feature-guides
export default createVuetify({
  theme: {
    themes: {
      light: {
        colors: {
          primary: '#007acd',
          secondary: '#23D2B5',
          success: '#418448',
          warning: '#ff8c47',
          error: '#db3835',
        },
      },
      // VText
    },
  },
  defaults: {
    VAlert: {
      variant: 'outlined',
      rounded: 0,
    },
    VTextField: {
      variant: 'outlined',
      rounded: 0,
      density: 'compact',
      hideDetails: 'auto',
      bgColor: 'white',
    },
    VTextarea: {
      variant: 'outlined',
      rounded: 0,
      density: 'compact',
      hideDetails: 'auto',
    },
    VSelect: {
      variant: 'outlined',
      rounded: 0,
      density: 'compact',
      hideDetails: 'auto',
    },
    VCard: {
      rounded: 0,
      border: true,
      elevation: 0,
    },
    VSnackbar: {
      rounded: 0,
    },
    VCheckbox: {
      hideDetails: 'auto',
      density: 'compact',
      color: 'primary',
    },
    VRadioGroup: {
      hideDetails: 'auto',
      density: 'compact',
      color: 'primary',
    },
    VSwitch: {
      inset: true,
      color: 'primary',
      hideDetails: 'auto',
      density: 'compact',
      size: 'small',
    },
    VAutocomplete: {
      rounded: 0,
      variant: 'outlined',
      density: 'compact',
      color: 'primary',
      hideDetails: 'auto',
    },
    VTabs: {
      sliderColor: 'primary',
    },
    VChip: {
      rounded: 0,
      closeIcon: 'mdi-close',
      color: 'primary',
    },
    VTable: {
      density: 'compact',
    },
    VFileInput: {
      density: 'compact',
      variant: 'outlined',
    },
    VBtnToggle: {
      rounded: 0,
    },
  },
})
