import type { User } from './user'

export interface Territory {
  id: number
  description: string
  zoneId: string
  territoryDetails: TerritoryDetails
}

export type SimpleTerritoryDto = Omit<Territory, 'territoryDetails'>

export interface TerritoryDetails {
  id: number
  territoryReferent?: User
  missionManagers?: User[]

  operationsAndPerformanceDirector?: User
  operationTerritoryCfo?: User
  networkDevelopmentDirector?: User
  industryDevelopmentDirector?: User
  buildingDevelopmentDirector?: User
  developmentDirector?: User
  territoryDirector?: User
}
export interface TerritoryDetailsRequest {
  territoryReferentUserId?: number
  missionManagerIds: number[]

  operationsAndPerformanceDirectorUserId?: number
  operationTerritoryCfoUserId?: number
  networkDevelopmentDirectorUserId?: number
  industryDevelopmentDirectorUserId?: number
  buildingDevelopmentDirectorUserId?: number
  developmentDirectorUserId?: number
  territoryDirectorUserId?: number
}
export const formatTerritory = (territory?: Territory) => {
  if (isDebug()) {
    return territory ? `#${territory.id} ${territory.description} (${territory.zoneId})` : ''
  }
  return territory ? `${territory.description} (${territory.zoneId})` : ''
}
