<template>
  <NjDataTable
    :items="items"
    :headers="headers"
    checkboxes
    multi-selection
    :selections="selections"
    :disabled-row="(item: UsedDocument) => !isExtensionPdf(item.document.originalFilename)"
    :hide-checkboxes="(item: UsedDocument) => !isExtensionPdf(item.document.originalFilename)"
    @update:selections="emit('update:selections', $event)"
  >
    <template #[`item.document.creationDateTime`]="{ item }">
      {{ formatHumanReadableLocalDate(item.document.creationDateTime) }}
    </template>
    <template #[`item.document.extension`]="{ item }">
      {{ item.document.originalFilename.split('.').slice(-1)[0] }}
    </template>
    <template #[`item.document.originalFilename`]="{ item }">
      <VLink @click="downloadFileUsedDocument(item)">
        {{ item.document.originalFilename }}
      </VLink>
    </template>
  </NjDataTable>
</template>
<script setup lang="ts">
import { useSnackbarStore } from '@/stores/snackbar'
import { formatHumanReadableLocalDate } from '@/types/date'
import { type UsedDocument } from '@/types/document'

defineProps({
  items: Array as PropType<UsedDocument[]>,
  selections: Array as PropType<UsedDocument[]>,
})

const emit = defineEmits<{ 'update:selections': [UsedDocument[]] }>()

const headers = [
  {
    title: 'Type',
    value: 'documentType.name',
    sortable: false,
  },
  {
    title: "Date d'import",
    value: 'document.creationDateTime',
    sortable: false,
  },
  {
    title: 'Doc.',
    value: 'document.extension',
    sortable: false,
  },
  {
    title: 'Nom',
    value: 'document.originalFilename',
    sortable: false,
  },
  {
    title: 'Description',
    value: 'description',
    sortable: false,
  },
]

const snackbarStore = useSnackbarStore()

const downloadFileUsedDocument = (item: UsedDocument) => {
  if (item.operationDocumentId) {
    operationDocumentApi
      .download(item.operationDocumentId)
      .then((response) => {
        downloadFile(item.document.originalFilename, response.data)
        snackbarStore.setSuccess('Le téléchargement a réussi')
      })
      .catch(() =>
        snackbarStore.setError(
          'Le téléchargement du fichier a échoué. Aviez-vous envoyé ce fichier sur boCEE ? Si oui, veuillez nous contacter.'
        )
      )
  } else if (item.operationGroupDocumentId) {
    operationsGroupDocumentApi
      .download(item.operationGroupDocumentId)
      .then((response) => {
        downloadFile(item.document.originalFilename, response.data)
        snackbarStore.setSuccess('Le téléchargement a réussi')
      })
      .catch(() =>
        snackbarStore.setError(
          'Le téléchargement du fichier a échoué. Aviez-vous envoyé ce fichier sur boCEE ? Si oui, veuillez nous contacter.'
        )
      )
  }
}

const isExtensionPdf = (filename: string) => filename.split('.').slice(-1)[0].toLowerCase() === 'pdf'
</script>
