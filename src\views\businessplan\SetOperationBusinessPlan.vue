<template>
  <VRow class="h-100">
    <VCol cols="6">
      <VRow class="flex-column content-layout">
        <VCol class="content-layout__header"> Simulations disponibles </VCol>
        <VCol class="content-layout__header">
          <VRow>
            <VCol>
              <VTextField
                v-model="searchInAvailableOperation"
                prepend-inner-icon="mdi-magnify"
                label="Rechercher"
                :loading="availableOperation.loading"
              >
                <template #loader="{ isActive }">
                  <VProgressLinear :active="isActive" color="primary" absolute height="4" indeterminate />
                </template>
              </VTextField>
            </VCol>
            <VCol>
              <VCheckbox v-model="pageFilter.myRequests" label="Mes simulations" />
            </VCol>
          </VRow>
        </VCol>
        <VCol class="content-layout__main">
          <NjDataTable
            :pageable="pageable"
            :page="availableOperation.value!"
            :headers="headers"
            class="table"
            fixed
            @update:pageable="updatePageable"
          >
            <template #[`item.property.code`]="{ item }">
              <VRow class="flex-nowrap">
                <VCol align-self="center">
                  {{ item?.property?.code }}
                </VCol>
                <VCol align="end" class="flex-grow-0">
                  <NjIconBtn :disabled="loading" icon="mdi-plus" rounded="0" @click="addToSelectedOperation(item)" />
                </VCol>
              </VRow>
            </template>
          </NjDataTable>
        </VCol>
      </VRow>
    </VCol>
    <VCol cols="6">
      <VRow class="flex-column content-layout">
        <VCol class="content-layout__header"> Simulations sélectionnées </VCol>
        <VCol class="content-layout__header">
          <VTextField
            v-model="searchInOperationsInOperationsGroup"
            prepend-inner-icon="mdi-magnify"
            label="Rechercher"
            :loading="operationInBusinessPlan.loading"
          >
            <template #loader="{ isActive }">
              <VProgressLinear :active="isActive" color="primary" absolute height="4" indeterminate />
            </template>
          </VTextField>
        </VCol>
        <VCol class="content-layout__main">
          <NjDataTable
            :pageable="pageableBusinessPlan"
            :page="operationInBusinessPlan.value"
            :headers="headers"
            class="table"
            fixed
            @update:pageable="updatePageableBusinessPlan"
          >
            <template #[`item.property.code`]="{ item }">
              <VRow class="flex-nowrap">
                <VCol align-self="center">
                  {{ item?.property?.code }}
                </VCol>
                <VCol align="end" class="flex-grow-0">
                  <NjIconBtn
                    :disabled="loading"
                    icon="mdi-minus"
                    rounded="0"
                    @click="removeFromSelectedOperation(item)"
                  />
                </VCol>
              </VRow>
            </template>
          </NjDataTable>
          <AlertDialog v-bind="removeOperationAlertDialog.props" title="Retirer l'opération" max-width="640px">
            Attention! En sortant cette opération du regroupement, tous les documents présents au niveau de regroupement
            seront par conséquent retirés de l'opération. Voulez-vous continuer?
          </AlertDialog>
        </VCol>
      </VRow>
    </VCol>
  </VRow>
</template>
<script setup lang="ts">
import { useSnackbarStore } from '@/stores/snackbar'
import type { Operation } from '@/types/operation'
import { debounce } from 'lodash'
import type { PropType } from 'vue'
import type { BusinessPlan } from '@/types/businessPlan'
import type { DataTableHeader } from '@/components/okta/NjDataTable.type'

const props = defineProps({
  businessPlan: {
    type: Object as PropType<BusinessPlan>,
    required: true,
  },
})
const emit = defineEmits<{
  'update:loading': [boolean]
}>()

//operation
const removeOperationAlertDialog = useConfirmAlertDialog()

const headers: DataTableHeader[] = [
  {
    title: 'Nom',
    value: 'simulationName',
    maxLength: 30,
  },
  {
    title: 'Code',
    value: 'standardizedOperationSheet.operationCode',
  },
  {
    title: 'Numéro installation',
    value: 'property.code',
  },
]

//Available operation
const searchInAvailableOperation = ref()
const {
  data: availableOperation,
  pageFilter,
  pageable,
  updatePageable,
  reload,
} = usePagination<Operation>(
  (filter, pageable) => operationApi.findAll(filter, pageable),
  {
    stepIds: [0],
    inBusinessPlan: false,
    operationStatuses: ['DOING'],
    withSimulations: true,
  },
  {}
)

const debounceSearchInAvailableOperation = debounce((v: string | undefined) => {
  pageFilter.value.search = v
  reload()
}, 300)

watch(searchInAvailableOperation, (v) => {
  availableOperation.value.loading = true
  debounceSearchInAvailableOperation(v)
})

//operation in operationgroup

const searchInOperationsInOperationsGroup = ref<string>('')

const debounceSearchInOperationsInOperationsGroup = debounce((v: string) => {
  pageFilterBusinessPlan.value.search = v
  reloadBusinessPlan()
}, 300)

watch(searchInOperationsInOperationsGroup, (v) => {
  operationInBusinessPlan.value.loading = true
  debounceSearchInOperationsInOperationsGroup(v)
})

const {
  data: operationInBusinessPlan,
  pageFilter: pageFilterBusinessPlan,
  pageable: pageableBusinessPlan,
  updatePageable: updatePageableBusinessPlan,
  reload: reloadBusinessPlan,
} = usePagination<Operation>(
  (filter, pageable) => operationApi.findAll(filter, pageable),
  {
    businessPlanIds: [props.businessPlan.id],
    withSimulations: true,
  },
  {}
)

const promisableAddingOperation = ref(emptyValue<Operation>())
const snackbarStore = useSnackbarStore()
const addToSelectedOperation = async (item: Operation) => {
  const request = mapToOperationRequest(item)
  request.businessPlanId = props.businessPlan.id
  handleAxiosPromise(promisableAddingOperation, simulationApi.updateSimulation(item.id, request), {
    afterSuccess: () => {
      reload()
      reloadBusinessPlan()
    },
    afterError: () => {
      snackbarStore.setError(
        promisableAddingOperation.value.error ?? "Erreur lors de l'ajout de l'opération au business plan",
        5000
      )
    },
  })
}

const promisableRemovingOperation = ref(emptyValue<Operation>())
const removeFromSelectedOperation = async (item: Operation) => {
  const request = mapToOperationRequest(item)
  request.businessPlanId = undefined
  handleAxiosPromise(promisableRemovingOperation, simulationApi.updateSimulation(item.id, request), {
    afterSuccess: () => {
      reload()
      reloadBusinessPlan()
    },
    afterError: () => {
      snackbarStore.setError(
        promisableRemovingOperation.value.error ??
          'Une erreur est survenue en tentant de retirer une opération du business plan'
      )
    },
  })
}

const loading = computed(
  () =>
    promisableRemovingOperation.value.loading ||
    promisableAddingOperation.value.loading ||
    availableOperation.value.loading ||
    operationInBusinessPlan.value.loading
)
watch(loading, (v) => emit('update:loading', v))
</script>
