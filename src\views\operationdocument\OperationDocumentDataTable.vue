<template>
  <DocumentDataTable>
    <template #tbody>
      <OperationDocumentDataTableRow
        v-for="item in items"
        :key="item.id"
        :item="item"
        :operation="operation"
        :reload-data="reloadData"
        @send="(id: number) => emits('send', id)"
      />
    </template>
  </DocumentDataTable>
</template>
<script lang="ts" setup>
import type { Operation } from '@/types/operation'
import type { PropType } from 'vue'
import DocumentDataTable from '../document/DocumentDataTable.vue'
import OperationDocumentDataTableRow from './OperationDocumentDataTableRow.vue'
import type { DocumentDataTableItem } from './types'

defineProps({
  operation: Object as PropType<Operation>,
  items: Array as PropType<DocumentDataTableItem[]>,
  totalDocumentElements: Number,
  reloadData: Function,
})

const emits = defineEmits<{
  send: [number]
}>()
</script>
