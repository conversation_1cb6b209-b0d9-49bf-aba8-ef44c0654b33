<template>
  <CardDialog
    v-if="operation"
    :model-value="props.modelValue"
    width="40%"
    title="Préparer la convention"
    :closeable="false"
    @update:model-value="emits('update:model-value', $event)"
  >
    <VAlert type="info" variant="outlined">
      Le document
      <b>{{ `ConventionCEE-${isSubsidiary(operation.entity) ? 'Tripartite-' : ''}${operation?.chronoCode}.docx` }}</b>
      est disponible. Merci de vérifier les informations avant signature.
    </VAlert>
    <template #actions>
      <NjBtn variant="outlined" @click="emits('update:model-value', false)">Annuler</NjBtn>
      <NjBtn :loading="downloading" @click="createConvention">Téléchargement</NjBtn>
    </template>
  </CardDialog>
</template>
<script setup lang="ts">
import type { Operation } from '@/types/operation'
import type { PropType } from 'vue'
import { isSubsidiary } from '@/types/entity'
import { useSnackbarStore } from '@/stores/snackbar'
import { trace } from '@/stores/analytics'
import { useAdminConfigurationStore } from '@/stores/adminConfiguration'

const props = defineProps({
  operation: Object as PropType<Pick<Operation, 'id' | 'entity' | 'chronoCode' | 'stepId'>>,
  modelValue: Boolean,
})

const emits = defineEmits<{
  'update:model-value': [boolean]
}>()

const snackbarStore = useSnackbarStore()
const adminStore = useAdminConfigurationStore()

// Gestion de la convention
const downloading = ref(false)
const createConvention = () => {
  if (!props.operation) {
    return
  }
  downloading.value = true
  trace('downloadFilledTemplate', {
    documentType: { id: adminStore.conventionDocumentTypeId?.valueAsInt, name: 'Convention' },
    operation: {
      id: props.operation.id,
      stepId: props.operation.stepId,
    },
  })
  conventionApi
    .create(props.operation.id)
    .then((response) => {
      downloadFile(
        `ConventionCEE-${isSubsidiary(props.operation!.entity) ? 'Tripartite' : ''}-${
          props.operation?.chronoCode
        }.docx`,
        response.data
      )
      snackbarStore.setSuccess("L'édition de la convention a réussi")
      emits('update:model-value', false)
    })
    .catch(async (err) => snackbarStore.setError(await handleAxiosException(err)))
    .finally(() => (downloading.value = false))
}
</script>
