<template>
  <VNavigationDrawer v-model:rail="closed" :model-value="true" permanent :rail-width="72" width="300">
    <VList variant="text" class="py-0">
      <VListItem class="px-6" title="Fermer" color="primary" @click="closed = !closed">
        <template #prepend>
          <VIcon :icon="closed ? 'mdi-chevron-right' : 'mdi-chevron-left'" color="primary" />
        </template>
      </VListItem>
      <!-- TODO revoir le style quand sélectionner ou hover -->
      <!-- eslint-disable-next-line vue/valid-v-for -->
      <VListItem
        v-for="item in items"
        class="px-6"
        color="primary"
        :title="item.title"
        :to="item.to ?? { name: item.routeName }"
        :exact="
          item.exact || (item.includedExactRoutes ? !item.includedExactRoutes.includes(route.name as string) : false)
        "
      >
        <template #prepend>
          <VIcon :icon="item.icon" color="primary" style="opacity: var(--v-medium-emphasis-opacity)" />
        </template>
      </VListItem>
    </VList>
  </VNavigationDrawer>
</template>

<script setup lang="ts">
import type { PropType } from 'vue'
import type { SideBarElement } from './SideBar.type'

// eslint-disable-next-line @typescript-eslint/no-unused-vars
const props = defineProps({
  items: {
    required: true,
    type: Array as PropType<SideBarElement[]>,
  },
})

const closed = ref(true)

const route = useRoute()
</script>

<style>
.sidebar_item--active {
  background-color: var(--nj-color-background-primary-selected) !important;
  border-left-color: var(--nj-color-border-brand-bold) !important;
  font-weight: 700;
}
</style>
