<template>
  <NjPage title="Liste des dossiers EMMY" expend-body :error-message="data.error">
    <template #header-actions>
      <NjBtn @click="newEmmyFolderDialog = true">Nouveau Dossier EMMY</NjBtn>
    </template>
    <template #body>
      <VRow class="flex-column w-100">
        <VCol class="flex-grow-0">
          <VRow>
            <VCol>
              <SearchInput
                :model-value="pageFilter.search"
                :loading="data.loading"
                @update:model-value="updateSearch"
              />
            </VCol>
            <VCol>
              <VCheckbox
                label="Mes dossiers"
                :model-value="pageFilter.myFolders"
                :false-value="null"
                @update:model-value="updateFilterByFieldname('myFolders', $event)"
              />
            </VCol>
            <!-- <VCol class="flex-grow-0">
              <NjBtn :disabled="!selections.length" variant="outlined" color="error" @click="deleteOperationsGroup"
                >Supprimer</NjBtn
              >
            </VCol> -->
            <VCol class="d-flex subheader-actions justify-end">
              <NjBtn @click="manageFolders = true">Gérer les opérations</NjBtn>
              <NjBtn @click="showColumnManagerNavigationDrawer = !showColumnManagerNavigationDrawer">
                Personnaliser
              </NjBtn>
            </VCol>
          </VRow>
        </VCol>
        <VCol>
          <NjDataTable
            :headers="emmyFolderColumnManagerRef?.headers ?? originalHeaders"
            :pageable="pageable"
            :on-click-row="clickRow"
            :page="data.value!"
            :to-row="(item: EmmyFolder) => ({ name: 'EmmyFolderOneView', params: { id: item.id } })"
            fixed
            @update:pageable="updatePageable"
          >
            <template #[`item.stepId`]="{ item }">
              <EmmyFolderStepChip :step-id="item.stepId" />
            </template>
          </NjDataTable>
          <ColumnManagerDialog
            id="emmyFolderAllView"
            ref="emmyFolderColumnManagerRef"
            v-model="showColumnManagerNavigationDrawer"
            :original-headers="originalHeaders"
          />
        </VCol>
      </VRow>
      <EmmyFolderCreateDialog
        v-model="newEmmyFolderDialog"
        @save:emmy-folder="
          router.push({
            name: 'EmmyFolderOneView',
            params: { id: $event.id },
          })
        "
      />
      <ManageOperationInEmmyFolderGlobalDialog v-model="manageFolders" />
    </template>
  </NjPage>
</template>
<script setup lang="ts">
import { emmyFolderApi, type EmmyFolderFilter } from '@/api/emmyFolder'
import NjBtn from '@/components/NjBtn.vue'
import type { DataTableHeader } from '@/components/okta/NjDataTable.type'
import NjDataTable from '@/components/okta/NjDataTable.vue'
import type { EmmyFolder } from '@/types/emmyFolder'
import { ref, watch, unref } from 'vue'
import { VCheckbox } from 'vuetify/components'
import EmmyFolderCreateDialog from './EmmyFolderCreateDialog.vue'
import ManageOperationInEmmyFolderGlobalDialog from './ManageOperationInEmmyFolderGlobalDialog.vue'
import ColumnManagerDialog from '@/components/ColumnManagerDialog.vue'
import EmmyFolderStepChip from './EmmyFolderStepChip.vue'

const showColumnManagerNavigationDrawer = ref(false)
const originalHeaders: DataTableHeader[] = [
  {
    title: 'Numéro dossier',
    value: 'emmyCode',
  },
  {
    title: 'Nom',
    value: 'name',
  },
  {
    title: 'Etape',
    value: 'stepId',
  },
  {
    title: 'Responsable',
    value: 'creationUser',
    formater: (_, value) => displayFullnameUser(value),
  },
  {
    title: 'Date de création',
    value: 'creationDateTime',
    formater: (_, value) => formatHumanReadableLocalDate(value),
  },
  {
    title: 'Envoi PNCEE',
    value: 'pnceeSubmissionDate',
    formater: (_, value) => (value ? formatHumanReadableLocalDate(value) : ''),
  },
  {
    title: 'Délivrance PNCEE',
    value: 'pnceeIssuedDate',
    formater: (_, value) => (value ? formatHumanReadableLocalDate(value) : ''),
  },
  {
    title: 'Numéro Délivrance classique PNCEE',
    value: 'pnceeClassicIssuedNumber',
  },
  {
    title: 'Numéro Délivrance précarité PNCEE',
    value: 'pnceePrecariousnessIssuedNumber',
  },
]

const { data, pageFilter, pageable, updatePageable, updateFilter, updateFilterByFieldname } = usePaginationInQuery<
  EmmyFolder,
  EmmyFolderFilter
>(
  (filter, pageable) => emmyFolderApi.findAll({ ...filter, myFolders: filter.myFolders ? true : undefined }, pageable),
  {
    defaultPageFilter: {},
    defaultPageablePartial: { sort: ['creationDateTime,DESC'] },
    saveFiltersName: 'EmmyFolderAllView',
  }
)
watch(pageFilter, updateFilter, { deep: true })

const router = useRouter()

const clickRow = (emmyFolder: EmmyFolder) => {
  router.push({
    name: 'EmmyFolderOneView',
    params: { id: emmyFolder.id },
  })
}

//Create OperationsGroup
const newEmmyFolderDialog = ref()
const manageFolders = ref()

// Search
const updateSearch = (v: string) => {
  data.value.loading = true
  const filter = {
    ...unref(pageFilter),
    search: v,
  }
  updateFilter(filter)
}

const emmyFolderColumnManagerRef = ref<typeof ColumnManagerDialog | null>(null)
</script>
