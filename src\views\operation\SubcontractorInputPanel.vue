<template>
  <NjExpansionPanel :model-value="modelValue" v-bind="$attrs">
    <template #title>
      <VInput :model-value="subcontractor" :rules="rules" hide-details="auto">
        <div class="d-flex align-center fill-width" style="font-size: 18px; font-weight: 700">
          Sous traitant
          <AlertIcon :rules="[isSubcontractorMandatory && !subcontractor ? 'Le sous traitant est requis' : true]" />
          <VSpacer />
          <NjIconBtn
            v-if="subcontractor !== null"
            icon="mdi-delete"
            color="primary"
            @click.stop="$emit('update:subcontractor', null)"
          />
          <VLink
            color="primary"
            icon="mdi-format-list-bulleted"
            style="font-weight: initial; font-size: initial"
            @click.stop="openSubcontractorDialog"
            >Sous traitant</VLink
          >
        </div>
      </VInput>
    </template>
    <VAlert v-show="subcontractor && subcontractor.address?.country !== 'FRA'" type="warning">
      Il est recommandé d'avertir que le sous-traitant est à l'étranger dans la partie commentaire du dossier
    </VAlert>

    <SubcontractorDisplayValue :model-value="subcontractor" />
  </NjExpansionPanel>
  <VDialog
    v-model="subcontractorDialog"
    :width="subcontractorMode === 'select' ? '90%' : '40%'"
    :height="subcontractorMode === 'select' ? '100%' : ''"
  >
    <SubcontractorForm
      v-if="subcontractorMode === 'form'"
      v-model:subcontractor="selectedSubcontractor[0]"
      v-model:updated-subcontractor="updatedSubcontractor"
      :mode="subcontractorFormMode"
      :cancel="hideSubcontractorForm"
      :after-success="hideSubcontractorForm"
      :can-certified="canCertified"
    />
    <VCard v-else-if="subcontractorMode === 'display'" :border="false">
      <VCardTitle>
        <div class="d-flex align-center">
          Détail d'un sous traitant
          <VSpacer />
          <NjIconBtn icon="mdi-close" rounded="0" @click="closeSubcontractorDialog" />
        </div>
      </VCardTitle>
      <SubcontractorDisplayValue :model-value="selectedSubcontractor[0]" expanded />
      <VCardActions>
        <VSpacer />
        <NjBtn @click="subcontractorMode = 'select'">Ok</NjBtn>
      </VCardActions>
    </VCard>
    <VCard v-else class="content-layout">
      <VCardTitle class="content-layout__header">
        <div class="d-flex">
          Choix d'un sous traitant
          <VSpacer />
          <NjIconBtn icon="mdi-close" rounded="0" @click="closeSubcontractorDialog" />
        </div>
        <VDivider />
      </VCardTitle>
      <VCardText class="content-layout content-layout__main overflow-hidden">
        <VRow class="content-layout__header">
          <VCol class="d-flex">
            <VRow>
              <VCol>
                <SearchInput v-model="searchSubcontractor" :loading="subcontractorLoading" />
              </VCol>
              <VSpacer />
              <VCol class="flex-grow-0">
                <NjBtn :disabled="!selectedSubcontractor[0]" @click="duplicateSubcontractor"> Dupliquer </NjBtn>
              </VCol>
              <VCol class="flex-grow-0">
                <NjBtn :disabled="!canEditSubcontractor" @click="editSubContractor"> Editer </NjBtn>
              </VCol>
              <VCol class="flex-grow-0">
                <NjBtn :disabled="!selectedSubcontractor[0]" @click="subcontractorMode = 'display'"> Visualiser </NjBtn>
              </VCol>
              <VCol class="flex-grow-0">
                <NjBtn @click="createSubcontractor"> Créer </NjBtn>
              </VCol>
            </VRow>
          </VCol>
        </VRow>
        <SubcontractorAllView
          v-model:selections="selectedSubcontractor"
          v-model:loading="subcontractorLoading"
          class="content-layout__main"
          :search="searchSubcontractor"
          fixed
        />
      </VCardText>
      <VCardActions>
        <VSpacer />
        <NjBtn variant="outlined" @click="closeSubcontractorDialog">Annuler</NjBtn>
        <NjBtn :disabled="!selectedSubcontractor[0]" @click="selectSubconstructor">Enregistrer</NjBtn>
      </VCardActions>
    </VCard>
  </VDialog>
</template>

<script setup lang="ts">
import type { Subcontractor } from '@/types/subcontractor'
import type { PropType } from 'vue'
import SubcontractorForm from '../SubcontractorForm.vue'
import SubcontractorAllView from '../SubcontractorAllView.vue'
import { VDialog, type VSpacer } from 'vuetify/components'
import { useUserStore } from '@/stores/user'
import type { ValidationRule } from '@/types/rule'
import AlertIcon from '../AlertIcon.vue'

const props = defineProps({
  modelValue: Boolean,
  subcontractor: {
    type: Object as PropType<Subcontractor | null>,
  },
  rules: {
    type: Array as PropType<Array<ValidationRule>>,
  },
  isSubcontractorMandatory: Boolean,
  canCertified: Boolean,
})

const emit = defineEmits<{
  'update:subcontractor': [value: Subcontractor | null]
}>()

const subcontractorDialog = ref(false)
const searchSubcontractor = ref<string>('')
const selectedSubcontractor = ref<Subcontractor[]>([])
const subcontractorLoading = ref(false)

const openSubcontractorDialog = () => {
  subcontractorDialog.value = true
}

const closeSubcontractorDialog = () => {
  subcontractorDialog.value = false
}

const subcontractorMode = ref('select')

const subcontractorFormMode = ref('create')
const showSubcontractorForm = (mode: string) => {
  subcontractorFormMode.value = mode
  subcontractorMode.value = 'form'
}

const hideSubcontractorForm = () => {
  subcontractorMode.value = 'select'
  selectedSubcontractor.value = []
}

const createSubcontractor = () => {
  selectedSubcontractor.value = []
  showSubcontractorForm('create')
}
const editSubContractor = () => {
  showSubcontractorForm('edit')
}

const duplicateSubcontractor = () => {
  selectedSubcontractor.value[0] = {
    ...selectedSubcontractor.value[0],
    certified: false,
  }
  showSubcontractorForm('duplicate')
}

const selectSubconstructor = () => {
  closeSubcontractorDialog()
  emit('update:subcontractor', selectedSubcontractor.value[0])
}

watch(subcontractorDialog, (v) => {
  if (v) {
    selectedSubcontractor.value = props.subcontractor ? [props.subcontractor] : []
    subcontractorMode.value = 'select'
  }
})

const updatedSubcontractor = ref<Subcontractor | null>()
watch(updatedSubcontractor, (v) => {
  if (v && v.id === props.subcontractor?.id) {
    emit('update:subcontractor', v)
  }
})
const userStore = useUserStore()
const canEditSubcontractor = computed(() => {
  if (selectedSubcontractor.value[0]) {
    if (selectedSubcontractor.value[0].certified) {
      return userStore.isSiege
    } else {
      return true
    }
  }
  return false
})
</script>
