<template>
  <NjPage style="background-color: #f3fffd">
    <template #body>
      <img :src="bannerJPG" class="w-100" />
      <div class="text-center font-weight-bold" style="font-size: 45px">Bienvenue sur CAPTE !</div>
      <VRow>
        <VCol cols="1"></VCol>
        <VCol class="font-weight-bold text-center" style="font-size: 25px">
          <VRow class="flex-column flex-grow-0">
            <VCol>Pour échanger ou poser des questions sur l'utilisation de CAPTE</VCol>
            <VCol>
              <a
                href="https://web.yammer.com/main/org/engie.com/groups/eyJfdHlwZSI6Ikdyb3VwIiwiaWQiOiIxNjE4ODQ3NDk4MjQifQ/all"
                target="_blank"
              >
                <img class="imgBtn" :src="yammerLogo" />
              </a>
            </VCol>
          </VRow>
        </VCol>
        <VCol class="font-weight-bold text-center" style="font-size: 25px">
          <VRow class="flex-column flex-grow-0">
            <VCol>Retrouver toutes les informations liées aux CEE sur le SharePoint</VCol>
            <VCol>
              <a
                href="https://engie.sharepoint.com/sites/PoleCAP?xsdata=MDV8MDF8fDFkOTBmODdhM2E2OTRmY2NlOTEyMDhkYmNkYmFhNjdifDI0MTM5ZDE0YzYyYzRjNDc4YmRkY2U3MWVhMWQ1MGNmfDB8MHw2MzgzMjk5NzM3NDM4MDY3Mzh8VW5rbm93bnxWR1ZoYlhOVFpXTjFjbWwwZVZObGNuWnBZMlY4ZXlKV0lqb2lNQzR3TGpBd01EQWlMQ0pRSWpvaVYybHVNeklpTENKQlRpSTZJazkwYUdWeUlpd2lWMVFpT2pFeGZRPT18MXxMMk5vWVhSekx6RTVPbVkxTldKaU5XWmlZV0poTnpSaFpXRTROVEJtT0RWbE1tVTFNMk5oTVRGaVFIUm9jbVZoWkM1Mk1pOXRaWE56WVdkbGN5OHhOamszTkRBd05UY3pOak0xfDhmY2JiYWRlMWE1NzQ3YjhlOTEyMDhkYmNkYmFhNjdifGQ3NGZjNzY5NGRmMTQ4OTdhMmM1Y2NlNDU4MmNkZjRi&sdata=aTBhOWdhWXFzckQrWjBvdGZOQXFpdi9iaUU4VCtHOHJac283ZFpGbmhxND0%3D&ovuser=24139d14-c62c-4c47-8bdd-ce71ea1d50cf%2CJP6463%40engie.com&OR=Teams-HL&CT=1697437009852&clickparams=eyJBcHBOYW1lIjoiVGVhbXMtRGVza3RvcCIsIkFwcFZlcnNpb24iOiIyNy8yMzA5MDExMjI3OCIsIkhhc0ZlZGVyYXRlZFVzZXIiOmZhbHNlfQ%3D%3D"
                target="_blank"
              >
                <img class="imgBtn" :src="sharepointLogo" />
              </a>
            </VCol>
          </VRow>
        </VCol>
        <VCol cols="1"></VCol>
      </VRow>
    </template>
  </NjPage>
</template>
<script setup lang="ts">
import bannerJPG from '@/assets/capte-banner/CAPTE-banniere-accueil.jpg'
import yammerLogo from '@/assets/capte-logo/logo-yammer.png'
import sharepointLogo from '@/assets/capte-logo/logo-sharepoint.png'
import { useSidebarStore } from '@/stores/sidebar'

const sidebarStore = useSidebarStore()
onMounted(() => {
  sidebarStore.setItems([])
})
</script>
<style>
.imgBtn {
  padding: 16px;
  background-color: white;
  border-radius: 4px;
  box-shadow:
    rgba(0, 0, 0, 0.2) 0px 3px 1px -2px,
    rgba(0, 0, 0, 0.14) 0px 2px 2px 0px,
    rgba(0, 0, 0, 0.12) 0px 1px 5px 0px;
  height: 125px;
}
</style>
