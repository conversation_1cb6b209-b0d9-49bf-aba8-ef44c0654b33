import { bankHolidays } from './bankHolidaysData'
import { add, sub } from 'date-fns'
import type { LocalDate } from './date'
import { isString } from 'lodash'

export const addBusinessDay = (startLocalDate: Date | LocalDate, numDays: number) => {
  let result = isString(startLocalDate) ? parseLocalDate(startLocalDate) : startLocalDate

  let businessDays = 0
  while (businessDays < numDays) {
    // Move to the next day
    result = add(result, { days: 1 })

    // Check if the current day is a weekend (Saturday or Sunday)
    if (result.getDay() === 0 || result.getDay() === 6) {
      continue // Skip weekends
    }

    // Check if the current day is a bank holiday
    const formattedDate = result.toISOString().slice(0, 10)
    if (formattedDate in bankHolidays) {
      continue // Skip bank holidays
    }

    businessDays++
  }

  // Return the final date after adding the specified business days
  return result
}

export const minusBusinessDay = (startLocalDate: Date, numDays: number) => {
  let result = startLocalDate

  let businessDays = 0
  while (businessDays < numDays) {
    // Move to the next day
    result = sub(result, { days: 1 })

    // Check if the current day is a weekend (Saturday or Sunday)
    if (result.getDay() === 0 || result.getDay() === 6) {
      continue // Skip weekends
    }

    // Check if the current day is a bank holiday
    const formattedDate = result.toISOString().slice(0, 10)
    if (formattedDate in bankHolidays) {
      continue // Skip bank holidays
    }

    businessDays++
  }

  // Return the final date after adding the specified business days
  return result
}

export const differenBusinessDay = (startLocalDate: Date, endLocalDate: Date) => {
  let result = startLocalDate

  let businessDays = 0
  while (result < endLocalDate) {
    // Move to the next day
    result = add(result, { days: 1 })

    // Check if the current day is a weekend (Saturday or Sunday)
    if (result.getDay() === 0 || result.getDay() === 6) {
      continue // Skip weekends
    }

    // Check if the current day is a bank holiday
    const formattedDate = result.toISOString().slice(0, 10)
    if (formattedDate in bankHolidays) {
      continue // Skip bank holidays
    }

    businessDays++
  }

  // Return the final date after adding the specified business days

  console.debug('Business days between', startLocalDate, 'and', endLocalDate, ':', businessDays)
  return businessDays
}
