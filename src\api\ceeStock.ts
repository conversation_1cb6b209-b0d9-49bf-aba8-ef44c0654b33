import type { Page, Pageable } from '@/types/pagination'
import type { CeeStock, AvailableStock } from '@/types/ceeStock'
import type { AxiosInstance, AxiosPromise } from 'axios'
import axiosInstance from '.'
import type { LocalDate } from '@/types/date'

export interface CeeStockFilter {
  search?: string
  startDate?: LocalDate
  endDate?: LocalDate
  eligibleForSale?: boolean
}

const CEE_STOCK_URL = 'cee_stocks'
class CeeStockApi {
  public constructor(private axios: AxiosInstance) {}

  public findAll(pageable: Pageable, filter: CeeStockFilter): AxiosPromise<Page<CeeStock>> {
    return this.axios.get(CEE_STOCK_URL, {
      params: { ...pageable, ...filter },
    })
  }

  public findAllAvailable(pageable: Pageable, filter: CeeStockFilter): AxiosPromise<Page<AvailableStock>> {
    return this.axios.get(CEE_STOCK_URL + '/available', {
      params: { ...pageable, ...filter },
    })
  }
}

export const stockCEEApi = new CeeStockApi(axiosInstance)
