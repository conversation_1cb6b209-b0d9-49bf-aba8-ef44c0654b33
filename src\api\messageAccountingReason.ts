import type { MessageAccountingReason, MessageAccountingReasonRequest } from '@/types/message'
import type { Page, Pageable } from '@/types/pagination'
import type { AxiosInstance, AxiosPromise } from 'axios'
import axiosInstance from '.'

export interface MessageAccountingReasonFilter {
  search?: string
  ids?: number[]
  visible?: boolean
}

const messageAccountingReasonsUri = '/message_accounting_reasons'
class MessageAccountingReasonApi {
  public constructor(private axios: AxiosInstance) {}

  public create(request: MessageAccountingReasonRequest): AxiosPromise<MessageAccountingReason> {
    return this.axios.post(messageAccountingReasonsUri, request)
  }

  public update(request: MessageAccountingReasonRequest, id: number): AxiosPromise<MessageAccountingReasonRequest> {
    return this.axios.put(messageAccountingReasonsUri + '/' + id, request)
  }

  public findAll(
    filter: MessageAccountingReasonFilter,
    pageable: Pageable
  ): AxiosPromise<Page<MessageAccountingReason>> {
    return this.axios.get(messageAccountingReasonsUri, {
      params: { ...pageable, ...filter },
    })
  }

  public findOne(id: number): AxiosPromise<MessageAccountingReasonRequest> {
    return this.axios.get(messageAccountingReasonsUri + '/' + id)
  }

  public delete(id: number): AxiosPromise<void> {
    return this.axios.delete(messageAccountingReasonsUri + '/' + id)
  }
}

export const messageAccountingReasonApi = new MessageAccountingReasonApi(axiosInstance)
