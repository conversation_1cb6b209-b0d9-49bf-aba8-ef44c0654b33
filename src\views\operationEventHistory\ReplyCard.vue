<template>
  <div class="ps-4">
    <VRow class="pb-2" style="font-size: 1rem !important">
      <VCol class="text-primary font-weight-bold flex-grow-0 text-no-wrap">
        {{ displayFullnameUser(props.message.sender) }}
      </VCol>
      <VCol>{{ formatHumanReadableLocalDateTime(props.message.sendDateTime) }}</VCol>
    </VRow>
    <span v-if="message.copy" class="v-card-subtitle pa-0 mt-n5 mb-2">
      Cc :
      <span v-for="copy in message.copy.substring(0, message.copy.length - 1).split(';')" :key="copy">
        {{ copy }},
      </span>
    </span>
    <pre wrap>{{ props.message.message }}</pre>
    <div v-if="props.message.screenshots.length > 0" class="d-flex" style="gap: 16px">
      <ScreenshotDisplay
        v-for="s in props.message.screenshots"
        :key="s.id"
        :message-id="message.id"
        :document-id="s.id"
      />
    </div>
    <span v-if="props.message.concernedDocumentTypes.length > 0 && props.documentsAvailable?.content">
      Documents concernés :
      <div v-for="document in props.message.concernedDocumentTypes" :key="document.id">
        <VLink
          @click="
            downloadDocumentFile(props.documentsAvailable.content.find((doc) => doc.documentType.id === document.id)!)
          "
        >
          {{
            props.documentsAvailable.content.find((doc) => doc.documentType.id === document.id)?.document
              .originalFilename
          }}
        </VLink>
      </div>
    </span>
    <MetaMessageDisplay v-if="message.metaMessage" :message="message" @treated="emits('treated', $event)" />
  </div>
</template>
<script setup lang="ts">
import { displayFullnameUser } from '@/types/user'
import { formatHumanReadableLocalDateTime } from '@/types/date'
import type { PropType } from 'vue'
import type { Message } from '@/types/message'
import type { EnhancedDocument } from '@/types/document'
import { useSnackbarStore } from '@/stores/snackbar'
import type { Page } from '@/types/pagination'
import MetaMessageDisplay from '../MetaMessageDisplay.vue'

const props = defineProps({
  operationId: Number,
  operationGroupId: Number,
  emmyId: Number,
  documentsAvailable: Object as PropType<Page<EnhancedDocument>>,
  message: {
    type: Object as PropType<Message>,
    required: true,
  },
})

const emits = defineEmits<{
  treated: [any]
}>()

const snackbarStore = useSnackbarStore()
const downloadDocumentFile = (item: EnhancedDocument) => {
  if (props.operationId) {
    operationDocumentApi
      .download(item.id)
      .then((response) => {
        downloadFile(item.document.originalFilename, response.data)
        snackbarStore.setSuccess('Le téléchargement a réussi')
      })
      .catch(() =>
        snackbarStore.setError(
          'Le téléchargement du fichier a échoué. Aviez-vous envoyé ce fichier sur boCEE ? Si oui, veuillez nous contacter.'
        )
      )
  } else {
    operationsGroupDocumentApi
      .download(item.id)
      .then((response) => {
        downloadFile(item.document.originalFilename, response.data)
        snackbarStore.setSuccess('Le téléchargement a réussi')
      })
      .catch(() =>
        snackbarStore.setError(
          'Le téléchargement du fichier a échoué. Aviez-vous envoyé ce fichier sur boCEE ? Si oui, veuillez nous contacter.'
        )
      )
  }
}
</script>
