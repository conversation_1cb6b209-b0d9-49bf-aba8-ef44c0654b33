import type { Entity } from './entity'
import type { Territory } from './territory'

export const profiles = [
  'AGENCE',
  'SUPPORT_AGENCE_PLUS',
  'AGENCE_PLUS',
  'TERRITOIRE',
  'SIEGE',
  'INSTRUCTEUR',
  'DAF',
  'DAF_SIEGE',
  'VENTE_CEE',
  'ADMIN',
  'ADMIN_PLUS',
  'ARRETE_CONTROLE_SIEGE',
  'SEE_OSH',
  'NO_VALUE_ATYPICAL',
  'DEVELOPPER',
] as const
export type ProfileType = (typeof profiles)[number]

export const displayProfiles: { title: string; value: ProfileType }[] = [
  {
    title: 'Agence',
    value: 'AGENCE',
  },
  {
    title: 'Support Agence +',
    value: 'SUPPORT_AGENCE_PLUS',
  },
  {
    title: 'Agence +',
    value: 'AGENCE_PLUS',
  },
  {
    title: 'Territoire',
    value: 'TERRITOIRE',
  },
  {
    title: '<PERSON><PERSON>',
    value: '<PERSON>IE<PERSON>',
  },
  {
    title: 'Instructeur',
    value: 'INSTRUCTEUR',
  },
  {
    title: 'Daf',
    value: 'DAF',
  },
  {
    title: '<PERSON>f Si<PERSON>',
    value: 'DAF_SIEGE',
  },
  {
    title: "Voir l'OSH",
    value: 'SEE_OSH',
  },
  {
    title: 'Vente CEE',
    value: 'VENTE_CEE',
  },
  {
    title: 'Admin',
    value: 'ADMIN',
  },
  {
    title: 'Admin +',
    value: 'ADMIN_PLUS',
  },
  {
    title: 'Arreté contrôle siege',
    value: 'ARRETE_CONTROLE_SIEGE',
  },
  {
    title: 'Exclure de la demande de valorisation atypique',
    value: 'NO_VALUE_ATYPICAL',
  },
  {
    title: 'Développeur',
    value: 'DEVELOPPER',
  },
]

export interface User {
  id: number
  email: string | undefined
  gid: string
  firstName: string
  lastName: string
  function: string
  active: boolean
  roles: ProfileType[]
  // rolesJsonRaw: string
}

export interface UserWithEntities extends User {
  entities: Entity[]
  territories: Territory[]
}

export interface UserRequest extends Omit<User, 'id' | 'entities'> {
  entityIds: string[]
  territoryIds: number[]
}

export function mapToUserRequest(user: UserWithEntities, territoryIds?: number[]): UserRequest {
  return {
    email: user.email,
    gid: user.gid,
    firstName: user.firstName,
    lastName: user.lastName,
    roles: user.roles,
    // rolesJsonRaw: JSON.parse(user.rolesJsonRaw),
    function: user.function,
    active: user.active,
    entityIds: user.entities.map((org) => org.id),
    territoryIds: territoryIds ?? user.territories.map((t) => t.id),
  }
}

export function makeEmptyUser(): User {
  return {
    id: 0,
    email: undefined,
    gid: '',
    firstName: '',
    lastName: '',
    function: '',
    active: false,
    roles: [],
    // rolesJsonRaw: '[]',
  }
}

export function makeEmptyUserWithEntities(): UserWithEntities {
  return {
    id: 0,
    email: undefined,
    gid: '',
    firstName: '',
    lastName: '',
    function: '',
    active: false,
    roles: [],
    entities: [],
    territories: [],
    // rolesJsonRaw: '[]',
  }
}

export const userHasRole = (user: User, ...requiredRoles: ProfileType[]) => {
  const userRoles = user.roles
  return userRoles.some((role) => requiredRoles.includes(role))
}

export const userIsSiege = (user: User) => userHasRole(user, 'SIEGE', 'INSTRUCTEUR', 'ADMIN', 'ADMIN_PLUS')

export const userIsAdmin = (user: User) => userHasRole(user, 'ADMIN', 'ADMIN_PLUS')

export const userCanManageOperation = (user: User) =>
  userHasRole(user, 'AGENCE_PLUS', 'SUPPORT_AGENCE_PLUS', 'TERRITOIRE', 'SIEGE', 'INSTRUCTEUR', 'ADMIN', 'ADMIN_PLUS')

export const userCanManageSimulation = (user: User) =>
  userHasRole(user, 'AGENCE', 'AGENCE_PLUS', 'SUPPORT_AGENCE_PLUS', 'TERRITOIRE', 'ADMIN', 'ADMIN_PLUS')

export const displayFullnameUser = (user?: User, emptyValue = ''): string => {
  if (user) {
    if (user.firstName || user.lastName) {
      return (user.firstName ?? '') + ' ' + (user.lastName ?? '')
    } else {
      return user.gid
    }
  } else {
    return emptyValue
  }
}
