import type { ResponseHand<PERSON> } from '@/types/responseHandler'
import type { AxiosInstance, AxiosPromise } from 'axios'
import axiosInstance from '.'

const uri = '/admin_documents/files'
class AdminDocumentApi {
  public constructor(private axios: AxiosInstance) {}

  public downloadAll(): AxiosPromise<ResponseHandler> {
    return this.axios.get(uri)
  }

  public uploadAll(zipFile: File): AxiosPromise<ResponseHandler> {
    const formData = new FormData()
    formData.append('file', zipFile, zipFile.name)

    return this.axios.post(uri, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    })
  }
}

export const adminDocumentApi = new AdminDocumentApi(axiosInstance)
