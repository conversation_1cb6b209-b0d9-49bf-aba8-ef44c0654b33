import type { RouteLocationRaw } from 'vue-router'

// TODO temporaire
export interface DataOptions {
  page: number
  itemsPerPage: number
  sortBy: string[]
  sortDesc: boolean[]
  groupBy: string[]
  groupDesc: boolean[]
  multiSort: boolean
  mustSort: boolean
}

export type DataTableCompareFunction<T = unknown> = (a: T, b: T) => number

export interface DataTableHeader<T = any> {
  title: string
  value: string
  key?: string
  onclick?: (item: T, value: any) => void
  align?: 'start' | 'center' | 'end'
  sortable?: boolean
  filterable?: boolean
  groupable?: boolean
  divider?: boolean
  class?: string | string[]
  cellClass?: string | string[]
  width?: string
  filter?: (value: unknown, search: string | null, item: T) => boolean
  sort?: DataTableCompareFunction<T>
  to?: (value: T) => RouteLocationRaw
  formater?: (item: T, value: any) => string | undefined
  maxLength?: number
}

export interface DataPagination {
  page: number
  itemsPerPage: number
  pageStart: number
  pageStop: number
  pageCount: number
  itemsLength: number
}
