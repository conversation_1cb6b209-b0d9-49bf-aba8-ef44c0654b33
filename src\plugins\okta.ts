import { OktaAuth } from '@okta/okta-auth-js'

export default new OktaAuth({
  issuer: import.meta.env.VITE_OKTA_ISSUER,
  clientId: import.meta.env.VITE_OKTA_CLIENT_ID,
  redirectUri: window.location.origin + '/implicit/callback',
  scopes: ['openid', 'profile', 'email', 'offline_access'],
  pkce: false,
  responseMode: 'fragment',
  tokenManager: {
    storage: 'sessionStorage',
    autoRenew: true,
  },
})
