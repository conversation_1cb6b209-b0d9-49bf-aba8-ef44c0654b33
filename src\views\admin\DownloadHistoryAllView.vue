<template>
  <NjPage title="Historique des exports">
    <template #sub-header>
      <VRow>
        <VCol cols="6">
          <SearchInput :loading="data.loading" @update:model-value="updateFilter({ ...filter, search: $event })" />
        </VCol>
      </VRow>
    </template>
    <template #body>
      <NjDataTable :headers="headers" :page="data.value" :pageable="pageable" fixed @update:pageable="updatePageable" />
    </template>
  </NjPage>
</template>
<script setup lang="ts">
import { type DownloadHistory } from '@/types/downloadHistory'
import { usePaginationInQuery } from '@/types/pagination'
import { downloadHistoryApi, type DownloadHistoryFilter } from '@/api/downloadHistory'
import NjPage from '@/components/NjPage.vue'
import NjDataTable from '@/components/okta/NjDataTable.vue'
import type { User } from '@/types/user'
import type { LocalDateTime } from '@/types/date'
import { filter } from 'lodash'
import { VCol, VRow } from 'vuetify/components'

const headers = [
  {
    title: 'Date',
    value: 'creationDateTime',
    formater: (_: any, value: LocalDateTime) => formatHumanReadableLocalDateTime(value),
  },
  {
    title: 'Utilisateur',
    value: 'creationUser',
    formater: (_: any, value: User) => displayFullnameUser(value),
  },
  {
    title: 'Evénement',
    value: 'event',
    formater: (_: any, value: string) => resolveEvent(value),
  },
  {
    title: 'Périmètre',
    value: 'metaData',
    sortable: false,
    formater: (item: any) => (item?.metaData ? item.metaData.entityNames?.join(', ') : ''),
  },
  {
    title: 'Nombre de ligne téléchargé',
    value: 'metaData.rowNumber',
    sortable: false,
    formater: (_: any, value: number) => (isNaN(value) ? '' : formatNumber(value)),
  },
]

const { data, updateFilter, pageable, updatePageable } = usePaginationInQuery<DownloadHistory, DownloadHistoryFilter>(
  (filter, pageable) => downloadHistoryApi.findAll(pageable, filter)
)

const resolveEvent = (event: string) => {
  switch (event) {
    case 'standardizedOperationSheets':
      return 'Export des FOS'
    case 'operations':
      return 'Export des opérations'
    case 'ceeStocks':
      return 'Export des stocks'
    case 'sales':
      return 'Export des ventes'
    case 'users':
      return 'Utilisateurs'
    default:
      return event
  }
}
</script>
