import Formula from 'fparser'
import type { Ref } from 'vue'
import type { PromisableValue } from '../promisableValue'
import type { ParameterFormula } from './parameterFormula'
import { ceil, floor } from 'lodash'

export const parsingFormula = (formula: string | null | undefined): PromisableValue<any> => {
  console.debug('parsingFormula')
  formula = formula?.trim()
  try {
    const formulaObject = new Formula(formula)
    // TODO je verrais plus tard si je fais une vérification des variables
    // const undefinedVariable = (formulaObject._variables as string[]).find(
    //   (variable) =>
    //     !props.ficheOperation.parameters.find((it) => it.id === variable) &&
    //     !props.ficheOperation.mappingTables.find((it) => it.id === variable)
    // )
    // if (undefinedVariable) {
    //   return errorValue("La variable ' " + undefinedVariable + "' n'existe pas")
    // }
    return succeedValue(formulaObject)
  } catch (e: unknown) {
    if (e instanceof Error) {
      if (
        e.message === 'Could not parse formula: Syntax error.' ||
        e.message === 'Could not parse formula: incorrect syntax?'
      ) {
        return errorValue('Erreur de syntaxe dans la formule')
      } else {
        logException(e)
        return errorValue("Erreur imprévu lors de l'évaluation de la formule")
      }
    } else {
      logException(e)
      return errorValue("Erreur imprévu lors de l'évaluation de la formule")
    }
  }
}

export const evaluateFormula = (
  formulaObject: any,
  values: Record<string, any>,
  allValues: Record<string, any>[] | undefined = undefined
): PromisableValue<number> => {
  console.debug('evaluateFormula')
  try {
    return succeedValue(
      formulaObject.evaluate({
        ...values,
        ppq: (a: number, b: number) => (a < b ? 1 : 0),
        ppe: (a: number, b: number) => (a <= b ? 1 : 0),
        pgq: (a: number, b: number) => (a > b ? 1 : 0),
        pge: (a: number, b: number) => (a >= b ? 1 : 0),
        egal: (a: number, b: number) => (a === b ? 1 : 0),
        ne: (a: number, b: number) => (a !== b ? 1 : 0),
        non: (a: number) => (a === 0 ? 1 : 0),
        et: (...args: number[]) => (args.every((a) => a > 0) ? 1 : 0),
        ou: (...args: number[]) => (args.some((a) => a > 0) ? 1 : 0),
        si: (condition: number, then: number, other: number) => (condition === 1 ? then : other),
        arrondi: (nombre: number, x: number) => Number(nombre.toFixed(x)),
        arrondiInferieur: (nombre: number, x: number) => floor(nombre, x),
        arrondiSuperieur: (nombre: number, x: number) => ceil(nombre, x),
        SommePuissance: () => {
          if (allValues) {
            return allValues.reduce((acc, v) => acc + Number(v['puissance_plus']), 0)
          } else {
            return 0
          }
        },
        SommeEquipementEligible: () => {
          if (allValues) {
            return allValues.reduce(
              (acc, v) => acc + (v['est_equipement_eligible'] === 1 ? Number(v['puissance_plus']) : 0),
              0
            )
          } else {
            return 0
          }
        },
      } as Record<
        string,
        | ((arg: number) => number)
        | ((arg1: number, arg2: number) => number)
        | ((arg1: number, arg2: number, arg3: number) => number)
      >)
    )
  } catch (e) {
    if (e instanceof Error) {
      if (e.message.endsWith(': No value given')) {
        return errorValue('Il manque des champs à remplir (' + e.message + ')')
      } else {
        logException(e)
        return errorValue('Erreur pendant le calcul')
      }
    } else {
      logException(e)
      return errorValue('Erreur pendant le calcul')
    }
  }
}

export const useFormulaEvaluator = () => {
  const updateComputedParameter = (iCas: number, i: number, f: string) => {
    updateFormula('cp' + iCas + i, f)
  }

  const updateFormula = (index: string, formula: string | undefined | null) => {
    if (formula) {
      formulas.value[index] = parsingFormula(formula)
    }
  }
  const formulas: Ref<Record<string, PromisableValue<any>>> = ref({})
  return { formulas, updateComputedParameter, updateFormula }
}

export const convertChoiceToIndex = (parameters: ParameterFormula[], pValues: any) => {
  console.debug('convertChoiceToIndex')
  const values = { ...pValues }
  parameters
    .filter((it) => it.type === 'CHOICE')
    .forEach((p) => {
      const items = p.data.split(';') as string[]
      values[p.id] = items.indexOf(values[p.id])
      if (values[p.id] >= 0) {
        values[p.id]++
      }
    })
  return values
}
