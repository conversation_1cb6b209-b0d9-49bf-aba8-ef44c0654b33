import type { MessageTemplate, MessageTemplateRequest } from '@/types/message'
import type { Page, Pageable } from '@/types/pagination'
import type { AxiosInstance, AxiosPromise } from 'axios'
import axiosInstance from '.'

export interface MessageTemplateFilter {
  search?: string
}

class MessageTemplateApi {
  public constructor(private axios: AxiosInstance) {}

  public create(request: MessageTemplateRequest): AxiosPromise<MessageTemplate> {
    return this.axios.post('/message_templates', request)
  }

  public update(id: number, request: MessageTemplateRequest): AxiosPromise<MessageTemplate> {
    return this.axios.put('/message_templates/' + id, request)
  }

  public findAll(filter: MessageTemplateFilter, pageable: Pageable): AxiosPromise<Page<MessageTemplate>> {
    return this.axios.get('/message_templates', {
      params: { ...pageable, ...filter },
    })
  }

  public findOne(id: number): AxiosPromise<MessageTemplate> {
    return this.axios.get('/message_templates/' + id)
  }

  public deleteById(id: number): AxiosPromise<void> {
    return this.axios.delete('/message_templates/' + id)
  }
}

export const messageTemplateApi = new MessageTemplateApi(axiosInstance)
