<template>
  <VCard v-if="modelValue">
    <VCardTitle class="text-primary font-weight-bold" style="font-size: 1rem !important">
      <VRow>
        <VCol>
          {{ displayFullnameUser(modelValue.creationUser) }}
        </VCol>
        <VSpacer />
        <VCol class="flex-grow-0">
          {{ modelValue.event == 'CREATED' ? 'Création' : 'Mis à jour' }}
        </VCol>
      </VRow>
    </VCardTitle>
    <VCardSubtitle class="ps-4">
      {{ formatHumanReadableLocalDateTime(modelValue.creationDateTime) }}
    </VCardSubtitle>
    <VCardText>
      <div v-for="(change, index) in modelValue.changeSet" :key="index">
        <template v-if="change.fieldName != 'id'">
          <NjDisplayValue
            v-if="modelValue.event == 'CREATED'"
            :label="getLabel(change.fieldName)"
            :value="getValue(change.fieldName, change.newValue)"
          />
          <NjDisplayValue
            v-else
            :label="getLabel(change.fieldName)"
            :value="`${getValue(change.fieldName, change.oldValue) ?? ''} → ${getValue(change.fieldName, change.newValue)}`"
          />
        </template>
      </div>
    </VCardText>
  </VCard>
</template>
<script setup lang="ts">
import { displayFullnameUser } from '@/types/user'
import { formatHumanReadableLocalDateTime } from '@/types/date'
import type { PropType } from 'vue'
import type { History } from '@/types/history'

const props = defineProps({
  modelValue: Object as PropType<History>,
  displayProperties: Array as PropType<{ fieldname: string; label: string; resolver?: (value: any) => any }[]>,
})

const getPropertiesByFieldName = (fieldName: string) => {
  return props.displayProperties?.find((i) => i.fieldname == fieldName)
}

const getLabel = (fieldName: string): string => {
  return getPropertiesByFieldName(fieldName)?.label ?? fieldName
}

const getValue = (fieldName: string, value: any) => {
  const resolver = getPropertiesByFieldName(fieldName)?.resolver
  if (resolver) {
    return resolver(value)
  }
  return value
}
</script>
