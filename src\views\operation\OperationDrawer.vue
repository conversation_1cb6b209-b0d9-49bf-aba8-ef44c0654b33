<template>
  <div v-if="modelValue" location="right" style="width: 600px" permanent>
    <OperationCard
      ref="operationCardRef"
      v-model:edit="edit"
      v-model:operation="selectedOperation"
      :in-operation-group="inOperationGroup"
      :loading="loading"
      mode="drawer"
      @close="emit('update:model-value')"
      @saved="emit('update:operation')"
      @cancel-operation-k-o-p-n-c-e-e="onCancel"
      @final-version-sent="emit('final-version-sent')"
    />
  </div>
</template>
<script lang="ts" setup>
import type { Operation } from '@/types/operation'
import { clone, cloneDeep, isEqual } from 'lodash'
import type { PropType } from 'vue'
import OperationCard from './OperationCard.vue'

const props = defineProps({
  operation: {
    type: Object as PropType<Operation>,
    default: makeEmptyOperation,
  },
  inOperationGroup: Boolean,
  loading: <PERSON>olean,
  modelValue: <PERSON>olean,
})

const emit = defineEmits<{
  'update:model-value': [void]
  'update:operation': [void]
  cancelOperationKOPNCEE: [void]
  'final-version-sent': []
}>()

const selectedOperation = ref(clone(props.operation))
const operationCardRef = ref<typeof OperationCard | null>(null)
const edit = ref(false)
watch(
  () => props.operation,
  (v) => {
    if (v.id !== selectedOperation.value.id) {
      edit.value = false
    }
    if (!isEqual(v, selectedOperation.value)) {
      selectedOperation.value = cloneDeep(v)
    }
  },
  {
    immediate: true,
  }
)
const onCancel = () => {
  emit('cancelOperationKOPNCEE')
}
defineExpose({
  check: (callback: () => void) => {
    if (operationCardRef.value) {
      operationCardRef.value?.check(callback)
    } else {
      callback()
    }
  },
})
</script>
