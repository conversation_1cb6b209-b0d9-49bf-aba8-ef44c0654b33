import { cloneDeep, isEqual } from 'lodash'
import type { Ref } from 'vue'

export interface UnsavedDataDialogData {
  active: boolean
  confirmCallback: (() => void) | undefined
}

export const useUnsavedData = (...refs: Ref<unknown>[]) => {
  const copiedData: Ref<unknown[]> = ref([])
  let savedData: boolean[] = refs.map(() => true)
  refs.forEach((it, i) => {
    watch(
      it,
      (v) => {
        // console.debug('watch unsaved', i, isRef(v), JSON.stringify(v), JSON.stringify(copiedData.value[i]))
        savedData[i] = isEqual(v, copiedData.value[i])
        let response = true
        savedData.forEach((it) => {
          response &&= it
        })
        saved.value = response
      },
      {
        deep: true,
      }
    )
  })
  const saved = ref(true)
  const roSaved = readonly(saved)

  const disabled = ref(false)
  const disable = () => (disabled.value = true)
  const enable = () => (disabled.value = false)

  const reset = () => {
    saved.value = true
    copiedData.value = refs.map((it) => cloneDeep(unref(it)))
    savedData = refs.map(() => true)
    unsavedDataDialog.value?.confirmCallback?.()
    unsavedDataDialog.value = {
      active: false,
      confirmCallback: undefined,
    }
  }
  const unsavedDataDialog = ref<UnsavedDataDialogData>({
    active: false,
    confirmCallback: () => clearChanges(),
  })

  // TODO essayer de transformer plutôt en promise
  // check retournea le promise pour sa voir si le mec a fait ne rien sauver ou sauvegarder si vrai, ou bien false s'il a cliqué sur annuler
  // Il y aura un paramètre pour dire quoi faire en plus s'il clique sur sauvegarder
  const check = (confirmCallback: () => void): boolean => {
    // console.debug(JSON.stringify(refs.map((it) => it.value)))
    // console.debug(JSON.stringify(copiedData))
    if (!saved.value) {
      unsavedDataDialog.value = {
        active: true,
        confirmCallback,
      }
      return false
    } else {
      confirmCallback()
      return true
    }
  }
  onBeforeRouteLeave((_to, _from, next) => {
    if (disabled.value) {
      next()
      return
    }
    check(next)
  })
  onBeforeRouteUpdate((_to, _from, next) => {
    if (disabled.value) {
      next()
      return
    }
    check(next)
  })
  const succeedSave = () => {
    unsavedDataDialog.value.active = false
    reset()
  }
  const failedSave = () => {
    unsavedDataDialog.value.active = false
  }
  const clearChanges = () => {
    console.debug('clearChanges')
    refs.forEach((el, i) => {
      el.value = cloneDeep(copiedData.value[i])
    })
  }
  return {
    copitedData: readonly(copiedData),
    saved: roSaved,
    disabled,
    disable,
    enable,
    onSave: reset,
    unsavedDataDialog,
    succeedSave,
    failedSave,
    check,
    clearChanges,
  }
}
