export const downloadFile = (filename: string, data: Blob) => {
  const url = window.URL.createObjectURL(
    new File([data], filename, {
      type: data.type,
    })
  )

  if (data.type.startsWith('image/') || data.type === 'application/pdf') {
    window.open(url, '_blank')
  } else {
    const link = document.createElement('a')
    link.target = '_blank'
    link.href = url
    link.download = filename
    link.click()
  }
}

export const getFileExtension = (filename: string) => {
  return filename.split('.').pop()
}
