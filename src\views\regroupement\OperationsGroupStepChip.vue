<template>
  <StepChip v-if="step" :status="stepChipType" :text="step <= 100 ? step + '' : 'Validée'" />
</template>
<script setup lang="ts">
import type { OperationStatus } from '@/types/operation'
import type { PropType } from 'vue'
import StepChip from '../operation/StepChip.vue'

const props = defineProps({
  step: Number,
  status: String as PropType<OperationStatus>,
})

const stepChipType = computed(() => {
  if (props.status == 'DOING') {
    return 'success'
  } else if (props.status == 'DONE') {
    return 'done'
  } else if (props.status) {
    return 'error'
  }
})
</script>
