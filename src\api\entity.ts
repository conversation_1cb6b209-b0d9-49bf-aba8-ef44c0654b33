import type { Entity, EntityDetails, EntityRequest, UpdateEntityDetailsRequest } from '@/types/entity'
import type { Page, Pageable } from '@/types/pagination'
import type { AxiosInstance, AxiosPromise } from 'axios'
import axiosInstance from '.'

export interface EntityFilter {
  ids?: string[]
  startWithNavFullIds?: string[]
  search?: string
  name?: string
  enabled?: boolean
  visible?: boolean
  exclusions?: string[]
  myEntities?: boolean
  level?: number
  canSell?: boolean
  territoryIds?: number[]
}

class EntityApi {
  public constructor(private axios: AxiosInstance) {}

  public create(request: EntityRequest): AxiosPromise<Entity> {
    return this.axios.post('entities', request)
  }
  public updateFromGtf(request: EntityRequest[]): AxiosPromise<Entity> {
    return this.axios.put('entities', request, {
      params: {
        fromGtf: true,
      },
    })
  }

  public getAll(filter: EntityFilter, pageable: Pageable): AxiosPromise<Page<Entity>> {
    return this.axios.get('entities', {
      params: { ...filter, ...pageable },
    })
  }

  public getAllPublic(filter: EntityFilter, pageable: Pageable): AxiosPromise<Page<Entity>> {
    return this.axios.get('public_entities', {
      params: { ...filter, ...pageable },
    })
  }

  public getOne(id: string): AxiosPromise<Entity> {
    return this.axios.get('entities/' + id)
  }

  // public update(id: string, request: UpdateEntityRequest): AxiosPromise<Entity> {
  //   return this.axios.put('entities/' + id, request)
  // }
  public updateDetails(id: string, request: UpdateEntityDetailsRequest): AxiosPromise<EntityDetails> {
    return this.axios.put('entities/' + id + '/details', request)
  }
}

export const entityApi = new EntityApi(axiosInstance)
