import type { CeeStock } from '@/types/ceeStock'
import { entityLevelLabels } from '@/types/entity'

export const ceeStockByEntityHeader = [
  {
    title: 'Désignation',
    value: 'name',
    sortable: false,
  },
  {
    title: 'Prime CEE Agence €',
    value: 'ceePrime',
    sortable: false,
    formater: (_: any, value: number) => formatPriceNumber(value),
    cellClass: 'text-right justify-end',
  },
  {
    title: 'Charge Cellule CEE €',
    value: 'ceeCharge',
    formater: (_: any, value: number) => formatPriceNumber(value),
    sortable: false,
    cellClass: 'text-right justify-end',
  },
  {
    title: 'Nb. Total kWhc',
    value: 'totalCumac',
    formater: (item: CeeStock) => formatKwhcNumber(item.classicCumac + item.precariousnessCumac),
    sortable: false,
    cellClass: 'text-right justify-end',
  },
  {
    title: 'Nb. Class. kWhc',
    value: 'classicCumac',
    formater: (_: any, value: number) => formatKwhcNumber(value),
    sortable: false,
    cellClass: 'text-right justify-end',
  },
  {
    title: 'Nb. Préc. kWhc',
    value: 'precariousnessCumac',
    formater: (_: any, value: number) => formatKwhcNumber(value),
    sortable: false,
    cellClass: 'text-right justify-end',
  },
  {
    title: 'Code',
    value: 'id',
    formater: (item: CeeStock) => item.entity.id,
  },
  {
    title: 'Niveau',
    value: 'level',
    formater: (item: CeeStock) => entityLevelLabels[item.entity.level],
  },
  {
    title: 'Hiérarchie',
    value: 'originEntityNavFullId',
    formater: (item: CeeStock) => item.entity.navFullId,
  },
]
