$color_primary: #007acd;

.p-relative {
  position: relative !important;
}

.fill-width {
  width: 100%;
}

.fill-height {
  min-height: 100%;
}

.text-section-title {
  font-size: 18px;
  font-weight: 700;
  line-height: 28px;
}

html,
body {
  height: 100%;
  overflow-y: auto !important;
  background-color: #f5f5f5;
}

.sortable-handle {
  cursor: grab;
}

.content-layout {
  display: flex !important;
  height: 100%;
  flex-direction: column;
  // grid-template-columns: 1fr;
  // grid-template-rows: auto 1fr auto;
  // grid-template-areas:
  //   'header'
  //   'main'
  //   'footer';

  > main,
  > .content-layout__main {
    // grid-area: main;
    flex-basis: 0;
    flex-grow: 1;
    overflow: auto;
  }

  > header,
  > .content-layout__header {
    // grid-area: header;
    flex-grow: 0;
  }

  > footer,
  > .content-layout__footer {
    // grid-area: footer;
    flex-grow: 0;
  }
}

.v-table {
  font-size: 1rem;
}

.content-layout > .v-table__wrapper {
  height: 100%;
}

.subheader-actions {
  display: flex;
  gap: 16px;
}

.v-row.h-100,
.h-100--v-row,
.content-layout.v-row {
  height: calc(100% + 24px) !important;
}
.v-row.w-100,
.w-100--v-row {
  width: calc(100% + 24px) !important;
}
.v-row.v-row--dense.h-100,
.w-100--v-row--dense,
.content-layout.v-row.v-row--dense {
  height: calc(100% + 8px) !important;
}
.v-row.v-row--dense.w-100,
.h-100--v-row--dense,
.content-layout.v-row.v-row--dense {
  width: calc(100% + 8px) !important;
}

.value {
  &__label {
    font-size: 0.875rem;
    color: #60798b;
  }

  &__value {
    font-size: 1rem;
    font-weight: 700;
  }
}

.flex-basis-0 {
  flex-basis: 0 !important;
}

.flex-basis-content {
  flex-basis: content !important;
}

.rich-text {
  ul {
    display: block;
    list-style-type: disc;
    margin-inline-start: 0px;
    margin-inline-end: 0px;
    padding-inline-start: 30px;
  }

  ol {
    display: block;
    list-style-type: decimal;
    margin-inline-start: 0px;
    margin-inline-end: 0px;
    padding-inline-start: 30px;
  }

  blockquote {
    display: block;
    margin-block-start: 1em;
    margin-block-end: 1em;
    margin-inline-start: 15px;
    padding-left: 1rem;
    border-left: 2px solid rgba(13, 13, 13, 0.1);
  }

  pre {
    font-family: 'JetBrainsMono', monospace;
    padding: 0.75rem 1rem;
    border-radius: 0.5rem;

    code {
      background: #0d0d0d;
      color: #fff;
      padding: 0;
      font-size: 0.8rem;
      display: block;
    }
  }

  code {
    background-color: #6161611a;
    color: #616161;
  }
}

kbd {
  background-color: #eee;
  border-radius: 3px;
  border: 1px solid #b4b4b4;
  box-shadow:
    0 1px 1px rgba(0, 0, 0, 0.2),
    0 2px 0 0 rgba(255, 255, 255, 0.7) inset;
  color: #333;
  display: inline-block;
  font-size: 0.85em;
  font-weight: 700;
  line-height: 1;
  padding: 2px 4px;
  white-space: nowrap;
}

.clickable {
  cursor: pointer;
}

@for $i from 1 through 8 {
  .gap-#{$i} {
    gap: #{$i * 4}px;
  }
}

.v-input.scrollable .v-field .v-field__input {
  max-height: max(var(--v-input-control-height), calc(var(--max-height, 0px) - 24px));
  overflow-y: auto;
  margin-top: 24px;
  padding-top: 0px;
}

.v-hidden {
  visibility: hidden !important;
}

.text-discovery {
  color: #744299
}