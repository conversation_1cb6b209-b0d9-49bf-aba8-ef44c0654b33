import type { DataTableHeader } from '@/components/okta/NjDataTable.type'
import type { Operation } from '@/types/operation'
import { displayFullnameUser } from '@/types/user'
import { mapToReadableStatus } from '@/types/operation'
import { commercialStatusTitle } from '@/types/steps'
import { formatNumber, formatPriceNumber } from '@/types/format'

import router from '@/router'
import { useUserStore } from '@/stores/user'

const userStore = useUserStore()

export const operationTableHeaders = (
  clickRow: (item: Operation, value: any) => void
): DataTableHeader<Operation>[] => [
  {
    title: 'Chrono',
    value: 'chronoCode',
    cellClass: 'text-no-wrap',
    onclick: clickRow,
  },
  {
    title: 'Organisation',
    value: 'entity.name',
    formater: (item) => `${item.entity.name} (${item.entity.id})`,
  },
  {
    title: 'Installation',
    value: 'property',
  },
  {
    title: 'Étape',
    value: 'stepId',
  },
  {
    title: 'Nb. Rés. Class. kWhc',
    value: 'reservedClassicCumac',
    formater: (_, value) => formatNumber(value),
    cellClass: 'text-right justify-end',
  },
  {
    title: 'Nb. Rés. Préca. kWhc',
    value: 'reservedPrecariousnessCumac',
    formater: (_, value) => formatNumber(value),
    cellClass: 'text-right justify-end',
  },
  {
    title: 'Date de réservation',
    value: 'reservedDate',
    formater: (_, value) => formatHumanReadableLocalDate(value),
  },
  {
    title: 'Valo. Rés. Class',
    value: 'reservedClassicValuationValue',
    formater: (_, value) => formatPriceNumber(value) + '/MWhc',
    cellClass: 'text-right justify-end',
  },
  {
    title: 'Mnt. Rés. Class €',
    value: 'reservedClassicAmount',
    sortable: false,
    formater: (item) =>
      formatPriceNumber(((item.reservedClassicValuationValue ?? 0) * item.reservedClassicCumac) / 1000),
    cellClass: 'text-right justify-end',
  },
  {
    title: 'Valo. Rés. Préca',
    value: 'reservedPrecariousnessValuationValue',
    formater: (_, value) => formatPriceNumber(value) + '/MWhc',
    cellClass: 'text-right justify-end',
  },
  {
    title: 'Mnt. Rés. Préca €',
    value: 'reservedPrecariousnessAmount',
    sortable: false,
    formater: (item) =>
      formatPriceNumber(((item.reservedPrecariousnessValuationValue ?? 0) * item.reservedPrecariousnessCumac) / 1000),
    cellClass: 'text-right justify-end',
  },
  {
    title: 'Nb. Rés. Total kWhc',
    value: 'totalPrecariousnessCumac',
    sortable: false,
    formater: (item) => formatNumber(item.reservedClassicCumac + item.reservedPrecariousnessCumac),
    cellClass: 'text-right justify-end',
  },
  {
    title: 'Type Valorisation',
    value: 'valuationType.name',
  },
  {
    title: 'Offre commerciale',
    value: 'commercialStatus',
    formater: (_, value) => commercialStatusTitle.find((status) => status.value === value)?.title,
    cellClass: 'text-no-wrap',
  },
  {
    title: 'Demandeur',
    value: 'applicantUser',
    formater: (_, value) => displayFullnameUser(value),
  },
  {
    title: 'Raison social Bénéficiaire',
    value: 'beneficiary.socialReason',
    cellClass: 'text-no-wrap',
  },
  {
    title: 'Nom Bénéficiaire',
    value: 'beneficiary.lastName',
    formater: (item) => (item.beneficiary?.firstName ?? '') + (item.beneficiary?.lastName ?? ''),
    cellClass: 'text-no-wrap',
  },
  {
    title: 'SIREN Bénéficiaire',
    value: 'beneficiary.siren',
  },
  {
    title: 'Adresse Bénéficiaire',
    value: 'beneficiary.address',
    cellClass: 'text-no-wrap',
  },
  {
    title: 'Code postal Bénéficiaire',
    value: 'beneficiary.postalCode',
  },
  {
    title: 'Ville Bénéficiaire',
    value: 'beneficiary.city',
  },
  {
    title: 'Numéro offre client',
    value: 'customerOfferNumber',
  },
  {
    title: 'Engagement prévisionnel',
    value: 'estimatedCommitmentDate',
    formater: (_, value) => formatHumanReadableLocalDate(value),
  },
  {
    title: 'Engagement réel',
    value: 'signedDate',
    formater: (_, value) => formatHumanReadableLocalDate(value),
  },
  {
    title: 'Offre commerciale avant incitation financière CEE TTC',
    value: 'commercialOfferWithoutFinancialIncentive',
    formater: (_, value) => formatPriceNumber(value),
    cellClass: 'text-right justify-end',
  },
  {
    title: 'Incitation financière CEE TTC',
    value: 'customerFinancialIncentive',
    formater: (_, value) => formatPriceNumber(value),
    cellClass: 'text-right justify-end',
  },
  {
    title: "Montant de l'offre commerciale après déduction de l'incitation financière CEE TTC",
    value: 'commercialOfferWithFinancialIncentive',
    sortable: false,
    formater: (item) =>
      formatPriceNumber(item.commercialOfferWithoutFinancialIncentive - item.customerFinancialIncentive),
    cellClass: 'text-right justify-end',
  },
  {
    title: 'Marge CEE Nette TTC',
    value: 'netMargin',
    formater: (item) => formatPriceNumber(getNetMargin(item)),
    cellClass: 'text-right justify-end',
    sortable: false,
  },
  {
    title: 'Code opération',
    value: 'standardizedOperationSheet.operationCode',
    cellClass: 'text-no-wrap',
  },
  {
    title: 'Libellé opération',
    value: 'standardizedOperationSheet.description',
    cellClass: 'text-no-wrap',
  },
  {
    title: 'Numéro EMMY',
    value: 'emmyFolder.emmyCode',
  },
  {
    title: 'Nom du dossier EMMY',
    value: 'emmyFolder.name',
  },

  {
    title: 'Date envoi PNCEE',
    value: 'emmyFolder.pnceeSubmissionDate',
    formater: (_, value) => formatHumanReadableLocalDate(value),
  },
  {
    title: 'Nb. Dem. Class. kWhc',
    value: 'classicCumac',
    formater: (_, value) => formatNumber(value),
    cellClass: 'text-right justify-end',
  },
  {
    title: 'Valo. Dem. Class',
    value: 'classicValuationValue',
    sortable: false,
    formater: (item) => formatPriceNumber(item.atypicalClassicValuationValue ?? item.classicValuationValue) + '/MWhc',
    cellClass: 'text-right justify-end',
  },
  {
    title: 'Mnt. Dem. Class €',
    value: 'ClassicAmount',
    sortable: false,
    formater: (item) =>
      formatPriceNumber(
        ((item.atypicalClassicValuationValue ?? item.classicValuationValue) * item.classicCumac) / 1000
      ),
    cellClass: 'text-right justify-end',
  },

  {
    title: 'Nb. Dem. Préca. kWhc',
    value: 'precariousnessCumac',
    formater: (_, value) => formatNumber(value),
    cellClass: 'text-right justify-end',
  },
  {
    title: 'Valo. Dem. Préca',
    value: 'precariousnessValuationValue',
    sortable: false,
    formater: (item) =>
      formatPriceNumber(item.atypicalPrecariousnessValuationValue ?? item.precariousnessValuationValue) + '/MWhc',
    cellClass: 'text-right justify-end',
  },
  {
    title: 'Mnt. Dem. Préca €',
    value: 'PrecariousnessAmount',
    sortable: false,
    formater: (item) =>
      formatPriceNumber(
        ((item.atypicalPrecariousnessValuationValue ?? item.precariousnessValuationValue) * item.precariousnessCumac) /
          1000
      ),
    cellClass: 'text-right justify-end',
  },
  {
    title: 'Nb. Dem. Total kWhc',
    value: 'totalClassicCumac',
    sortable: false,
    formater: (item) => formatNumber(item.classicCumac + item.precariousnessCumac),
    cellClass: 'text-right justify-end',
  },
  {
    title: 'Date de délivrance PNCEE',
    value: 'emmyFolder.pnceeIssuedDate',
    formater: (_, value) => formatHumanReadableLocalDate(value),
  },
  {
    title: 'Fin Travaux prévisionnelle',
    value: 'estimatedEndOperationDate',
    formater: (_, value) => formatHumanReadableLocalDate(value),
  },
  {
    title: 'Fin Travaux réelle',
    value: 'actualEndWorksDate',
    formater: (_, value) => formatHumanReadableLocalDate(value),
  },
  {
    title: 'Période',
    value: 'period',
    formater: (_, value) => value?.name,
  },
  {
    title: 'Instructeur',
    value: 'instructor',
    formater: (_, value) => displayFullnameUser(value),
  },
  {
    title: 'Début validité opération',
    value: 'standardizedOperationSheet.startDate',
    formater: (_, value) => formatHumanReadableLocalDate(value),
  },
  {
    title: 'Fin validité opération',
    value: 'standardizedOperationSheet.expirationDate',
    formater: (_, value) => formatHumanReadableLocalDate(value),
  },
  {
    title: 'Nom opération',
    value: 'operationName',
    cellClass: 'text-no-wrap',
    onclick: clickRow,
  },
  {
    title: 'Num. Simulation',
    value: 'id',
    onclick: clickRow,
    formater: (item) => (item.simulationName ? item.id.toString() : ''),
  },
  {
    title: 'Nom Simulation',
    value: 'simulationName',
    cellClass: 'text-no-wrap',
  },
  {
    title: 'Adresse',
    value: 'finalAddress.street',
  },
  {
    title: 'Statut',
    value: 'statusRank',
    formater: (item) => mapToReadableStatus(item.status),
    cellClass: 'text-no-wrap',
  },
  {
    title: 'Regroupement',
    value: 'operationsGroup.name',
    to: (item) =>
      item.operationsGroup ? { name: 'OperationsGroupOneView', params: { id: item.operationsGroup.id } } : '',
    onclick: (item, value) =>
      item.operationsGroup
        ? router.push({ name: 'OperationsGroupOneView', params: { id: item.operationsGroup.id } })
        : clickRow(item, value),
  },
  {
    title: 'Soumise à arrêté contrôle',
    value: 'standardizedOperationSheet.controlOrderStartDate',
    formater: (_, value) => formatHumanReadableLocalDate(value),
    sortable: false,
  },
  {
    title: 'Coup de pouce',
    value: 'cdp',
    sortable: false,
  },
  {
    title: 'CPE',
    value: 'epc',
    sortable: false,
  },
  {
    title: 'Précarité',
    value: 'precariousness',
    sortable: false,
  },
  {
    title: 'Date envoi offre client',
    value: 'offersDispatchDate',
    formater: (_, value) => formatHumanReadableLocalDate(value),
  },
  {
    title: 'Comptabilisation',
    value: 'accountedMessagesNumber',
    sortable: false,
    cellClass: 'text-right justify-end',
  },
  {
    title: "Nombre de renvoi à l'étape 50",
    value: 'backToStep50Counter',
    sortable: false,
    formater: (_: any, value: number) => formatNumber(value),
    cellClass: 'text-right justify-end',
  },
  {
    title: 'Charge pôle CEE',
    value: 'agenceFee',
    formater: (item) => {
      return formatPriceNumber(((item.classicCumac + item.precariousnessCumac) / 1000) * getPoleChargeCEE(item))
    },
    sortable: false,
    cellClass: 'text-right justify-end',
  },
  {
    title: 'Date de génération du CSV',
    value: 'exportToCsvDateTime',
    formater: (_, value) => formatHumanReadableLocalDateTime(value),
  },
  {
    title: 'Saisie dans l’outil',
    value: 'manualCumac',
    sortable: false,
  },
  {
    title: 'Opération chapeau',
    value: 'headOperation',
  },
  {
    title: "Nb. estimé d'opération dans le chapeau",
    value: 'estimatedNumberOfOperationInHead',
    formater: (_: any, value: number) => formatNumber(value),
    cellClass: 'text-right justify-end',
  },
  {
    title: 'Second interlocuteur',
    value: 'secondInterlocutor',
  },
  ...(userStore.isAdminPlus || userStore.isSiege
    ? [
        {
          title: 'Affectation OSH',
          value: 'oshPriority',
          sortable: false,
          formater: (operation: Operation, value: number | null) =>
            value !== null ? 'P' + operation.period?.id + ' - D' + value : '',
        },
      ]
    : []),
]
