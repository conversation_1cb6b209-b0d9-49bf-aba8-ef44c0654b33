import type { EpcBonusSheet } from '@/types/epcBonus'
import type { Operation } from '@/types/operation'
import { isEqual, toString, sum } from 'lodash'
import type { Ref } from 'vue'
import {
  DEFAULT_DATE_MAX_BONIFICATION,
  DEFAULT_DATE_MIN_BONIFICATION,
  useFindCorrectBoostBonusSheet,
} from './admin/boostBonusSheet/boostBonusSheetComposition'
import { useFindCorrectPrecariousnessBonus } from './admin/precariousnessBonus/precariousnessBonusComposition'
import type { BoostBonusSimulation } from '@/types/boostBonus'
import type { Page } from '@/types/pagination'
import type { PromisableValue } from '@/types/promisableValue'
import { usePeriodsStore } from '@/stores/periods'
import { formatISO } from 'date-fns'

const roundCumac = (val: number) => -Math.round(Math.round(-val * 100) / 100)

const roundCumacOperationLines = (values: number[]) => {
  return values.map((val) => roundCumac(val))
}
export const sumCumacOperationLines = (value: number[]) => value.reduce((acc, val) => acc + roundCumac(val), 0)

export const useOperation = (operation: Ref<Operation | undefined>) => {
  const showBoostBonus = ref(false)
  const showEpcBonus = ref(false)
  const showPrecariousnessBonus = ref(false)

  const predefinedValues = computed(() => ({
    zone_climatique: resolveZoneClimatique(
      operation.value?.finalAddress?.postalCode ?? operation.value?.property?.postalCode
    ),
    region: resolveRegion(operation.value?.finalAddress?.postalCode ?? operation.value?.property?.postalCode),
  }))
  const volumeCEE = ref<number[]>([0])
  const usingOtherAddress = ref(false)

  const boostBonusSimulationValues = ref<BoostBonusSimulation['parameterValues']>({})
  const epcBonusSimulationValues = ref()
  const precariousnessBonusSimulationValues = ref()

  watch(volumeCEE, (v) => {
    if (operation.value) {
      operation.value.operationLineCumacWithoutBonus = v
      operation.value.issuedFromStandardizedOperationSheetCumac = sumCumacOperationLines(v)
    }
  })

  // Coup de pouce
  const predefinedValuesForBoostBonusSheet = computed(() => {
    return volumeCEE.value.map((it) => ({
      ...predefinedValues.value,
      V: it,
    }))
  })
  const volumeCEEAfterBoostBonusSheet = ref<number[]>([0])
  const { boostBonusSheet: boostBonusSheet, boostBonusSheets: boostBonusSheets } =
    useFindCorrectBoostBonusSheet(operation)
  watch(
    showBoostBonus,
    (v) => {
      if (v) {
        if (operation.value && operation.value?.boostBonusSimulation == null && boostBonusSheet.value) {
          operation.value.boostBonusSimulation = {
            boostBonusSheet: boostBonusSheet.value.value!,
            parameterValues: {},
          }
        }
      } else {
        operation.value!.boostBonusSimulation = null
      }
    },
    {
      immediate: true,
    }
  )
  watch(boostBonusSheet, (v) => {
    if (v.value === undefined && v.error && showBoostBonus.value) {
      showBoostBonus.value = false
    }
  })

  // CPE
  const epcBonus = ref(1)
  const epcBonusSheets = ref(emptyValue<Page<EpcBonusSheet>>())
  const load = (standardizedOperationSheetCode: string) => {
    return handleAxiosPromise(
      epcBonusSheets,
      epcBonusSheetApi.getAll(
        { size: 1000 },
        { prefix: standardizedOperationSheetCode.substring(0, 3), certified: true }
      )
    ).catch(() => {
      loadingTimeoutId.value = setTimeout(() => load(standardizedOperationSheetCode), 5000)
    })
  }
  const loadingTimeoutId = ref<number>()
  onUnmounted(() => clearTimeout(loadingTimeoutId.value))
  watch(
    () => operation.value?.standardizedOperationSheet.operationCode,
    (v) => {
      clearTimeout(loadingTimeoutId.value)
      if (v) {
        load(v)
      } else {
        boostBonusSheets.value = emptyValue()
      }
    },
    {
      immediate: true,
    }
  )
  const epcBonusSheet = computed((): PromisableValue<EpcBonusSheet> => {
    const commitmentDate = operation.value?.signedDate || operation.value?.estimatedCommitmentDate
    const endOperation = operation.value?.actualEndWorksDate || operation.value?.estimatedEndOperationDate
    if (
      epcBonusSheets.value.loading ||
      ((operation.value === undefined || epcBonusSheets.value.value === undefined) && !epcBonusSheets.value.error)
    ) {
      return loadingValue()
    } else if (epcBonusSheets.value.error) {
      return errorValue(epcBonusSheets.value.error)
    } else if (epcBonusSheets.value.value!.totalElements === 0) {
      return errorValue('Indispo. pour cette fiche')
    } else if (!operation.value!.standardizedOperationSheet.epcBonusEligible) {
      return errorValue('Indispo. pour cette fiche')
    } else if (!commitmentDate) {
      return errorValue('Date eng. ou sign. requise')
    } else if (!endOperation) {
      return errorValue('Date fin travaux prév. ou réel. requise')
    } else {
      const v = epcBonusSheets.value
        .value!.content.sort((op1, op2) => op1.startDate.localeCompare(op2.startDate))
        .filter(
          (it) =>
            (commitmentDate ?? DEFAULT_DATE_MAX_BONIFICATION) <= (it.endDate ?? DEFAULT_DATE_MAX_BONIFICATION) &&
            (commitmentDate ?? DEFAULT_DATE_MIN_BONIFICATION) >= it.startDate
        )
      if (v.length === 0) {
        return errorValue('Aucun CPE pour ces dates')
      } else if (v.length !== 1) {
        return errorValue("Erreur imprévue survenue. Contactez l'administrateur")
      } else {
        return succeedValue(v[0])
      }
    }
  })
  watch(showEpcBonus, (v) => {
    if (v) {
      if (operation.value && operation.value?.epcBonusParameterValues == null && epcBonusSheet.value.value) {
        operation.value.epcBonusParameterValues = {}
      }
    } else {
      // On est obligé de mettre à null car à cause de la réactivité de watch(simulation), qui s'active tout le temps, en cas de
      // modification de l'opération et qu'il y a les anciennes valeurs, cela va réactiver l'option de bonification
      operation.value!.epcBonusParameterValues = null
    }
  })
  watch(epcBonusSheet, (v) => {
    if (v.value === undefined && v.error && showEpcBonus.value) {
      showEpcBonus.value = false
    }
  })

  // Précarité
  const calculatedClassicCumac = ref<number[]>([0])
  const calculatedPrecariousnessCumac = ref<number[]>([0])

  const predefinedValuesForPrecariousnessBonus = computed(() => {
    return {
      // V: showBoostBonus.value ? volumeCEEAfterBoostBonusSheet.value : volumeCEE.value,
      Bcpe: showEpcBonus.value ? epcBonus.value : 1,
      ...predefinedValues.value,
    }
  })
  const volumeCEEForPrecariousness = computed(() => {
    return showBoostBonus.value && !boostBonusSheet.value.error ? volumeCEEAfterBoostBonusSheet.value : volumeCEE.value
  })
  watch(showPrecariousnessBonus, (v) => {
    if (!v) {
      operation.value!.precariousnessBonusParameterValues = undefined
    }
  })

  const operationLineClassicCumac = computed((): number[] => {
    return roundCumacOperationLines(
      showPrecariousnessBonus.value && !precariousnessBonusSheet.value.error
        ? calculatedClassicCumac.value
        : showEpcBonus.value
          ? volumeCEE.value.map((it) => it * epcBonus.value)
          : showBoostBonus.value
            ? volumeCEEAfterBoostBonusSheet.value
            : volumeCEE.value
    )
  })

  // total
  const totalCEEClassique = computed((): number => sum(operationLineClassicCumac.value))

  const operationLinePrecariousnessCumac = computed((): number[] => {
    return roundCumacOperationLines(
      showPrecariousnessBonus.value && !precariousnessBonusSheet.value.error
        ? calculatedPrecariousnessCumac.value
        : new Array(volumeCEE.value.length).fill(0)
    )
  })

  const totalCEEPrecariousness = computed((): number => sum(operationLinePrecariousnessCumac.value))

  watch(operationLineClassicCumac, (v) => {
    if (operation.value) {
      operation.value.operationLineClassicCumac = v
    }
  })

  watch(operationLinePrecariousnessCumac, (v) => {
    if (operation.value) {
      operation.value.operationLinePrecariousnessCumac = v
    }
  })

  const { precariousnessBonusSheet: precariousnessBonusSheet } = useFindCorrectPrecariousnessBonus(operation)
  watch(precariousnessBonusSheet, (v) => {
    if (v.value === undefined && v.error && showPrecariousnessBonus.value) {
      showPrecariousnessBonus.value = false
    }
  })

  // load
  watch(
    operation,
    (v, oldV) => {
      if (v?.id !== oldV?.id || v?.updateDateTime !== oldV?.updateDateTime) {
        showBoostBonus.value = v?.boostBonusSimulation != null
        showEpcBonus.value = v?.epcBonusParameterValues != null
        showPrecariousnessBonus.value = v?.precariousnessBonusParameterValues != null
        if (v) {
          usingOtherAddress.value =
            !isEqual(v.finalAddress?.postalCode, v.property?.postalCode) ||
            v.finalAddress?.city !== v.property?.city ||
            v.finalAddress?.street.trim() !==
              (toString(v.property?.streetNumber) + ' ' + toString(v.property?.streetName)).trim()
        }
      }
    },
    {
      immediate: true,
    }
  )

  // Incitation Financiaire
  const commercialOfferWithFinancialIncentive = computed(() => {
    return (
      (operation.value?.commercialOfferWithoutFinancialIncentive ?? 0) -
      (operation.value?.customerFinancialIncentive ?? 0)
    )
  })

  const cumacHasChanged = computed(() => {
    // if (simulation.value.id)
    console.debug(
      'cumacHasChanged',
      operation.value?.precariousnessCumac,
      totalCEEPrecariousness.value,
      operation.value?.classicCumac,
      totalCEEClassique.value,
      calculatedPrecariousnessCumac.value,
      operation.value?.status
    )
    if (
      operation.value == null ||
      totalCEEClassique.value == null ||
      !operation.value.id ||
      operation.value.stepId < 30 ||
      operation.value.parameterValues.length === 0 ||
      operation.value.status === 'CANCELLED'
    ) {
      return false
    }
    return (
      operation.value.precariousnessCumac != totalCEEPrecariousness.value ||
      operation.value.classicCumac != totalCEEClassique.value
    )
  })

  return {
    cumacHasChanged,
    predefinedValues,
    volumeCEE,
    volumeCEEAfterBoostBonusSheet: volumeCEEAfterBoostBonusSheet,
    predefinedValuesForBoostBonusSheet: predefinedValuesForBoostBonusSheet,
    boostBonusSheet: boostBonusSheet,
    boostBonusSheets: boostBonusSheets,
    epcBonusSheet: epcBonusSheet,
    epcBonusSheets: epcBonusSheets,
    epcBonus: epcBonus,
    calculatedClassicCumac: calculatedClassicCumac,
    calculatedPrecariousnessCumac: calculatedPrecariousnessCumac,
    predefinedValuesForPrecariousnessBonus: predefinedValuesForPrecariousnessBonus,
    precariousnessBonusSheet: precariousnessBonusSheet,

    totalCEEClassique,
    totalCEEPrecariousness: totalCEEPrecariousness,

    showBoostBonus: showBoostBonus,
    showEpcBonus: showEpcBonus,
    showPrecariousnessBonus: showPrecariousnessBonus,
    usingOtherAddress,

    boostBonusSimulationValues: boostBonusSimulationValues,
    epcBonusSimulationValues: epcBonusSimulationValues,
    precariousnessBonusSimulationValues: precariousnessBonusSimulationValues,

    commercialOfferWithFinancialIncentive,
    volumeCEEForPrecariousness,

    operationLineClassicCumac,
    operationLinePrecariousnessCumac,
  }
}

export const useRealPeriod = (operation: Ref<Operation>) => {
  const periodsStore = usePeriodsStore()

  const realPeriod = computed(() => {
    const date =
      operation.value.signedDate ||
      operation.value.estimatedCommitmentDate ||
      operation.value.creationDateTime ||
      formatISO(new Date())
    const period = periodsStore.periods?.find((it) => date >= it.startDate && date <= it.endDate)
    return period
  })

  return { realPeriod }
}
