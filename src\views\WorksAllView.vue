<template>
  <VRow class="flex-column h-100">
    <VCol v-if="data.error" class="d-flex align-center flex-grow-0">
      <ErrorAlert :message="data.error" />
    </VCol>
    <VCol v-if="!data.loading && data.value?.empty" class="flex-grow-0">
      <VAlert type="info">
        Aucun chantier n'a été trouvé. <br />Si le numéro de chantier est introuvable, veillez à supprimer les filtres
        par défaut présents dans les champs.
      </VAlert>
    </VCol>
    <VCol>
      <NjDataTable
        :headers="headers"
        :selections="props.selections"
        select-on-id
        :pageable="pageable"
        :page="data.value!"
        fixed
        multi-selection
        checkboxes
        hide-total
        @update:selections="updateModelValue"
        @update:pageable="updatePageable"
      >
        <template #[`item.closed`]="{ item }">
          <NjBooleanIcon :condition="item.closed" />
        </template>
      </NjDataTable>
    </VCol>
  </VRow>
</template>
<script setup lang="ts">
import { type GtfGdePropertyDto } from '@/api/external/gtf/gtfGde'
import { gtfTraApi, type GtfTraWorkFilter } from '@/api/external/gtf/gtfTra'
import { mapAxiosResponse } from '@/api/external/gtf/type'
import type { DataTableHeader } from '@/components/okta/NjDataTable.type'
import { usePagination, type Page, type Pageable } from '@/types/pagination'
import type { Property } from '@/types/property'
import type { Works } from '@/types/works'
import type { AxiosResponse } from 'axios'
import { cloneDeep, debounce } from 'lodash'
import type { PropType } from 'vue'

const props = defineProps({
  selections: {
    type: Object as PropType<Works[]>,
  },
  // search: {
  //   type: String,
  // },
  // propertyCode: {
  //   type: String,
  // },
  // entityNavFullIds: {
  //   type: Array as PropType<string[]>,
  // },
  filter: {
    type: Object as PropType<GtfTraWorkFilter>,
    required: true,
  },
})

const emit = defineEmits(['update:selections', 'update:loading'])
const updateModelValue = (value: Property[]) => {
  emit('update:selections', value)
}

const convertNameHeader: Partial<Record<keyof Property, keyof GtfGdePropertyDto>> = {
  streetName: 'road_name',
  postalCode: 'postal_code',
  city: 'city',
}
const mapSortName = (pageable: Pageable): Pageable => ({
  ...pageable,
  sort: pageable.sort?.map((it) => {
    const a = it.split(',') as [keyof Property, 'ASC' | 'DESC']
    return [convertNameHeader[a[0]], a[1]].filter((it) => it).join(',')
  }),
})

const { data, pageable, updatePageable, updateFilter } = usePagination<Works, GtfTraWorkFilter>(
  (filter, pageable) => {
    return gtfTraApi
      .getAllWorks(filter, mapSortName(pageable))
      .then(mapAxiosResponse)
      .then((it): AxiosResponse<Page<Works>> => {
        return {
          ...it,
          data: {
            ...it.data,
            content: it.data.content.map(
              (it): Works => ({
                id: it.id,
                worksType: it.type_service_id.substring(0, 2),
                propertyCode: it.property_code?.toString(),
                address: {
                  country: it.works_site_country,
                  postalCode: it.works_site_postal_code,
                  additionalPostalAddress: it.works_site_additionnal_address,
                  city: it.works_site_city,
                  street: ((it.works_site_track_number ?? '') + ' ' + (it.works_site_track_name ?? '')).trim(),
                },
                closed: it.closed === 'O',
              })
            ),
          },
        }
      })
  },
  cloneDeep(props.filter),
  {
    page: 0,
    size: 20,
    sort: [],
  }
)

watch(
  () => props.filter,
  (v) => {
    debounceUpdateFilter(v)
  },
  {
    deep: true,
  }
)
const headers: DataTableHeader<Works>[] = [
  {
    title: 'Numéro Chantier',
    value: 'id',
    sortable: false,
  },
  {
    title: 'Type Chantier',
    value: 'worksType',
    sortable: false,
  },
  {
    title: 'Code Installation',
    value: 'propertyCode',
    sortable: false,
  },
  {
    title: 'Clôturé',
    value: 'closed',
    sortable: false,
  },
  {
    title: 'Adresse Chantier',
    value: 'address',
    sortable: false,
    formater(item, value) {
      return formatAddressable(value)
    },
  },
]

const debounceUpdateFilter = debounce((v: GtfTraWorkFilter) => {
  updateFilter(v)
}, 500)

watch(
  () => data.value.loading,
  (v) => {
    emit('update:loading', v)
  },
  {
    immediate: true,
  }
)
</script>
