<template>
  <VContainer>
    <VRow>
      <VCol cols="3">
        <VList class="h-100">
          <VListItem v-for="c in components" :key="c.routeName" :to="{ name: c.routeName }">{{ c.label }}</VListItem>
        </VList>
      </VCol>
      <VCol>
        <RouterView />
      </VCol>
    </VRow>
  </VContainer>
</template>

<script setup lang="ts">
const components: { label: string; routeName: string }[] = [
  {
    label: 'RemoteAutoComplete',
    routeName: 'DebugComponentRemoteAutoComplete',
  },
  {
    label: 'NjDataTable',
    routeName: 'DebugComponentNjDataTable',
  },
  {
    label: 'RichTextEditor',
    routeName: 'DebugComponentRichTextEditor',
  },
]
</script>
