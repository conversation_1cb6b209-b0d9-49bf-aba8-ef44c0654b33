<template>
  <VInput :model-value="effectiveSubcontractor" :rules="rules" hide-details="auto">
    <div class="h-100 w-100">
      <div v-if="fromGroup" style="font-style: italic">(Provenant du regroupement)</div>
      <VCard v-if="effectiveSubcontractor" class="w-100">
        <template v-if="withTitle">
          <VCardTitle>
            <slot name="title"></slot>
          </VCardTitle>
          <VDivider />
        </template>
        <VCardText class="pa-2">
          <VRow class="flex-column" :no-gutters="!expanded">
            <VCol>
              <NjDisplayValue label="Raison social" :value="effectiveSubcontractor.socialReason" />
            </VCol>
            <VCol v-if="effectiveSubcontractor.siret">
              <NjDisplayValue label="SIREN" :value="effectiveSubcontractor.siret.substring(0, 9)" />
            </VCol>
            <VCol v-if="effectiveSubcontractor.siret">
              <NjDisplayValue label="SIRET" :value="effectiveSubcontractor.siret" />
            </VCol>
            <VCol>
              <NjDisplayValue label="Adresse" :value="effectiveSubcontractor.address.street" />
            </VCol>
            <VCol>
              <NjDisplayValue label="Code postal" :value="effectiveSubcontractor.address.postalCode" />
            </VCol>
            <VCol>
              <NjDisplayValue label="Ville" :value="effectiveSubcontractor.address.city" />
            </VCol>
            <VCol>
              <NjDisplayValue label="Pays" :value="countries.getName(effectiveSubcontractor.address.country!, 'fr')" />
            </VCol>
            <VCol v-if="withHistory">
              <NjExpansionPanel title="Traçabilité" :loading-value="histories.loading">
                <VProgressCircular v-show="histories.loading" indeterminate size="24" color="primary" />
                <VRow dense class="flex-column">
                  <VCol v-for="history in histories.value?.content" :key="history.uuid">
                    <HistoryCard :display-properties="subcontractorHistoryDisplayProperties" :model-value="history" />
                  </VCol>
                  <VCol v-if="!histories.value?.totalElements">
                    <i>Aucun historique</i>
                  </VCol>
                </VRow>
              </NjExpansionPanel>
            </VCol>
          </VRow>
        </VCardText>
      </VCard>
      <div v-else>
        <i>Aucun sous traitant sélectionné</i>
      </div>
    </div>
  </VInput>
</template>
<script lang="ts" setup>
import type { ValidationRule } from '@/types/rule'
import type { Subcontractor } from '@/types/subcontractor'
import type { PropType } from 'vue'
import countries from '@/types/countries'
import { VCardTitle, VDivider } from 'vuetify/components'
import { type SubcontractorHistory, subcontractorHistoryDisplayProperties } from '@/types/history'
import type { Page } from '@/types/pagination'
import { subcontractorHistoryApi } from '@/api/subcontractorHistory'

const props = defineProps({
  modelValue: {
    type: Object as PropType<Subcontractor | null>,
  },
  subcontractorModelValue: {
    type: Object as PropType<Subcontractor | null>,
  },
  rules: {
    type: Array as PropType<Array<ValidationRule>>,
  },
  expanded: Boolean,
  withTitle: Boolean,
  withHistory: Boolean,
})

const histories = ref(emptyValue<Page<SubcontractorHistory>>())

const effectiveSubcontractor = computed(() => {
  return props.modelValue ?? props.subcontractorModelValue
})

const fromGroup = computed(() => props.modelValue == null && props.subcontractorModelValue != null)

watch(
  [() => props.withHistory, () => props.modelValue],
  (v) => {
    histories.value = emptyValue<Page<SubcontractorHistory>>()
    if (v[0] && v[1]) {
      handleAxiosPromise(
        histories,
        subcontractorHistoryApi.findAll({ size: 1000, sort: ['creationDateTime,DESC'] }, { subcontractorId: v[1].id })
      )
    }
  },
  {
    immediate: true,
  }
)
</script>
