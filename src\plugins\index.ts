/**
 * plugins/index.ts
 *
 * Automatically included in `./src/main.ts`
 */

// Plugins
import OktaVue from '@okta/okta-vue'
import router from '../router'
import pinia from '../stores'
import oktaAuth from './okta'
import vuetify from './vuetify'

// Types
import type { App } from 'vue'

export function registerPlugins(app: App) {
  // initSentry(app, router)
  app.use(vuetify).use(router).use(pinia).use(OktaVue, { oktaAuth })
}
