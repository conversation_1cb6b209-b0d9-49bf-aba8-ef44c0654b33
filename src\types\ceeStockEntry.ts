import { isEqual } from 'lodash'
import type { LocalDate, LocalDateTime } from './date'
import type { Entity } from './entity'
import { type Operation, getPoleChargeCEE } from './operation'

export interface CeeStockEntry {
  operation: Operation
  entity: Entity
  creationDateTime: LocalDateTime
  classicCumac: number
  precariousnessCumac: number
  classicValuationValue: number
  precariousnessValuationValue: number
  worksId1: number
  worksId2: number
  worksId3: number
  fee: number
}
export interface CeeStockEntryDto {
  operationId: number
  chronoCode: string
  operationName: string
  stepId: number
  entityId: number
  entityName: string
  creationDateTime: LocalDateTime
  classicCumac: number
  precariousnessCumac: number
  classicValuationValue: number
  precariousnessValuationValue: number
  emmyFolderCode: string
  worksId1: number
  worksId2: number
  worksId3: number
  operationReservedDate: LocalDate
  soldClassicCumac: number
  soldPrecariousnessCumac: number
  availableClassicCumac: number
  availablePrecariousnessCumac: number
  fee: number
  minCreationDateTime: LocalDateTime
  lastEntrySapCode: string
  lastEntryCreationDate: LocalDate
  lastWorksCeeCostSapCode: string
  lastWorksCeeCostCreationDate: LocalDate
}

export interface CeeStockEntryComparable {
  classicCumac: number
  precariousnessCumac: number
  classicValuationValue: number
  precariousnessValuationValue: number
  fee: number
  entityId: string
}

export interface CeeStockOperationWithAccountingEntrySummary extends CeeStockEntryDto {
  companyId: number
  lastEntityTransferSapCode: string
  lastEntityTransferCreationDate: LocalDate
  lastExitSapCode: string
  lastExitCreationDate: LocalDate
  lastSellSapCode: string
  lastSellCreationDate: LocalDate
  classicCumacSoldAmount: number
  precariousnessCumacSoldAmount: number
  soldClassicCumac: number
  soldPrecariousnessCumac: number
}

export const mapOperationToCeeStockEntryComparable = (operation: Operation): CeeStockEntryComparable => ({
  classicCumac: operation.classicCumac,
  precariousnessCumac: operation.precariousnessCumac,
  classicValuationValue: operation.atypicalClassicValuationValue
    ? operation.atypicalClassicValuationValue
    : operation.classicValuationValue,
  precariousnessValuationValue: operation.atypicalPrecariousnessValuationValue
    ? operation.atypicalPrecariousnessValuationValue
    : operation.precariousnessValuationValue,
  fee: getPoleChargeCEE(operation),
  entityId: operation.entity.id,
})

export const mapCeeStockEntryToCeeStockEntryComparable = (ceeStockEntry: CeeStockEntry): CeeStockEntryComparable => ({
  classicCumac: ceeStockEntry.classicCumac,
  precariousnessCumac: ceeStockEntry.precariousnessCumac,
  classicValuationValue: ceeStockEntry.classicValuationValue,
  precariousnessValuationValue: ceeStockEntry.precariousnessValuationValue,
  fee: ceeStockEntry.fee,
  entityId: ceeStockEntry.entity.id,
})

export const shouldCreateCeeStockEntry = (oldEntry: CeeStockEntryComparable, newEntry: CeeStockEntryComparable) =>
  !isEqual(newEntry, oldEntry)
