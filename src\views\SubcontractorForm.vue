<template>
  <VCard>
    <VCardTitle>
      <div v-if="mode == 'create'">Créer un sous traitant</div>
      <div v-else-if="mode == 'edit'">Modifier un sous traitant</div>
      <div v-else>Dupliquer un sous traitant</div>
      <VDivider />
    </VCardTitle>
    <VCardText v-if="localSubcontractor">
      <VForm ref="formRef">
        <VRow class="flex-column">
          <VCol>
            <VTextField v-model="localSubcontractor.socialReason" label="Raison sociale" :rules="[requiredRule]" />
          </VCol>
          <VCol v-if="localSubcontractor.address.country === 'FRA'">
            <NjSiretInput v-model="localSubcontractor.siret" label="SIRET" required />
          </VCol>
          <VCol>
            <VTextField v-model="localSubcontractor.address.street" label="Adresse" />
          </VCol>
          <VCol>
            <VTextField
              v-model="localSubcontractor.address.postalCode"
              label="Code postal"
              :rules="[emptyOrPostalCodeRule]"
            />
          </VCol>
          <VCol>
            <VTextField v-model="localSubcontractor.address.city" label="Ville" />
          </VCol>
          <VCol v-show="gouvSuggestedAdressRef?.displaySuggestion">
            <GouvSuggestedAdress
              ref="gouvSuggestedAdressRef"
              v-model:city="localSubcontractor.address.city"
              v-model:postal-code="localSubcontractor.address.postalCode"
              v-model:street="localSubcontractor.address.street"
            />
          </VCol>
          <VCol>
            <NjCountryInput v-model="localSubcontractor.address.country" label="Pays" :rules="[requiredRule]" />
          </VCol>
          <VCol>
            <VSwitch v-if="canCertified" v-model="localSubcontractor.certified" label="Certifié"></VSwitch>
          </VCol>
        </VRow>
      </VForm>
    </VCardText>
    <VCardActions>
      <VSpacer />
      <NjBtn variant="outlined" @click="cancel">Annuler</NjBtn>
      <NjBtn v-if="mode == 'create'" :disabled="promisableSubcontractor.loading" @click="createSubcontractor"
        >Ajouter</NjBtn
      >
      <NjBtn v-else-if="mode == 'edit'" :disabled="promisableSubcontractor.loading" @click="editSubContractor"
        >Valider</NjBtn
      >
      <NjBtn v-else :disabled="promisableSubcontractor.loading" @click="createSubcontractor">Valider</NjBtn>
    </VCardActions>
  </VCard>
</template>
<script setup lang="ts">
import { subcontractorApi } from '@/api/subcontractor'
import GouvSuggestedAdress from '@/components/GouvSuggestedAdress.vue'
import NjBtn from '@/components/NjBtn.vue'
import { useSnackbarStore } from '@/stores/snackbar'
import { emptyOrPostalCodeRule, requiredRule } from '@/types/rule'
import type { Subcontractor } from '@/types/subcontractor'
import { makeEmptySubcontractor, mapToSubcontractorRequest } from '@/types/subcontractor'
import type { PropType } from 'vue'
import { VForm } from 'vuetify/components/VForm'

const props = defineProps({
  mode: {
    type: String,
    default: 'create',
  },
  subcontractor: {
    type: Object as PropType<Subcontractor>,
  },
  cancel: {
    type: Function as PropType<(item: any) => void>,
    default: () => {},
  },
  afterSuccess: {
    type: Function as PropType<(subcontractor: Subcontractor) => void>,
    default: () => {},
  },
  canCertified: {
    type: Boolean,
  },
  updatedSubcontractor: {
    type: Object as PropType<Subcontractor | null>,
  },
})

const emit = defineEmits(['update:updatedSubcontractor'])

const localSubcontractor = ref<Subcontractor>()

const snackbarStore = useSnackbarStore()

onMounted(() => {
  if (props.subcontractor) {
    localSubcontractor.value = { ...props.subcontractor, certified: false }
  } else {
    localSubcontractor.value = makeEmptySubcontractor()
    localSubcontractor.value.address.country = 'FRA'
  }
})

const formRef = ref<VForm | null>(null)
const promisableSubcontractor = ref(emptyValue<Subcontractor>())

const createSubcontractor = async () => {
  const validation = await formRef.value!.validate()
  if (validation.valid) {
    const country = localSubcontractor.value!.address.country
    if (country && country !== 'FRA') {
      localSubcontractor.value!.siret = ''
    }
    handleAxiosPromise(
      promisableSubcontractor,
      subcontractorApi.create(mapToSubcontractorRequest(localSubcontractor.value!)),
      {
        afterSuccess: () => {
          snackbarStore.setSuccess('Le sous traitant a bien été créé')
          props.afterSuccess(promisableSubcontractor.value.value!)
        },
        afterError: (e) => {
          snackbarStore.setError(e ?? 'Echec lors de la création du sous traitant')
        },
      }
    )
  }
}

const editSubContractor = async () => {
  const validation = await formRef.value!.validate()
  if (validation.valid) {
    const country = localSubcontractor.value!.address.country
    if (country && country !== 'FRA') {
      localSubcontractor.value!.siret = ''
    }
    handleAxiosPromise(
      promisableSubcontractor,
      subcontractorApi.update(localSubcontractor.value!.id, mapToSubcontractorRequest(localSubcontractor.value!)),
      {
        afterSuccess: () => {
          emit('update:updatedSubcontractor', promisableSubcontractor.value.value)
          localSubcontractor.value = promisableSubcontractor.value.value
          snackbarStore.setSuccess('Le sous traitant a bien été modifié')
          props.afterSuccess(promisableSubcontractor.value.value!)
        },
        afterError: (e) => {
          snackbarStore.setError(e ?? "Echec lors de l'édition du sous traitant")
        },
      }
    )
  }
}

const gouvSuggestedAdressRef = ref<typeof GouvSuggestedAdress | null>(null)
</script>
