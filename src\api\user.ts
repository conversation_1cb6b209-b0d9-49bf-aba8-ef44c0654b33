import type { Page, Pageable } from '@/types/pagination'
import type { ProfileType, User, UserRequest, UserWithEntities } from '@/types/user'
import type { AxiosInstance, AxiosPromise } from 'axios'
import axiosInstance from '.'

export interface UserFilter {
  ids?: number[]
  search?: string
  roles?: ProfileType[]
  excludedRoles?: ProfileType[]
  withoutSelf?: boolean
  operationId?: number
  active?: boolean
  emailSearch?: string
  entityIds?: string[]
  territoryIds?: number[]
}

class UserApi {
  public constructor(private axios: AxiosInstance) {}

  public create(user: UserRequest): AxiosPromise<User> {
    return this.axios.post('/users', user)
  }

  public update(id: number, user: UserRequest): AxiosPromise<User> {
    return this.axios.put(`/users/${id}`, user)
  }

  public getAll(pageable: Pageable, filter: UserFilter): AxiosPromise<Page<UserWithEntities>> {
    return this.axios.get('/users', {
      params: { ...pageable, ...filter },
    })
  }

  public getAllWithSameEntity(filter: UserFilter = {}): AxiosPromise<User[]> {
    return this.axios.get('/users/entities', { params: filter })
  }

  public getOne(id: number): AxiosPromise<UserWithEntities> {
    return this.axios.get(`/users/${id}`)
  }

  public export(filter: UserFilter) {
    return this.axios.get('/users/export', {
      params: filter,
      responseType: 'blob',
    })
  }

  public addEntityToUser(entityId: string, territoryId: number) {
    return this.axios.put('/users/entities', {
      entityId,
      territoryId,
    })
  }

  public changeRoleToAdminPlus(): AxiosPromise<User> {
    return this.axios.put('/users/me/superprincess')
  }
}

export const userApi = new UserApi(axiosInstance)
