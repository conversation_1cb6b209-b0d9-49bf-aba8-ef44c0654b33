import type { ControlOrderBatchDocumentRequest, EnhancedDocument } from '@/types/document'
import type { Page, Pageable } from '@/types/pagination'
import type { AxiosInstance, AxiosPromise } from 'axios'
import axiosInstance from '.'

export interface ControlOrderBatchDocumentFilter extends Record<string, any> {
  controlOrderBatchId?: number
}

const controlOrderBatchDocumentUri = '/control_order_batch_documents'
class ControlOrderBatchDocumentApi {
  public constructor(private axios: AxiosInstance) {}

  public findAll(filter: ControlOrderBatchDocumentFilter, pageable: Pageable): AxiosPromise<Page<EnhancedDocument>> {
    return this.axios.get(controlOrderBatchDocumentUri, {
      params: { ...pageable, ...filter },
    })
  }

  public update(id: number, request: ControlOrderBatchDocumentRequest): AxiosPromise<EnhancedDocument> {
    return this.axios.put(`${controlOrderBatchDocumentUri}/${id}`, request)
  }

  public delete(id: number): AxiosPromise {
    return this.axios.delete(`${controlOrderBatchDocumentUri}/${id}`)
  }

  public download(id: number): AxiosPromise<File> {
    return this.axios.get(`${controlOrderBatchDocumentUri}/${id}/file`, {
      responseType: 'blob',
    })
  }

  public async uploadFile(
    controlOrderBatchDocumentRequest: ControlOrderBatchDocumentRequest,
    file: File,
    documentToReplaceId?: number
  ): AxiosPromise<EnhancedDocument> {
    const formData = new FormData()
    formData.append('file', file, file.name)
    const hash = await hashFile(file)
    formData.append('hash', hash)

    const request = JSON.stringify(engieFormatRequestTransformKey(controlOrderBatchDocumentRequest))
    const blob = new Blob([request], {
      type: 'application/json',
    })
    formData.append('request', blob)

    return this.axios.post(controlOrderBatchDocumentUri, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      params: {
        documentToReplaceId,
      },
    })
  }
}

export const controlOrderBatchDocumentApi = new ControlOrderBatchDocumentApi(axiosInstance)
