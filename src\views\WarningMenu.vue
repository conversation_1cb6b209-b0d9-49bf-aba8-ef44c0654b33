<template>
  <VMenu v-if="errorsToDisplay.length > 0" open-on-hover>
    <template #activator="{ props }">
      <NjIconBtn icon="mdi-alert" color="#FF8C47" v-bind="props" />
    </template>
    <!-- eslint-disable-next-line vue/valid-v-for -->
    <VCard v-for="e in errorsToDisplay" min-width="400px">
      <VCardText>
        {{ e }}
      </VCardText>
    </VCard>
  </VMenu>
</template>
<script setup lang="ts">
import { kafkaNotificationApi } from '@/api/kafkaNotification'
import { kafkaProcessedOffsetsApi } from '@/api/kafkaProcessedOffsets'
import { addHours } from 'date-fns'
import { VCard, VCardText, VMenu } from 'vuetify/components'

const timeoutIds: number[] = []

const errors = ref<string[]>([])

const errorsToDisplay = computed(() => errors.value.filter((it) => !!it))

const errorComputers: { callback: (date: Date) => Promise<true | string>; msInterval: number }[] = [
  {
    callback: async (date) => {
      return await remainingMailApi
        .getAll({ size: 1 }, { creationDateTimeLower: addHours(date, -6).toISOString() })
        .then((response) => {
          return response.data.empty || "Un mail n'a pas été envoyé depuis plus de 6 heures"
        })
    },
    msInterval: 1000 * 3600 * 6,
  },
  {
    callback: async (date) => {
      return await kafkaNotificationApi
        .getAll({ size: 1 }, { creationDateTimeLower: addHours(date, -1).toISOString() })
        .then((response) => {
          return response.data.empty || "Certains évènements Kafka n'ont pas été envoyé depuis plus de 1 heure"
        })
    },
    msInterval: 1000 * 3600 * 1,
  },
  {
    callback: async (date) => {
      return await kafkaProcessedOffsetsApi
        .getAll({ size: 1 }, { lastProcessedDateTimeLower: addHours(date, -1).toISOString(), outOfDate: true })
        .then((response) => {
          return response.data.empty || "Il semble y avoir un blocage à l'arrivage des messages Kafka"
        })
    },
    msInterval: 1000 * 3600 * 1,
  },
]

const getWarning = () => {
  errorComputers.forEach((e, i) => {
    const callback = () =>
      e
        .callback(new Date())
        .then((v) => {
          if (v !== true) {
            errors.value[i] = v
          } else {
            errors.value[i] = ''
          }
        })
        .finally(() => {
          timeoutIds[i] = setTimeout(callback, e.msInterval)
        })
    callback()
  })
}

onMounted(getWarning)

onUnmounted(() => {
  timeoutIds.forEach((timeoutId) => {
    clearTimeout(timeoutId)
  })
})
</script>
