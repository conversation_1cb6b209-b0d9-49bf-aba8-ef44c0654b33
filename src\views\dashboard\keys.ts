import type { OperationFilter } from '@/api/operation'
import type { DocumentType } from '@/types/documentType'

export const dashboardDataKey = Symbol('dashboardDataKey') as InjectionKey<{
  loading: Ref<boolean>
  filter: Ref<OperationFilter>
  dashboardFilter: Ref<OperationFilter>
}>
// export const dashboardFilterKey = Symbol() as InjectionKey<{ dashboardFilter: Ref<OperationFilter> }>
export const dashboardCardKey = Symbol('dashboardCardKey') as InjectionKey<{
  selected: Ref<boolean>
  disabled: Ref<boolean>
}>

export const missingDocumentTypeIdsKey = Symbol('missingDocumentTypeIds') as InjectionKey<{
  missingDocumentTypes: Ref<DocumentType[]>
  reload: () => Promise<DocumentType[]>
}>
