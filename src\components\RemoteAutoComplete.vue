<template>
  <VAutocomplete
    v-bind="$attrs"
    v-model="localModelValue"
    v-model:search="searchInput"
    v-model:focused="hasFocus"
    v-model:menu="menu"
    :label="label"
    :items="contentItems"
    :loading="loading"
    :hide-no-data="loading"
    :error-messages="error"
    :item-title="itemTitle as any"
    :item-value="itemValue as any"
    :placeholder="placeholder"
    no-data-text="Aucun élément trouvé"
    no-filter
    :clearable="clearable"
    :rules="rules"
    return-object
    :multiple="multiple"
    :closable-chips
    class="bg-white"
    @focus="initList"
  >
    <!-- eslint-disable-next-line prettier/prettier -->
    <template v-for="(_, slot) of $slots as {}" #[slot]="scope">
      <slot :name="slot" v-bind="scope as any" />
    </template>

    <template #append-inner>
      <VProgressCircular v-show="loading" indeterminate size="24" color="primary" />
    </template>

    <template #append-item>
      <div
        v-if="!items.last && infiniteScroll"
        v-intersect="intersectInfiniteScroll"
        style="height: 60px"
        class="d-flex align-center justify-center"
      >
        <VProgressCircular indeterminate />
      </div>
    </template>

    <template #chip="{ props, index, item }">
      <VChip v-if="index < maxElements" v-bind="props">
        {{ item.title }}
      </VChip>
      <VTooltip v-else-if="index === maxElements">
        <template #activator="{ props }">
          <VChip v-bind="props" :closable="false" color="primary">
            + {{ modelValue.length - maxElements }} autres éléments
          </VChip>
        </template>

        <ul class="pa-1">
          <li v-for="entity in modelValue.slice(maxElements)" :key="entity.id">
            {{ fieldSelector(entity, itemTitle) }}
          </li>
        </ul>
      </VTooltip>
    </template>
  </VAutocomplete>
</template>
<script setup lang="ts">
import type { Page, Pageable } from '@/types/pagination'
import type { ValidationRule } from '@/types/rule'
import { isAxiosError, type AxiosPromise } from 'axios'
import { debounce, isEqual, isArray, cloneDeep } from 'lodash'
import type { PropType } from 'vue'
import type { VAutocomplete } from 'vuetify/components'
import { VChip } from 'vuetify/components/VChip'

export interface T extends Record<string, any> {}

const props = defineProps({
  modelValue: [String, Number, Array, Object] as PropType<any>,
  label: String,
  placeholder: String,
  delay: {
    type: Number,
    default: 500,
  },
  pageSize: {
    type: Number,
    default: 20,
  },
  clearable: Boolean,
  argsForQueryForAll: Object, // Sert à rendre dynamique et réactive les paramètres de recherches
  queryForAll: {
    type: Function as PropType<(v: string, pageable: Pageable, args: any) => AxiosPromise<Page<T>>>,
    required: true,
  },
  queryForOne: {
    type: Function as PropType<(v: any) => AxiosPromise<T>>,
  },
  queryForOnes: {
    type: Function as PropType<(v: any[]) => AxiosPromise<Page<T>>>,
  },
  itemTitle: {
    type: [String, Function] as PropType<'none' | string | ((item: T, fallback?: any) => any)>,
    default: 'label',
  },
  itemValue: {
    type: [String, Function] as PropType<string | ((item: T, fallback?: any) => any)>,
    default: 'id',
  },
  rules: Array as PropType<ValidationRule[]>,
  errorMessages: {
    type: String,
  },
  returnObject: Boolean,
  infiniteScroll: Boolean,
  multiple: Boolean,
  maxElements: {
    type: Number,
    default: 10,
  },
  closableChips: Boolean,
})
const emit = defineEmits(['update:model-value'])
defineSlots<VAutocomplete['$slots']>()
const searchInput = ref('')
const loading = ref(false)
const error = ref<string>()
const items = ref(makeEmptyPage<T>()) as Ref<Page<T>>
const menu = ref<boolean>(false)
const hasFocus = ref<boolean>(false)
const localModelValue = shallowRef<any>()

watch(localModelValue, (v) => {
  console.debug('watch localModelValue', v)
  if (props.returnObject) {
    copyModelValue.value = v
  } else {
    copyModelValue.value = props.multiple ? (v as unknown[]).map((it) => mapValue(it)) : mapValue(v)
  }

  if (!isEqual(props.modelValue, copyModelValue.value)) {
    emit('update:model-value', copyModelValue.value)
  }
})

const initList = () => {
  query(searchInput.value ?? '')
}

const pageNumber = ref(0)
watch(pageNumber, (v) => {
  if (v) {
    load(searchInput.value)
  }
})

const load = (v: string) => {
  props
    .queryForAll(v, { page: pageNumber.value }, props.argsForQueryForAll)
    .then((r) => {
      if (!pageNumber.value) {
        items.value = r.data
      } else {
        items.value = {
          ...r.data,
          content: items.value.content.concat(r.data.content),
        }
      }
      menu.value = hasFocus.value
    })
    .catch(async (e) => {
      error.value = await handleAxiosException(e)
    })
    .finally(() => {
      loading.value = false
    })
}

const debounced = debounce(load, props.delay)

const query = (value: string) => {
  if (value != null && value != undefined) {
    loading.value = true
    error.value = undefined
    pageNumber.value = 0
    debounced(value)
  }
}
const fieldSelector = (o: any, keySelector: string | ((o: T) => unknown)): unknown => {
  if (o == null) {
    return undefined
  } else if (typeof keySelector === 'string') {
    return o[keySelector]
  } else {
    return keySelector(o)
  }
}
watch(searchInput, (v, oldV) => {
  // console.debug('searchInput', v, oldV)
  const criteraName = v === '' ? oldV : v
  let needToSearch
  if (props.itemTitle === 'none') {
    const o = items.value.content.find(
      (it: any) => (typeof props.itemTitle === 'string' ? it[props.itemTitle!] : props.itemTitle!(it)) === criteraName
    )
    needToSearch = !o || oldV !== fieldSelector(o, props.itemTitle)
  } else {
    needToSearch = true
  }
  if (needToSearch) {
    query(v)
  }
})
watch(
  () => props.argsForQueryForAll,
  () => {
    debounced(searchInput.value)
  },
  {
    deep: true,
  }
)

const copyModelValue = ref<any>()
const mapValue = (v: unknown) => {
  return fieldSelector(v, props.itemValue)
}

watch(
  () => props.modelValue,
  (v) => {
    // console.debug('watch props.modelValue', v, copyModelValue.value)
    if (
      (props.multiple ? v?.length : props.returnObject ? v?.id : v) &&
      !isEqual(v, copyModelValue.value) &&
      !items.value.content.find((it: any) =>
        props.returnObject ? v.id === it.id : fieldSelector(it, props.itemValue) === v
      )
    ) {
      copyModelValue.value = cloneDeep(v)
      if (!v || (isArray(v) && v.length === 0)) {
        return
      }
      if (props.returnObject) {
        localModelValue.value = cloneDeep(v)
      } else if (!props.multiple && props.queryForOne) {
        loading.value = true
        props
          .queryForOne(v as number)
          .then((r) => {
            items.value = makePage([r.data])

            localModelValue.value = r.data
          })
          .catch(async (e) => {
            if (isAxiosError(e) && e.response?.status === 404) {
              items.value = makeEmptyPage()

              localModelValue.value = []
              searchInput.value = ''
              emit('update:model-value', undefined)
            } else {
              error.value = await handleAxiosException(e)
            }
          })
          .finally(() => {
            loading.value = false
          })
      } else if (props.multiple && props.queryForOnes) {
        loading.value = true
        props
          .queryForOnes(v as unknown as number[])
          .then((r) => {
            items.value = r.data

            localModelValue.value = r.data.content.concat()
            pageNumber.value = 0
            debounced('')
          })
          .catch(async (e) => {
            if (isAxiosError(e) && e.response?.status === 404) {
              items.value = makeEmptyPage()
              searchInput.value = ''
              emit('update:model-value', undefined)
            } else {
              error.value = await handleAxiosException(e)
            }
          })
          .finally(() => {
            loading.value = false
          })
      }
    } else if (v === undefined) {
      localModelValue.value = undefined
      copyModelValue.value = undefined
      items.value = makeEmptyPage()
    } else if (v?.length == 0) {
      localModelValue.value = []
      copyModelValue.value = []
      items.value = makeEmptyPage()
    }
  },
  {
    immediate: true,
  }
)

const intersectInfiniteScroll = (
  isIntersecting: boolean,
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  entries: IntersectionObserverEntry[],
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  observer: IntersectionObserver
) => {
  // console.debug('intersectInfiniteScroll', entries, observer, isIntersecting)
  if (isIntersecting) {
    // let moreVendors = loadMoreFromApi()
    // this.vendors = [ ...this.vendors, ...moreVendors]
    pageNumber.value++
  }
}

watch(
  () => props.errorMessages,
  () => {
    error.value = props.errorMessages
  }
)

const contentItems = computed(() => {
  return (!localModelValue.value ? [] : props.multiple ? localModelValue.value : [localModelValue.value])
    .filter((modelValueItem: any) => items.value.content.find((it) => isEqual(it, modelValueItem)) === undefined)
    .concat(items.value.content)
})
</script>
