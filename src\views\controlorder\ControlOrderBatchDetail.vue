<template>
  <VRow class="flex-column content-layout">
    <VCol class="content-layout__main">
      <VForm ref="form">
        <VRow class="flex-column" dense>
          <VCol>
            <NjDisplayValue label="Numéro" :value="controlOrderBatch?.batchCode" />
          </VCol>

          <VCol>
            <NjDisplayValue
              label="Date minimum d'engagement"
              :value="formatHumanReadableLocalDate(controlOrderBatch?.usedMinimumSatisfyingControlRate.startDate)"
            />
          </VCol>
          <VCol>
            <NjDisplayValue
              label="Date maximum d'engagement"
              :value="formatHumanReadableLocalDate(controlOrderBatch?.usedMinimumSatisfyingControlRate.endDate)"
            />
          </VCol>
          <VCol> <NjDisplayValue label="Nombre d'opérations" :value="props.numberOfOperations" /> </VCol>
          <VCol>
            <NjDisplayValue label="Etape" :value="mapControlOrderStep(controlOrderBatch?.step ?? null)" />
          </VCol>
          <VCol>
            <NjDisplayValue
              label="Taux de non satisfaction annuel reglementaire"
              :value="currentAnnualNotSatisfyingRate + '%'"
            />
          </VCol>
          <VCol>
            <VTextField
              v-if="edit"
              v-model="controlOrderBatchRequest!.notSatisfyingRate"
              label="Taux de non satisfaction du lot"
              :rules="[emptyOrPositiveOrNullNumber()]"
              suffix="%"
            />
            <NjDisplayValue
              v-else
              label="Taux de non satisfaction du lot"
              :value="
                controlOrderBatch?.notSatisfyingRate ? formatNumber(controlOrderBatch?.notSatisfyingRate) + '%' : ''
              "
              :color-value="currentAnnualNotSatisfyingRate > (controlOrderBatch?.notSatisfyingRate ?? 0) ? '' : 'error'"
            />
          </VCol>
          <VCol>
            <NjDisplayValue
              label="Contrôle sur site"
              :value="['SITE', 'SITE_AND_CONTACT'].includes(controlOrderType ?? '') ? 'OUI' : 'NON'"
            />
          </VCol>
          <VCol>
            <NjDisplayValue
              label="Contrôle contact"
              :value="['CONTACT', 'SITE_AND_CONTACT'].includes(controlOrderType ?? '') ? 'OUI' : 'NON'"
            />
          </VCol>
          <VCol>
            <NjExpansionPanel title="Taux de satisfaction des contrôles">
              <VRow class="flex-column" dense>
                <template v-if="['SITE', 'SITE_AND_CONTACT'].includes(controlOrderType ?? '')">
                  <VCol>
                    <NjDisplayValue
                      label="Taux minimum de satisfaction des contrôles sur site"
                      :value="controlOrderBatch?.usedMinimumSatisfyingControlRate.siteRate + '%'"
                    />
                  </VCol>
                  <VCol>
                    <VTextField
                      v-if="edit"
                      v-model="controlOrderBatchRequest!.siteComplianceRate"
                      label="Taux de satisfaction des contrôles sur site du lot"
                      :rules="[emptyOrPositiveOrNullNumber()]"
                      suffix="%"
                    />
                    <NjDisplayValue
                      v-else
                      label="Taux de satisfaction des contrôles sur site du lot"
                      :color-value="
                        (controlOrderBatch?.siteComplianceRate ??
                          controlOrderBatch?.usedMinimumSatisfyingControlRate.siteRate!) <
                        controlOrderBatch?.usedMinimumSatisfyingControlRate.siteRate!
                          ? 'error'
                          : 'black'
                      "
                      :value="
                        controlOrderBatch?.siteComplianceRate
                          ? formatNumber(controlOrderBatch?.siteComplianceRate) + '%'
                          : ''
                      "
                    />
                  </VCol>
                </template>
                <template v-if="['CONTACT', 'SITE_AND_CONTACT'].includes(controlOrderType ?? '')">
                  <VCol>
                    <NjDisplayValue
                      label="Taux minimum de satisfaction des contrôles par contact"
                      :value="controlOrderBatch?.usedMinimumSatisfyingControlRate.contactRate + '%'"
                    />
                  </VCol>
                  <VCol>
                    <VTextField
                      v-if="edit"
                      v-model="controlOrderBatchRequest!.contactComplianceRate"
                      label="Taux de satisfaction des contrôles par contact du lot "
                      :rules="[emptyOrPositiveOrNullNumber()]"
                      suffix="%"
                    />
                    <NjDisplayValue
                      v-else
                      label="Taux de satisfaction des contrôles par contact du lot "
                      :color-value="
                        (controlOrderBatch?.contactComplianceRate ??
                          controlOrderBatch?.usedMinimumSatisfyingControlRate.contactRate!) <
                        controlOrderBatch?.usedMinimumSatisfyingControlRate.contactRate!
                          ? 'error'
                          : 'black'
                      "
                      :value="
                        controlOrderBatch?.contactComplianceRate
                          ? formatNumber(controlOrderBatch?.contactComplianceRate) + '%'
                          : ''
                      "
                    />
                  </VCol>
                </template>
              </VRow>
            </NjExpansionPanel>
          </VCol>
          <VCol>
            <NjExpansionPanel>
              <template #title>
                <div class="d-flex align-center">
                  Organisme de contrôle

                  <template v-if="edit">
                    <VSpacer />
                    <NjIconBtn
                      v-show="controlOrderBatchRequest!.controlOrganismId"
                      icon="mdi-delete"
                      color="primary"
                      @click.stop="
                        () => {
                          selectedControlOrganism = undefined
                          controlOrderBatchRequest!.controlOrganismId = undefined
                        }
                      "
                    >
                    </NjIconBtn>
                    <VLink
                      size="small"
                      style="font-weight: initial; font-size: initial"
                      icon="mdi-format-list-bulleted"
                      @click.stop="controlOrganismDialog = true"
                    >
                      Organisme de contrôle
                      <ControlOrganismDialog
                        v-model="controlOrganismDialog"
                        v-model:control-organism="selectedControlOrganism"
                        @update:control-organism="
                          controlOrderBatchRequest!.controlOrganismId = selectedControlOrganism?.id
                        "
                      />
                    </VLink>
                  </template>
                </div>
              </template>
              <VRow dense class="flex-column">
                <VCol v-if="edit ? selectedControlOrganism : controlOrderBatch?.controlOrganism">
                  <ControlOrganismDisplayValue
                    :model-value="edit ? selectedControlOrganism : controlOrderBatch?.controlOrganism"
                  />
                </VCol>
                <VCol v-else>
                  <span>Aucun organisme de contrôle n'est sélectionné</span>
                </VCol>
                <VCol>
                  <NjDisplayValue
                    label="Date de retour de l'organisme de contrôle"
                    :value="formatHumanReadableLocalDate(controlOrderBatch?.controlOrganismReturnDate)"
                  >
                    <template v-if="edit" #value>
                      <NjDatePicker v-model="controlOrderBatchRequest!.controlOrganismReturnDate" />
                    </template>
                  </NjDisplayValue>
                </VCol>
              </VRow>
            </NjExpansionPanel>
          </VCol>
        </VRow>
      </VForm>
    </VCol>
    <VCol v-if="edit" class="content-layout__footer">
      <VRow class="align-center" dense>
        <VSpacer />
        <VCol class="flex-grow-0">
          <NjBtn variant="outlined" @click="check(() => emit('update:edit', false))"> Annuler </NjBtn>
        </VCol>
        <VCol class="flex-grow-0">
          <NjBtn @click="save()"> Enregistrer </NjBtn>
        </VCol>
      </VRow>
    </VCol>
    <ConfirmUnsavedDataDialog v-model:unsaved-data-dialog="unsavedDataDialog" @save="save" />
  </VRow>
</template>
<script setup lang="ts">
import { formatNumber } from '@/types/format'
import { VCol, VForm, VRow, VSpacer, VTextField } from 'vuetify/components'
import { emptyOrPositiveOrNullNumber } from '@/types/rule'
import { formatHumanReadableLocalDate } from '@/types/date'
import type { PropType } from 'vue'
import { type ControlOrderBatch, mapControlOrderStep, type ControlOrderBatchRequest } from '@/types/controlOrder'
import { useSnackbarStore } from '@/stores/snackbar'
import { type ControlOrderType } from '@/types/calcul/standardizedOperationSheet'
import type { ControlOrganism } from '@/types/controlOrganism'
import ControlOrganismDialog from '../controlorganism/ControlOrganismDialog.vue'

const props = defineProps({
  controlOrderBatch: Object as PropType<ControlOrderBatch>,
  edit: Boolean as PropType<boolean>,
  numberOfOperations: Number,
  controlOrderType: String as PropType<ControlOrderType>,
})

const emit = defineEmits<{
  'update:control-order-batch': [ControlOrderBatch]
  'update:edit': [boolean]
}>()
const controlOrderBatchRequest = ref<ControlOrderBatchRequest>()
const selectedControlOrganism = ref<ControlOrganism>()
watch(
  () => props.edit,
  (v) => {
    if (v) {
      selectedControlOrganism.value = props.controlOrderBatch?.controlOrganism
      controlOrderBatchRequest.value = mapToControlOrderBatchRequest(props.controlOrderBatch!)
    }
    succeedSave()
  }
)

const currentAnnualNotSatisfyingRate = ref<number>(0)

watch(
  () => props.controlOrderBatch,
  (v) => {
    if (v) {
      annualNotSatisfyingRateApi
        .findByYear(parseInt(v.creationDateTime.split('-')[0] ?? 0))
        .then((res) => {
          currentAnnualNotSatisfyingRate.value = res.data.notSatisfyingRate
        })
        .catch(async (err) => {
          snackbarStore.setError(await handleAxiosException(err))
        })
    }
  }
)

const snackbarStore = useSnackbarStore()
const form = ref<typeof VForm | null>()
const save = async () => {
  if (!props.edit) {
    return
  }
  if (!(await form.value!.validate()).valid) {
    return
  }
  controlOrderBatchApi
    .update(props.controlOrderBatch!.id, controlOrderBatchRequest.value!)
    .then((res) => {
      succeedSave()
      emit('update:control-order-batch', res.data)
      snackbarStore.setSuccess('Le lot de contrôle à bien été mis à jour')
    })
    .catch(async (err) => {
      snackbarStore.setError(await handleAxiosException(err))
      failedSave()
    })
}

const { unsavedDataDialog, failedSave, succeedSave, check } = useUnsavedData(controlOrderBatchRequest)

const controlOrganismDialog = ref(false)

defineExpose({
  check,
})
</script>
