import type { LocalDateTime } from './date'
import type { Entity } from './entity'
import type { User } from './user'

export interface UserProfileRequest {
  id: number
  user: User
  userRole: 'AGENCE' | 'AGENCE_PLUS'
  function: string
  requestComment: string
  entity: Entity

  response?: boolean
  responseComment?: string
  creationDateTime: LocalDateTime
  updateDateTime: LocalDateTime
  responseDateTime?: LocalDateTime
}

export type UserProfileRequestRequest = Omit<UserProfileRequest, 'entity'> & {
  entityId: string
}

export function makeEmptyUserProfileRequest(): UserProfileRequestRequest {
  return {
    id: 0,
    creationDateTime: '',
    entityId: '',
    function: '',
    requestComment: '',
    updateDateTime: '',
    user: makeEmptyUser(),
    userRole: 'AGENCE',
  }
}
