<template>
  <VRow class="h-100 w-100">
    <VCol class="h-100">
      <NjDataTable
        :pageable="pageable"
        :page="data.value!"
        :headers="headers"
        :loading="data.loading"
        fixed
        @update:pageable="updatePageable"
      >
        <template #[`item.delete`]="{ item }">
          <NjIconBtn
            icon="mdi-delete-outline"
            class="h-50 w-50"
            color="primary"
            @click.stop="() => deleteSale(item.id)"
          />
        </template>
      </NjDataTable>
    </VCol>
    <AlertDialog v-bind="deleteSaleDialog.props" title="Supprimer la vente" max-width="640px">
      Êtes-vous sûr de vouloir supprimer la vente?
    </AlertDialog>
  </VRow>
</template>
<script lang="ts" setup>
import type { CeeSaleFilter } from '@/api/ceeSale'
import type { DataTableHeader } from '@/components/okta/NjDataTable.type'
import { useSnackbarStore } from '@/stores/snackbar'
import type { CeeSale } from '@/types/ceeSale'
import type { LocalDate } from '@/types/date'

const props = defineProps({
  operationId: Number,
  saleDate: Object as PropType<LocalDate>,
})

const emit = defineEmits<{
  reload: [void]
}>()

const snackbarStore = useSnackbarStore()

const { data, pageable, updatePageable, pageFilter, reload } = usePagination<CeeSale, CeeSaleFilter>(
  (filter, pageable) => ceeSaleApi.findAll(pageable, filter),
  { operationId: props.operationId, saleDate: props.saleDate, active: true },
  {}
)

const headers: DataTableHeader<CeeSale>[] = [
  {
    title: 'Chrono',
    value: 'operation.chronoCode',
  },
  {
    title: 'kWhc dem. Class',
    value: 'operation.classicCumac',
    formater: (_, value) => formatNumber(value),
    cellClass: 'text-right',
  },
  {
    title: 'kWhc vendus Class.',
    value: 'soldClassicCumac',
    formater: (_, value) => formatNumber(value),
    cellClass: 'text-right',
  },
  {
    title: 'Prix vente unitaire Class.',
    value: 'classicCumacUnitSellingPrice',
    formater: (_, value) => formatPriceNumber(value) + '/MWhc',
    cellClass: 'text-right',
  },
  {
    title: 'Mnt vendus Class.',
    value: 'classicCumacSoldAmount',
    formater: (_, value) => formatPriceNumber(value),
    cellClass: 'text-right',
  },
  {
    title: 'kWhc dem. Préc',
    value: 'operation.precariousnessCumac',
    formater: (_, value) => formatNumber(value),
    cellClass: 'text-right',
  },
  {
    title: 'kWhc vendus Préc.',
    value: 'soldPrecariousnessCumac',
    formater: (_, value) => formatNumber(value),
    cellClass: 'text-right',
  },
  {
    title: 'Prix vente unitaire Préc.',
    value: 'precariousnessCumacUnitSellingPrice',
    formater: (_, value) => formatPriceNumber(value) + '/MWhc',
    cellClass: 'text-right',
  },
  {
    title: 'Mnt vendus Préc.',
    value: 'precariousnessCumacSoldAmount',
    formater: (_, value) => formatPriceNumber(value),
    cellClass: 'text-right',
  },
  {
    title: 'Acheteur',
    value: 'buyer',
  },
  {
    title: 'Remarque',
    value: 'comment',
  },
  {
    title: 'Date de vente',
    value: 'saleDate',
    formater: (_, value) => formatHumanReadableLocalDate(value),
  },
  {
    title: 'Date 100',
    value: 'step100Date',
    formater: (_, value) => formatHumanReadableLocalDate(value),
  },
  {
    title: 'Création',
    value: 'creationDateTime',
    formater: (_, value) => formatHumanReadableLocalDate(value),
  },
]

const deleteSaleDialog = useConfirmAlertDialog()
const deleteSale = async (id: number) => {
  if (await deleteSaleDialog.confirm()) {
    ceeSaleApi
      .deleteById(id)
      .then(() => {
        snackbarStore.setSuccess('La vente a bien été supprimée')
        reload()
        emit('reload')
      })
      .catch(async (err) =>
        snackbarStore.setError(
          await handleAxiosException(err, undefined, {
            defaultMessage: 'Une erreur est survenue lors de la suppression de la vente',
          })
        )
      )
  }
}

watch(
  () => props.operationId,
  (v) => {
    pageFilter.value = { ...pageFilter.value, operationId: v }
  }
)

watch(
  () => props.saleDate,
  (v) => {
    pageFilter.value = { ...pageFilter.value, saleDate: v }
    if (v) {
      if (headers.findIndex((h) => h.value === 'delete') === -1) {
        headers.reverse().push({
          title: '',
          value: 'delete',
          sortable: false,
        })
        headers.reverse()
      }
    } else {
      headers.splice(
        headers.findIndex((h) => h.value === 'na'),
        1
      )
    }
  },
  {
    immediate: true,
  }
)

defineExpose({
  reload,
})
</script>
