<template>
  <NjPage :loading="data.loading" :error-message="data.error" expend-body>
    <template #sub-header>
      <VRow>
        <VCol cols="3">
          <SearchInput
            v-model:loading="data.loading"
            :model-value="pageFilter.search"
            @update:model-value="updateSearch"
          />
        </VCol>
        <VSpacer />
        <VCol class="flex-grow-0">
          <NjBtn :to="{ name: 'StandardizedOperationSheetOneNewView' }">Nouvelle Fiche de calcul</NjBtn>
        </VCol>
      </VRow>
    </template>
    <template #body>
      <VRow class="w-100">
        <VCol>
          <StandardizedOperationSheetDataTable
            :pageable="pageable"
            :page="data.value!"
            :headers="headers"
            :on-click-row="
              (value: StandardizedOperationSheet) =>
                router.push({
                  name: 'StandardizedOperationSheetOneView',
                  params: { id: value.id },
                })
            "
            :to-row="
              (item: StandardizedOperationSheet) => ({
                name: 'StandardizedOperationSheetOneView',
                params: { id: item.id },
              })
            "
            :disabled-row="disabledRow"
            fixed
            :download="downloadExport"
            @update:pageable="updatePageable"
          />
        </VCol>
      </VRow>
    </template>
  </NjPage>
</template>

<script lang="ts" setup>
import { standardizedOperationSheetApi } from '@/api/standardizedOperationSheet'
import NjBtn from '@/components/NjBtn.vue'
import NjPage from '@/components/NjPage.vue'
import SearchInput from '@/components/SearchInput.vue'
import type { DataTableHeader } from '@/components/okta/NjDataTable.type'
import router from '@/router'
import { useSnackbarStore } from '@/stores/snackbar'
import type {
  StandardizedOperationSheet,
  StandardizedOperationSheetFilter,
} from '@/types/calcul/standardizedOperationSheet'
import { usePaginationInQuery } from '@/types/pagination'
import { compareAsc } from 'date-fns'
import type { VSpacer } from 'vuetify/components'
import StandardizedOperationSheetDataTable from '@/views/StandardizedOperationSheetDataTable.vue'

const { data, pageFilter, pageable, updatePageable, updateFilter } = usePaginationInQuery(
  (filter, pageable) => standardizedOperationSheetApi.findAll(pageable, filter),
  {
    defaultPageFilter: {
      label: '',
    } as StandardizedOperationSheetFilter,
    defaultPageablePartial: {
      sort: ['operationCode', 'startDate'],
    },
    saveFiltersName: 'StandardizedOperationSheetAllView',
  }
)

const headers: DataTableHeader[] = [
  {
    title: 'Opération',
    value: 'operationCode',
  },
  {
    title: 'Description',
    value: 'description',
  },
  {
    title: 'Début Validité',
    value: 'startDate',
    formater(_, value) {
      return formatHumanReadableLocalDate(value)
    },
  },
  {
    title: 'Fin Validité',
    value: 'expirationDate',
    formater(_, value) {
      return formatHumanReadableLocalDate(value)
    },
  },
  {
    title: 'Liens',
    value: 'action',
    sortable: false,
  },
  {
    title: 'RGE',
    value: 'rgeMandatory',
  },
  {
    title: 'Fonds chaleur',
    value: 'heatFund',
  },
  {
    title: 'Certifié',
    value: 'certified',
  },
  {
    title: 'Création par',
    value: 'creationUser',
    formater(_, value) {
      return displayFullnameUser(value)
    },
  },
  {
    title: 'Date Création',
    value: 'creationDateTime',
    formater(_, value) {
      return formatHumanReadableLocalDateTime(value)
    },
  },
  {
    title: 'MAJ par',
    value: 'updateUser',
    formater(_, value) {
      return displayFullnameUser(value)
    },
  },
  {
    title: 'Date MAJ',
    value: 'updateDateTime',
    formater(_, value) {
      return formatHumanReadableLocalDateTime(value)
    },
  },
]

const disabledRow = (i: StandardizedOperationSheet): boolean =>
  !!i.expirationDate && compareAsc(new Date(i.expirationDate), new Date()) === -1

// search
const updateSearch = (v: string) => {
  const filter = {
    ...unref(pageFilter),
    search: v,
  }
  updateFilter(filter)
}

const snackbarStore = useSnackbarStore()
const downloadExport = () => {
  standardizedOperationSheetApi
    .export()
    .then((response) => {
      downloadFile('export fiche opération.xlsx', response.data)
    })
    .catch(async (err) =>
      snackbarStore.setError(await handleAxiosException(err, JSON.parse(await err.response.data.text()).message))
    )
}
</script>
