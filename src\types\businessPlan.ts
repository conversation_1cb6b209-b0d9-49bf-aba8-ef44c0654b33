import type { Historisable } from './historisation'

export interface BusinessPlan extends Historisable {
  id: number
  name: string
  toProcess: boolean
}

export interface BusinessPlanRequest
  extends Omit<BusinessPlan, 'id' | 'creationUser' | 'creationDateTime' | 'updateDateTime' | 'updateUser'> {}

export interface BusinessPlanSummary {
  classicCumacSum: number
  precariousnessCumacSum: number
  numberOfOperation: number
  classicValuationAmountSum: number
  precariousnessValuationAmountSum: number
  feeSum: number
  entityIds: string[]
}
