<template>
  <template v-if="displayForm">
    <VForm ref="formRef" class="content-layout__main h-100">
      <VRow class="flex-column" no-gutters>
        <VCol>
          <NjExpansionPanel title="Profil">
            <VRow class="flex-column">
              <VCol>
                <VSwitch v-model="localUser.active" label="Actif" />
              </VCol>
              <VCol>
                <VTextField v-model="localUser.gid" label="gid" :rules="[requiredRule]" />
              </VCol>
              <VCol>
                <VTextField v-model="localUser.function" label="Fonction" :rules="[requiredRule]" />
              </VCol>
              <VCol>
                <RemoteAutoComplete
                  v-model="localUser.territories"
                  label="Territoire"
                  :query-for-one="(v) => territoryApi.findOne(v)"
                  :query-for-all="(v, p) => territoryApi.findAll({ search: v }, p)"
                  :item-title="(it) => formatTerritory(it as any)"
                  item-value="id"
                  infinite-scroll
                  clearable
                  :page-size="50"
                  multiple
                  chips
                  closable-chips
                  return-object
                />
              </VCol>
            </VRow>
          </NjExpansionPanel>
        </VCol>
        <template v-if="localUser.active">
          <VCol>
            <VDivider />
          </VCol>
          <VCol>
            <NjExpansionPanel title="Rôles">
              <VAutocomplete
                v-model="localUser.roles"
                chips
                clearable
                label="Roles"
                :items="filteredDisplayProfiles"
                multiple
                :disabled="!localUser.active"
                :rules="localUser.active ? [requiredRule] : []"
                :readonly="!!localUser.roles.find((role) => role === 'DAF')"
                @update:model-value="checkRoles"
              />
              <NjDisplayValue
                v-if="localUser.roles.find((role) => role === 'DAF')"
                label="Le rôle DAF est incompatible avec les autres"
              />
            </NjExpansionPanel>
          </VCol>
        </template>
        <VCol>
          <VDivider />
        </VCol>
        <VCol>
          <NjExpansionPanel>
            <template #title>
              <div class="d-flex align-center fill-width">
                Organisations
                <VSpacer />
                <VLink
                  size="small"
                  icon="mdi-format-list-bulleted"
                  style="font-weight: initial; font-size: initial"
                  @click.stop="openEntity"
                >
                  Organisations
                </VLink>
                <EntityFilterDialog v-if="!inline" v-model="orgDialog" v-model:selected="localUser.entities" />
              </div>
            </template>
            <VRow class="flex-column">
              <VList>
                <VListItem v-for="org in localUser.entities" :key="org.id">
                  {{ org.name }}
                  <template #append>
                    <NjIconBtn icon="mdi-delete-outline" @click="removeEntity(org)" />
                  </template>
                </VListItem>
              </VList>
            </VRow>
          </NjExpansionPanel>
        </VCol>
        <VCol>
          <VDivider />
        </VCol>
      </VRow>
    </VForm>
    <VDivider />
    <VCardActions class="content-layout__footer">
      <VSpacer />
      <NjBtn variant="outlined" @click="emit('cancel')"> Annuler </NjBtn>
      <NjBtn @click="save"> Valider </NjBtn>
    </VCardActions>
  </template>

  <EntitySelect
    v-else
    v-model:selected="localUser.entities"
    @update:selected="emit('update:displayForm', true)"
    @previous="emit('update:displayForm', true)"
  />
</template>
<script lang="ts" setup>
import { requiredRule } from '@/types/rule'
import { VForm } from 'vuetify/components/VForm'
import VLink from '@/components/VLink.vue'
import EntityFilterDialog from '@/views/operation/dialog/EntityFilterDialog.vue'
import type { PropType } from 'vue'
import { displayProfiles, mapToUserRequest, type UserRequest, type UserWithEntities } from '@/types/user'
import { useSnackbarStore } from '@/stores/snackbar'
import NjExpansionPanel from '@/components/NjExpansionPanel.vue'
import EntitySelect from '@/views/operation/dialog/EntitySelect.vue'
import { VDivider, VList, VListItem } from 'vuetify/components'
import type { Entity } from '@/types/entity'
import { useUserStore } from '@/stores/user'
import { territoryApi } from '@/api/territory'
import { formatTerritory } from '@/types/territory'
import { cloneDeep } from 'lodash'

const props = defineProps({
  user: {
    type: Object as PropType<UserWithEntities>,
    required: true,
  },
  inline: Boolean,
  displayForm: {
    Boolean,
    default: true,
  },
})

const emit = defineEmits<{
  cancel: [void]
  save: [void]
  'update:displayForm': [boolean]
}>()

const localUser = ref<UserWithEntities>(makeEmptyUserWithEntities())
const territories = ref<number[]>([])

watch(
  () => props.user,
  (v) => {
    localUser.value = cloneDeep(v)
    territories.value = v.territories.map((it) => it.id)
  },
  {
    immediate: true,
  }
)

const snackbarStore = useSnackbarStore()

const formRef = ref<VForm | null>(null)

const userStore = useUserStore()
const filteredDisplayProfiles = computed(() =>
  userStore.isAdminPlus ? displayProfiles : displayProfiles.filter((i) => i.value != 'ADMIN_PLUS')
)

const orgDialog = ref(false)

const checkRoles = () => {
  if (localUser.value.roles.find((role) => role === 'DAF')) {
    localUser.value.roles = ['DAF']
  }
  if (localUser.value.roles.find((role) => role === 'AGENCE_PLUS')) {
    localUser.value.roles = localUser.value.roles.filter((r) => r !== 'AGENCE' && r !== 'SUPPORT_AGENCE_PLUS')
  }
  if (localUser.value.roles.find((role) => role === 'SUPPORT_AGENCE_PLUS')) {
    localUser.value.roles = localUser.value.roles.filter((r) => r !== 'AGENCE' && r !== 'AGENCE_PLUS')
  }
}

// Enregistrement
const save = async () => {
  if ((await formRef.value!.validate()).valid) {
    const request: UserRequest = mapToUserRequest(localUser.value)
    if (props.user.id) {
      userApi
        .update(localUser.value.id, request)
        .then(() => {
          snackbarStore.setSuccess('Utilisateur mis à jour avec succès')
          emit('save')
        })
        .catch(async (e) => snackbarStore.setError(await handleAxiosException(e)))
    } else {
      userApi
        .create(request)
        .then(() => {
          snackbarStore.setSuccess('Utilisateur créé avec succès')
          emit('save')
        })
        .catch(async (e) => snackbarStore.setError(await handleAxiosException(e)))
    }
  }
}

const openEntity = () => {
  if (props.inline) {
    emit('update:displayForm', false)
  } else {
    orgDialog.value = true
  }
}

const removeEntity = (org: Entity) => {
  localUser.value.entities = localUser.value.entities.filter((item) => item.id != org.id)
}
</script>
