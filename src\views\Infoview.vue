<template>
  <VContainer fluid>
    <VRow>
      <VCol>
        <VCard>
          <VCardTitle>Info Front</VCardTitle>
          <VCardText>
            <NjDisplayValue label="shaGit" :value="buildInfo.shaGit" />
            <NjDisplayValue label="date" :value="buildInfo.date" />
            <NjDisplayValue label="version" :value="buildInfo.version" />
          </VCardText>
        </VCard>
      </VCol>
      <VCol>
        <VCard :loading="remoteBuildInfo.loading">
          <VCardTitle>Info Back</VCardTitle>
          <VCardText>
            <NjDisplayValue label="shaGit" :value="remoteBuildInfo.value?.build.revision" />
            <NjDisplayValue label="date" :value="remoteBuildInfo.value?.build.date" />
            <NjDisplayValue label="version" :value="remoteBuildInfo.value?.version" />
          </VCardText>
        </VCard>
      </VCol>
    </VRow>
  </VContainer>
</template>

<script setup lang="ts">
import axiosInstance from '@/api'
import NjDisplayValue from '@/components/NjDisplayValue.vue'
import type { PromisableValue } from '@/types/promisableValue'
import type { Ref } from 'vue'

const buildInfo = ref({
  shaGit: import.meta.env.VITE_BUILD_COMMIT,
  date: import.meta.env.VITE_BUILD_DATE,
  version: import.meta.env.VITE_APP_VERSION,
})

const remoteBuildInfo: Ref<PromisableValue<any>> = ref(emptyValue())
onMounted(() => {
  handleAxiosPromise(remoteBuildInfo, axiosInstance.get('info'))
})
</script>
