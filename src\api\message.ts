import type { Message, MessageRequest } from '@/types/message'
import type { Page, Pageable } from '@/types/pagination'
import type { AxiosInstance, AxiosPromise } from 'axios'
import axiosInstance from '.'

export interface MessageFilter {
  operationId?: number
  operationGroupId?: number
  parentId?: number
  accounted?: boolean
}

class MessageApi {
  public constructor(private axios: AxiosInstance) {}

  public getAll(pageable: Pageable, filter: MessageFilter): AxiosPromise<Page<Message>> {
    return this.axios.get('messages', { params: { ...pageable, ...filter } })
  }

  public create(request: MessageRequest, screenshots: Blob[]): AxiosPromise<Message> {
    const formData = new FormData()
    const blob = new Blob([JSON.stringify(engieFormatRequestTransformKey(request))], {
      type: 'application/json',
    })
    formData.append('request', blob)
    screenshots.forEach((it) => {
      formData.append('screenshots', it)
    })
    return this.axios.post('/messages', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    })
  }

  public update(id: number, request: MessageRequest): AxiosPromise<Message> {
    return this.axios.put(`/messages/${id}`, request)
  }

  public downloadScreenshot(id: number, documentId: number): AxiosPromise<Blob> {
    return this.axios.get(`/messages/${id}/documents/${documentId}`, {
      responseType: 'blob',
    })
  }
}

export const messageApi = new MessageApi(axiosInstance)
