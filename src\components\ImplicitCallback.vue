<template>
  <div class="fill-width d-flex align-center justify-center" style="height: 100vh">
    <ErrorAlert :message="error" style="max-width: 640px">
      <div class="mb-4">{{ error }}</div>
      <div>(Message d'erreur : {{ completeError }})</div>
      <NjBtn to="/" color="white" variant="outlined">Réessayer</NjBtn>

      <div v-if="error.endsWith('User is not assigned to the client application.')" class="mt-4">
        (Avez-vous fait "Ajouter des applications" sur
        <a href="https://engie.okta-emea.com/app/UserHome">Engie OKTA</a>, et rajouté "CAPTE" ?)
      </div>
    </ErrorAlert>
    <VCard v-if="!error">
      <VCardText style="text-align: center">
        <p class="font-weight-bold">Vous allez bientôt être redirigée...</p>
        <div>Si ce n'est pas le cas, veuillez rafraîchir votre page</div>
        <VBtn variant="text" color="secondary" to="/">Cliquez ici</VBtn>
      </VCardText>
    </VCard>
  </div>
</template>

<script setup lang="ts">
import axiosInstance from '@/api'
import { authenticationApi } from '@/api/auth'
import { useAuth } from '@okta/okta-vue'
import ErrorAlert from './ErrorAlert.vue'
import { setGid } from '@/stores/analytics'

const auth = useAuth()
const error = ref('')
const completeError = ref('')
onBeforeMount(async () => {
  manageRedirection()
})

const router = useRouter()

async function manageRedirection() {
  try {
    await auth.handleLoginRedirect()
    const user = await auth.getUser()
    // TODO a factorier dans App.vue
    const gid = user.preferred_username?.replace('@engie.com', '')
    axiosInstance.defaults.headers.common['x-api-sub'] = gid
    setGid(gid ?? '')

    await authenticationApi.login(
      {
        firstName: user.given_name!,
        lastName: user.family_name!,
        email: user.email!,
      },
      {
        headers: {
          Authorization: auth.getIdToken(),
        },
      }
    )
    await router.push(JSON.parse(sessionStorage.getItem('okta-current-route') ?? '{}'))
  } catch (e) {
    error.value = 'Une erreur est survenue lors de votre authentication OKTA.'
    completeError.value = (e as any).toString?.()
    logException(e)

    localStorage.removeItem('okta-cache-storage')
    localStorage.removeItem('okta-token-storage')
    sessionStorage.removeItem('okta-token-storage')
    sessionStorage.removeItem('okta-cache-storage')

    // goToLandingPage()
  }
}

// function goToLandingPage() {
//   router.push('HomeView')
// }
</script>
