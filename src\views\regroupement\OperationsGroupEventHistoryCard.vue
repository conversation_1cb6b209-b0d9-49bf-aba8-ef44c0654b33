<template>
  <MessageCard
    v-if="event.message"
    :message="event.message!"
    :operation-group-id="event.operationsGroupHistory.id"
    :documents-available="documentsAvailable"
    @send="emits('reload')"
    @treated="emits('treated', $event)"
  />
  <VCard v-else>
    <VCardTitle class="text-primary font-weight-bold" style="font-size: 1rem !important">
      <VRow>
        <VCol>
          {{ displayFullnameUser(event.creationUser) }}
        </VCol>
        <VSpacer />
        <VCol class="flex-grow-0">
          {{ getEventLabel(event) }}
        </VCol>
      </VRow>
    </VCardTitle>
    <VCardSubtitle class="ps-4">
      {{ formatHumanReadableLocalDateTime(event.creationDateTime) }}
    </VCardSubtitle>
    <VCardText>
      {{ getContent(event) }}
      <div v-for="(change, index) in event.documentChangeSet" :key="index">
        <NjDisplayValue
          v-if="event.event == 'operationsGroup.document.created'"
          :label="changeSetFieldNameLabel.find((i) => i.fieldName == change.fieldName)?.value ?? change.fieldName"
          :value="resolveValue(change, false)"
        />
        <NjDisplayValue
          v-else-if="event.event == 'operationsGroup.document.updated'"
          :label="changeSetFieldNameLabel.find((i) => i.fieldName == change.fieldName)?.value ?? change.fieldName"
          :value="resolveValue(change, true) + ' → ' + resolveValue(change, false)"
        />
        <NjDisplayValue
          v-else-if="event.event == 'operationsGroup.document.deleted'"
          :label="changeSetFieldNameLabel.find((i) => i.fieldName == change.fieldName)?.value ?? change.fieldName"
          :value="resolveValue(change, true)"
        />
      </div>
      <div v-for="(change, index) in filterChangeSet(event)" :key="index">
        <NjDisplayValue
          v-if="isCreationEvent(event.event)"
          :label="fieldNameLabel(change.fieldName) ?? change.fieldName"
          :value="resolveValue(change, false)"
        />
        <NjDisplayValue
          v-else-if="isDeletionEvent(event.event)"
          :label="fieldNameLabel(change.fieldName) ?? change.fieldName"
          :value="resolveValue(change, true)"
        />
        <NjDisplayValue
          v-else
          :label="fieldNameLabel(change.fieldName) ?? change.fieldName"
          :value="`${resolveValue(change, true)} → ${resolveValue(change, false)}`"
        />
      </div>
    </VCardText>
  </VCard>
</template>
<script setup lang="ts">
import type { OperationsGroupEvent } from '@/types/operationsGroupEvent'
import type { PropType } from 'vue'
import MessageCard from '../operationEventHistory/MessageCard.vue'
import type { Page } from '@/types/pagination'
import type { EnhancedDocument } from '@/types/document'
import { formatHumanReadableLocalDateTime } from '@/types/date'
import { displayFullnameUser } from '@/types/user'
import { changeSetFieldNameLabel } from '@/types/operationEvent'
import NjDisplayValue from '@/components/NjDisplayValue.vue'
import type { ObjectChange } from '@/types/operationEvent'
import type { AtypicalValuationMessageRequest } from '@/types/message'

defineProps({
  event: {
    type: Object as PropType<OperationsGroupEvent>,
    required: true,
  },
  documentsAvailable: Object as PropType<Page<EnhancedDocument>>,
})

const emits = defineEmits<{
  reload: [void]
  treated: [AtypicalValuationMessageRequest]
}>()

const getEventLabel = (event: OperationsGroupEvent) => {
  switch (event.event) {
    case 'operationsGroup.created':
      return 'Création'
    case 'operationsGroup.updated':
      return 'Mis à jour'
    case 'operationsGroup.operation.removed':
      return "Enlèvement d'une opération"
    case 'operationsGroup.operation.added':
      return "Ajout d'une opération"
    case 'operationsGroup.validateStep.30':
      return "Validation d'étape"
    case 'operationsGroup.validateStep.40':
      return "Validation d'étape"
    case 'operationsGroup.document.created':
      return "Ajout d'un document"
    case 'operationsGroup.document.updated':
      return "Mis à jour d'un document"
    case 'operationsGroup.document.deleted':
      return "Suppression d'un document"
    case 'operationsGroup.message.created':
      return 'Message'
    case 'operationsGroup.unvalidateStep':
      return "Retour à l'étape précédente"
    default:
      return event.event
  }
}

const getContent = (event: OperationsGroupEvent) => {
  switch (event.event) {
    case 'operationsGroup.operation.removed':
      return `L'opération ${event.concernedOperationName} a été enlevée`
    case 'operationsGroup.operation.added':
      return `L'opération ${event.concernedOperationName} a été ajoutée`
    case 'operationsGroup.validateStep.30':
      return "Validation de l'étape 30"
    case 'operationsGroup.validateStep.40':
      return "Validation de l'étape 40"
    case 'operationsGroup.unvalidateStep':
      return "Retour à l'étape précédente"
    default:
      return null
  }
}

const resolveValue = (change: ObjectChange, isOldValue: boolean) => {
  const value = isOldValue ? change.oldValue : change.newValue
  if (value === null) {
    return 'Aucun'
  } else if (value === false) {
    return 'Non'
  } else if (value === true) {
    return 'Oui'
  } else {
    return value
  }
}

const filterChangeSet = (event: OperationsGroupEvent): ObjectChange[] => {
  return event.changeSet
}

const fieldNameLabel = (fieldName: string) => {
  return changeSetFieldNameLabel.find((item) => item.fieldName == fieldName)?.value
}

const displayOnlyNewValueEvent: string[] = [
  // 'operation.created',
  // 'operation.document.created',
  // 'operation.addedInControlOrderBatch',
  // 'operation.addedInEmmyFolder',
]
const displayOnlyOldValueEvent: string[] = [
  // 'operation.removedFromControlOrderBatch',
  // 'operation.removedFromEmmyFolder',
  // 'operation.document.delete',
]
const isCreationEvent = (event: string) => {
  return !!displayOnlyNewValueEvent.find((item) => event == item)
}

const isDeletionEvent = (event: string) => {
  return !!displayOnlyOldValueEvent.find((item) => event == item)
}
</script>
