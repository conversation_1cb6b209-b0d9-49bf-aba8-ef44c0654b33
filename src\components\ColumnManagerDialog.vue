<template>
  <VNavigationDrawer :model-value="modelValue" location="end" disable-resize-watcher width="300">
    <VCard class="content-layout">
      <VCardTitle class="content-layout__header">
        <VRow class="align-center">
          <VCol> Personnaliser </VCol>
          <VCol class="flex-grow-0">
            <NjIconBtn icon="mdi-close" rounded="0" @click="emit('update:model-value', false)"></NjIconBtn>
          </VCol>
        </VRow>
      </VCardTitle>
      <VDivider />
      <VCardText class="content-layout__main pt-0">
        <VList ref="list">
          <VListItem
            v-for="h in userTablePersonalization.value?.columnPersonalizations"
            :key="h.columnName"
            density="compact"
            class="px-0"
          >
            <div class="d-flex align-center">
              <VIcon class="sortable-handle me-2">mdi-drag</VIcon>
              <span class="d-flex flex-grow-1">{{
                originalHeaders.find((i) => (i.key ?? i.value) == h.columnName)?.title
              }}</span>
              <VCheckboxBtn v-model="h.visible" class="flex-grow-0" @update:model-value="debouncedUpdate()" />
            </div>
          </VListItem>
        </VList>
      </VCardText>
    </VCard>
  </VNavigationDrawer>
</template>

<script setup lang="ts">
import { useSnackbarStore } from '@/stores/snackbar'
import { useUserTablePersonalizationsStore } from '@/stores/userTablePersonalization'
import { type ColumnPersonalization, type UserTablePersonalization } from '@/types/userTablePersonalization'
import { moveArrayElement, useSortable, type UseSortableReturn } from '@vueuse/integrations/useSortable'
import { cloneDeep, debounce, sortBy } from 'lodash'
import type { PropType } from 'vue'
import { VCheckboxBtn, VNavigationDrawer } from 'vuetify/components'
import type { DataTableHeader } from './okta/NjDataTable.type'

const props = defineProps({
  originalHeaders: {
    type: Array as PropType<DataTableHeader[]>,
    required: true,
  },
  id: {
    type: String,
    required: true,
  },
  modelValue: {
    type: Boolean,
    required: true,
  },
})

const emit = defineEmits<{
  'update:model-value': [boolean]
}>()

const userTablePersonalization = ref(emptyValue<UserTablePersonalization>())

const snackbarStore = useSnackbarStore()

const headers = computed(() => {
  if (userTablePersonalization.value.value) {
    const columnPersonalizations = userTablePersonalization.value.value.columnPersonalizations

    return columnPersonalizations
      .filter((i) => i.visible)
      .map((column) => props.originalHeaders.find((i) => (i.key ?? i.value) == column.columnName)!)
  } else {
    return cloneDeep(props.originalHeaders)
  }
})

const userTablePersonalizationStore = useUserTablePersonalizationsStore()
watch(
  [() => props.id, () => props.originalHeaders],
  async () => {
    if (
      props.originalHeaders.reduce((acc, v) => acc.add(v.key ?? v.value), new Set<string>()).size !=
      props.originalHeaders.length
    ) {
      // Permet de prévenir de potentiels bug car on s'en sert comme keys.
      console.error(
        'ColumnManager: some columns have same ids',
        sortBy(Array.from(props.originalHeaders.reduce((acc, v) => acc.add(v.key ?? v.value), new Set<string>()))),
        sortBy(props.originalHeaders.map((it) => it.key ?? it.value))
      )
    }
    try {
      const personnalization = await userTablePersonalizationStore.getOne(props.id, props.originalHeaders)
      removeNotExistingColumn(personnalization)
      addMissingColumn(personnalization)
      userTablePersonalization.value.value = personnalization
      initializeSortable(userTablePersonalization.value.value.columnPersonalizations)
    } catch (err) {
      snackbarStore.setError(await handleAxiosException(err))
    }
  },
  {
    immediate: true,
  }
)

const removeNotExistingColumn = (userTablePersonalization: UserTablePersonalization) => {
  userTablePersonalization.columnPersonalizations = userTablePersonalization.columnPersonalizations.filter((column) =>
    props.originalHeaders.find((i) => (i.key ?? i.value) == column.columnName)
  )
}

const addMissingColumn = (userTablePersonalization: UserTablePersonalization) => {
  const missingColumns = props.originalHeaders
    .filter(
      (column) =>
        !userTablePersonalization.columnPersonalizations.find((i) => (column.key ?? column.value) == i.columnName)
    )
    .map((i) => ({
      columnName: i.key ?? i.value,
      visible: false,
    }))

  userTablePersonalization.columnPersonalizations.push(...missingColumns)
}

const sortable = ref<UseSortableReturn | null>(null)

const listRef = useTemplateRef('list')
const initializeSortable = (columnPersonalizations: ColumnPersonalization[]) => {
  sortable.value = useSortable(
    computed(() => listRef.value?.$el),
    columnPersonalizations,
    {
      handle: '.sortable-handle',
      onUpdate: (event) => {
        moveArrayElement(columnPersonalizations, event.oldIndex ?? 0, event.newIndex ?? 0)
        // nextTick required here as moveArrayElement is executed in a microtas
        // so we need to wait until the next tick until that is finished.
        nextTick(() => {
          debouncedUpdate()
        })
      },
    }
  )
}

const debouncedUpdate = debounce(() => {
  update.value = false
  const v = userTablePersonalization.value.value

  userTablePersonalizationStore.update({
    tableName: v!.id.tableName,
    columnPersonalizations: v!.columnPersonalizations,
  })
}, 1000)

const update = ref<boolean>(false)

defineExpose({ headers })
</script>
