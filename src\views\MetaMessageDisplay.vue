<template>
  <div v-if="message.metaMessage?.['@type'] === 'AtypicalValuationMessage'">
    Valorisations demandées :<br />
    <VRow>
      <VCol>
        <NjDisplayValue label="Classique" :value="message.metaMessage.classicValuationValue" />
      </VCol>
      <VCol>
        <NjDisplayValue label="Précarité" :value="message.metaMessage.precariousnessValuationValue" />
      </VCol>
    </VRow>
    <div
      v-if="
        message.metaMessage.accepted === null &&
        (userHasRole(userStore.currentUser, 'ADMIN_PLUS') ||
          message.recipients.split(';').includes(userStore.currentUser.email ?? ''))
      "
    >
      Accepter cette demande? <VLink class="ps-4" @click="treatMessage(true)">Oui</VLink>
      <VLink class="ps-4" @click="treatMessage(false)">Non</VLink>
    </div>
    <div v-else-if="message.metaMessage.accepted !== null">
      Demande {{ message.metaMessage.accepted ? 'acceptée' : 'refusée' }} par
      {{ displayFullnameUser(message.metaMessage.respondedUser!) }} le
      {{ formatHumanReadableLocalDateTime(message.metaMessage.respondedDateTime!) }}
    </div>
  </div>
</template>
<script lang="ts" setup>
import { formatHumanReadableLocalDateTime } from '@/types/date'
import { displayFullnameUser } from '@/types/user'
import type { AtypicalValuationMessageRequest, Message } from '@/types/message'
import type { PropType } from 'vue'
import { userHasRole } from '@/types/user'
import { useUserStore } from '@/stores/user'
import { useSnackbarStore } from '@/stores/snackbar'

const props = defineProps({
  message: {
    type: Object as PropType<Message>,
    required: true,
  },
})

const emits = defineEmits<{
  treated: [AtypicalValuationMessageRequest]
}>()

const userStore = useUserStore()

const snackbarStore = useSnackbarStore()

const treatMessage = (accepted: boolean) => {
  messageApi
    .update(props.message.id, {
      ...mapToMessageRequest(props.message),
      metaMessage: {
        '@type': 'AtypicalValuationMessageRequest',
        classicValuationValue: props.message.metaMessage!.classicValuationValue,
        precariousnessValuationValue: props.message.metaMessage!.precariousnessValuationValue,
        accepted: accepted,
      },
    })
    .then(() => {
      emits('treated', {
        accepted: accepted,
        classicValuationValue: props.message.metaMessage!.classicValuationValue,
        precariousnessValuationValue: props.message.metaMessage!.precariousnessValuationValue,
        '@type': 'AtypicalValuationMessageRequest',
      })
      snackbarStore.setSuccess('Le message a été traité avec succès')
    })
    .catch(async (err) => {
      snackbarStore.setError(err.error ?? 'Une erreur est survenue lors du traitement du message')
    })
}
</script>
