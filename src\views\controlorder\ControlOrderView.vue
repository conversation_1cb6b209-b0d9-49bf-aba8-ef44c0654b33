<template>
  <div class="content-layout">
    <div class="content-layout__header px-4 pt-4">
      <h1><PERSON><PERSON><PERSON><PERSON></h1>
      <VTabs>
        <VTab v-for="t in tabs" :key="t.routeName" :to="{ name: t.routeName }">{{ t.label }}</VTab>
      </VTabs>
      <VDivider />
    </div>
    <RouterView class="content-layout__main" />
  </div>
</template>
<script lang="ts" setup>
const tabs: { routeName: string; label: string }[] = [
  { label: 'Créer un lot de contrôle', routeName: 'ControlOrderView' },
  { label: 'Liste des lots de contrôle', routeName: 'ControlOrderBatchAllView' },
]
</script>
