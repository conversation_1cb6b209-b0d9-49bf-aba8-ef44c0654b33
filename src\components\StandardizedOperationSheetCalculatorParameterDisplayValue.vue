<template>
  <NjDisplayValue :label="parameterFormula?.label ?? ''" :value="displayValue" />
</template>
<script setup lang="ts">
import type { PropType } from 'vue'
import NjDisplayValue from './NjDisplayValue.vue'
import type { ParameterFormula } from '@/types/calcul/parameterFormula'

const props = defineProps({
  parameterFormula: Object as PropType<ParameterFormula>,
  value: String as PropType<string | number>,
})

const displayValue = computed(() => {
  let formatValue

  if (props.value == undefined) {
    formatValue = ''
  } else if (props.parameterFormula?.type == 'DATE') {
    formatValue = formatHumanReadableLocalDate(props.value as string)
  } else {
    formatValue = props.value
  }

  return formatValue + ' ' + (props.parameterFormula?.suffix ?? '')
})
</script>
