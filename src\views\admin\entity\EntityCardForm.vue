<template>
  <VCard class="content-layout">
    <VCardTitle class="d-flex align-center py-0">
      Détails
      <VSpacer />
      <NjIconBtn color="primary" icon="mdi-close" class="me-n4 rounded-0" @click="check(() => emit('close'))" />
    </VCardTitle>
    <VDivider />
    <VCardText class="content-layout__main pa-0 content-layout">
      <VTabs v-model="tab" class="content-layout__header">
        <VTab value="data">Données</VTab>
        <VTab value="history">Traçabilité</VTab>
      </VTabs>
      <VDivider />
      <VWindow v-model="tab" class="content-layout__main">
        <VWindowItem value="data">
          <VForm ref="formRef">
            <VRow class="flex-column" no-gutters>
              <VCol>
                <VRow class="flex-column" no-gutters>
                  <VCol>
                    <NjExpansionPanel title="Organisation">
                      <VRow class="flex-column" dense>
                        <VCol v-if="entity.value?.parentId">
                          <NjDisplayValue label="Parent">
                            <template #value>
                              <VLink @click="check(() => emit('click:parentId', entity.value!.parentId!))">{{
                                parentEntity.value
                                  ? `${parentEntity.value.name} (${parentEntity.value.id})`
                                  : entity.value.parentId
                              }}</VLink>
                            </template>
                          </NjDisplayValue>
                        </VCol>

                        <VCol>
                          <NjDisplayValue label="Code" :value="entity.value?.id" />
                        </VCol>
                        <VCol>
                          <NjDisplayValue label="Hiérarchie" :value="entity.value?.navFullId" />
                        </VCol>
                        <VCol>
                          <NjDisplayValue label="Désignation" :value="entity.value?.name" />
                        </VCol>
                        <VCol>
                          <NjDisplayValue label="Territoire" :value="formatTerritory(entity.value?.territory)" />
                        </VCol>
                        <VCol>
                          <NjDisplayValue label="Adresse" :value="formatAddressable(entity.value?.address)" />
                        </VCol>
                        <VCol>
                          <NjDisplayValue label="Téléphone" :value="entity.value?.phoneNumber" />
                        </VCol>
                        <VCol>
                          <NjDisplayValue label="SIRET" :value="entity.value?.siret" />
                        </VCol>
                        <VCol>
                          <NjDisplayValue label="Niveau" :value="entityLevelLabels[entity.value?.level ?? 0]" />
                        </VCol>
                        <VCol>
                          <NjDisplayValue label="Actif">
                            <template #value>
                              <VIcon
                                :icon="
                                  entity.value?.enabled ? 'mdi-checkbox-marked-outline' : 'mdi-checkbox-blank-outline'
                                "
                              />
                            </template>
                          </NjDisplayValue>
                        </VCol>
                        <VDivider />
                        <VCol>
                          <VSwitch
                            v-model="request.visible"
                            label="Visible"
                            :disabled="!entity.value?.enabled && !entity.value?.entityDetails.visible"
                          />
                        </VCol>
                        <VCol>
                          <VSwitch
                            v-model="request.canSellCumacs"
                            label="Vente de CEE possible"
                            :disabled="parentEntity.value?.entityDetails.canSellCumacs === false"
                          />
                        </VCol>
                      </VRow>
                    </NjExpansionPanel>
                  </VCol>
                  <VDivider />
                  <VCol>
                    <NjExpansionPanel title="Charge pôle CEE">
                      <VRow class="flex-column" dense>
                        <VCol v-if="!entity.value?.entityDetails.fee">
                          Charge pôle CEE unitaire par défaut : {{ entity.value?.entityDetails.effectiveFee }}
                        </VCol>
                        <VCol>
                          <VTextField
                            v-model="request.fee"
                            type="number"
                            label="Charge pôle CEE unitaire"
                            class="w-100"
                            suffix="€"
                          />
                        </VCol>
                      </VRow>
                    </NjExpansionPanel>
                  </VCol>
                </VRow>
              </VCol>
              <VDivider />
              <VCol>
                <VRow class="flex-column" no-gutters>
                  <VCol>
                    <UserEntityExpansionPanel
                      title="Instructeur"
                      :model-value="entity.value?.entityDetails.instructor"
                      :no-data-label="
                        'Aucun instructeur sélectionné' +
                        (entity.value?.entityDetails.effectiveInstructor
                          ? ' (Instructeur par défaut : ' +
                            entity.value?.entityDetails.effectiveInstructor.firstName +
                            ' ' +
                            entity.value?.entityDetails.effectiveInstructor.lastName +
                            ')'
                          : '')
                      "
                      dialog-title="Sélectionner un instructeur"
                      :filter="{ roles: ['INSTRUCTEUR'] as ProfileType[], active: true }"
                      @update:model-value="(v) => (request.instructorUserId = v?.id)"
                    />
                  </VCol>
                  <VDivider />
                  <VCol>
                    <UserEntityExpansionPanel
                      :model-value="entity.value?.entityDetails.territoryReferent"
                      title="Référent"
                      :no-data-label="
                        'Aucun référent sélectionné' +
                        (entity.value?.effectiveTerritoryReferent
                          ? ' (Référent par défaut : ' +
                            entity.value?.effectiveTerritoryReferent.firstName +
                            ' ' +
                            entity.value?.effectiveTerritoryReferent.lastName +
                            ')'
                          : '')
                      "
                      :filter="{ roles: ['ADMIN', 'ADMIN_PLUS'] as ProfileType[] }"
                      dialog-title="Sélectionner un référent"
                      @update:model-value="(v) => (request.territoryReferentUserId = v?.id)"
                    />
                  </VCol>
                  <VDivider />
                  <VCol>
                    <NjExpansionPanel title="Représentant">
                      <VRow class="flex-column" dense>
                        <VCol>
                          <VRow>
                            <VCol class="flex-grow-0"> Civilité </VCol>
                            <VCol class="align-self-end">
                              <VRadioGroup v-model="request.civilityAgent" inline class="v-radiogroup--align-end">
                                <VRadio label="Monsieur" value="MONSIEUR"></VRadio>
                                <VRadio label="Madame" value="MADAME"></VRadio>
                              </VRadioGroup>
                            </VCol>
                          </VRow>
                        </VCol>
                        <VCol>
                          <VRow>
                            <VCol class="flex-grow-0 text-no-wrap"> Pronom possessif </VCol>
                            <VCol class="align-self-end">
                              <VRadioGroup
                                v-model="request.possessivePronounAgent"
                                inline
                                class="v-radiogroup--align-end"
                              >
                                <VRadio label="Son" value="SON"></VRadio>
                                <VRadio label="Sa" value="SA"></VRadio>
                              </VRadioGroup>
                            </VCol>
                          </VRow>
                        </VCol>
                        <VCol>
                          <VRow dense class="flex-column">
                            <VCol>
                              <VTextField v-model="request.lastNameAgent" label="Nom" />
                            </VCol>
                            <VCol>
                              <VTextField v-model="request.firstNameAgent" label="Prénom" />
                            </VCol>
                            <VCol>
                              <VTextField v-model="request.officeAgent" label="Fonction" />
                            </VCol>
                          </VRow>
                        </VCol>
                      </VRow>
                    </NjExpansionPanel>
                  </VCol>
                  <VCol v-if="entity.value && !isSubsidiary(entity.value)">
                    <UserEntityExpansionPanel
                      :model-value="entity.value?.entityDetails.financialManager"
                      title="Responsable financier"
                      no-data-label="Aucun responsable financier"
                      @update:model-value="(v) => (request.financialManagerUserId = v?.id)"
                    />
                  </VCol>
                  <VCol>
                    <NjExpansionPanel title="Mails DAF">
                      <VRow class="flex-column">
                        <VCol v-if="entity.value?.entityDetails.dafMail.length === 0">
                          <div><i>Aucun mail de contact DAF renseigné pour cette organisation</i></div>
                          <div v-if="entity.value?.entityDetails.effectiveDafMail.length">
                            Mail par défaut : {{ entity.value?.entityDetails.effectiveDafMail.join(', ') }}
                          </div>
                        </VCol>
                        <VCol v-for="(m, i) in request.dafMail" :key="i" class="d-flex align-center">
                          <VTextField
                            v-model="request.dafMail[i]"
                            type="email"
                            :label="'Mail Envoi à la DAF #' + (i + 1)"
                            :rules="[emptyOrEmail]"
                            class="w-100"
                          />
                          <NjIconBtn icon="mdi-minus" @click="request.dafMail.splice(i, 1)" />
                        </VCol>
                        <VCol>
                          <NjBtn class="w-100" @click="request.dafMail.push('')">Ajouter un email de contact DAF</NjBtn>
                        </VCol>
                      </VRow>
                    </NjExpansionPanel>
                  </VCol>

                  <VCol>
                    <NjExpansionPanel title="Autres rôles">
                      <VRow class="flex-column" dense>
                        <VCol v-for="(v, k) in otherEntityJobTitles" :key="k">
                          <RemoteAutoComplete
                            v-model="request[k]"
                            :label="v"
                            :query-for-one="(id) => userApi.getOne(id)"
                            :query-for-all="
                              (v: string, pageable: Pageable) => userApi.getAll(pageable, { active: true, search: v })
                            "
                            :item-title="(item: User) => displayFullnameUser(item)"
                            clearable
                          />
                        </VCol>
                      </VRow>
                    </NjExpansionPanel>
                  </VCol>
                </VRow>
              </VCol>
            </VRow>
          </VForm>
        </VWindowItem>
        <VWindowItem value="history">
          <VRow dense class="flex-column gap-2 ma-0 py-2">
            <NjInfiniteScroll
              ref="historyList"
              :callback="
                (p) =>
                  entityDetailsHistoryApi.findAll(
                    { size: 10, page: p, sort: ['creationDateTime,DESC'] },
                    { entityId: props.id }
                  )
              "
            >
              <template #item="{ history }">
                <HistoryCard
                  :display-properties="entityDetailsHistoryDisplayProperties"
                  :model-value="history"
                  class="mx-2"
                />
              </template>
              <template #end-data>
                <VDivider />
                <div class="px-4 py-2">Fin de l'historique</div>
              </template>
              <template #no-data>
                <div class="pa-4"><i>Il n'y a aucun historique pour le moment pour cette organisation.</i></div>
              </template>
            </NjInfiniteScroll>
          </VRow>
        </VWindowItem>
      </VWindow>
    </VCardText>

    <VDivider />
    <VCardActions>
      <VSpacer />
      <NjBtn :loading="entity.loading" @click="save">Enregistrer les modifications</NjBtn>
    </VCardActions>
    <ConfirmUnsavedDataDialog v-model:unsaved-data-dialog="unsavedDataDialog" @save="save" />
  </VCard>
</template>

<script setup lang="ts">
import { entityApi } from '@/api/entity'
import { entityDetailsHistoryApi } from '@/api/entityHistory'
import { userApi } from '@/api/user'
import NjBtn from '@/components/NjBtn.vue'
import NjDisplayValue from '@/components/NjDisplayValue.vue'
import NjExpansionPanel from '@/components/NjExpansionPanel.vue'
import VLink from '@/components/VLink.vue'
import { useEntityStore } from '@/stores/entity'
import { useSnackbarStore } from '@/stores/snackbar'
import { formatAddressable } from '@/types/address'
import {
  entityLevelLabels,
  isSubsidiary,
  makeEmptyEntity,
  type Entity,
  type EntityDetails,
  type UpdateEntityDetailsRequest,
} from '@/types/entity'
import { entityDetailsHistoryDisplayProperties } from '@/types/history'
import type { Pageable } from '@/types/pagination'
import type { PromisableValue } from '@/types/promisableValue'
import { emptyOrEmail } from '@/types/rule'
import { formatTerritory } from '@/types/territory'
import { displayFullnameUser, type ProfileType, type User } from '@/types/user'
import { VDivider, VForm } from 'vuetify/components'
import UserEntityExpansionPanel from './UserEntityExpansionPanel.vue'

const props = defineProps({
  id: {
    type: String,
    required: true,
  },
})

const emit = defineEmits<{
  'click:parentId': [string]
  saved: []
  close: []
}>()
const request = ref<UpdateEntityDetailsRequest>({
  visible: false,
  canSellCumacs: false,
  address: makeEmptyAddress(),
  dafMail: [],
  updateDateTime: '',
})

const formRef = ref<typeof VForm | null>(null)
const historyListRef = useTemplateRef('historyList')
const snackbarStore = useSnackbarStore()
const router = useRouter()
const detailUpdating = ref(emptyValue<EntityDetails>())
const save = async () => {
  request.value.dafMail = request.value.dafMail.map((it) => it.trim()).filter((it) => !!it)
  if ((await formRef.value!.validate()).valid) {
    handleAxiosPromise(detailUpdating, entityApi.updateDetails(props.id!, request.value), {
      afterSuccess(d) {
        emit('saved')
        request.value.updateDateTime = d.data.updateDateTime
        succeedSave()
        entityStore.clear()
        if (props.id) {
          snackbarStore.setSuccess('Modification bien enregistrée')
          historyListRef.value?.reload()
        } else {
          snackbarStore.setSuccess('Organisation bien créée')
          router.push({ name: 'EntityAllView' })
        }
      },
      afterError() {
        snackbarStore.setError(entity.value.error ?? "Erreur lors de la sauvegarde de l'organisation")
        failedSave()
      },
    })
  }
}

// daf mails
const entityStore = useEntityStore()
const parentEntity = ref<PromisableValue<Readonly<Entity>>>(emptyValue())

// chargement
const entity = ref(succeedValue(makeEmptyEntity()))
const { unsavedDataDialog, failedSave, succeedSave, check } = useUnsavedData(request)
watch(
  () => props.id,
  (id) => {
    if (id) {
      handleAxiosPromise(entity, entityApi.getOne(id), {
        afterSuccess: (v) => {
          request.value = makeUpdateEntityDetailsRequest(v.data.entityDetails)
          succeedSave()
          handlePromise(parentEntity, entityStore.getOne(v.data.parentId))
          historyListRef.value?.reload()
        },
        afterError: failedSave,
      })
    } else {
      request.value = makeUpdateEntityDetailsRequest(makeEmptyEntityDetails())
      succeedSave()
    }
  },
  {
    immediate: true,
  }
)

const otherEntityJobTitles = computed(() => {
  if (isSubsidiary(entity.value.value!)) {
    return {
      chiefExecutiveUserId: 'Directeur(e) général(e)',
      chairmanUserId: "Président(e) du conseil d'administration",
    }
  } else {
    return {
      regionalDirectorUserId: 'Directeur(e) régional(e)',
      directorOfOperationalActivitiesUserId: 'Directeur des activités Opérationnelles',
      industryOperationsAndPerformanceManagerUserId: 'Responsable Opérations et Performance (industrie)',
    }
  }
})

const tab = ref()

defineExpose({ check })
</script>
