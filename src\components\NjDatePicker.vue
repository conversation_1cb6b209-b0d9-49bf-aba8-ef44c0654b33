<template>
  <VMenu v-model="dateMenu" :close-on-content-click="false" contained>
    <template #activator="{ props }">
      <component
        :is="newStyle ? NjTextField : VTextField"
        v-bind="{ ...$attrs, ...props }"
        v-model="textFieldValue"
        :label="label"
        :error-messages="error"
        :disabled="disabled"
        append-inner-icon="mdi-calendar-blank"
        hint="Format: JJ/MM/AAAA"
        field-class="bg-white"
        :required
        :recommended
      />
    </template>
    <VLocaleProvider locale="fr">
      <VDatePicker
        v-click-outside="() => (dateMenu = false)"
        rounded="0"
        title="Sélectionnez une date"
        show-adjacent-months
        :model-value="dateValue"
        hide-actions
        elevation="4"
        position="absolute"
        hide-header
        @update:model-value="updateFieldValue"
        @click:cancel="dateMenu = false"
      >
      </VDatePicker>
    </VLocaleProvider>
  </VMenu>
</template>

<script setup lang="ts">
import {
  dateHumanFormat,
  formatHumanReadableLocalDate,
  parseHumanReadableLocalDate,
  type LocalDate,
} from '@/types/date'
import { format, formatISO, isEqual, parseISO } from 'date-fns'
import type { PropType } from 'vue'
import type { VLocaleProvider } from 'vuetify/components'
import { VDatePicker } from 'vuetify/components'
import { VTextField } from 'vuetify/components/VTextField'
import NjTextField from './NjTextField.vue'

const props = defineProps({
  modelValue: {
    type: String as PropType<LocalDate | null>,
  },
  label: {
    type: String,
  },
  disabled: Boolean,
  required: Boolean,
  recommended: Boolean,
  newStyle: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits<{
  'update:model-value': [LocalDate | null]
}>()

const textFieldValue = ref<string | null>(null)
const dateValue = ref<Date | null>(null)
const dateMenu = ref(false)

const error = ref('')

const updateFieldValue = (date: any) => {
  console.debug('updateFieldValue', date)
  textFieldValue.value = date ? format(date, dateHumanFormat) : null
  dateValue.value = date
}

watch(textFieldValue, (v) => {
  if (!v) {
    error.value = ''
    emit('update:model-value', null)
  } else {
    const parsedDate = parseHumanReadableLocalDate(v)
    error.value = ''
    if (parsedDate.getTime() > 0) {
      emit('update:model-value', formatISO(parsedDate, { representation: 'date' }))
      dateValue.value = parsedDate
    } else {
      error.value = 'Format attendu : JJ/MM/AAAA'
    }
  }
})

watch(
  () => props.modelValue,
  (v) => {
    if (v == '' || v === null) {
      error.value = ''
      textFieldValue.value = ''
    }
    if (v && (!textFieldValue.value || !isEqual(parseISO(v), parseHumanReadableLocalDate(textFieldValue.value)))) {
      textFieldValue.value = formatHumanReadableLocalDate(v)
      dateValue.value = parseISO(v)
    }
  },
  {
    immediate: true,
  }
)
</script>
