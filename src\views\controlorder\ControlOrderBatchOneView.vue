<template>
  <NjPage
    :title="'Lot de contrôle : ' + (controlOrderBatch.value?.batchCode ?? '...')"
    expend-body
    :error-message="data.error"
    :can-go-back="{ name: 'ControlOrderBatchAllView' }"
  >
    <template #header-actions>
      <VRow>
        <VCol class="d-flex justify-end" style="gap: 16px">
          <NjBtn v-if="controlOrderBatch.value && !controlOrderBatch.value.step" @click="manageOperationsDialog = true">
            Gérer les opérations
          </NjBtn>
          <NjBtn
            v-if="controlOrderBatch.value && !controlOrderBatch.value.step"
            :disabled="!selections.length"
            @click="removeOperationFromControlOrderBatch"
          >
            Sortir les opérations du lot de contrôle
          </NjBtn>
          <NjBtn :loading="downloadExportExcelLoading" @click="downloadExportExcel">Télécharger la synthèse</NjBtn>
          <NjBtn :loading="downloadExportCsvLoading" @click="downloadExportCsv">Générer CSV</NjBtn>
          <NjBtn
            v-if="controlOrderBatch.value?.step && currentStepInStepper != 'Terminé'"
            variant="outlined"
            @click="goToPreviousStep"
          >
            Etape précédente
          </NjBtn>
          <NjBtn v-if="controlOrderBatch.value?.step != 'TO_BE_CONTROLLED'" @click="goToNextStep">
            Valider l'étape {{ currentStepItem?.shortName }}
          </NjBtn>
        </VCol>
      </VRow>
    </template>
    <template #sub-header>
      <VRow class="flex-column" dense>
        <VCol>
          <GenericStepper
            :steps="controlOrderBatchStepItems.map((i) => i.title).concat('Terminé')"
            :current-step="currentStepInStepper"
          />
        </VCol>
        <VCol>
          <VRow>
            <VCol cols="4">
              <SearchInput
                v-model:loading="data.loading"
                :model-value="pageFilter.search"
                @update:model-value="updateSearch"
              />
            </VCol>
          </VRow>
        </VCol>
      </VRow>
    </template>
    <template #body>
      <VRow class="w-100 flex-nowrap" dense>
        <VCol :cols="openDrawer ? '12' : '7'">
          <NjDataTable
            v-model:selections="selections"
            :headers="headers"
            :pageable="pageable"
            :page="data.value!"
            :disabled-row="(item: Operation) => !!item.validateImportInEmmyDateTime"
            :on-click-row="clickRow"
            :clicked-row="clickedRow"
            fixed
            :checkboxes="!controlOrderBatch.value?.step"
            multi-selection
            @update:pageable="updatePageable"
          />
        </VCol>
        <VCol v-show="!openDrawer" cols="5">
          <VCard class="h-100 content-layout">
            <VCardTitle class="content-layout__header">
              <VRow>
                <VCol>
                  <VTabs v-model="tab">
                    <VTab>Lot de contrôle</VTab>
                    <VTab>Documents associés</VTab>
                  </VTabs>
                </VCol>
                <VCol class="flex-grow-0">
                  <NjIconBtn :active="edit" icon="mdi-pencil" color="primary" rounded="0" @click="handleEditClick" />
                </VCol>
              </VRow>
            </VCardTitle>
            <VDivider />
            <VCardText class="content-layout__main">
              <VWindow v-model="tab" class="h-100">
                <VWindowItem :class="tab === 0 ? 'content-layout' : ''">
                  <ControlOrderBatchDetail
                    ref="controlOrderBatchDetail"
                    v-model:control-order-batch="controlOrderBatch.value"
                    v-model:edit="edit"
                    :number-of-operations="data.value?.totalElements"
                    :control-order-type="data.value?.content[0]?.standardizedOperationSheet.controlOrderType"
                    @update:control-order-batch="
                      () => {
                        edit = false
                      }
                    "
                  />
                </VWindowItem>
                <VWindowItem :class="tab === 1 ? 'content-layout' : ''">
                  <VRow class="flex-column content-layout">
                    <VCol v-if="userStore.isAdmin" class="content-layout__header">
                      <VSwitch v-model="showDisabledDocuments" label="Voir les documents inactifs" />
                    </VCol>
                    <VCol class="content-layout__main content-layout">
                      <ControlOrderBatchDocumentAllView
                        :id="props.id"
                        ref="controlOrderBatchDocumentAllViewRef"
                        :filter="{ controlOrderBatchId: props.id }"
                        actions
                        :show-disabled-documents="showDisabledDocuments"
                      />
                    </VCol>
                    <VCol class="content-layout__footer">
                      <NjBtn prepend-icon="mdi-plus-circle-outline " @click="addDocumentDialog = true"> Ajouter </NjBtn>
                      <SubmitDocumentDialog
                        v-model="addDocumentDialog"
                        :control-order-batch-id="props.id"
                        @save-document="
                          (event) => {
                            if (event.some((it) => it.type === 'success')) {
                              controlOrderBatchDocumentAllViewRef?.reload()
                            }
                          }
                        "
                      />
                    </VCol>
                  </VRow>
                </VWindowItem>
              </VWindow>
            </VCardText>
          </VCard>
        </VCol>
      </VRow>
      <ManageOperationInControlOrderBatchDialog
        v-if="controlOrderBatch.value && data.value?.content[0]"
        ref="manageOperationInControlOrderBatchRef"
        v-model="manageOperationsDialog"
        :control-order-batch="controlOrderBatch.value"
        :standardized-operation-sheet-id="data.value.content[0].standardizedOperationSheet.id"
      >
        <template #actions>
          <NjBtn variant="outlined" @click="handleCancelManageOperation">Annuler</NjBtn>
          <NjBtn
            @click="
              () => {
                manageOperationsDialog = false
                reloadControlOrderBatch(id)
              }
            "
          >
            Valider
          </NjBtn>
        </template>
      </ManageOperationInControlOrderBatchDialog>
    </template>
    <template #drawer>
      <OperationDrawer
        v-model="openDrawer"
        :operation="clickedRow"
        @update:operation="reload"
        @cancel-operation-k-o-p-n-c-e-e="reload"
      />
    </template>
  </NjPage>
</template>
<script lang="ts" setup>
import type { OperationFilter } from '@/api/operation'
import type { DataTableHeader } from '@/components/okta/NjDataTable.type'
import { type ControlOrderBatch } from '@/types/controlOrder'
import type { Operation } from '@/types/operation'
import OperationDrawer from '../operation/OperationDrawer.vue'
import { useSnackbarStore } from '@/stores/snackbar'
import { formatNumber } from '@/types/format'
import { VCardText, VCardTitle, VCol, VRow, VTab, VTabs, VWindow, VWindowItem } from 'vuetify/components'
import ControlOrderBatchDocumentAllView from './ControlOrderBatchDocumentAllView.vue'
import ManageOperationInControlOrderBatchDialog from './ManageOperationInControlOrderBatchDialog.vue'
import ControlOrderBatchDetail from './ControlOrderBatchDetail.vue'
import { useUserStore } from '@/stores/user'
import {
  controlOrderSiteStatusItems,
  controlOrderAfterSalesServiceStatusItems,
  controlOrderBatchStepItems,
  type ControlOrderBatchStep,
} from '@/types/controlOrder'
import { useDialogStore } from '@/stores/dialog'
import { deleteControlOrderBatchDialogRequest } from '../dialog/dialogRequest'
import { controlOrderTypeLabel } from '@/types/calcul/standardizedOperationSheet'
import SubmitDocumentDialog from '../document/SubmitDocumentDialog.vue'

const props = defineProps<{
  id: number
}>()

const { data, pageFilter, pageable, updatePageable, reload, updateFilter } = usePaginationInQuery<
  Operation,
  OperationFilter
>((filter, pageable) => operationApi.findAll(filter, pageable), {
  defaultPageFilter: {
    controlOrderBatchId: props.id,
  },
})

const userStore = useUserStore()

const edit = ref<boolean>(false)
const controlOrderBatchDetail = ref<typeof ControlOrderBatchDetail | null>(null)

const showDisabledDocuments = ref(false)

const headers: DataTableHeader<Operation>[] = [
  {
    title: 'Nom',
    value: 'operationName',
  },
  {
    title: 'Chrono',
    value: 'chronoCode',
  },
  {
    title: 'kWhc total',
    value: 'totalCumac',
    formater: (item) => formatNumber(item.classicCumac + item.precariousnessCumac),
    cellClass: 'text-right',
    sortable: false,
  },
  {
    title: 'Type de contrôle',
    value: 'controlOrderDetails.controlOrderType',
    formater: (_, value) => controlOrderTypeLabel.find((i) => i.value == value)?.label,
  },
  {
    title: 'Status du contrôle',
    value: 'controlOrderDetails.controlOrderStatus',
    formater: (_, value) => controlOrderSiteStatusItems.find((i) => i.value == value)?.title,
  },
  {
    title: 'Status du SAV',
    value: 'controlOrderDetails.afterSalesServiceStatus',
    formater: (_, value) => controlOrderAfterSalesServiceStatusItems.find((i) => i.value == value)?.title,
  },
  {
    title: 'Commentaire',
    value: 'controlOrderDetails.commmentary',
  },
]

const tab = ref(0)
const controlOrderBatch = ref(emptyValue<ControlOrderBatch>())

const openDrawer = ref(false)
const clickedRow = ref<Operation>()
const clickRow = async (item: Operation) => {
  if (item.id !== clickedRow.value?.id) {
    clickedRow.value = item
    openDrawer.value = true
    return
  }
  openDrawer.value = !openDrawer.value
}

const updateSearch = (v: string) => {
  const filter = {
    ...unref(pageFilter),
    search: v,
  }
  updateFilter(filter)
}

const reloadControlOrderBatch = (id: number) => handleAxiosPromise(controlOrderBatch, controlOrderBatchApi.findOne(id))

watch(
  () => props.id,
  (v) => {
    if (v) {
      reloadControlOrderBatch(v)
    }
  },
  {
    immediate: true,
  }
)

watch(openDrawer, (v) => {
  if (!v) {
    clickedRow.value = undefined
  }
})

const snackbarStore = useSnackbarStore()
const downloadExportExcelLoading = ref(false)
const downloadExportExcel = () => {
  downloadExportExcelLoading.value = true
  controlOrderBatchApi
    .exportExcelFile(props.id!)
    .then((response) => downloadFile(`${controlOrderBatch.value.value?.batchCode} INI.xlsx`, response.data))
    .catch(async (err) => {
      snackbarStore.setError(await handleAxiosException(err, undefined, { defaultMessage: "L'export a échoué" }))
    })
    .finally(() => (downloadExportExcelLoading.value = false))
}

const downloadExportCsvLoading = ref(false)
const downloadExportCsv = () => {
  downloadExportCsvLoading.value = true
  controlOrderBatchApi
    .exportCsvFile(props.id!)
    .then((response) => downloadFile(`Export ${controlOrderBatch.value.value?.batchCode}.csv`, response.data))
    .catch(async (err) => {
      snackbarStore.setError(await handleAxiosException(err, undefined, { defaultMessage: "L'export a échoué" }))
    })
    .finally(() => (downloadExportCsvLoading.value = false))
}

const manageOperationsDialog = ref(false)
const manageOperationInControlOrderBatchRef = ref<typeof ManageOperationInControlOrderBatchDialog>()
const handleCancelManageOperation = async () => {
  await Promise.all(
    manageOperationInControlOrderBatchRef.value!.updatedOperation.addedOperations.map((operationId: number) => {
      return controlOrderBatchApi.removeOperation(controlOrderBatch.value.value!.id, operationId)
    })
  )
  await Promise.all(
    manageOperationInControlOrderBatchRef.value!.updatedOperation.removedOperation.map((operationId: number) => {
      return controlOrderBatchApi.addOperation(controlOrderBatch.value.value!.id, operationId)
    })
  )
  manageOperationsDialog.value = false
}

watch(manageOperationsDialog, (v) => {
  if (!v) {
    reload()
  }
})

const handleEditClick = () => {
  tab.value = 0
  if (edit.value) {
    controlOrderBatchDetail.value?.check(() => {
      edit.value = false
    })
  } else {
    edit.value = true
  }
}
const controlOrderBatchDocumentAllViewRef = ref<typeof ControlOrderBatchDocumentAllView | null>(null)

const currentStepItem = computed(() =>
  controlOrderBatchStepItems.find((i) => i.value == controlOrderBatch.value.value?.step)
)

const currentStepInStepper = computed(() =>
  controlOrderBatch.value.value?.step == 'TO_BE_CONTROLLED' && controlOrderBatch.value.value?.step70OperationsCount == 0
    ? 'Terminé'
    : currentStepItem.value?.title
)

const goToNextStep = () => {
  const request = mapToControlOrderBatchRequest({ ...controlOrderBatch.value.value! })

  request.step = controlOrderBatchStepItems[
    controlOrderBatchStepItems.findIndex((i) => i.value == controlOrderBatch.value.value?.step) + 1
  ].value as ControlOrderBatchStep | undefined

  handleAxiosPromise(controlOrderBatch, controlOrderBatchApi.update(props.id, request), {
    afterSuccess: () => snackbarStore.setSuccess("L'étape a bien été mise à jour"),
    afterError: (err) => snackbarStore.setError(err ?? 'Une erreur est survenue'),
  })
}

const goToPreviousStep = () => {
  const request = mapToControlOrderBatchRequest({ ...controlOrderBatch.value.value! })
  request.step =
    controlOrderBatchStepItems[
      controlOrderBatchStepItems.findIndex((i) => i.value == controlOrderBatch.value.value?.step) - 1
    ].value ?? (undefined as ControlOrderBatchStep | undefined)

  handleAxiosPromise(controlOrderBatch, controlOrderBatchApi.update(props.id, request), {
    afterSuccess: () => snackbarStore.setSuccess("L'étape a bien été mise à jour"),
    afterError: (err) => snackbarStore.setError(err ?? 'Une erreur est survenue'),
  })
}

const router = useRouter()
const selections = ref<Operation[]>([])
const dialogStore = useDialogStore()

const removeOperation = async () => {
  await Promise.all(
    selections.value.map((ope) => controlOrderBatchApi.removeOperation(controlOrderBatch.value.value!.id, ope.id))
  )
    .then(() => {
      snackbarStore.setSuccess('Les opération ont bien été sortie du lot de contrôle')
    })
    .catch(async (err) => {
      snackbarStore.setError(err)
    })
    .finally(() => reload())
}

const removeOperationFromControlOrderBatch = async () => {
  if (selections.value.length == data.value.value?.totalElements) {
    if (await dialogStore.addAlert(deleteControlOrderBatchDialogRequest)) {
      await removeOperation()
      selections.value = []
      controlOrderBatchApi
        .delete(props.id)
        .then(() => {
          snackbarStore.setSuccess('Le lot de contrôle a bien été supprimé')
          router.push({ name: 'ControlOrderBatchAllView' })
        })
        .catch(async (error) =>
          snackbarStore.setError(
            await handleAxiosException(error, undefined, {
              defaultMessage: 'Une erreur est survenue lors de la suppression du lot de contrôle',
            })
          )
        )
    }
  } else {
    removeOperation()
  }
}

const addDocumentDialog = ref(false)
</script>
