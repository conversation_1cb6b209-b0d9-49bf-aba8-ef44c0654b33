<template>
  <NjPage
    v-bind="$attrs"
    :loading="controlOrderExportTemplate.loading"
    :title="
      props.id && controlOrderExportTemplate.value?.name
        ? controlOrderExportTemplate.value.name
        : 'Création d\'un modèle arrêté controle'
    "
  >
    <template #header-actions>
      <VRow class="flex-nowrap" dense>
        <VCol>
          <NjBtn variant="outlined" color="error" :to="{ name: 'ControlOrderAdministrationView' }"> Annuler </NjBtn>
        </VCol>
        <VCol>
          <NjBtn @click="duplicate"> Dupliquer </NjBtn>
        </VCol>
        <VCol>
          <NjBtn @click="save"> Enregistrer </NjBtn>
        </VCol>
      </VRow>
    </template>
    <template #body>
      <VForm v-if="localControlOrderExportTemplate && !controlOrderExportTemplate.loading" ref="form">
        <VCard>
          <VCardText>
            <VRow class="flex-column">
              <VCol>
                <VTextField v-model="localControlOrderExportTemplate.name" label="Nom" :rules="[requiredRule]" />
              </VCol>
              <VCol>
                <ColumnValue
                  v-model="localControlOrderExportTemplate.fullfillByApplicantColumnValues"
                  v-model:merged-column-label="localControlOrderExportTemplate.fullfillByApplicantMergedColumnLabel"
                  label="Données remplies par le demandeur"
                />
              </VCol>
              <VCol>
                <ColumnValue
                  v-model="localControlOrderExportTemplate.fullfillByControlOrganismColumnValues"
                  v-model:merged-column-label="
                    localControlOrderExportTemplate.fullfillByControlOrganismMergedColumnLabel
                  "
                  label="Données remplies par l'organisme ayant réalisé le contrôle"
                  :offset="localControlOrderExportTemplate.fullfillByApplicantColumnValues.length"
                />
              </VCol>
              <VCol>
                <ColumnValue
                  v-model="localControlOrderExportTemplate.fullfillByContactControlOrganismColumnValues"
                  v-model:merged-column-label="
                    localControlOrderExportTemplate.fullfillByContactControlOrganismMergedColumnLabel
                  "
                  label="Données remplies par l'organisme ayant réalisé le contrôle par contact"
                  :offset="
                    localControlOrderExportTemplate.fullfillByApplicantColumnValues.length +
                    localControlOrderExportTemplate.fullfillByControlOrganismColumnValues.length
                  "
                />
              </VCol>
              <VCol>
                <ColumnValue
                  v-model="localControlOrderExportTemplate.completedByApplicantColumnValues"
                  v-model:merged-column-label="localControlOrderExportTemplate.completedByApplicantMergedColumnLabel"
                  label="Données complétées par le demandeur"
                  :offset="
                    localControlOrderExportTemplate.fullfillByApplicantColumnValues.length +
                    localControlOrderExportTemplate.fullfillByControlOrganismColumnValues.length +
                    +localControlOrderExportTemplate.fullfillByContactControlOrganismColumnValues.length
                  "
                />
              </VCol>
              <VCol>
                <VRow>
                  <VCol>
                    <RemoteAutoComplete
                      v-model="operationToTest"
                      label="Opération de Base pour tester"
                      :query-for-one="(a) => operationApi.findById(a)"
                      :query-for-all="(v, pageable) => operationApi.findAll({ search: v }, pageable)"
                      :item-title="(arg) => `${arg.chronoCode} - ${arg.operationName}`"
                      item-value="id"
                      infinite-scroll
                      return-object
                    />
                  </VCol>
                  <VCol class="flex-grow-0">
                    <NjBtn :disabled="!operationToTest?.id || loading" @click="handleTestExport">
                      Tester le modèle
                    </NjBtn>
                  </VCol>
                </VRow>
              </VCol>
            </VRow>
          </VCardText>
        </VCard>
      </VForm>
    </template>
  </NjPage>
  <ConfirmUnsavedDataDialog v-model:unsaved-data-dialog="unsavedDataDialog" @save="save" />
</template>
<script setup lang="ts">
import {
  type ControlOrderExportTemplate,
  mapToControlOrderExportTemplateRequest,
} from '@/types/controlOrderExportTemplate'
import { controlOrderExportTemplateApi } from '@/api/controlOrderExportTemplateApi'
import { VCard, VCardText, VCol, VForm, VRow, type VTextField } from 'vuetify/components'
import { cloneDeep } from 'lodash'
import { requiredRule } from '@/types/rule'
import { useSnackbarStore } from '@/stores/snackbar'
import ColumnValue from './ColumnValue.vue'
import type { Operation } from '@/types/operation'
import { operationApi } from '@/api/operation'
import { downloadFile } from '@/types/file'
import ConfirmUnsavedDataDialog from '@/components/ConfirmUnsavedDataDialog.vue'

const props = defineProps({
  id: Number,
})

const controlOrderExportTemplate = ref(emptyValue<ControlOrderExportTemplate>())
const localControlOrderExportTemplate = ref<ControlOrderExportTemplate>(makeEmptyControlOrderExportTemplate())
const router = useRouter()

const { unsavedDataDialog, failedSave, succeedSave, check, disable } = useUnsavedData(localControlOrderExportTemplate)

const duplicate = () => {
  controlOrderExportTemplate.value = emptyValue<ControlOrderExportTemplate>()
  check(() => {
    disable()
    localControlOrderExportTemplate.value!.name += '- Copie'
    router.push({
      name: 'ControlOrderExportTemplateNewView',
    })
  })
}

watch(
  () => props.id,
  (v) => {
    if (v) {
      handleAxiosPromise(controlOrderExportTemplate, controlOrderExportTemplateApi.findOne(v), {
        afterSuccess: () => {
          localControlOrderExportTemplate.value = cloneDeep(controlOrderExportTemplate.value.value!)
          succeedSave()
        },
        afterError: () =>
          snackbarStore.setError(controlOrderExportTemplate.value.error ?? 'Erreur lors de la récupération du modèle'),
      })
    }
  },
  {
    immediate: true,
  }
)

const snackbarStore = useSnackbarStore()

const form = ref<VForm | null>(null)

const save = async () => {
  if (!(await form.value?.validate())?.valid) {
    return
  }

  if (controlOrderExportTemplate.value.value?.id) {
    handleAxiosPromise(
      controlOrderExportTemplate,
      controlOrderExportTemplateApi.save(
        controlOrderExportTemplate.value.value?.id,
        mapToControlOrderExportTemplateRequest(localControlOrderExportTemplate.value!)
      ),
      {
        afterSuccess: () => {
          succeedSave()
          snackbarStore.setSuccess('Le modèle a bien été sauvegardé')
        },
        afterError: () => {
          failedSave()
          snackbarStore.setError(controlOrderExportTemplate.value.error ?? 'Erreur lors de la sauvegarde')
        },
      }
    )
  } else {
    handleAxiosPromise(
      controlOrderExportTemplate,
      controlOrderExportTemplateApi.create(
        mapToControlOrderExportTemplateRequest(localControlOrderExportTemplate.value!)
      ),
      {
        afterSuccess: (res) => {
          snackbarStore.setSuccess('Le modèle a bien été créé')
          disable()
          router.push({
            name: 'ControlOrderExportTemplatOneView',
            params: { id: res.data.id },
          })
        },
        afterError: () =>
          snackbarStore.setError(controlOrderExportTemplate.value.error ?? 'Erreur lors de la création'),
      }
    )
  }
}

const loading = ref(false)
const operationToTest = ref<Operation>()

const handleTestExport = () => {
  check(() => {
    loading.value = true
    controlOrderExportTemplateApi
      .testExport(props.id!, operationToTest.value?.id!)
      .then((response) => {
        downloadFile('export test modele.xlsx', response.data)
      })
      .catch(async (err) => snackbarStore.setError(await handleAxiosException(err)))
      .finally(() => {
        loading.value = false
      })
  })
}
</script>
