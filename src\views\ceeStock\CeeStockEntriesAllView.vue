<template>
  <VRow class="flex-column content-layout" dense>
    <VCol class="content-layout__header">
      <h2>Historique des entrées en stock pour le chrono en cours</h2>
    </VCol>
    <VCol>
      <NjDataTable
        :headers="headers"
        :pageable="pageable"
        :page="data.value!"
        :loading="data.loading"
        fixed
        @update:pageable="updatePageable"
      />
    </VCol>
  </VRow>
</template>
<script setup lang="ts">
import { ceeStockEntryApi } from '@/api/ceeStockHistory'
import NjDataTable from '@/components/okta/NjDataTable.vue'
import { type CeeStockEntry } from '@/types/ceeStockEntry'
import { formatHumanReadableLocalDate } from '@/types/date'
import { formatNumber } from '@/types/format'

const props = defineProps({
  operationId: {
    type: Number,
    required: true,
  },
})

const headers = [
  {
    title: 'Date',
    value: 'creationDateTime',
    formater: (item: CeeStockEntry) => formatHumanReadableLocalDate(item.creationDateTime),
  },
  {
    title: 'Organisation',
    value: 'entity',
    formater: (item: CeeStockEntry) => `(${item.entity.id}) ${item.entity.name}`,
  },
  {
    title: 'Prime CEE Agence €',
    value: '',
    sortable: false,
    formater: (item: CeeStockEntry) =>
      formatPriceNumber(
        (item.classicCumac * item.classicValuationValue +
          item.precariousnessCumac * item.precariousnessValuationValue) /
          1000
      ),
    cellClass: 'text-right justify-end',
  },
  {
    title: 'Charge Cellule CEE',
    value: '',
    sortable: false,
    formater: (item: CeeStockEntry) =>
      formatPriceNumber(((item.classicCumac + item.precariousnessCumac) * item.fee) / 1000),
    cellClass: 'text-right justify-end',
  },
  {
    title: 'Nb. kWhc',
    value: 'sumCumac',
    formater: (item: CeeStockEntry) => formatKwhcNumber(item.classicCumac + item.precariousnessCumac),
    sortable: false,
    cellClass: 'text-right justify-end',
  },
  {
    title: 'Nb. Class. kWhc',
    value: 'classicCumac',
    formater: (_: any, value: number) => formatKwhcNumber(value),
    cellClass: 'text-right justify-end',
  },
  {
    title: 'Nb. Préc. kWhc',
    value: 'precariousnessCumac',
    formater: (_: any, value: number) => formatKwhcNumber(value),
    cellClass: 'text-right justify-end',
  },
  {
    title: 'Prime CEE Ag. Class.',
    value: '',
    sortable: false,
    formater: (item: CeeStockEntry) => formatPriceNumber((item.classicCumac * item.classicValuationValue) / 1000),
    cellClass: 'text-right justify-end',
  },
  {
    title: 'Prime CEE Ag. Préc.',
    value: '',
    sortable: false,
    formater: (item: CeeStockEntry) =>
      formatNumber((item.precariousnessCumac * item.precariousnessValuationValue) / 1000),
    cellClass: 'text-right justify-end',
  },
  {
    title: 'Charge Cel. CEE Class €',
    value: 'chargeClass',
    sortable: false,
    formater: (item: CeeStockEntry) => formatPriceNumber((item.classicCumac * item.fee) / 1000),
    cellClass: 'text-right justify-end',
  },
  {
    title: 'Charge Cel. CEE Préc €',
    value: 'chargePreca',
    sortable: false,
    formater: (item: CeeStockEntry) => formatPriceNumber((item.precariousnessCumac * item.fee) / 1000),
    cellClass: 'text-right justify-end',
  },
]
watch(
  () => props.operationId,
  () => {
    reload()
  }
)

const { data, reload, pageable, updatePageable } = usePagination<CeeStockEntry>((filter, pageable) =>
  ceeStockEntryApi.findAll(props.operationId, pageable, { active: false })
)
</script>
