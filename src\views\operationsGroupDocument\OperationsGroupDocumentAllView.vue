<template>
  <div class="h-100 content-layout">
    <VProgressLinear
      absolute
      indeterminate
      :active="operationsGroupDocuments.loading"
      color="primary"
    ></VProgressLinear>

    <VSwitch
      v-if="userStore.isAdmin"
      v-model="showDisabledDocuments"
      class="content-layout__header px-2"
      label="Voir les documents inactifs"
    />

    <OperationsGroupDocumentDataTable
      class="content-layout__main"
      :operations-group="operationsGroup"
      :items="operationsGroupDocumentDataTableItems"
      :reload="reload"
    />
    <OperationsGroupDocumentAllViewFooter
      v-if="operationsGroup"
      class="content-layout__footer"
      :operations-group="operationsGroup"
      :total-elements="operationsGroupDocuments.value?.totalElements"
      :number-of-properties="numberOfProperties"
      :number-of-standardized-operation-sheet="numberOfStandardizedOperationSheet"
      @save-document="onSaveDocumentEvent"
    />
  </div>
</template>
<script setup lang="ts">
import { operationsGroupDocumentApi } from '@/api/operationsGroupDocument'
import type { OperationsGroupDocumentFilter } from '@/api/operationsGroupDocument'
import OperationsGroupDocumentDataTable from './OperationsGroupDocumentDataTable.vue'
import OperationsGroupDocumentAllViewFooter from './OperationsGroupDocumentAllViewFooter.vue'
import type { EnhancedDocument } from '@/types/document'
import type { EnhancedOperationsGroup } from '@/types/operationsGroup'
import { useUserStore } from '@/stores/user'
import type { DocumentDataTableItem } from '../operationdocument/types'
import type SendDocumentResult from '../document/sendDocumentResult'

const props = defineProps({
  id: {
    type: Number,
    required: true,
  },
  actions: {
    type: Boolean,
    default: false,
  },
  operationsGroup: Object as PropType<EnhancedOperationsGroup>,
  numberOfProperties: Number,
  numberOfStandardizedOperationSheet: Number,
  displayConventionButton: Boolean,
})

const {
  data: operationsGroupDocuments,
  pageFilter,
  updateFilter,
  reload,
} = usePagination<EnhancedDocument, OperationsGroupDocumentFilter>(
  (filter, pageable) => operationsGroupDocumentApi.findAll(filter, pageable),
  {
    operationsGroupId: props.id,
    active: true as boolean | undefined,
  },
  {
    size: 1000,
    sort: ['document.creationDateTime,DESC'],
  }
)

const showDisabledDocuments = ref(false)
const userStore = useUserStore()

watch(showDisabledDocuments, (v) => {
  if (v) {
    updateFilter({ ...pageFilter.value, active: undefined })
  } else {
    updateFilter({ ...pageFilter.value, active: true })
  }
})

const operationsGroupDocumentDataTableItems = computed((): DocumentDataTableItem[] => {
  const mappedRowOperationsGroupDocument: DocumentDataTableItem[] = (
    operationsGroupDocuments.value.value?.content ?? []
  ).map((i) => {
    return {
      id: i.id,
      emediaId: i.document.emediaId,
      emediaErrors: i.document.emediaErrors,
      documentType: i.documentType,
      creationDateTime: i.document.creationDateTime,
      format: getFileExtension(i.document.originalFilename),
      description: i.description,
      fillableTemplateId: i.documentType.fillableTemplate?.id,
      templateId: i.documentType.template?.id,
      active: i.active,
    }
  })

  return mappedRowOperationsGroupDocument
})

const onSaveDocumentEvent = (event: SendDocumentResult[]) => {
  if (event.some((it) => it.type === 'success')) {
    reload()
  }
}

defineExpose({
  reload,
})
</script>
