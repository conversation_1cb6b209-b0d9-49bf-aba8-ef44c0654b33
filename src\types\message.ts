import type { Operation } from './operation'
import { formatHumanReadableLocalDate, type LocalDateTime } from './date'
import type { DocumentType } from './documentType'
import type { User } from './user'
import type { Document } from './document'
import type { BusinessPlan, BusinessPlanSummary } from './businessPlan'

export interface Message {
  id: number
  operationId?: number
  operationsGroupId?: number
  parentId?: number
  repliesNumber: number
  sender: User
  recipients: string
  copy: string
  message: string
  concernedDocumentTypes: DocumentType[]
  accounted: boolean
  reasons: MessageAccountingReason[]
  sendDateTime: LocalDateTime
  metaMessage: MetaMessage | null
  screenshots: Document[]
}

export interface MessageRequest
  extends Omit<
    Message,
    'id' | 'sender' | 'concernedDocumentTypes' | 'reasons' | 'sendDateTime' | 'repliesNumber' | 'metaMessage'
  > {
  concernedDocumentTypeIds: number[]
  reasonIds: number[]
  metaMessage: MetaMessageRequest | null
  businessPlanId?: number
}

export interface MessageAccountingReason {
  id: number
  reason: string
  visible: boolean
}

export type MessageAccountingReasonRequest = Omit<MessageAccountingReason, 'id'>

export interface MessageTemplate {
  id: number
  message: string
  isAccountedMessage: boolean
  accountingReason?: MessageAccountingReason
  updateDateTime: LocalDateTime
}

export type MessageTemplateRequest = Omit<MessageTemplate, 'id' | 'accountingReason'> & {
  accountingReasonId?: number
}

export const makeEmptyMessageRequest = (): MessageRequest => ({
  recipients: '',
  copy: '',
  message: '',
  concernedDocumentTypeIds: [],
  accounted: false,
  reasonIds: [],
  metaMessage: null,
  screenshots: [],
})

export const mapToMessageRequest = (message: Message): MessageRequest => ({
  operationId: message.operationId,
  operationsGroupId: message.operationsGroupId,
  recipients: message.recipients,
  copy: message.copy,
  message: message.message,
  concernedDocumentTypeIds: message.concernedDocumentTypes.map((doc) => doc.id),
  accounted: message.accounted,
  reasonIds: message.reasons.map((reason) => reason.id),
  metaMessage: null,
  screenshots: [],
})

export const makeEmptyMessageTemplate = (): MessageTemplate => ({
  id: 0,
  accountingReason: {
    id: 0,
    reason: '',
    visible: true,
  },
  isAccountedMessage: false,
  message: '',
  updateDateTime: '',
})

export interface AtypicalValuationMessage {
  '@type': 'AtypicalValuationMessage'
  classicValuationValue: number
  precariousnessValuationValue: number
  accepted: boolean | null
  respondedUser: User | null
  respondedDateTime: LocalDateTime | null
}

export const makeEmptyAtypicalValuationMessage = (): AtypicalValuationMessage => ({
  '@type': 'AtypicalValuationMessage',
  classicValuationValue: 0,
  precariousnessValuationValue: 0,
  accepted: null,
  respondedUser: null,
  respondedDateTime: null,
})

export type MetaMessage = AtypicalValuationMessage

export interface AtypicalValuationMessageRequest {
  '@type': 'AtypicalValuationMessageRequest'
  classicValuationValue: number
  precariousnessValuationValue: number
  accepted: boolean | null
}

export interface SendFinalVersionMessageRequest {
  '@type': 'SendFinalVersionMessageRequest'
}

export interface OperationToProcessMessageRequest {
  '@type': 'OperationToProcessMessageRequest'
}

export const makeEmptyAtypicalValuationMessageRequest = (): AtypicalValuationMessageRequest => ({
  '@type': 'AtypicalValuationMessageRequest',
  classicValuationValue: 0,
  precariousnessValuationValue: 0,
  accepted: null,
})

export type MetaMessageRequest =
  | AtypicalValuationMessageRequest
  | SendFinalVersionMessageRequest
  | OperationToProcessMessageRequest

export const atypicalMessageHeader = 'Demande de valorisation atypique'

export const dafMail = (operation: Operation, isFirstMail: boolean) => {
  const result = `${isFirstMail ? 'Entrée en stock' : 'Modification de stock'} à réaliser pour le Chronos ${
    operation.chronoCode
  }
  DATE : ${formatHumanReadableLocalDate(new Date().toISOString())}
  Numéro de Chronos :  ${operation.chronoCode}
  Nom de la réservation : ${operation.operationName}
  Organisation : ${operation.entity.name}
  Nombre de CEE classiques demandés : ${formatNumber(operation.classicCumac / 1000)} MWhc
  Valorisation CEE classiques : ${formatNumber(getClassicValuationValue(operation))} €/MWhc
  Nombre de CEE précarités demandés : ${formatNumber(operation.precariousnessCumac / 1000)} MWhc
  Valorisation CEE précarité : ${formatNumber(getPrecariousnessValuationValue(operation))} €/MWhc
  Nombre total de CEE demandé : ${formatNumber((operation.classicCumac + operation.precariousnessCumac) / 1000)} MWhc
  Numéro(s) de chantier : ${
    [operation.works1?.id, operation.works2?.id, operation.works3?.id].filter((s) => s != undefined).join('/') ||
    'aucun'
  }
  Montant à passer en stock : ${formatNumber(
    (operation.classicCumac * getClassicValuationValue(operation) +
      operation.precariousnessCumac * getPrecariousnessValuationValue(operation)) /
      1000
  )} €
  Charge pôle CEE sur le chantier : ${formatNumber(getChargeCellule(operation))} €`

  return result
}

export const toProcessMail = (
  arg: Operation | { businessPlan: BusinessPlan; businessPlanSummary: BusinessPlanSummary }
) => {
  const result =
    'businessPlanSummary' in arg
      ? `Demande de traitement d'opération
  Bonjour, j'aimerais transformer ces simulations en opérations, je vous prie.

  Nom du Business Plan : ${arg.businessPlan.name}
  Total de CEE classiques demandés : ${formatNumber(arg.businessPlanSummary.classicCumacSum / 1000)} MWhc
  Total Valorisations CEE classiques : ${formatNumber(arg.businessPlanSummary.classicValuationAmountSum)} €
  Total de CEE précarités demandés : ${formatNumber(arg.businessPlanSummary.precariousnessCumacSum / 1000)} MWhc
  Total Valorisation CEE précarité : ${formatNumber(arg.businessPlanSummary.precariousnessValuationAmountSum)} €
  Nombre total de CEE demandé : ${formatNumber(
    (arg.businessPlanSummary.classicCumacSum + arg.businessPlanSummary.precariousnessCumacSum) / 1000
  )} MWhc`
      : `Demande de traitement d'opération
  Bonjour, j'aimerais transformer cette simulation en opération, je vous prie.

  Nom de la réservation : ${arg.operationName}
  Organisation : ${arg.entity.name}
  Nombre de CEE classiques demandés : ${formatNumber(arg.classicCumac / 1000)} MWhc
  Valorisation CEE classiques : ${formatNumber(getClassicValuationValue(arg))} €/MWhc
  Nombre de CEE précarités demandés : ${formatNumber(arg.precariousnessCumac / 1000)} MWhc
  Valorisation CEE précarité : ${formatNumber(getPrecariousnessValuationValue(arg))} €/MWhc
  Nombre total de CEE demandé : ${formatNumber((arg.classicCumac + arg.precariousnessCumac) / 1000)} MWhc`

  return result
}

export const vfMail = (operation: Operation) =>
  `Madame, Monsieur,

Le dispositif des certificats d'économie d'énergie a permis à ${operation.entity.name} de vous proposer et de réaliser des actions de performance énergétique pour votre site:
${operation.finalAddress.additionalPostalAddress}
${operation.finalAddress.street}
${operation.finalAddress.postalCode} ${operation.finalAddress.city}

Nature de l'opération: ${operation.standardizedOperationSheet.operationCode}. ${operation.standardizedOperationSheet.description}

Dans le cadre de la réglementation régissant ce dispositif, la validation et la signature des différents justificatifs ont permis le dépôt de la demande de certificat d'économie d'énergie auprès de l'administration.
A ce titre, vous trouverez ci-joint le dossier constitué de ces pièces administratives qui pourront vous être réclamées lors d'un contrôle éventuel initié par les pouvoirs publics.

En vous souhaitant bonne réception et en restant à votre disposition pour tout renseignement complémentaire`
