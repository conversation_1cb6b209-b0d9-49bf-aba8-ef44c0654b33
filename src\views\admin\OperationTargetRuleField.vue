<template>
  <VRow class="flex-column">
    <VCol>
      <div class="text-section-title">Type d'opération :</div>
      <VTextField
        label="Type d'opération"
        :model-value="modelValue.operationCodeTarget"
        @update:model-value="emits('update:model-value', { ...modelValue, operationCodeTarget: $event })"
      />
    </VCol>
    <VDivider />
    <VCol v-if="targetedFosList.value?.content">
      <div class="text-section-title">Opérations concernées :</div>
      <RemoteAutoComplete
        v-model="targetedFosList.value!.content"
        label="Fiche opération"
        :query-for-one="findOneFos"
        :query-for-all="findAllFosTarget"
        item-title="operationCode"
        return-object
        chips
        multiple
        infinite-scroll
        @update:model-value="
          (v: StandardizedOperationSheet[]) =>
            emits('update:model-value', {
              ...props.modelValue,
              target: ';' + v.map((it) => it.id).join(';') + ';',
            })
        "
      >
        <template #selection="{ item }">
          <StandardizedOperationSheetChip :standardized-operation-sheet="item.raw as StandardizedOperationSheet" />
        </template>
        <template #item="{ item, props }">
          <StandardizedOperationSheetItem
            :standardized-operation-sheet="item.raw as StandardizedOperationSheet"
            v-bind="props"
          />
        </template>
      </RemoteAutoComplete>
    </VCol>
    <VDivider />
    <VCol v-if="excludedFosList.value?.content">
      <div class="text-section-title">Opérations non concernées :</div>
      <RemoteAutoComplete
        v-model="excludedFosList.value!.content"
        label="Fiche opération"
        :query-for-one="findOneFos"
        :query-for-all="findAllFosExclude"
        item-title="operationCode"
        return-object
        chips
        multiple
        infinite-scroll
        @update:model-value="
          (v: StandardizedOperationSheet[]) =>
            emits('update:model-value', {
              ...props.modelValue,
              exclusion: ';' + v.map((it) => it.id).join(';') + ';',
            })
        "
      >
        <template #selection="{ item }">
          <StandardizedOperationSheetChip :standardized-operation-sheet="item.raw as StandardizedOperationSheet" />
        </template>
        <template #item="{ item, props }">
          <StandardizedOperationSheetItem
            :standardized-operation-sheet="item.raw as StandardizedOperationSheet"
            v-bind="props"
          />
        </template>
      </RemoteAutoComplete>
    </VCol>
    <VDivider />
    <VCol>
      <div class="text-section-title">Période de validité du document :</div>
      <VRow>
        <VCol cols="6">
          <NjDatePicker
            label="Début"
            :model-value="modelValue.minSignedDate"
            clearable
            @update:model-value="
              emits('update:model-value', { ...props.modelValue, minSignedDate: $event ?? undefined })
            "
          />
        </VCol>
        <VCol cols="6">
          <NjDatePicker
            label="Fin"
            :model-value="modelValue.maxSignedDate"
            clearable
            @update:model-value="
              emits('update:model-value', { ...props.modelValue, maxSignedDate: $event ?? undefined })
            "
          />
        </VCol>
        <VDivider />
        <VCol>
          <div class="text-section-title">Document requis pour :</div>
          <div class="d-flex align-center">
            <VRadioGroup
              inline
              :model-value="modelValue.bonusType"
              @update:model-value="emits('update:model-value', { ...props.modelValue, bonusType: $event })"
            >
              <VRadio label="CPE" value="EPC" />
              <VRadio label="Coup de pouce" value="BOOST" />
              <VRadio label="Précarité" value="PRECARIOUSNESS" />
            </VRadioGroup>
            <VLink @click="emits('update:model-value', { ...props.modelValue, bonusType: null })">
              Réinitialiser la sélection
            </VLink>
          </div>
        </VCol>
      </VRow>
    </VCol>
    <slot name="more" />
  </VRow>
</template>
<script setup lang="ts">
import { type PropType } from 'vue'
import { type OperationTargetRule } from '@/types/operationTargetRule'
import { VCol, VDivider, VRadioGroup, VRow } from 'vuetify/components'
import type { Page, Pageable } from '@/types/pagination'
import type { StandardizedOperationSheet } from '@/types/calcul/standardizedOperationSheet'
import { useSnackbarStore } from '@/stores/snackbar'

const props = defineProps({
  id: Number,
  modelValue: {
    type: Object as PropType<OperationTargetRule>,
    required: true,
  },
})

const emits = defineEmits<{
  'update:model-value': [OperationTargetRule | undefined]
}>()

const snackbarStore = useSnackbarStore()

const targetedFosList = ref(emptyValue<Page<StandardizedOperationSheet>>())
const excludedFosList = ref(emptyValue<Page<StandardizedOperationSheet>>())

watch(
  () => props.id,
  () => {
    if (props.modelValue?.target.length && props.modelValue.target.length > 2) {
      handleAxiosPromise(
        targetedFosList,
        standardizedOperationSheetApi.findAll(
          { size: 100 },
          {
            ids: props.modelValue.target
              .substring(1, props.modelValue.target.length - 1)
              .split(';')
              .map((it) => parseInt(it)),
          }
        ),
        {
          afterError() {
            snackbarStore.setError(targetedFosList.value.error!)
          },
        }
      )
    } else {
      targetedFosList.value = succeedValue(makeEmptyPage())
    }
    if (props.modelValue?.exclusion.length && props.modelValue.exclusion.length > 2) {
      handleAxiosPromise(
        excludedFosList,
        standardizedOperationSheetApi.findAll(
          { size: 100 },
          {
            ids: props.modelValue.exclusion
              .substring(1, props.modelValue.exclusion.length - 1)
              .split(';')
              .map((it) => parseInt(it)),
          }
        ),
        {
          afterError() {
            snackbarStore.setError(excludedFosList.value.error!)
          },
        }
      )
    } else {
      excludedFosList.value = succeedValue(makeEmptyPage())
    }
  },
  {
    immediate: true,
  }
)

const findOneFos = (v: unknown) => standardizedOperationSheetApi.findOne(v as number)

const findAllFosTarget = (v: string, pageable: Pageable) =>
  standardizedOperationSheetApi.findAll(pageable, {
    search: v,
    excluded: targetedFosList.value.value?.content.map((it) => it.id),
  })

const findAllFosExclude = (v: string, pageable: Pageable) =>
  standardizedOperationSheetApi.findAll(pageable, {
    search: v,
    excluded: excludedFosList.value.value?.content.map((it) => it.id),
  })
</script>
