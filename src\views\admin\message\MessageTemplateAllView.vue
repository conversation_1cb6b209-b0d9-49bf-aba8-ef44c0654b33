<template>
  <NjPage title expend-body v-bind="$attrs" :error-message="data.error">
    <template #header-actions>
      <NjBtn @click="reasonDialogActive = true"> Gestion des Raisons </NjBtn>
      <NjBtn @click="createPrerecordedMessage">Créer un message pré-enregistré</NjBtn>
    </template>
    <template #subtitle>
      <VRow>
        <VCol cols="3">
          <SearchInput
            v-model:loading="data.loading"
            :model-value="pageFilter.search"
            @update:model-value="updateSearch"
          />
        </VCol>
      </VRow>
    </template>
    <template #body>
      <NjDataTable
        :headers="headers"
        :pageable="pageable"
        :page="data.value"
        :on-click-row="clickRow"
        fixed
        class="w-100"
        @update:pageable="updatePageable"
      >
        <template #[`item.message`]="{ item }">
          {{
            truncate(item.message, {
              length: 32,
              omission: ' [...]',
            })
          }}
        </template>
        <template #[`item.isAccountedMessage`]="{ item }">
          <NjBooleanIcon :condition="item.isAccountedMessage" />
        </template>
        <template #[`item.action`]="{ item }">
          <NjIconBtn
            icon="mdi-delete"
            color="primary"
            :loading="messageTemplateToDelete.id === item.id && deletingMessageTempate.loading"
            @click.stop="deleteMessageTemplate(item)"
          />
        </template>
      </NjDataTable>
    </template>
  </NjPage>
  <VNavigationDrawer location="end" :model-value="drawer.active" width="480" disable-resize-watcher>
    <MessageTemplateCardForm
      :id="drawer.id"
      @click:parent="drawer.id = $event"
      @saved="onSaved"
      @cancel="drawer.active = false"
    />
  </VNavigationDrawer>

  <VDialog v-model="reasonDialogActive">
    <VCard>
      <VCardTitle class="d-flex align-center">
        Gestion des raisons

        <VCheckbox
          v-model="accountingReasonsComposition.pageFilter.value.visible"
          label="Cacher les désactivées"
          class="ms-4"
          :false-value="null"
        />

        <VProgressCircular
          v-show="deletingReason.loading || accountingReasonsComposition.data.value.loading"
          class="ms-4"
          indeterminate
        />

        <VSpacer />

        <NjBtn @click="addReasonDialog.active = true">Ajouter une raison de comptage</NjBtn>
      </VCardTitle>
      <VCardText>
        <VRow>
          <VCol v-for="r in accountingReasonsComposition.data.value.value?.content" :key="r.id" cols="6" md="3" xl="2">
            <VCard
              height="100%"
              class="px-6 pa-4 d-flex align-center justify-space-between"
              :class="{ 'text-blue-grey-lighten-1': !r.visible }"
              @click="switchVisibleReason(r)"
            >
              <span>{{ r.reason }}</span>
              <NjSwitch :model-value="r.visible" class="ma-n3" />
            </VCard>
          </VCol>
        </VRow>
      </VCardText>
      <VCardActions>
        Total: {{ accountingReasonsComposition.data.value.value?.totalElements }}
        <VSpacer />
        <VPagination
          density="comfortable"
          :length="accountingReasonsComposition.data.value.value?.totalPages"
          total-visible="3"
          rounded="0"
          :model-value="(accountingReasonsComposition.data.value.value?.number ?? 0) + 1"
          color="primary"
          @update:model-value="accountingReasonsComposition.pageable.value.page = $event - 1"
        />
      </VCardActions>
    </VCard>
  </VDialog>

  <VDialog v-model="addReasonDialog.active" max-width="640px">
    <VForm ref="addReasonFormRef" :disabled="creatingReason.loading" @submit.prevent="createReason">
      <VCard>
        <VCardTitle>Ajouter d'une raison de comptage</VCardTitle>
        <ErrorAlert :message="creatingReason.error" />
        <VCardText>
          <VTextField v-model="addReasonDialog.reason" autofocus :rules="[requiredRule]" />
        </VCardText>
        <VCardActions class="justify-end">
          <NjBtn :loading="creatingReason.loading" @click="createReason">Enregistrer</NjBtn>
        </VCardActions>
      </VCard>
    </VForm>
  </VDialog>

  <AlertDialog
    title="Suppression du message pré-enregistré"
    v-bind="deleteMessageConfirmDialogComposition.props"
    width="40%"
  >
    Le message pré-enregistré suivant sera supprimé.<br />
    <blockquote>
      <VIcon icon="mdi-format-quote-open"></VIcon>
      <div style="flex-grow: 1">{{ messageTemplateToDelete.message }}</div>
      <VIcon icon="mdi-format-quote-close" class="align-self-end"></VIcon>
    </blockquote>
  </AlertDialog>
</template>
<script lang="ts" setup>
import { messageAccountingReasonApi, type MessageAccountingReasonFilter } from '@/api/messageAccountingReason'
import { messageTemplateApi, type MessageTemplateFilter } from '@/api/messageTemplate'
import NjPage from '@/components/NjPage.vue'
import type { DataTableHeader } from '@/components/okta/NjDataTable.type'
import NjDataTable from '@/components/okta/NjDataTable.vue'
import { useSnackbarStore } from '@/stores/snackbar'
import { type MessageAccountingReason, type MessageTemplate } from '@/types/message'
import { requiredRule } from '@/types/rule'
import { truncate } from 'lodash'
import {
  VCardActions,
  VCardText,
  VCardTitle,
  VCheckbox,
  VForm,
  VNavigationDrawer,
  VPagination,
  VProgressCircular,
  VTextField,
} from 'vuetify/components'
import MessageTemplateCardForm from './MessageTemplateCardForm.vue'
import { useDialogStore } from '@/stores/dialog'

const { data, pageable, updatePageable, updateFilter, reload, pageFilter } = usePaginationInQuery<
  MessageTemplate,
  MessageTemplateFilter
>((filter, pageable) => messageTemplateApi.findAll(filter, pageable), {
  defaultPageablePartial: {
    page: 0,
    size: 50,
    sort: ['id'],
  },
})

const headers: DataTableHeader[] = [
  {
    title: 'Début Message',
    value: 'message',
  },
  {
    title: 'Message Comptabilisé',
    value: 'isAccountedMessage',
  },
  {
    title: 'Raison Comptabilisation',
    value: 'accountingReason.reason',
  },
  {
    title: 'Action',
    value: 'action',
    sortable: false,
  },
]

const drawer = ref({
  active: false,
  id: 0,
})
const clickRow = (item: MessageTemplate) => {
  if (!drawer.value.active || item.id !== drawer.value.id) {
    drawer.value = {
      active: true,
      id: item.id,
    }
  } else {
    drawer.value.active = false
  }
}

const onSaved = () => {
  drawer.value.active = false
  reload()
}

const createPrerecordedMessage = () => {
  drawer.value = { id: 0, active: true }
}

// search
const updateSearch = (v: string) => {
  const filter = {
    ...unref(pageFilter),
    search: v,
  }
  updateFilter(filter)
}

const reasonDialogActive = ref(false)

// reasons
const accountingReasonsComposition = usePagination(
  (filter, pageable) => messageAccountingReasonApi.findAll(filter, pageable),
  {} as MessageAccountingReasonFilter,
  {
    page: 0,
    size: 20,
    sort: ['reason'],
  }
)

// add reasons
const addReasonDialog = ref({
  active: false,
  reason: '',
})
const addReasonFormRef = ref<typeof VForm | null>(null)
const creatingReason = ref(emptyValue<MessageAccountingReason>())
const snackbarStore = useSnackbarStore()
const createReason = async () => {
  if ((await addReasonFormRef.value!.validate()).valid) {
    handleAxiosPromise(
      creatingReason,
      messageAccountingReasonApi.create({ reason: addReasonDialog.value.reason, visible: true }),
      {
        afterSuccess() {
          snackbarStore.setSuccess('Raison de comptage sauvegardée')
          addReasonDialog.value.active = false
          accountingReasonsComposition.reload()
        },
      }
    )
  }
}
watch(
  () => addReasonDialog.value.active,
  (v) => {
    if (v) {
      addReasonDialog.value.reason = ''
      creatingReason.value = emptyValue()
    }
  }
)

// delete Reason
const dialogStore = useDialogStore()
const deletingReason = ref(emptyValue<MessageAccountingReason>())

const switchVisibleReason = async (reason: MessageAccountingReason) => {
  if (
    await dialogStore.addAlert({
      title: reason.visible
        ? 'Désactivation de la raison “' + reason.reason + '”'
        : 'Résactivation de la raison “' + reason.reason + '”',
      message: reason.visible
        ? 'Voulez-vous désactiver la raison "' + reason.reason + '" ?'
        : 'Voulez-vous réactiver la raison "' + reason.reason + '" ?',
      positiveButton: reason.visible ? 'Désactiver' : 'Réactiver',
      maxWidth: '480px',
    })
  ) {
    handleAxiosPromise(
      deletingReason,
      messageAccountingReasonApi.update(
        {
          reason: reason.reason,
          visible: !reason.visible,
        },
        reason.id
      ),
      {
        afterSuccess: () => {
          accountingReasonsComposition.reload()
          deletingReason.value = emptyValue()
        },
        afterError: snackbarStore.setError,
      }
    )
  }
}

// Delete message template
const deleteMessageConfirmDialogComposition = useConfirmAlertDialog()
const messageTemplateToDelete = ref<MessageTemplate>(makeEmptyMessageTemplate())
const deletingMessageTempate = ref(emptyValue<void>())
const deleteMessageTemplate = async (item: MessageTemplate) => {
  messageTemplateToDelete.value = item
  const result = await deleteMessageConfirmDialogComposition.confirm()
  if (result) {
    handleAxiosPromise(deletingMessageTempate, messageTemplateApi.deleteById(item.id), {
      afterSuccess: reload,
      async afterError() {
        snackbarStore.setError(await handleAxiosException(deletingMessageTempate.value.error))
      },
    })
  }
}
</script>

<style scoped>
blockquote {
  border: 1px grey solid;
  padding: 8px;
  display: flex;
}
</style>
