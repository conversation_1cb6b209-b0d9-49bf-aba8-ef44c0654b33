<template>
  <div class="content-layout">
    <div class="content-layout__header px-4 pt-4">
      <h1><PERSON><PERSON><PERSON><PERSON> controle</h1>
      <VTabs v-model="tab">
        <VTab>Modèle arrêté controle </VTab>
        <VTab>Taux de non satisfaction annuel</VTab>
      </VTabs>
      <VDivider />
    </div>
    <NjPage v-if="tab === 0" class="content-layout__main">
      <template #header>
        <VContainer fluid class="pb-0">
          <VRow>
            <VSpacer />
            <VCol class="flex-grow-0">
              <NjBtn :to="{ name: 'ControlOrderExportTemplateNewView' }">Créer un nouveau modèle</NjBtn>
            </VCol>
          </VRow>
        </VContainer>
      </template>
      <template #body>
        <NjDataTable
          :headers="headers"
          :page="data.value!"
          :pageable="pageable"
          fixed
          @update:pageable="updatePageable"
          @click-row="
            (item) => {
              router.push({
                name: 'ControlOrderExportTemplatOneView',
                params: { id: item.id },
              })
            }
          "
        />
      </template>
    </NjPage>
    <NjPage v-if="tab === 1" class="content-layout__main">
      <template #header>
        <VContainer fluid class="pb-0">
          <VRow>
            <VSpacer />
            <VCol class="flex-grow-0">
              <NjBtn @click="handleCreateAnnualNotSatisfyingRate">Ajouter ou modifier un taux</NjBtn>
              <AlertDialog title="Ajouter ou modifier un taux" v-bind="createAnnualNotSatisfyingRateDialog.props">
                <VForm ref="annualNotSatifyingRateForm">
                  <VRow class="flex-column">
                    <VCol>
                      <VTextField
                        v-model="annualtNotSatisfyingRateRequest.year"
                        :rules="[requiredRule]"
                        type="number"
                        label="Année"
                      />
                    </VCol>
                    <VCol>
                      <VTextField
                        v-model="annualtNotSatisfyingRateRequest.notSatisfyingRate"
                        :rules="[requiredRule]"
                        label="Taux de non satisfaction"
                        type="number"
                      />
                    </VCol>
                  </VRow>
                </VForm>
                <template #positiveButton>
                  <NjBtn
                    @click="
                      async () => {
                        if ((await annualNotSatifyingRateForm!.validate()).valid) {
                          createAnnualNotSatisfyingRateDialog.props['onClick:positive']()
                        }
                      }
                    "
                  >
                    Valider
                  </NjBtn>
                </template>
              </AlertDialog>
            </VCol>
            <VCol class="flex-grow-0">
              <NjBtn
                variant="outlined"
                color="red"
                :disabled="annualtNotSatisfyingRateSelections.length === 0"
                @click="
                  () => {
                    handleDelete()
                  }
                "
              >
                Supprimer
              </NjBtn>
            </VCol>
          </VRow>
        </VContainer>
      </template>
      <template #body>
        <NjDataTable
          v-model:selections="annualtNotSatisfyingRateSelections"
          :headers="annualNotSatisfyingHeaders"
          :page="annualNotSatifyingRates.value!"
          :pageable="annualNotSatifyingRatePageable"
          fixed
          @update:pageable="annualNotSatifyingRateUpdatePageable"
        />
      </template>
    </NjPage>
  </div>
</template>
<script setup lang="ts">
import type { DataTableHeader } from '@/components/okta/NjDataTable.type'
import NjDataTable from '@/components/okta/NjDataTable.vue'
import { useSnackbarStore } from '@/stores/snackbar'
import type { AnnualNotSatisfyingRateRequest, AnnualNotSatisfyingRate } from '@/types/annualNotSatisfyingRate'
import { formatHumanReadableLocalDate } from '@/types/date'
import { VCol, VRow, VTab, VTabs, VTextField } from 'vuetify/components'
import { VForm } from 'vuetify/components/VForm'
import { requiredRule } from '@/types/rule'

const tab = ref()

const headers: DataTableHeader[] = [
  {
    title: 'Nom',
    value: 'name',
  },
  {
    title: 'Créateur',
    value: 'creationUser',
    formater: (item, value) => displayFullnameUser(value),
  },
  {
    title: 'Date de création',
    value: 'creationDateTime',
    formater: (item, value) => formatHumanReadableLocalDate(value),
  },
  {
    title: 'MAJ par',
    value: 'updateUser',
    formater: (item, value) => displayFullnameUser(value),
  },
  {
    title: 'MAJ à',
    value: 'updateDateTime',
    formater: (item, value) => formatHumanReadableLocalDate(value),
  },
]

const { data, pageable, updatePageable } = usePagination((filter, pageable) => {
  return controlOrderExportTemplateApi.findAll(pageable, filter)
})

const router = useRouter()
const snackbarStore = useSnackbarStore()

const annualNotSatisfyingHeaders: DataTableHeader[] = [
  {
    title: 'Année',
    value: 'year',
  },
  {
    title: 'Taux de non satisfaction',
    value: 'notSatisfyingRate',
    formater: (item, value) => formatNumber(value),
  },
]
const {
  data: annualNotSatifyingRates,
  pageable: annualNotSatifyingRatePageable,
  updatePageable: annualNotSatifyingRateUpdatePageable,
  reload: annualNotSatifyingRateReload,
} = usePagination((filter, pageable) => {
  return annualNotSatisfyingRateApi.findAll(pageable)
})

const annualtNotSatisfyingRateRequest = ref<AnnualNotSatisfyingRateRequest>({
  year: 0,
  notSatisfyingRate: 0,
})
const createAnnualNotSatisfyingRateDialog = useConfirmAlertDialog()
const annualNotSatifyingRateForm = ref<typeof VForm | null>(null)
const annualtNotSatisfyingRateSelections = ref<AnnualNotSatisfyingRate[]>([])
const handleCreateAnnualNotSatisfyingRate = async () => {
  if (annualtNotSatisfyingRateSelections.value.length > 0) {
    annualtNotSatisfyingRateRequest.value = { ...annualtNotSatisfyingRateSelections.value[0] }
  } else {
    annualtNotSatisfyingRateRequest.value = {
      year: 0,
      notSatisfyingRate: 0,
    }
  }

  if (!(await createAnnualNotSatisfyingRateDialog.confirm())) {
    return
  }
  annualNotSatisfyingRateApi
    .update(annualtNotSatisfyingRateRequest.value)
    .then(() => {
      annualNotSatifyingRateReload()
      snackbarStore.setSuccess('Le taux a bien été enregistré')
    })
    .catch(async (err) => {
      snackbarStore.setError(await handleAxiosException(err))
    })
}

const handleDelete = () => {
  annualNotSatisfyingRateApi
    .delete(annualtNotSatisfyingRateSelections.value.map((i) => i.year))
    .then(() => {
      snackbarStore.setSuccess('Les taux ont bien été supprimé')
      annualNotSatifyingRateReload()
    })
    .catch(async (err) => {
      snackbarStore.setError(await handleAxiosException(err))
    })
}
</script>
