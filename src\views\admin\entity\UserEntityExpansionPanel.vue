<template>
  <NjExpansionPanel>
    <template #title>
      <div class="d-flex align-center fill-width">
        {{ title }}
        <VSpacer />
        <NjIconBtn v-if="selectedUser.length > 0" icon="mdi-delete" color="primary" @click.stop="selectedUser = []" />
        <VLink
          size="small"
          icon="mdi-format-list-bulleted"
          style="font-weight: initial; font-size: initial"
          @click.stop="openDialog"
        >
          Utilisateurs
        </VLink>
      </div>
    </template>
    <UserDisplayValue :no-data="noDataLabel" :model-value="selectedUser[0]" />
  </NjExpansionPanel>
  <UserAllViewDialog v-model="userDialog" v-model:selections="selectedUser" :title="dialogTitle" :filter="filter" />
</template>
<script setup lang="ts">
import type { UserFilter } from '@/api/user'
import type { User } from '@/types/user'
import UserAllViewDialog from '@/views/UserAllViewDialog.vue'
import type { PropType } from 'vue'

const modelValue = defineModel<User>()

defineProps({
  title: String,
  noDataLabel: String,
  dialogTitle: String,
  filter: Object as PropType<UserFilter>,
})

const userDialog = ref(false)
const selectedUser = ref<User[]>([])

watch(selectedUser, (v) => {
  if (v.length > 0) {
    modelValue.value = v[0]
  } else {
    modelValue.value = undefined
  }
})

watch(
  modelValue,
  (v) => {
    if (v) {
      selectedUser.value = [v]
    } else {
      selectedUser.value = []
    }
  },
  {
    immediate: true,
  }
)

const openDialog = () => {
  userDialog.value = true
  selectedUser.value = []
}
</script>
